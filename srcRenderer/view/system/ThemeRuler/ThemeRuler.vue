<template>
  <div class="w100 h100 ruler-config">
    <div class="tabsContent">
      <div class="aside">
        <div class="input">
          <el-autocomplete placeholder="请输入菜单" v-model="tabName" :fetch-suggestions="querySearch"
            :trigger-on-focus="false" @select="handleSelect" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
            <template slot-scope="{ item }">
              <span :title="item.value">{{ item.value }}</span>
            </template>
          </el-autocomplete>
        </div>
        <div style="height: calc(100% - 48px); overflow-y: auto">
          <div v-for="(item, index) in filterList" :key="index"
            :class="['industry-item', { active: editTab === item.id }]" @click="handleClickIndustry(item)">
            <span>{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div class="tabs">
        <!-- <el-tabs>
            <el-tab-pane
              label="停车规则配置"
              v-if="hasPermission('themeRuler:stop')"
            > -->
        <stopConfig v-if="hasPermission('themeRuler:stop') &&
          currentTab == 'themeRuler:stop'
          "></stopConfig>
        <Layout class="layout--theme-1" lazy label="周期配置" v-if="hasPermission('themeRuler:time') &&
          currentTab == 'themeRuler:time'
          ">
          <template slot="aside">
            <ElementTree type="department" @node-click="selectGroupTwoNode"></ElementTree>
          </template>
          <template slot="content">
            <div class="form-group">
              <div class="title">周定义</div>
              <div class="query-item">
                <span>配&ensp;置&ensp;值:</span>
                <el-select placeholder="请选择" v-model="addSysConfigWeek.config_value">
                  <el-option label="周日" value="1"></el-option>
                  <el-option label="周一" value="2"></el-option>
                  <el-option label="周二" value="3"></el-option>
                  <el-option label="周三" value="4"></el-option>
                  <el-option label="周四" value="5"></el-option>
                  <el-option label="周五" value="6"></el-option>
                  <el-option label="周六" value="7"></el-option>
                </el-select>
              </div>
              <div class="query-item">
                <span>配置说明:</span>
                <el-input v-model="addSysConfigWeek.config_desc"></el-input>
              </div>
              <div class="query-item">
                <span>备&emsp;&emsp;注:</span>
                <el-input v-model="addSysConfigWeek.remark"></el-input>
              </div>
              <el-button type="primary" @click="addSysConfigWeekFun" style="margin: 15px 0 20px 0">确认
              </el-button>
            </div>
            <div class="form-group">
              <div class="title">月定义</div>
              <div class="query-item">
                <span>月份归属:</span>
                <el-select placeholder="请选择" v-model="addSysConfigMonth.month_belong">
                  <el-option label="下月" value="1"></el-option>
                  <el-option label="本月" value="0"></el-option>
                </el-select>
              </div>
              <div class="query-item">
                <span>配&ensp;置&ensp;值:</span>
                <el-input placeholder="月开始日期（几号：1-28）" v-model="addSysConfigMonth.month_start"></el-input>
              </div>
              <div class="query-item">
                <span>配置说明:</span>
                <el-input v-model="addSysConfigMonth.config_desc"></el-input>
              </div>
              <div class="query-item">
                <span>备&emsp;&emsp;注:</span>
                <el-input v-model="addSysConfigMonth.remark"></el-input>
              </div>
              <el-button type="primary" @click="addSysConfigMonthFun" style="margin: 15px 0 20px 0">确认
              </el-button>
            </div>
          </template>
        </Layout>

        <SpeedConfig v-if="hasPermission('themeRuler:speed') &&
          currentTab == 'themeRuler:speed'
          "></SpeedConfig>

        <AlarmConfig v-if="hasPermission('themeRuler:job') && currentTab == 'themeRuler:job'
          "></AlarmConfig>

        <AlarmEventConfig v-if="hasPermission('themeRuler:jobEvent') &&
          currentTab == 'themeRuler:jobEvent'
          "></AlarmEventConfig>

        <MediaPlatFormUrl v-if="hasPermission('themeRuler:screen') &&
          currentTab == 'themeRuler:screen'
          "></MediaPlatFormUrl>

        <DangerEventConfig v-if="hasPermission('themeRuler:dangerEvent') &&
          currentTab == 'themeRuler:dangerEvent'
          "></DangerEventConfig>

        <AlarmEventCarConfig v-if="hasPermission('themeRuler:alarmEvent') &&
          currentTab == 'themeRuler:alarmEvent'
          "></AlarmEventCarConfig>

        <TTSMould v-if="hasPermission('themeRuler:ttsMould') &&
          currentTab == 'themeRuler:ttsMould'
          "></TTSMould>
        <FuelMould v-if="hasPermission('themeRuler:fuelMould') &&
          currentTab == 'themeRuler:fuelMould'
          "></FuelMould>
        <AreaPermission v-if="hasPermission('themeRuler:areaPermission') &&
          currentTab == 'themeRuler:areaPermission'
          "></AreaPermission>

        <OliPermission v-if="hasPermission('themeRuler:oilsource') &&
          currentTab == 'themeRuler:oilsource'
          "></OliPermission>

        <Overtemperaturesource v-if="hasPermission('themeRuler:overtemperaturesource') &&
          currentTab == 'themeRuler:overtemperaturesource'
          "></Overtemperaturesource>

        <OnLineStatusConfig v-if="hasPermission('themeRuler:onLineStatus') &&
          currentTab == 'themeRuler:onLineStatus'
          "></OnLineStatusConfig>

        <Rules1078 v-if="hasPermission('themeRuler:1078rules') &&
          currentTab == 'themeRuler:1078rules'
          "></Rules1078>

        <Rules808 v-if="hasPermission('themeRuler:808rules') &&
          currentTab == 'themeRuler:808rules'
          "></Rules808>

        <WaitingTime v-if="hasPermission('themeRuler:waiting') &&
          currentTab == 'themeRuler:waiting'
          "></WaitingTime>
        <Troubleshooting v-if="hasPermission('themeRuler:terminalTroubleConf') &&
          currentTab == 'themeRuler:terminalTroubleConf'
          "></Troubleshooting>
        <ShortMessage v-if="hasPermission('themeRuler:AlarmSMSSendConf') &&
          currentTab === 'themeRuler:AlarmSMSSendConf'
          ">
        </ShortMessage>
        <ShortStopTime v-if="hasPermission('themeRuler:shortStopTime') &&
          currentTab === 'themeRuler:shortStopTime'
          ">
        </ShortStopTime>
        <EventMsgConfig v-if="hasPermission('themeRuler:EventMsgConfig') &&
          currentTab === 'themeRuler:EventMsgConfig'">
        </EventMsgConfig>
        <DrivingUnderFatigueConfig v-if="hasPermission('themeRuler:TiredDriveReportConfig') &&
          currentTab === 'themeRuler:TiredDriveReportConfig'">
        </DrivingUnderFatigueConfig>
        <MsgConfigLonggang v-if="currentTab === 'themeRuler:MsgConfigLonggang'">
        </MsgConfigLonggang>
        <DrivingScoringRulesConfig v-if="currentTab === 'themeRuler:DrivingScoringRulesConfig'">

        </DrivingScoringRulesConfig>
        <AudioRulesConfig v-if="currentTab === 'themeRuler:AudioRulesConfig'">
        </AudioRulesConfig>
        <OffLineAlarm v-if="currentTab === 'themeRuler:OffLineRulesConfig'">

        </OffLineAlarm>
        <fenseTypeConfig v-if="currentTab === 'themeRuler:fenseTypeConfig'"></fenseTypeConfig>
        <MileageSource v-if="currentTab === 'themeRuler:mileSourceConfig'"></MileageSource>
        <ProlongedAbnormalStopAlarm v-if="currentTab === 'themeRuler:stopOvertime'"></ProlongedAbnormalStopAlarm>
        <SystemName v-if="currentTab == 'themeRuler:systemName'
          "></SystemName>
        <FuelConsumptionCoefficient v-if="currentTab === 'themeRuler:FuelConsumptionCoefficient'">
        </FuelConsumptionCoefficient>
        <LEDcommand v-if="currentTab === 'themeRuler:LEDcommand'"></LEDcommand>
        <AddressDistribution v-if="currentTab === 'themeRuler:AddressDistribution'">
        </AddressDistribution>
        <AlarmAiAnalysis v-if="currentTab === 'themeRuler:AlarmAiAnalysis'"></AlarmAiAnalysis>
        <BalancedScorecard v-if="hasPermission('themeRuler:BalancedScorecard') && currentTab==='themeRuler:BalancedScorecard'"></BalancedScorecard>
       
<UltrasonicSensor v-if="hasPermission('themeRuler:ultrasonicSensor') && currentTab==='themeRuler:ultrasonicSensor'"></UltrasonicSensor>
<FacialRecognition v-if="currentTab === 'themeRuler:FacialRecognition' && hasPermission('themeRuler:FacialRecognition')"></FacialRecognition>
        <!--
						添加新配置页面需要在data里面的tabsList添加对应的对象，参照上面添加即可 -->
      </div>
    </div>
  </div>
</template>

<script>
import MediaPlatFormUrl from "./MediaPlatformUrl";
import DangerEventConfig from "./DangerEventConfig";
import AlarmEventCarConfig from "./AlarmEventCarConfig";
import AlarmConfig from "./AlarmConfig/AlarmConfig";
import AlarmEventConfig from "./AlarmEventConfig/AlarmEventConfig";
import SpeedConfig from "./components/SpeedConfig/SpeedConfig";
import TTSMould from "./components/TTSMould";
import FuelMould from "./components/FuelMould";

import AreaPermission from "./components/AreaPermission";
import OliPermission from "./components/OliPermission";
import Overtemperaturesource from "./components/OvertemperatureSource";
import OnlineOfflineVehicle from "../../customReport/reportComponent/bar/OnlineOfflineVehicle";
import OnLineStatusConfig from "./components/OnLineStatusConfig";
import WaitingTime from "./components/WaitingTime";
import ShortMessage from "./components/ShortMessage";
import ShortStopTime from './components/ShortStopTime'
import Rules1078 from "./components/Rules1078";
import Rules808 from "./components/Rules808";
import stopConfig from "./components/stopConfig";
import pinyinMatch from "pinyin-match";
import Troubleshooting from "./components/Troubleshooting";
import EventMsgConfig from "./components/EventMsgConfig";
import DrivingUnderFatigueConfig from "./components/DrivingUnderFatigueConfig";
import MsgConfigLonggang from "./components/MsgConfigLonggang";
import DrivingScoringRulesConfig from "./components/DrivingScoringRulesConfig";
import AudioRulesConfig from "./components/AudioRulesConfig";
import OffLineAlarm from "./components/OffLineAlarm";
import fenseTypeConfig from "./components/fenseTypeConfig";
import MileageSource from "./components/MileageSource";
import ProlongedAbnormalStopAlarm from "./components/ProlongedAbnormalStopAlarm"
import SystemName from "./components/SystemName"
import FuelConsumptionCoefficient from "./components/FuelConsumptionCoefficient.vue";
import LEDcommand from "./components/LEDcommand";
import AddressDistribution from "./components/AddressDistribution";
import AlarmAiAnalysis from "./components/AlarmAiAnalysis";
import BalancedScorecard from "./components/BalancedScorecard.vue";
import FacialRecognition from "./components/FacialRecognition";
import UltrasonicSensor from "./components/UltrasonicSensor.vue";




export default {
  name: "themeRuler",
  components: {
    OnLineStatusConfig,
    WaitingTime,
    OnlineOfflineVehicle,
    MediaPlatFormUrl,
    DangerEventConfig,
    AlarmEventCarConfig,
    AlarmConfig,
    AlarmEventConfig,
    SpeedConfig,
    FuelMould,
    TTSMould,
    AreaPermission,
    OliPermission,
    Overtemperaturesource,
    Rules1078,
    Rules808,
    stopConfig,
    Troubleshooting,
    ShortMessage,
    ShortStopTime,
    EventMsgConfig,
    DrivingUnderFatigueConfig,
    MsgConfigLonggang,
    DrivingScoringRulesConfig,
    AudioRulesConfig,
    OffLineAlarm,
    fenseTypeConfig,
    MileageSource,
    ProlongedAbnormalStopAlarm,
    SystemName,
    FuelConsumptionCoefficient,
    LEDcommand,
    AddressDistribution,
    AlarmAiAnalysis,
    BalancedScorecard,
    FacialRecognition,
    UltrasonicSensor
  },
  data() {
    return {
      /* 车辆树配置------------------------------------------------- */
      RuleCarObj: null,
      RuleCarID: "RuleCarID",
      RuleCarSetting: {
        check: { enable: false, chkStyle: "radio" },
        data: { simpleData: { enable: true } },
        view: { showIcon: true },
        callback: { onClick: this.zTreeOnClicVehicle },
      },
      /* 车辆树配置------------------------------------------------- */

      /* 那一坨选中框的----------------------------------------- */
      adasTypeList: [],
      adasTypeListAll: [],
      adasTypeListIsShow: false,
      /* 那一坨选中框的---------------------------------------- */

      /* 行车停车-------------------------------------------- */
      checkList: true,
      stopRuleState: false,
      stopList: [
        { value: 0, label: "0" },
        { value: 1, label: "1" },
        { value: 2, label: "2" },
        { value: 3, label: "3" },
        { value: 8, label: "4" },
        { value: 10, label: "5" },
      ],
      //车辆上下线
      // onlineState:0,
      //恶劣天气车速百分比
      PercList: [
        { value: 50, label: "50" },
        { value: 55, label: "55" },
        { value: 60, label: "60" },
        { value: 65, label: "65" },
        { value: 70, label: "70" },
        { value: 75, label: "75" },
        { value: 80, label: "80" },
        { value: 85, label: "85" },
        { value: 90, label: "90" },
        { value: 95, label: "95" },
      ],
      stopTimeList: [
        { value: 1, label: "1分钟" },
        { value: 2, label: "2分钟" },
        { value: 3, label: "3分钟" },
        { value: 8, label: "8分钟" },
        { value: 10, label: "10分钟" },
        { value: 15, label: "15分钟" },
        { value: 20, label: "20分钟" },
        { value: 30, label: "30分钟" },
        { value: 60, label: "1小时" },
      ],
      /* 输入框-------------------------------------------------- */
      addSysConfigMonth: {
        id: null,
        obj_id: null,
        obj_type: 1,
        config_type: 1,
        month_belong: null,
        month_start: null,
        config_desc: null,
        remark: null,
      },
      addSysConfigWeek: {
        id: null,
        obj_id: null,
        obj_type: 1,
        config_type: 0,
        config_value: null,
        config_desc: null,
        remark: null,
      },
      addSysConfigSpeed: {
        id: null,
        obj_id: null,
        obj_type: null, //
        config_type: 4, // 4超速配置 5恶劣天气超速配置(当前解决方案, 处理都使用4, 后台处理时写入俩条数据)
        weather_perc: null,
        config_value_normal: null,
        config_value_turn: null,
        config_value_cross: null,
        config_value_arrive: null,
        config_value_zebra: null,
        config_desc: null,
        remark: null,
      },
      speedObj: "",
      isWeatherSpeed: false,
      sysConfigSpeedList: [],
      sysConfigList: [],
      sysConfigListRunAndStop: [],
      /* 输入框-------------------------------------------------- */
      addSysConfigRunAndStop: {
        id: null,
        obj_id: null,
        speed: null,
        time: null,
        // online:0,
      },
      getMediaDate: {
        page: 1,
        count: 30,
        vehicle_id: null,
      },
      MediaDateList: [],
      addMediaDateList: {
        vehicleId: null,
        alarmType: null,
        uploadAuto: null,
        uploadInterval: null,
        uploadDelay: null,
        uploadSpeed: null,
        uploadDuration: null,
        remark: null,
      },
      deleteMediaList: {
        vehicleId: null,
        id: null,
      },
      themerulerLoading: false,
      totalCount: 0,
      adduploadAuto: null,
      adduploadAutoUp: null,
      test: null,
      updateMediaDateList: {
        id: null,
        vehicleId: null,
        alarmType: null,
        uploadAuto: null,
        uploadInterval: null,
        uploadDelay: null,
        uploadSpeed: null,
        uploadDuration: null,
        remark: null,
      },
      tabsList: [
        //新增配置需添加对应的对象
        {
          value: "停车规则配置",
          id: "themeRuler:stop",
        },
        // {
        //   value: "周期配置",
        //   id: "themeRuler:time",
        // },
        {
          value: "超时驾驶规则配置",
          id: "themeRuler:TiredDriveReportConfig",
        },
        {
          value: "速度配置",
          id: "themeRuler:speed",
        },
        {
          value: "行业报警配置",
          id: "themeRuler:job",
        },
        {
          value: "行业报警事件配置",
          id: "themeRuler:jobEvent",
        },
        {
          value: "视频平台地址配置",
          id: "themeRuler:screen",
        },
        {
          value: "危险事件配置",
          id: "themeRuler:dangerEvent",
        },
        {
          value: "报警事件规则配置",
          id: "themeRuler:alarmEvent",
        },
        {
          value: "TTS模板",
          id: "themeRuler:ttsMould",
        },
        {
          value: "油量异常故障模板",
          id: "themeRuler:fuelMould",
        },
        {
          value: "区域级别配置",
          id: "themeRuler:areaPermission",
        },

        {
          value: "油耗来源配置",
          id: "themeRuler:oilsource",
        },
        {
          value: "超温报警来源配置",
          id: "themeRuler:overtemperaturesource",
        },
        {
          value: "在线状态配置",
          id: "themeRuler:onLineStatus",
        },
        {
          value: "1078规则配置",
          id: "themeRuler:1078rules",
        },
        {
          value: "808转发配置",
          id: "themeRuler:808rules",
        },
        {
          value: "待命时长配置",
          id: "themeRuler:waiting",
        },
        {
          value: "故障检测配置",
          id: "themeRuler:terminalTroubleConf",
        },
        {
          value: '短信配置',
          id: 'themeRuler:AlarmSMSSendConf'
        },
        {
          value: '短倒配置',
          id: 'themeRuler:shortStopTime'
        },
        { value: '事件短信模板配置', id: 'themeRuler:EventMsgConfig' },
        { value: '龙港短信配置', id: 'themeRuler:MsgConfigLonggang' },
        { value: '驾驶评分规则配置', id: 'themeRuler:DrivingScoringRulesConfig' },
        { value: '音频规则配置', id: 'themeRuler:AudioRulesConfig' },
        { value: '未上线报警配置', id: 'themeRuler:OffLineRulesConfig' },
        { value: '里程来源配置', id: 'themeRuler:mileSourceConfig' },
        { value: '围栏类型配置', id: 'themeRuler:fenseTypeConfig' },
        { value: '长时间异常停车报警配置', id: 'themeRuler:stopOvertime' },
        { value: '自定义平台名称', id: 'themeRuler:systemName' },
        { value: '油耗系数配置', id: 'themeRuler:FuelConsumptionCoefficient' },
        { value: '商砼出厂LED设置', id: 'themeRuler:LEDcommand' },
        { value: '地址下发配置', id: 'themeRuler:AddressDistribution' },
        { value: '报警智能分析配置', id: 'themeRuler:AlarmAiAnalysis' },
        { value: '平衡计分卡配置', id: 'themeRuler:BalancedScorecard' },
        {value:'人脸识别配置',id:'themeRuler:FacialRecognition'},
        { value: '液位传感器配置', id: 'themeRuler:ultrasonicSensor' },

      ],
      tabName: "", //搜索结果
      editTab: "themeRuler:stop", //暂存当前点击的tab名，用于背景高亮
      currentTab: "themeRuler:stop", //当前展现的tab-
    };
  },
  mounted() {
    // 加载页面时就查询用户下超速配置信息
    this.handleClickIndustry({
      value: "停车规则配置",
      id: "themeRuler:stop",
    });
    this.initUserSpeedConfig();
  },
  computed: {
    filterList: function () {
      let list = this.tabsList.filter((item) => {
        if (this.hasPermission(item.id)) {
          return item;
        }
      });
      return list;
    },
  },
  methods: {
    handleClickIndustry(item) {
      this.editTab = item.id;
      this.currentTab = item.id;
    },
    querySearch(value, cd) {
      let list = [];
      list = this.filterList.filter((data) => {
        return pinyinMatch.match(data.value, value);
      });
      cd(list);
    },
    handleSelect(item) {
      this.editTab = item.id;
      this.currentTab = item.id;
    },
    zTreeOnClicVehicle(event, treeId, treeNode) {
      this.selectGroupVehicle(treeNode);
    },

    selectGroupVehicle: _.debounce(function (treeNode) {
      if (treeNode.type != 4) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请选择车辆！",
        });
        return;
      } else {
        this.adduploadAutoUp = null;
        this.addMediaDateList.alarmType = null;
        this.addMediaDateList.uploadAuto = null;
        this.addMediaDateList.uploadInterval = null;
        this.addMediaDateList.uploadDelay = null;
        this.addMediaDateList.uploadSpeed = null;
        this.addMediaDateList.uploadDuration = null;
        this.addMediaDateList.remark = null;
        this.getMediaDate.vehicle_id = parseInt(treeNode.id);
        this.addMediaDateList.vehicleId = parseInt(treeNode.id);
        this.getMediaUploadConfigList(this.getMediaDate);
        this.getMediaUploadType(parseInt(treeNode.id));
        this.getMediaUploadTypeAll(parseInt(treeNode.id));
      }
    }, 500),

    getMediaUploadTypeAll(date) {
      this.themerulerLoading = true;
      this.$api
        .selectMediaUploadTypeAll({ vehicleId: date })
        .then((res) => {
          if (res.rs === 1) {
            this.adasTypeListAll = res.sDicts;
            this.themerulerLoading = false;
          } else {
            this.adasTypeListAll = [];
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    getMediaUploadType(date) {
      this.themerulerLoading = true;
      this.$api
        .selectMediaUploadType({ vehicleId: date })
        .then((res) => {
          if (res.rs === 1) {
            this.adasTypeList = res.sDicts;
            if (this.adasTypeList.length > 0) {
              this.adasTypeListIsShow = true;
            } else {
              this.adasTypeListIsShow = false;
            }
            this.themerulerLoading = false;
          } else {
            this.adasTypeList = [];
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    getMediaUploadConfigList(date) {
      this.themerulerLoading = true;
      this.$api
        .selectMediaUploadConfigInfo(date)
        .then((res) => {
          if (res.rs === 1) {
            this.MediaDateList = res.media_upload_config;
            this.totalCount = res.count;
            this.themerulerLoading = false;
          } else {
            this.MediaDateList = [];
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    /*---------------------------------------------------------------------------------------------- */
    selectGroupRunAndStop: _.debounce(function (data, node, $node) {
      if (data.type == 3) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "至少选择到企业级别！",
        });
        return;
      } else {
        this.getSysConfigRunAndStop(data.id);
      }
    }, 500),
    getSysConfigRunAndStop(id) {
      this.themerulerLoading = true;
      this.$api
        .selectSysConfigRunAndStop({ companyId: id })
        .then((res) => {
          if (res.RS === 1) {
            this.sysConfigListRunAndStop = res.SysConfigsList;
            if (this.sysConfigListRunAndStop.length > 0) {
              this.addSysConfigRunAndStop.id =
                this.sysConfigListRunAndStop[0].id;
              this.addSysConfigRunAndStop.obj_id =
                this.sysConfigListRunAndStop[0].objId;
              if (this.sysConfigListRunAndStop[0].monthBelong == 4) {
                this.addSysConfigRunAndStop.speed = null;
                this.stopRuleState = false;
              } else {
                this.addSysConfigRunAndStop.speed =
                  this.sysConfigListRunAndStop[0].monthBelong;
                this.stopRuleState = true;
              }
              this.addSysConfigRunAndStop.time =
                this.sysConfigListRunAndStop[0].monthStart;
              // this.addSysConfigRunAndStop.online = this.sysConfigListRunAndStop[0].onlineJudge?1:0
              this.themerulerLoading = false;
            } else {
              this.sysConfigListRunAndStop = [];
              this.stopRuleState = false;
              this.addSysConfigRunAndStop.id = null;
              this.addSysConfigRunAndStop.obj_id = id;
              this.addSysConfigRunAndStop.speed = null;
              this.addSysConfigRunAndStop.time = null;
              this.themerulerLoading = false;
              // this.addSysConfigRunAndStop.online = 0;
            }
          } else {
            this.sysConfigListRunAndStop = [];
            this.stopRuleState = false;
            this.addSysConfigRunAndStop.id = null;
            this.addSysConfigRunAndStop.obj_id = id;
            this.addSysConfigRunAndStop.speed = null;
            this.addSysConfigRunAndStop.time = null;
            // this.addSysConfigRunAndStop.online = 0;
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    SysConfigRunAndStopAddOrUpdate() {
      this.themerulerLoading = true;
      if (
        this.addSysConfigRunAndStop.obj_id == null ||
        this.addSysConfigRunAndStop.obj_id == ""
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "找不到企业id！",
        });
        return;
      }
      if (this.stopRuleState == true) {
        if (
          this.addSysConfigRunAndStop.speed == null ||
          this.addSysConfigRunAndStop.speed == ""
        ) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请选择速度！",
          });
          return;
        }
      } else {
        this.addSysConfigRunAndStop.speed == null;
      }
      if (
        this.addSysConfigRunAndStop.time == null ||
        this.addSysConfigRunAndStop.time == ""
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请选择时间！",
        });
        return;
      }
      this.$api
        .insertSysConfigRunAndStop(this.addSysConfigRunAndStop)
        .then((res) => {
          if (res.RS === 1) {
            if (
              this.addSysConfigRunAndStop.id == null ||
              this.addSysConfigRunAndStop.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增成功！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改成功！",
              });
            }
            this.themerulerLoading = false;
          } else {
            if (
              this.addSysConfigRunAndStop.id == null ||
              this.addSysConfigRunAndStop.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增失败！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改失败！",
              });
            }
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    /*---------------------------------------------------------------------------------------------- */
    selectGroupTwoNode: _.debounce(function (data, node, $node) {
      if (data.type == 3) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "至少选择到企业级别！",
        });
        return;
      } else {
        this.getSysConfig(data.id);
      }
    }, 500),
    getSysConfig(id) {
      this.themerulerLoading = true;
      this.$api
        .selectSysConfig({ deptId: id })
        .then((res) => {
          if (res.RS === 1) {
            this.sysConfigList = res.SysConfigsList;
            if (this.sysConfigList.length > 0) {
              this.addSysConfigWeek.id = null;
              this.addSysConfigWeek.obj_id = id;
              this.addSysConfigWeek.obj_type = 1;
              this.addSysConfigWeek.config_type = 0;
              this.addSysConfigWeek.config_value = null;
              this.addSysConfigWeek.config_desc = null;
              this.addSysConfigWeek.remark = null;

              this.addSysConfigMonth.id = null;
              this.addSysConfigMonth.obj_id = id;
              this.addSysConfigMonth.obj_type = 1;
              this.addSysConfigMonth.config_type = 1;
              this.addSysConfigMonth.month_belong = null;
              this.addSysConfigMonth.month_start = null;
              this.addSysConfigMonth.config_desc = null;
              this.addSysConfigMonth.remark = null;
              if (this.sysConfigList[0].configType == 0) {
                this.addSysConfigWeek.id = this.sysConfigList[0].id;
                this.addSysConfigWeek.obj_id = this.sysConfigList[0].objId;
                this.addSysConfigWeek.obj_type = this.sysConfigList[0].objType;
                this.addSysConfigWeek.config_type =
                  this.sysConfigList[0].configType;
                this.addSysConfigWeek.config_value =
                  this.sysConfigList[0].configValue;
                this.addSysConfigWeek.config_desc =
                  this.sysConfigList[0].configDesc;
                this.addSysConfigWeek.remark = this.sysConfigList[0].remark;
              } else {
                this.addSysConfigMonth.id = this.sysConfigList[0].id;
                this.addSysConfigMonth.obj_id = this.sysConfigList[0].objId;
                this.addSysConfigMonth.obj_type = this.sysConfigList[0].objType;
                this.addSysConfigMonth.config_type =
                  this.sysConfigList[0].configType;
                this.addSysConfigMonth.month_belong =
                  this.sysConfigList[0].monthBelong + "";
                this.addSysConfigMonth.month_start =
                  this.sysConfigList[0].monthStart;
                this.addSysConfigMonth.config_desc =
                  this.sysConfigList[0].configDesc;
                this.addSysConfigMonth.remark = this.sysConfigList[0].remark;
              }
              if (this.sysConfigList[1]) {
                if (this.sysConfigList[1].configType == 0) {
                  this.addSysConfigWeek.id = this.sysConfigList[1].id;
                  this.addSysConfigWeek.obj_id = this.sysConfigList[1].objId;
                  this.addSysConfigWeek.obj_type =
                    this.sysConfigList[1].objType;
                  this.addSysConfigWeek.config_type =
                    this.sysConfigList[1].configType;
                  this.addSysConfigWeek.config_value =
                    this.sysConfigList[1].configValue;
                  this.addSysConfigWeek.config_desc =
                    this.sysConfigList[1].configDesc;
                  this.addSysConfigWeek.remark = this.sysConfigList[1].remark;
                } else {
                  this.addSysConfigMonth.id = this.sysConfigList[1].id;
                  this.addSysConfigMonth.obj_id = this.sysConfigList[1].objId;
                  this.addSysConfigMonth.obj_type =
                    this.sysConfigList[1].objType;
                  this.addSysConfigMonth.config_type =
                    this.sysConfigList[1].configType;
                  this.addSysConfigMonth.month_belong =
                    this.sysConfigList[1].monthBelong + "";
                  this.addSysConfigMonth.month_start =
                    this.sysConfigList[1].monthStart;
                  this.addSysConfigMonth.config_desc =
                    this.sysConfigList[1].configDesc;
                  this.addSysConfigMonth.remark = this.sysConfigList[1].remark;
                }
              }
              this.themerulerLoading = false;
            } else {
              this.sysConfigList = [];
              this.addSysConfigWeek.id = null;
              this.addSysConfigWeek.obj_id = id;
              this.addSysConfigWeek.obj_type = 1;
              this.addSysConfigWeek.config_type = 0;
              this.addSysConfigWeek.config_value = null;
              this.addSysConfigWeek.config_desc = null;
              this.addSysConfigWeek.remark = null;

              this.addSysConfigMonth.id = null;
              this.addSysConfigMonth.obj_id = id;
              this.addSysConfigMonth.obj_type = 1;
              this.addSysConfigMonth.config_type = 1;
              this.addSysConfigMonth.month_belong = null;
              this.addSysConfigMonth.month_start = null;
              this.addSysConfigMonth.config_desc = null;
              this.addSysConfigMonth.remark = null;
              this.themerulerLoading = false;
            }
          } else {
            this.sysConfigList = [];
            this.addSysConfigWeek.id = null;
            this.addSysConfigWeek.obj_id = id;
            this.addSysConfigWeek.obj_type = 1;
            this.addSysConfigWeek.config_type = 0;
            this.addSysConfigWeek.config_value = null;
            this.addSysConfigWeek.config_desc = null;
            this.addSysConfigWeek.remark = null;

            this.addSysConfigMonth.id = null;
            this.addSysConfigMonth.obj_id = id;
            this.addSysConfigMonth.obj_type = 1;
            this.addSysConfigMonth.config_type = 1;
            this.addSysConfigMonth.month_belong = null;
            this.addSysConfigMonth.month_start = null;
            this.addSysConfigMonth.config_desc = null;
            this.addSysConfigMonth.remark = null;
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    selectGroupSpeed: _.debounce(function (data, node, $node) {
      switch (data.type) {
        case 0:
          this.speedObj = "(平台)";
          this.getSysConfigSpeed(data.id, 5);
          this.addSysConfigSpeed.obj_id = data.id;
          this.addSysConfigSpeed.obj_type = 5;
          break;
        case 1:
          this.speedObj = "(代理商)";
          this.getSysConfigSpeed(data.id, 6);
          this.addSysConfigSpeed.obj_id = data.id;
          this.addSysConfigSpeed.obj_type = 6;
          break;
        case 2:
          this.speedObj = "(企业)";
          this.getSysConfigSpeed(data.id, 1);
          this.addSysConfigSpeed.obj_id = data.id;
          this.addSysConfigSpeed.obj_type = 1;
          break;
        case 3:
          this.speedObj = "(车队)";
          this.getSysConfigSpeed(data.id, 3);
          this.addSysConfigSpeed.obj_id = data.id;
          this.addSysConfigSpeed.obj_type = 3;
          break;
        case 4:
          this.speedObj = "(车辆)";
          this.getSysConfigSpeed(data.id, 2);
          this.addSysConfigSpeed.obj_id = parseInt(data.id);
          this.addSysConfigSpeed.obj_type = 2;
          break;
        default:
          this.speedObj = "";
          this.initUserSpeedConfig();
          this.$message({
            type: "warning",
            showClose: true,
            message: "请选择平台、代理商、企业、车队或车辆！",
          });
          break;
      }
    }, 222),
    initUserSpeedConfig() {
      this.sysConfigList = [];
      this.addSysConfigSpeed.id = null;
      this.addSysConfigSpeed.obj_id = null;
      this.addSysConfigSpeed.obj_type = null;
      this.addSysConfigSpeed.config_type = 4;
      this.addSysConfigSpeed.config_value_normal = null;
      this.addSysConfigSpeed.config_value_arrive = null;
      this.addSysConfigSpeed.config_value_turn = null;
      this.addSysConfigSpeed.config_value_cross = null;
      this.addSysConfigSpeed.config_value_zebra = null;
      this.addSysConfigSpeed.weather_perc = null;
      this.addSysConfigSpeed.config_desc = null;
      this.addSysConfigSpeed.remark = null;
    },
    getSysConfigSpeed(objId, objType) {
      this.themerulerLoading = true;
      this.$api
        .selectSysConfigSpeed({ objId: objId, objType: objType })
        .then((res) => {
          if (res.RS === 1) {
            this.sysConfigSpeedList = res.SysConfigsList;
            if (this.sysConfigSpeedList.length > 0) {
              this.isWeatherSpeed = true;
              this.addSysConfigSpeed.id = null;
              this.addSysConfigSpeed.obj_id = objId;
              this.addSysConfigSpeed.obj_type = objType;
              this.addSysConfigSpeed.config_type = 4;
              this.addSysConfigSpeed.config_value_normal = null;
              this.addSysConfigSpeed.config_value_arrive = null;
              this.addSysConfigSpeed.config_value_turn = null;
              this.addSysConfigSpeed.config_value_cross = null;
              this.addSysConfigSpeed.config_value_zebra = null;
              this.addSysConfigSpeed.weather_perc = null;
              this.addSysConfigSpeed.config_desc = null;
              this.addSysConfigSpeed.remark = null;
              if (this.sysConfigSpeedList[0]) {
                this.addSysConfigSpeed.obj_id =
                  this.sysConfigSpeedList[0].objId;
                this.addSysConfigSpeed.obj_type =
                  this.sysConfigSpeedList[0].objType;
                this.addSysConfigSpeed.config_type = 4;
                this.addSysConfigSpeed.config_desc =
                  this.sysConfigSpeedList[0].configDesc;
                this.addSysConfigSpeed.remark =
                  this.sysConfigSpeedList[0].remark;
                this.addSysConfigSpeed.id = this.sysConfigSpeedList[0].id;
                this.addSysConfigSpeed.config_value_normal =
                  this.sysConfigSpeedList[0].configValueNormal;
                this.addSysConfigSpeed.config_value_arrive =
                  this.sysConfigSpeedList[0].configValueArrive;
                this.addSysConfigSpeed.config_value_turn =
                  this.sysConfigSpeedList[0].configValueTurn;
                this.addSysConfigSpeed.config_value_cross =
                  this.sysConfigSpeedList[0].configValueCross;
                this.addSysConfigSpeed.config_value_zebra =
                  this.sysConfigSpeedList[0].configValueZebra;
                this.addSysConfigSpeed.weather_perc =
                  this.sysConfigSpeedList[0].weatherPerc;
              }
              this.themerulerLoading = false;
            } else {
              this.isWeatherSpeed = false;
              this.sysConfigSpeedList = [];
              this.addSysConfigSpeed.id = null;
              this.addSysConfigSpeed.obj_id = objId;
              this.addSysConfigSpeed.obj_type = objType;
              this.addSysConfigSpeed.config_type = 4;
              this.addSysConfigSpeed.config_value_normal = null;
              this.addSysConfigSpeed.config_value_arrive = null;
              this.addSysConfigSpeed.config_value_turn = null;
              this.addSysConfigSpeed.config_value_cross = null;
              this.addSysConfigSpeed.config_value_zebra = null;
              this.addSysConfigSpeed.weather_perc = null;
              this.addSysConfigSpeed.config_desc = null;
              this.addSysConfigSpeed.remark = null;
              this.themerulerLoading = false;
            }
          } else {
            this.isWeatherSpeed = false;
            this.sysConfigList = [];
            this.addSysConfigSpeed.id = null;
            this.addSysConfigSpeed.obj_id = objId;
            this.addSysConfigSpeed.obj_type = objType;
            this.addSysConfigSpeed.config_type = 4;
            this.addSysConfigSpeed.config_value_normal = null;
            this.addSysConfigSpeed.config_value_arrive = null;
            this.addSysConfigSpeed.config_value_turn = null;
            this.addSysConfigSpeed.config_value_cross = null;
            this.addSysConfigSpeed.config_value_zebra = null;
            this.addSysConfigSpeed.weather_perc = null;
            this.addSysConfigSpeed.config_desc = null;
            this.addSysConfigSpeed.remark = null;
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    //新增,修改速度配置
    addSysConfigSpeedFun() {
      if (this.addSysConfigSpeed.obj_type == null) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请选择企业、车队或车辆",
        });
        return;
      }
      var reg = new RegExp("^[0-9]*$");
      if (
        !this.addSysConfigSpeed.config_value_normal == null ||
        !this.addSysConfigSpeed.config_value_normal == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.config_value_normal)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.config_value_normal = "";
      }
      if (
        !this.addSysConfigSpeed.config_value_arrive == null ||
        !this.addSysConfigSpeed.config_value_arrive == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.config_value_arrive)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.config_value_arrive = "";
      }
      if (
        !this.addSysConfigSpeed.config_value_cross == null ||
        !this.addSysConfigSpeed.config_value_cross == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.config_value_cross)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.config_value_cross = "";
      }
      if (
        !this.addSysConfigSpeed.config_value_zebra == null ||
        !this.addSysConfigSpeed.config_value_zebra == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.config_value_zebra)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.config_value_zebra = "";
      }
      if (
        !this.addSysConfigSpeed.config_value_turn == null ||
        !this.addSysConfigSpeed.config_value_turn == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.config_value_turn)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.config_value_turn = "";
      }
      if (
        !this.addSysConfigSpeed.weather_perc == null ||
        !this.addSysConfigSpeed.weather_perc == ""
      ) {
        if (!reg.test(this.addSysConfigSpeed.weather_perc)) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请输入数字值!",
          });
          return;
        }
      } else {
        this.addSysConfigSpeed.weather_perc = "";
      }
      this.themerulerLoading = true;
      this.$api
        .insertSysConfig(this.addSysConfigSpeed)
        .then((res) => {
          if (res.RS === 1) {
            if (
              this.addSysConfigSpeed.id == null ||
              this.addSysConfigSpeed.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: res.Reason,
              });
              this.getSysConfigSpeed(
                this.addSysConfigSpeed.obj_id,
                this.addSysConfigSpeed.obj_type
              );
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: res.Reason,
              });
              this.getSysConfigSpeed(
                this.addSysConfigSpeed.obj_id,
                this.addSysConfigSpeed.obj_type
              );
            }
            this.themerulerLoading = false;
          } else {
            if (
              this.addSysConfigSpeed.id == null ||
              this.addSysConfigSpeed.id == ""
            ) {
              this.$message({
                type: "error",
                showClose: true,
                message: res.Reason,
              });
            } else {
              this.$message({
                type: "error",
                showClose: true,
                message: res.Reason,
              });
            }
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    addSysConfigWeekFun() {
      if (
        this.addSysConfigWeek.obj_id == null ||
        this.addSysConfigWeek.config_type == null
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请填写完整！",
        });
        return;
      }
      if (
        this.addSysConfigWeek.config_value == null ||
        this.addSysConfigWeek.config_value == ""
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请配置值！",
        });
        return;
      }
      var reg = new RegExp("^[0-9]*$");
      if (!reg.test(this.addSysConfigWeek.config_value)) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "配置值，请输入数字!",
        });
        return;
      }
      if (
        parseInt(this.addSysConfigWeek.config_value) < 1 ||
        parseInt(this.addSysConfigWeek.config_value) > 7
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "配置值错误，请输入合理范围内的数字!",
        });
        return;
      }
      this.themerulerLoading = true;
      this.$api
        .insertSysConfig(this.addSysConfigWeek)
        .then((res) => {
          if (res.RS === 1) {
            if (
              this.addSysConfigWeek.id == null ||
              this.addSysConfigWeek.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增成功！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改成功！",
              });
            }
            this.themerulerLoading = false;
          } else {
            if (
              this.addSysConfigWeek.id == null ||
              this.addSysConfigWeek.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增失败！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改失败！",
              });
            }
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
    addSysConfigMonthFun() {
      if (
        this.addSysConfigMonth.obj_id == null ||
        this.addSysConfigMonth.config_type == null
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请填写完整！",
        });
        return;
      }
      if (
        this.addSysConfigMonth.month_belong == null ||
        this.addSysConfigMonth.month_belong == ""
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请选择月份归属！",
        });
        return;
      }
      if (
        this.addSysConfigMonth.month_start == null ||
        this.addSysConfigMonth.month_start == ""
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "请选择月开始时间！",
        });
        return;
      }
      var reg = new RegExp("^[0-9]*$");
      if (!reg.test(this.addSysConfigMonth.month_start)) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "配置值，请输入数字!",
        });
        return;
      }
      if (
        parseInt(this.addSysConfigMonth.month_start) < 1 ||
        parseInt(this.addSysConfigMonth.month_start) > 28
      ) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "配置值错误，请输入合理范围内的数字!",
        });
        return;
      }
      this.themerulerLoading = true;
      this.$api
        .insertSysConfig(this.addSysConfigMonth)
        .then((res) => {
          if (res.RS === 1) {
            if (
              this.addSysConfigMonth.id == null ||
              this.addSysConfigMonth.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增成功！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改成功！",
              });
            }
            this.themerulerLoading = false;
          } else {
            if (
              this.addSysConfigMonth.id == null ||
              this.addSysConfigMonth.id == ""
            ) {
              this.$message({
                type: "success",
                showClose: true,
                message: "新增失败！",
              });
            } else {
              this.$message({
                type: "success",
                showClose: true,
                message: "修改失败！",
              });
            }
            this.themerulerLoading = false;
          }
        })
        .catch((res) => {
          // 程序出错
          console.log(
            "-----------调用接口,程序出错,返回结果如下：------------"
          );
          console.log(res);
          this.themerulerLoading = false;
        });
    },
  },
  activated() {
    if (this.$route.query.tab) {
      this.editTab = this.$route.query.tab;
      this.currentTab = this.$route.query.tab;
    }
  },
};
</script>

<style lang="scss">
// 该页面未加scoped    存在样式冲突   其他页面应避免使用ruler-config该类名
.ruler-config {
  .tabsContent {
    display: flex;
    height: 100%;
    width: 100%;
    flex-direction: row;
    align-items: center;

    .aside {
      height: 100%;
      width: 11%;

      .input {
        padding: 0 6%;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      background-color: var(--background-color-light);

      .industry-item {
        color: var(--color-text-regular);
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: var(--color-primary-o-10);
        }

        &.active {
          // color: var(--color-bottom-operate);
          // background-color: var(--bacground-color-active);
          background: rgba(42, 128, 224, 0.2);
        }
      }
    }

    .tabs {
      margin-left: 1%;
      height: 100%;
      width: 90%;
    }
  }

  .el-tabs__nav {
    margin-left: 25px;
  }

  .form-group {
    max-width: 500px;

    >.title {
      height: 32px;
      line-height: 32px;
      margin: 4px 0;
      font-size: 14px;
      color: var(--color-primary);

      &:before {
        content: " ";
        border-left: 2px solid var(--color-primary);
        height: 24px;
        margin-right: 10px;
      }
    }

    >.query-item {
      display: flex;
      height: 40px;
      line-height: 40px;

      >span {
        flex-shrink: 0;
        margin-right: 10px;
      }
    }
  }
}
</style>
