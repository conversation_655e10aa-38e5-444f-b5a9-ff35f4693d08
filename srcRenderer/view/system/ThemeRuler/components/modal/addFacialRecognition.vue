<template>
  <PonyDialog
    v-model="show"
    class="dealer-site-info"
    :has-mask="false"
    :insert-body="true"
    :title="mode === 'add' ? addModeTitle : modifyModeTitle"
    :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText"
    :width="550"
    content-style="min-height:150px;overflow:auto;padding:15px"
    @confirm="add"
    @close="clear"
  >
    <el-row :gutter="20" style="padding: 5px 10px">
      <el-col :span="13">
        <ElementTree
          type="vehicle"
          ref="vehicleTree"
          @node-click="nodeClick"
          node-key="id"
          style="height: 300px"
          :extraKeys="['type']"
        >
        </ElementTree>
      </el-col>
      <el-col :span="11">
        <el-form
          :model="form"
          :rules="rules"
          label-width="140px"
          size="mini"
          ref="form"
          label-position="top"
          style="padding-top: 10px"
        >
          <el-form-item prop="identifyIntervals" label="识别间隔:" style="width: 100%">
            <el-input v-model="form.identifyIntervals" placeholder="请输入识别间隔">
              <template #append>分钟</template>
            </el-input>
          </el-form-item>
          <el-form-item prop="contrastSource" label="司机对比来源:" style="width: 100%">
            <div class="source-header">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange"> 来源 </el-checkbox>
              <span class="priority-text">优先级</span>
            </div>
            <draggable v-model="localOptions" group="people" @start="drag = true" @end="handleDragEnd">
              <div v-for="(item, index) in localOptions" :key="item.value" class="checkbox-item">
                <el-checkbox v-model="item.checked" @change="handleCheckedChange(item)">
                  {{ item.name }}
                </el-checkbox>
                <div class="order-badge">{{ index + 1 }}</div>
              </div>
            </draggable>
            <div class="drag-tip">提示：拖动改变优先级</div>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </PonyDialog>
</template>

<script>
import draggable from "vuedraggable";
export default {
  name: "addSpeedConfig",
  components: {
    draggable,
  },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      show: false,
      mode: "add",
      loading: false,
      activeTab: "basic",
      addModeTitle: "新增配置",
      addModeOkText: "确认新增",
      modifyModeTitle: "修改配置",
      modifyModeOkText: "确认修改",
      form: {
        contrastSource: null,
        identifyIntervals: null,
        faceRecognitionSourceType: [], // 存储选中的来源类型
      },
      deptSelectValue: null,
      deptSelectType: null,
      id: "",
      rules: {
        identifyIntervals: [{ required: true, message: "请填写识别间隔", trigger: "blur" }],
        contrastSource: [{ required: true, validator: this.validateContrastSource, trigger: "change" }],
      },
      checkAll: false,
      isIndeterminate: false,
      drag: false,
      optionList: [],
      localOptions: [], // 添加本地数组来管理选项
    };
  },
  created() {
    // 移除这个初始化，因为我们直接使用 options
    // this.form.faceRecognitionSourceType = this.options.map((item) => ({
    //   name: item.name,
    //   value: item.value,
    //   checked: false,
    // }));
  },
  mounted() {
    this.optionList = this.options;
  },
  methods: {
    async add() {
      if (!this.deptSelectValue) {
        return this.$warning("请选择配置对象！");
      }
      this.loading = true;
      try {
        this.$refs["form"].validate(async (valid) => {
          if (!valid) return;
          let param = {
            config_type: 131,
            obj_id: this.deptSelectValue,
            obj_type: this.deptSelectType,
            config_value: this.getConfigValue(),
          };
          if (this.mode === "modify") {
            param = Object.assign(param, { id: this.id });
          }
          let res = await this.$api.insertSysConfig(param);
          if (res.RS == 1) {
            this.$success(this.mode === "modify" ? "修改成功" : "新增成功");
            this.$emit("refresh");
            this.show = false;
          } else {
            this.$error(this.mode === "modify" ? "修改失败" : "新增失败" + res.Reason);
            throw new Error(this.mode === "modify" ? "修改失败" : "新增失败" + res.Reason);
          }
        });
      } catch (e) {
        this.$error(e);
      } finally {
        this.loading = false;
      }
    },
    async showModal(rowData) {
      this.show = true;
      // 初始化本地选项
      this.initLocalOptions();

      if (rowData) {
        this.mode = "modify";
        // 解析已保存的配置值
        const savedConfig = JSON.parse(rowData.config_value || "{}");

        // 设置识别间隔
        this.form.identifyIntervals = savedConfig.identifyIntervals;

        // 设置对比来源的选中状态
        const selectedSources = (savedConfig.contrastSource || "").split(",");
        this.localOptions.forEach((item) => {
          item.checked = selectedSources.includes(item.value.toString());
        });

        // 更新全选状态
        this.handleCheckedChange();

        this.deptSelectType = rowData.obj_type;
        this.deptSelectValue = rowData.obj_id;
        this.id = rowData.id;
        await this.$nextTick();
        await this.$refs["vehicleTree"].waitForInit;
        const $tree = this.$refs["vehicleTree"].$refs["tree"];
        $tree.setCurrentKey(rowData.obj_id);
        const node = $tree.getNode(rowData.obj_id);
        node.expand(null, true);
      } else {
        this.mode = "add";
        this.clear();
      }
    },
    // 单选树节点选择
    nodeClick(data, node) {
      this.deptSelectValue = data.id;
      this.deptSelectType = data.type;
    },
    clear() {
      this.form = {
        config_value: "",
      };
      this.deptSelectValue = null;
      this.deptSelectType = null;
      this.id = null;
      // 重置本地选项的选中状态
      this.localOptions.forEach((item) => {
        item.checked = false;
      });
      this.checkAll = false;
      this.isIndeterminate = false;
    },

    // 全选处理
    handleCheckAllChange(val) {
      this.localOptions.forEach((item) => {
        item.checked = val;
      });
      this.isIndeterminate = false;
    },

    // 单个选择框变化处理
    handleCheckedChange() {
      const checkedCount = this.localOptions.filter((item) => item.checked).length;
      const totalCount = this.localOptions.length;
      this.checkAll = checkedCount === totalCount;
      this.isIndeterminate = checkedCount > 0 && checkedCount < totalCount;
      // 触发表单验证
      this.$nextTick(() => {
        this.$refs.form.validateField("contrastSource");
      });
    },

    // 处理拖拽结束
    handleDragEnd(evt) {
      this.drag = false;
      // 不需要额外更新优先级，因为 index 会自动反映新的顺序
    },

    // 获取配置值
    getConfigValue() {
      const selectedSourceValues = this.localOptions
        .filter((item) => item.checked)
        .map((item) => item.value)
        .join(",");

      return JSON.stringify({
        identifyIntervals: Number(this.form.identifyIntervals),
        contrastSource: selectedSourceValues,
      });
    },

    // 修改表单验证方法
    validateContrastSource(rule, value, callback) {
      const hasChecked = this.localOptions.some((item) => item.checked);
      if (!hasChecked) {
        callback(new Error("请至少选择一个来源"));
      } else {
        callback();
      }
    },

    // 初始化本地选项
    initLocalOptions() {
      this.localOptions = this.options.map((item) => ({
        ...item,
        checked: false,
      }));
    },
  },
  watch: {
    options: {
      immediate: true,
      handler(newOptions) {
        if (newOptions && newOptions.length) {
          this.initLocalOptions();
        }
      },
    },
  },
};
</script>

<style scoped lang="scss">
.dealer-site-info {
  .detail .el-form-item--mini.el-form-item {
    height: 28px;
  }

  /deep/ .el-tab-pane {
    padding: 10px;
  }

  /deep/ .select-tree-input {
    width: 100% !important;
  }

  /deep/ .el-divider {
    margin: 12px 0 24px;
    float: left;

    .el-divider__text {
      padding: 0 10px;
      background-color: var(--background-color-light);
    }

    .el-button--mini.el-button--text {
      vertical-align: -1px;
    }
  }

  /deep/ .el-form-item {
    width: 50%;
    float: left;

    .el-input {
      width: 100%;
    }
  }
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  border: 1px solid var(--border-color-light);

  .priority-text {
    color: #606266;
    font-size: 14px;
  }
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 4px 12px; // 减小上下内边距
  border: 1px solid var(--border-color-light);
  margin-bottom: 2px; // 减小项目间距
  transition: all 0.3s;
  &:hover {
    background: var(--background-color-lighter);
    border-color: #c6e2ff;
  }
}

.order-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  font-size: 12px;
}

.drag-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  padding-left: 4px;
}

.checkbox-item {
  cursor: move; // 添加移动光标样式
}

// 如果需要，可以调整复选框本身的大小
:deep(.el-checkbox) {
  line-height: 28px; // 减小行高
  .el-checkbox__label {
    font-size: 13px; // 可选：调整文字大小
  }
}
</style>
