<template>
    <Layout :has-color="true" :contentLoading="table.loading" class="LockStatusQuery">
        <template slot="aside">
            <div class="query-top">
                <ElementTree ref="tree" type="unPointTerminal" :checkMode="true" :changeType="true" @change="treeChange"
                             :elementTypeList="elementTypeList" :elementType="true" @check="selectNodes"></ElementTree>
            </div>
            <div class="query-bottom bg bg--light box-shadow">
              <div class="query-item">
                <span>设备编号/名称:</span>
                <el-input v-model="search.terminalName" placeholder="请输入设备编号/名称"></el-input>
              </div>
                <div class="query-item">
                    <el-button type="primary" style="width: 100%" @click="searchList()">查询
                    </el-button>
                </div>
            </div>
        </template>
        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="exportDataInfo()" :loading="exportLoading">导出
                </el-button>
            </div>
            <div class="query-item">
                <el-button size="mini" type="primary" @click="add()">新增</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination background small :pager-count="5" :current-page.sync="pager.current"
                               layout="prev, pager, next, total" :page-size="pager.size" :total="pager.total">
                </el-pagination>
            </div>
        </template>
        <template slot="content">
            <el-table ref="breakTable" slot="content" class="el-table--ellipsis el-table--radius" border stripe
                      highlight-current-row size="mini" :data="formatList" height="100%"
                      style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"
                                 width="70"></el-table-column>
                <el-table-column prop="companyName" label="操作" min-width="20">
                    <template slot-scope="{row}">
                        <el-button type="text" title="修改" size="mini" @click="approval(row)">
                            <i class="pony-iconv2 pony-xiugai"></i>
                        </el-button>
                        <el-button type="text" size="mini" title="删除" @click="del(row)">
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="areaName" label="场地名称" min-width="50" show-overflow-tooltip></el-table-column>
                <el-table-column prop="terminalNum" label="设备数量" min-width="30"></el-table-column>
                <el-table-column prop="groupName" label="分组" min-width="50" show-overflow-tooltip></el-table-column>
                <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
                <el-table-column prop="updateBy" label="修改人" min-width="50"></el-table-column>
                <el-table-column prop="updateTimeView" label="修改时间" min-width="50"></el-table-column>
            </el-table>
        </template>
        <addSiteEquipmentMgt ref="addSiteEquipment" :list="elementTypeList" @refresh="updateList"></addSiteEquipmentMgt>
    </Layout>
</template>
<script>
import addSiteEquipmentMgt from './components/addSiteEquipmentMgt'

export default {
    name: 'siteEquipmentMgt',
    components: {
        addSiteEquipmentMgt
    },
    data() {
        return {
            table: {
                loading: false,
                data: [],
            },
            pager: {
                current: 1,
                size: 30,
                total: 0,
            },
            search: {
                operateType: 2,
                areaIdList: [],
                terminalName:''
            },
            renderDataType: 5,
            exportLoading: false,
            elementTypeList: []
        }
    },
    computed: {
        pageStart() {
            return (this.pager.current - 1) * this.pager.size;
        },
        formatList() {
            return this.table.data.slice((this.pager.current - 1) * this.pager.size, this.pager.current * this.pager.size);
        },
    },
    mounted() {
        this.getTreeList()
    },
    methods: {
        async getTreeList() {
            const res = await this.$api.areaTerminalType({
                page: 'siteEquipmentMgt'
            })
            if(res.status === 200) {
                this.elementTypeList = res.data
                await this.treeChange(0)
            } else {
                return this.$warning('获取树类型失败！')
            }
        },
        async treeChange(val) {
            this.$refs['tree'].loading = true
            this.treeType = this.elementTypeList[val].type
            this.renderDataType = this.elementTypeList[val].renderDataType
            await this.$refs['tree'].initTree(this.treeType)
            this.search.areaIdList = []
        },
        // 多选树
        selectNodes(current, {checkedNodes}) {
            this.search.areaIdList = checkedNodes
             .filter((item) => item.type == this.renderDataType)
             .map((item) => item.id);
        },
        add() {
            this.$refs.addSiteEquipment.showModal()
        },
        approval(row) {
            this.$refs.addSiteEquipment.showModal(JSON.stringify(row))
        },
        updateList() {
            if (this.search.areaIdList.length) {
                this.searchList()
            }
        },
        async searchList() {
            if (!this.search.areaIdList.length) {
                this.$warning('请选择数据!')
                return
            }
            this.table.data = []
            this.pager.total = 0
            this.table.loading = true;
            let res = await this.$api.areaTerminalBind(this.search);
            this.table.loading = false;
            if (!res || res.status !== 200) {
                this.$error(res.message || "查询失败");
                return
            }
            if (!res.data || !res.data.length) {
                this.$warning("未查询到数据");
                return;
            }
            this.table.data = res.data
            this.pager.total = res.data.length
            this.table.loading = false
            this.$nextTick(() => {
                this.$refs.breakTable.doLayout()
            })
        },
        del(row) {
            this.$confirm(`请确认删除${row.areaName}`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let result = await this.$api.areaTerminalBind({id: row.id, operateType: -2})
                if (!result || result.status !== 200) {
                    this.$warning(result.message || '操作失败')
                } else {
                    this.$success('删除成功!')
                    await this.searchList()
                }
            })
        },
        async exportDataInfo() {
            if (this.table.data.length == 0) {
                this.$warning("没有数据可以导出");
                return;
            }
            this.exportLoading = true;
            
            let sheetHeader = [
                "场地名称@areaName@8000@000000",
                "设备数量@terminalNum@8000@000000",
                "分组@groupName@8000@000000",
                "备注@remark@8000@000000",
                "修改人@updateBy@8000@000000",
                "修改时间@updateTimeView@8000@000000",
            ]
            
            let params = {}
            let paramsList = [];
            let title = `场地设备管理报表`
            
            params = {
                sheetName: "场地设备管理报表",
                title: title,
                headers: sheetHeader,
                dataList: this.table.data,
            }
            paramsList.push(params)
            await this.$utils.jsExcelExport(JSON.stringify(paramsList), title + '.xlsx')
            this.exportLoading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.LockStatusQuery {
    .query-top {
        height: calc(100% - 104px);
    }
    
    .query-bottom {
        margin-top: 5px;
        padding: 10px;
        
        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
            
            > div {
                flex-grow: 1;
            }
            
            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
</style>
