<template>
  <Layout class="car-mgnt" :content-loading="carTable.loading" :has-color="true">
    <template slot="aside">
      <ElementTree type="department" ref="tree" :checkMode="true" @check="selectNodes"></ElementTree>
    </template>
    <template slot="query">
      <div class="query-item">
        <el-select :placeholder="$ct('label.choosePlease')" v-model="query.type" style="width: 110px;margin-right: 10px;">
          <el-option label="车牌号" :value="0"></el-option>
          <el-option label="终端手机号" :value="1"></el-option>
          <el-option label="主终端ICCID卡号" :value="2"></el-option>
          <el-option label="绑定司机" :value="3"></el-option>
          <el-option label="VIN码" :value="4"></el-option>
          <el-option label="终端ID" :value="5"></el-option>

          
        </el-select>
      </div>
      <div class="query-item">
        <el-input v-model="query.value" style="width: 130px;margin-right: 10px"
                  :placeHolder="$ct('label.keyPlease')"></el-input>
      </div>
      <div class="query-item">
        <span>保险到期：</span>
        <el-select placeholder="请选择到期时间" v-model="dueDate" style="width: 110px;margin-right: 10px;">
          <el-option :label="item" :value="index" v-for="(item, index) in dueDateList" :key="item"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>年审到期：</span>
        <el-select placeholder="请选择到期时间" v-model="yearDueDate" style="width: 110px;margin-right: 10px;">
          <el-option :label="item" :value="index" v-for="(item, index) in dueDateList" :key="item"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>营运证到期：</span>
        <el-select placeholder="请选择到期时间" v-model="filterOperaDate" style="width: 110px;margin-right: 10px;">
          <el-option :label="item" :value="index" v-for="(item, index) in dueDateList" :key="item"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" class="sousuo" @click="getCarTableList()" :loading="carTable.loading">
          {{ $ct('label.search') }}
        </el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="exportTableList()" :loading="exportLoading">
          {{ $ct('label.export') }}
        </el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="easyAddVehicle()" v-if="hasPermission('zwy:easyAddVehicle')">
          便捷录车
        </el-button>
        <el-button size="mini" type="primary" @click="showTableSetting()">
          表格显示配置
        </el-button>

        <el-button size="mini" type="primary" @click="multipleModify()">
          批量修改
        </el-button>
        <el-button size="mini" type="primary" @click="multipleDelete()">
          批量删除
        </el-button>
        <el-button size="mini" type="primary" @click="multipleImport()">{{
            $ct('label.multipleImport')
          }}
        </el-button>
        <el-button size="mini" type="primary" @click="addVehicle()">{{ $ct('label.add') }}</el-button>
        <el-button size="mini" type="primary" @click="addVehicleOCR()" v-if="hasPermission('lx:ocrAddVehicle')">OCR录入</el-button>

      </div>
    </template>
    <template slot="content">
      <el-table class="el-table--radius " :empty-text="carTable.data.length ? $ct('label.noData') : ''" stripe
                :data="carTable.data" height="100%" border ref="carTable" highlight-current-row style="width: 100%"
                @sort-change="sortCurrentProp" @select-all="selectAll" @select="handleSelect"
                @selection-change="handleSelectionChange" :key="tableKey">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column type="index" label="序号">
          <template slot-scope="scope">
            <span>{{ (pager.current - 1) * pager.size + 1 + scope.$index }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100">
          <template slot-scope="scope">
            <el-button type="text" size="mini" :title="$ct('label.statusSwitch')" @click="toggleState(scope.row)">
              <i class="pony-iconv2 pony-zhuanhuan"></i>
            </el-button>
            <template v-if="scope.row.status === 0">
              <el-button type="text" :title="$ct('label.modify')" size="mini" @click="modifyVehicle(scope.row)"
                         style="margin-left: 1px">
                <i class="pony-iconv2 pony-xiugai"></i>
              </el-button>
              <el-dropdown>
                <el-button type="text" size="mini" style="margin-left:1px;">
                  <i class="pony-iconv2 pony-gengduo"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button type="text" size="mini" @click="driverBind(scope.row)">
                      <i class="pony-iconv2 pony-bangdingsiji"></i>{{ $ct('label.bindedDriver') }}
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" size="mini" @click="trailerBind(scope.row)">
                      <i class="pony-iconv2 pony-bangdingsiji"></i>绑定挂车
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" size="mini" @click="terminalList(scope.row)">
                      <i class="pony-iconv2 pony-xiangqing"></i>设备记录
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="text" size="mini" @click="driverList(scope.row)">
                      <i class="pony-iconv2 pony-xiangqing"></i>驾驶员记录
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="hasPermission('car:del')">
                    <el-button type="text" size="mini" @click="removeCar(scope.row)">
                      <i class="pony-iconv2 pony-shanchu"></i>{{ $ct('label.del') }}
                    </el-button>
                  </el-dropdown-item>
                  <!--<el-dropdown-item value="3" v-if="hasPermission('qrcode:bindqrcode')">-->
                  <!--<el-button type="text" size="mini"-->
                  <!--@click="handleClick(scope.row, 4),getQrcodeToSee()">-->
                  <!--<i class="pony-iconv2 pony-erweima"></i>绑定二维码-->
                  <!--</el-button>-->
                  <!--</el-dropdown-item>-->
                  <!--<el-dropdown-item value="4" v-if="hasPermission('car:addmile')">-->
                  <!--<el-button type="text" size="mini"-->
                  <!--@click="handleClick(scope.row, 5)">-->
                  <!--<i class="pony-iconv2 pony-sudukongzhi"></i>仪表里程抄录-->
                  <!--</el-button>-->
                  <!--</el-dropdown-item>-->
                  <el-dropdown-item v-if="hasPermission('car:report')">
                    <el-button type="text" size="mini" @click="vehicleReport(scope.row)">
                      <i class="pony-iconv2 pony-shangbao"></i>{{ $ct('label.report') }}
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="hasPermission('car:erweima')">
                    <el-button type="text" size="mini" @click="exportQrcode(scope.row)">
                      <i class="pony-iconv2 pony-erweima1"></i>导出二维码
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip v-for="(item, index) in tableSettingList" :label="item.name" :key="index"
                         :min-width="item.size" :align="item.align" :prop="item.key" :sortable="item.sort">
          <template slot-scope="scope">
            <span v-if="item.type === 'company_name'">{{
                scope.row.company_name + '>>' + scope.row.dept_name
              }}</span>
            <span v-else-if="item.type === 'trail_type_names'">{{
                isCLZCL ? scope.row.trail_type_names : (scope.row.industry_type == 8 ? scope.row.concrete_type_names :
                  scope.row.vehicle_type)
              }}
            </span>
            <span class="driver" @click="jump(scope.row)" v-else-if="item.type === 'driver_name'">{{
                scope.row.driver_name
              }}</span>
            <span v-else-if="item.type === 'status'">{{ scope.row.status ? '停用' : '启用' }}</span>
            <span v-else-if="item.type === 'channel_count'">{{ scope.row.channel_count || 4 }}</span>
            <span v-else-if="item.type === 'black_white'"> {{
                scope.row.black_white ? (scope.row.black_white === 1 ? $ct('label.whiteList') : $ct('label.blackList')) :
                  $ct('label.unset')
              }}</span>
            <span v-else-if="item.type === 'terminal_type_name_one'" class="tableList">
              <span class="list" :title="scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_type_name
                    : '-'">{{
                  scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_type_name
                    : '-'
                }}</span>
              <span class="list" :title="scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_code : '-'">{{
                  scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_code : '-'
                }}</span>
              <span class="list" :title="scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_desc : '-'">{{
                  scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].terminal_desc : '-'
                }}</span>
              <span class="list" :title="scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].sim_code : '-'">{{
                  scope.row.terminal_bind_list[0] ? scope.row.terminal_bind_list[0].sim_code : '-'
                }}</span>
              <span class="list">
                <el-popover v-if="scope.row.terminal_bind_list.length > 1" placement="top" width="400" trigger="hover">
                  <el-table :data="scope.row.terminal_bind_list.slice(1, scope.row.terminal_bind_list.length)">
                    <el-table-column property="terminal_type_name" label="终端类型名" show-overflow-tooltip></el-table-column>
                    <el-table-column property="terminal_code" label="终端手机号" show-overflow-tooltip></el-table-column>
                    <el-table-column property="terminal_desc" label="终端型号" show-overflow-tooltip></el-table-column>

                    <el-table-column property="sim_code" label="主终端ICCID卡号" show-overflow-tooltip></el-table-column>
                  </el-table>
                  <el-tag slot="reference" type="success" v-if="scope.row.terminal_bind_list.length > 1">
                    +{{ scope.row.terminal_bind_list.length - 1 }}
                  </el-tag>
                </el-popover>
              </span>
            </span>
            <div class="signList" v-else-if="item.type === 'sign'">
              <el-tag v-if="scope.row.videoDevice">
                视频设备
              </el-tag>
              <el-tag v-if="scope.row.virtualDevice">
                虚拟设备
              </el-tag>
              <el-tag type="warning" v-if="scope.row.sourceData">
                原车数据接入
              </el-tag>
              <el-tag type="success" v-if="scope.row.inputType == 2">
                808转发
              </el-tag>
              <el-tag type="success" v-if="scope.row.inputType == 3">
                809接入
              </el-tag>
              <el-tag type="success" v-if="scope.row.inputType == 4">
                中交兴路
              </el-tag>
              <el-tag type="danger" v-if="scope.row.feeInvalid">
                服务到期
              </el-tag>
            </div>
            <span v-else>
              {{ scope.row[item.key] }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template slot="footer">
      <el-pagination background small layout="prev, pager, next, total" :pager-count="5"
                     :current-page.sync="pager.current" @current-change="getCarTableList" :page-size="pager.size"
                     :total="pager.total">
      </el-pagination>
    </template>
    <CarInfo ref="carInfo" @refresh="getCarTableList()"></CarInfo>
    <CarInfoOCR ref="carInfoOCR" @refresh="getCarTableList()"></CarInfoOCR>

    <DriverBind ref="driverBind" @refresh="getCarTableList()"></DriverBind>
    <TrailerBind ref="trailerBind" @refresh="getCarTableList()"></TrailerBind>
    <MultipleImport ref="multipleImport" @refresh="getCarTableList()"></MultipleImport>
    <MultipleModify ref="multipleModify" @refresh="getCarTableList()"></MultipleModify>
    <TableShowConfigList ref="tableShowConfigList" v-model="tableSettingList" :filterableSearch=true
                         :list="allSettingList" :defaultSetting="defaultSettingList" :auto-width="640" :width="665"
                         @change="settable" :ocrList="ocrSettingList" :showOcr="true">
    </TableShowConfigList>
    <TerminalList ref="TerminalList"></TerminalList>
    <EasyAddVehicle ref="EasyAddVehicle" @refresh="getCarTableList()"></EasyAddVehicle>
    <DriverList ref="driverList" title="驾驶员绑定记录"></DriverList>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-05-23 15:45:43
 * @LastEditors: yezy
 * @LastEditTime: 2019-05-23 15:45:43
 * @Description: 车辆管理重构
 */
import CarInfo from './modal/CarInfo'
import CarInfoOCR from './modal/CarInfoOCR'

import DictionarySelect from '../../../components/common/DictionarySelect'
import DriverBind from './modal/DriverBind'
import MultipleImport from './modal/MultipleImport'
import MultipleModify from './modal/MultipleModify'
import EasyAddVehicle from './modal/EasyAddVehicle'
import TableShowConfigList from "../../report/components/TableShowConfigList";
import {allSettingList, defaultSettingList, ocrSettingList} from './carMgtTableList'
import TerminalList from './modal/TerminalList'
import TrailerBind from './modal/TrailerBind.vue'
import DriverList from "./modal/DriverList";

export default {
  name: "carMgt",
  components: {
    CarInfo, DriverBind, MultipleImport, DictionarySelect,
    MultipleModify, TableShowConfigList, TerminalList, EasyAddVehicle,
    TrailerBind,DriverList,CarInfoOCR
  },
  data() {
    return {
      currentNode: null,
      dueDate: 0,
      yearDueDate: 0,
      filterOperaDate: 0,
      dueDateList: ["全部", "已到期", "3天内到期", "10天内到期", "30天内到期","2个月到期"],
      query: {
        type: 0,
        value: null,
        deptId: [],
        vehicle_type_list: null,
      },
      pager: {
        current: 1,
        total: 0,
        size: 30,
      },
      carTable: {
        data: [],
        loading: false,
      },
      exportLoading: false,
      selection: [],
      IdStr: "",
      tableSettingList: [],
      allSettingList,
      defaultSettingList,
      ocrSettingList,
      tableKey: 0,
      selectAllStatus: false,
    }
  },
  mounted() {
    if (this.$route.query) {
      if (this.$route.query.plate_no) {
        this.query.value = this.$route.query.plate_no
        this.getCarTableList()
      } else {
        this.yearDueDate = this.$route.query.filter_annual ? this.$route.query.filter_annual : 0
        this.dueDate = this.$route.query.filter_insurance ? this.$route.query.filter_insurance : 0
        this.filterOperaDate = this.$route.query.filter_opera ? this.$route.query.filter_opera : 0
        this.getCarTableList(this.$route.query);
      }
    } else {
      this.getCarTableList();
    }
    if (localStorage.getItem('carTableSetting')) {
      this.tableSettingList = JSON.parse(localStorage.getItem('carTableSetting'))
    } else {
      this.tableSettingList = JSON.parse(JSON.stringify(this.defaultSettingList))
    }
  },
  computed: {
    isCLZCL: function () {
      return this.$route.params.company === 'clzcl'
    },
  },
  methods: {
    driverList(row) {
      this.$refs['driverList'].showModal(row)
    },
    async selectAll(selection) {
      if (selection.length > 0) {
        let params = {
          dept_id_list: this.query.deptId,
          vehicle_type_list: this.query.vehicle_type_list ? [this.query.vehicle_type_list] : null,
          sorting: -1,
          plate_no: this.query.type === 0 ? this.query.value : null,
          code: this.query.type === 1 ? this.query.value : null,
          sim_code: this.query.type === 2 ? this.query.value : null,
          driver_name: this.query.type === 3 ? this.query.value : null,
          vin: this.query.type === 4 ? this.query.value : null,
          device_id:this.query.type === 5 ? this.query.value : null,
          filter_annual: this.yearDueDate,
          filter_insurance: this.dueDate,
          filter_opera: this.filterOperaDate

        }
        this.carTable.loading = true
        let result = await this.$api.getsysvehiclepageid(params)
        if (result.status == 200) {
          this.carTable.loading = false
          this.selection = result.data
        }
      } else {
        this.selection = []
      }

    },
    handleSelect(selection, row) {
      if (selection.includes(row)) {
        selection.forEach(item => {
          if (!this.selection.includes(item.id)) {
            this.selection.push(item.id)
          }
        })
      } else {
        if (this.selection.includes(row.id)) {
          let set = new Set(this.selection)
          set.delete(row.id)
          this.selection = [...set]
        }
      }
    },
    handleSelectionChange(list) {
      // this.selection = list

    },
    easyAddVehicle() {
      this.$refs['EasyAddVehicle'].showModal()
    },
    terminalList(row) {
      this.$refs['TerminalList'].showModal(row)

    },
    //混合排序,数字,字母,汉字排序
    sortCurrentProp(column) {
      if (!column.order || !column.prop) return
      let chineseChars = [], chars = [], codeChars = [], nullList = []
      let prop = column.prop
      this.carTable.data.forEach(item => {
        let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
        let reg1 = /^[\d]+$/
        if (!item[prop]) {
          nullList.push(item)
        } else if (reg.test(item[prop])) {
          chineseChars.push(item);   // 姓名首字符为中文的
        } else if (reg1.test(item[prop])) {
          chars.push(item);   // 姓名首字符非中文的（字母，数字）
        } else {
          codeChars.push(item)
        }
      })
      if (column.order === 'ascending') {
        chars.sort((a, b) => a[prop] - b[prop]);
        codeChars.sort((a, b) => a[prop].charCodeAt(0) - b[prop].charCodeAt(0));
        chineseChars.sort((a, b) => a[prop].localeCompare(b[prop]));
      } else {
        chars.sort((a, b) => b[prop] - a[prop]);
        codeChars.sort((a, b) => b[prop].charCodeAt(0) - a[prop].charCodeAt(0));
        chineseChars.sort((a, b) => b[prop].localeCompare(a[prop]));
      }
      this.carTable.data = chars.concat(codeChars).concat(chineseChars).concat(nullList)
    },
    settable(value) {
      this.tableSettingList = value
      localStorage.setItem('carTableSetting', JSON.stringify(value))
      this.$nextTick(() => {
        this.$refs.carTable.doLayout()
        this.tableKey++
      })

    },
    showTableSetting() {
      this.$refs.tableShowConfigList.showModel()
    },
    multipleModify() {
      if (this.selection.length == 0) {
        // this.$warning("未选择批量修改的车辆")
        this.exportTableListApi()
        return
      }
      this.$refs['multipleModify'].showModal(this.selection)
    },

    //批量删除
    async multipleDelete() {
      if (this.selection.length == 0) {
        this.$warning("未选择批量删除的车辆")
        return
      }
      this.IdStr=""
      for (let i = 0; i < this.selection.length; i++) {
        this.IdStr += this.selection[i] + ","
      }
      let IdArray = this.IdStr.substring(0, this.IdStr.lastIndexOf(','))
      await this.$confirm(`已选择${this.selection.length}辆，将永久删除已选择车辆、司机绑定、准驾记录，是否继续?`, this.$ct('label.tip'), {
        type: 'error'
      })
      try {
        let res = await this.$api.removeSysVehicleInfoArray({
          IdArray: IdArray,
        });
        if (res.RS === 1) {
          this.$message({type: 'success', showClose: true, message: "批量删除成功!"})
          this.getCarTableList();
        } else {
          this.$error(res.Reason)
        }
      } catch (e) {
        this.$message(this.$ct('messageInfo.2'))
      }
    },
    // 导出二维码
    async exportQrcode(row) {
      this.exportLoading = true
      // let PDFlist = currentList.filter(item => item.status == 1)
      let data = {
        data: [row]
      }
      try {
        await this.$utils.excelExport(
          "/ponysafety2/a/vehicle/qrcode/pdf",
          JSON.stringify(data),
          "二维码" + ".pdf"
        );
      } catch (error) {
        this.$error('打印失败')
      } finally {
        this.exportLoading = false
      }
      this.exportLoading = false
    },

    jump(data) {
      this.$router.push({
        name: 'driverMgt',
        query: {
          name: data.driver_name
        }
      })
    },
    // async checkAllVehicle($tree) {
    //     let rootNodes = $tree.data.map($tree.getNode);
    //     rootNodes.forEach(node => {
    //         node.setChecked(true, true)
    //     })
    //     this.query.deptId = await $tree.getCheckedNodes(true).filter(item => item.type >= 3).map(item => item.id);
    //     this.getCarTableList();
    // },
    selectNodes(current, {checkedNodes}) {
      let currentNodes = checkedNodes.filter(item => item.type >= 3).map(item => item.id)
      this.query.deptId = currentNodes
      if (checkedNodes.length && !this.query.deptId.length) {
        this.carTable.data = [];
        this.pager.total = 0;
      } else {
        this.getCarTableList();
      }

    },
    async getCarTableList(routeParams, pageIndex = 1) {
      let params = {
        dept_id_list: this.query.deptId,
        vehicle_type_list: this.query.vehicle_type_list ? [this.query.vehicle_type_list] : null,
        page: this.pager.current,
        count: this.pager.size,
        sorting: -1,
        plate_no: this.query.type === 0 ? this.query.value : null,
        code: this.query.type === 1 ? this.query.value : null,
        sim_code: this.query.type === 2 ? this.query.value : null,
        driver_name: this.query.type === 3 ? this.query.value : null,
        vin: this.query.type === 4 ? this.query.value : null,
        device_id:this.query.type === 5 ? this.query.value : null,

        filter_annual: this.yearDueDate,
        filter_insurance: this.dueDate,
        filter_opera: this.filterOperaDate

      }
      if (routeParams && Object.keys(routeParams).length) {
        params = routeParams
      }
      // this.pager.current = pageIndex;
      this.carTable.loading = true;
      let res = await this.$api.getSysVehicleInfoV2(params, true);
      this.carTable.loading = false;
      // this.videoRoad=res.
      this.carTable.data = res.VehicleList || [];
      this.pager.total = res.Count || 0;
      this.$nextTick(() => {
        this.$refs['carTable'].doLayout()
        this.carTable.data.forEach(item => {
          if (this.selection.includes(item.id)) {
            this.$refs['carTable'].toggleRowSelection(item, true);
          }
        })
      })
    },
    // nodeClick(data, node, $node) {
    //     console.log("data",data);
    //     // this.query.companyId = null;

    //     this.query.deptId = null;

    //     // switch (data.type) {
    //     //     case 1:
    //     //         this.query.companyId = data.children.map(item => (item.id));
    //     //         break;
    //     //     case 2:
    //     //         this.query.companyId = [data.id];
    //     //         break;
    //     //     case 3:
    //     //         this.query.deptId = [data.id];
    //     //         break;
    //     //     case 0:
    //     //     default:
    //     //         this.query.companyId = null;
    //     //         this.query.deptId = null;
    //     // }
    //     this.currentNode = data;
    //     this.getCarTableList();
    // },
    modifyVehicle(row) {

      if(row.createBy && row.createBy.includes(':ocr')){
        this.$refs['carInfoOCR'].showModal(row);
      }else {
        this.$refs['carInfo'].showModal(row);

      }
    },
    addVehicle() {
      this.$refs['carInfo'].showModal();
    },
    addVehicleOCR(){
      this.$refs['carInfoOCR'].showModal();

    },
    driverBind(row) {
      this.$refs['driverBind'].showModal(row);
    },
    trailerBind(row){
      this.$refs['trailerBind'].showModal(row);
    },
    multipleImport() {
      this.$refs['multipleImport'].showModal();
    },
    async removeCar(row) {
      await this.$confirm(this.$ct('messageInfo.0'), this.$ct('label.tip'), {
        type: 'error'
      })
      try {
        let res = await this.$api.removeSysVehicleInfo({
          id: row.id,
        });
        if (res.RS === 1) {
          this.$message({type: 'success', showClose: true, message: this.$ct('messageInfo.1')})
          this.getCarTableList();
        } else {
          this.$error(res.Reason)
        }
      } catch (e) {
        this.$message(this.$ct('messageInfo.2'))
      }
    },
    async toggleState(row, state) {
      await this.$confirm(this.$ct('messageInfo.3'), this.$ct('label.tip'), {
        type: 'warning'
      })
      if (state === undefined) {
        state = row.status == 0 ? -2 : 0
      }
      let res = await this.$api.modifyCarStatus({
        id: row.id,
        status: state,
      })
      if (res.RS === 1) {
        row.status = state ? -1 : 0
        this.$message({
          type: 'success',
          showClose: true,
          message: this.$ct('expression.0', [row.status == 0 ? this.$ct('label.enable') : this.$ct('label.disable')])
        })

      } else {
        this.$message({type: 'error', showClose: true, message: this.$ct('messageInfo.4')})
      }
    },
    async vehicleReport(row) {
      let res = await this.$api.pushVehicleInfo2JTAQJC({
        vehicle_id: row.id,
      })
      if (res.code === 200) {
        this.$message({type: 'success', message: this.$ct('label.successReport'), showClose: true});
      } else {
        this.$message({type: 'error', message: res.message, showClose: true});
      }
    },
    // async exportTableList() {
    //     let searchList = {
    //         dept_id_list: this.query.deptId,
    //         plate_no: this.query.type === 0 ? this.query.value : null,
    //         code: this.query.type === 1 ? this.query.value : null,
    //         sim_code: this.query.type === 2 ? this.query.value : null,
    //         page: 1,
    //         count: 30,
    //         sorting: -1,
    //         vehicle_type_list: this.query.vehicle_type_list ? [this.query.vehicle_type_list] : null,
    //         driver_name: this.query.type === 3 ? this.query.value : null,
    //         extra_column_key: this.isCLZCL ? 'clzcl' : undefined
    //     }
    //     let search = {}
    //     this.allSettingList.forEach(item =>{
    //         console.log(item)
    //         search[item.key] = this.query[item.key]
    //     })
    //     console.log(search)
    //     // this.exportLoading = true;
    //     // await this.$utils.excelExport("/ponysafety2/a/vehicle/excelSysVehicleInfoV2", JSON.stringify(searchList),
    //     //   this.$ct('messageInfo.5'));
    //     // this.exportLoading = false;
    // },

    async exportTableListApi() {
      let searchList = {
        dept_id_list: this.query.deptId,
        vehicle_type_list: this.query.vehicle_type_list ? [this.query.vehicle_type_list] : null,
        page: 0,
        count: this.pager.size,
        sorting: -1,
        plate_no: this.query.type === 0 ? this.query.value : null,
        code: this.query.type === 1 ? this.query.value : null,
        sim_code: this.query.type === 2 ? this.query.value : null,
        driver_name: this.query.type === 3 ? this.query.value : null,
        vin: this.query.type === 4 ? this.query.value : null,
        device_id:this.query.type === 5 ? this.query.value : null,

        filter_annual: this.yearDueDate,
        filter_insurance: this.dueDate,
        filter_opera: this.filterOperaDate

      }
      this.exportLoading = true;
      await this.$utils.excelExport("/ponysafety2/a/vehicle/excelSysVehicleInfoV2", JSON.stringify(searchList), "车辆管理" + ".xlsx");
      this.exportLoading = false;
    },
    async exportTableList() {
      let query = {
        dept_id_list: this.query.deptId,
        vehicle_type_list: this.query.vehicle_type_list ? [this.query.vehicle_type_list] : null,
        page: 0,
        count: this.pager.size,
        sorting: -1,
        plate_no: this.query.type === 0 ? this.query.value : null,
        code: this.query.type === 1 ? this.query.value : null,
        sim_code: this.query.type === 2 ? this.query.value : null,
        driver_name: this.query.type === 3 ? this.query.value : null,
        vin: this.query.type === 4 ? this.query.value : null,
        device_id:this.query.type === 5 ? this.query.value : null,

        filter_annual: this.yearDueDate,
        filter_insurance: this.dueDate,
        filter_opera: this.filterOperaDate

      }
      this.exportLoading = true;
      let res = await this.$api.getSysVehicleInfoV2(query);
      let vehicleList = res.VehicleList
      if (this.selection.length > 0) {
        vehicleList = res.VehicleList.filter(item => {
          return this.selection.includes(item.id)
        })
      }

      let sheetName = []
      this.tableSettingList.forEach((item) => {
        if (item.key === 'terminal_type_name_one') {
          sheetName.push('终端类型名@terminal_type_name_one@8000@000000')
          sheetName.push('终端手机号@terminal_code@8000@000000')
          sheetName.push('终端型号@terminal_desc@8000@000000')
          sheetName.push('主终端ICCID卡号@sim_code@8000@000000')
        } else {
          sheetName.push(item.name + '@' + item.key + '@' + 8000 + '@000000')
        }
      })
      let params = {}
      let paramsList = [];
      let resData = []

      let inputList = {
        1: '设备直连 ',
        2: '808转发 ',
        3: '809接入 ',
        4: '中交兴路 '
      }
      for (let i = 0; i < vehicleList.length; i++) {
        let item = vehicleList[i]
        let arr = {}
        arr['index'] = i + 1
        this.tableSettingList.forEach(data => {
          if (data.key === 'company_name') {
            arr[data.key] = item.company_name + '>>' + item.dept_name
          } else if (data.key === 'trail_type_names') {
            arr[data.key] = this.isCLZCL ? item.trail_type_names : (item.industry_type == 8 ? item.concrete_type_names : item.vehicle_type)
          } else if (data.key === 'status') {
            arr[data.key] = item.status ? '停用' : '启用'
          } else if (data.key === 'channel_count') {
            // arr[data.key] = item.channel_count || 4
            arr[data.key] = item.channel_count
          } else if (data.key === 'black_white') {
            arr[data.key] = item.black_white ? (item.black_white === 1 ? '白名单' : '黑名单') : '未设置'
          } else if (data.key === 'terminal_type_name_one') {
            if (item.terminal_bind_list.length > 0) {
              arr['terminal_type_name_one'] = item.terminal_type_name_one
              arr['terminal_code'] = item.code_one
              arr['terminal_desc'] = item.terminal_desc_one
              arr['sim_code'] = item.sim_code_one
            } else {
              arr['terminal_type_name_one'] = ''
              arr['terminal_code'] = ''
              arr['terminal_desc'] = ''
              arr['sim_code'] = ''
            }
          } else if (data.key === 'sign') {
            let str = ' '
            if (item.videoDevice) {
              str += '视频设备 '
            }
            if (item.sourceData) {
              str += '原车数据接入 '
            }
            if (item.inputType) {
              str += inputList[item.inputType]
            }
            if (item.feeInvalid) {
              str += '服务到期 '
            }
            arr[data.key] = str
          } else {
            arr[data.key] = item[data.key]
          }
        })
        resData.push(arr)
      }

      let fileName = '导出车辆'
      let title = '导出车辆'

      params = {
        sheetName: "导出车辆",
        title: title,
        headers: sheetName,
        dataList: resData,
      }
      paramsList.push(params)
      await this.$utils.jsExcelExport(JSON.stringify(paramsList), fileName + '.xlsx')
      this.exportLoading = false;
    },
  },
  activated() {
    if (this.$route.query) {
      if (this.$route.query.plate_no) {
        this.query.value = this.$route.query.plate_no
        this.getCarTableList()
      }
      if (this.$route.query.filter_annual) {
        this.yearDueDate = this.$route.query.filter_annual ? Number(this.$route.query.filter_annual) : 0
        this.dueDate = this.$route.query.filter_insurance ? Number(this.$route.query.filter_insurance) : 0
        this.filterOperaDate = this.$route.query.filter_opera ? Number(this.$route.query.filter_opera) : 0
        this.getCarTableList(this.$route.query);
      }
      this.$nextTick(async () => {
        await this.$router.push("/home/<USER>");
      });
    }
  }
}
</script>

<style scoped lang="scss">
.driver:hover {
  color: #2880e2;
  cursor: pointer;
}

.tableList {
  width: 100%;
  display: flex;

  .list {
    display: inline-block;
    flex: 4;
    text-align: center;
    border-right: 1px solid var(--border-color-lighter);
    overflow: hidden;
    text-overflow: ellipsis;

    &:nth-child(5) {
      border: 0;
      flex: 1;
      margin-left: 10px;
    }
  }
}

/deep/ .el-table__row {
  height: 33px !important;

  tr {
  }

  td {
    padding: 0 !important;

    .cell {
      height: 33px !important;
      line-height: 33px !important;
    }
  }
}

.signList {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
}
</style>
