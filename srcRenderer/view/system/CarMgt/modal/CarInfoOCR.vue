<template>
  <PonyDialog
    v-model="show"
    :title="mode === 'add' ? addModeTitle : modifyModeTitle"
    class="car-modal"
    :width="890"
    content-style="max-height:650px;overflow:auto;padding:0"
  >
    <el-form :model="data" :rules="rules" size="mini" ref="form" label-width="126px" class="form-group">
      <el-form-item prop="deptGroup" :label="$ct('label.group') + ':'">
        <SelectTreeInput
          class="elWidth"
          v-model="data.deptGroup"
          type="department"
          ref="departmentInput"
          :placeholder="$ct('label.deptChoosePlease')"
          :title="$ct('label.deptChoosePlease')"
          :withParent="true"
          :condition="deptCondition"
        ></SelectTreeInput>
      </el-form-item>
      <el-form-item label="车牌号:" prop="plate_no">
        <el-input v-model="data.plate_no" class="elWidth"></el-input>
      </el-form-item>
      <el-form-item label="车辆类型:" prop="register_type">
        <el-input v-model="data.register_type" class="elWidth"></el-input>
      </el-form-item>

      <el-form-item label="品牌型号:" prop="vehicle_model">
        <el-input class="elWidth" v-model="data.vehicle_model"></el-input>
      </el-form-item>
      <el-form-item label="识别代号VIN:" prop="vin">
        <el-input class="elWidth" v-model="data.vin"></el-input>
      </el-form-item>
      <el-form-item label="发动机号:" prop="engineNumber">
        <el-input class="elWidth" v-model="data.engineNumber"></el-input>
      </el-form-item>
      <el-form-item label="注册日期:" prop="register_date">
        <el-date-picker
          class="elWidth"
          v-model="data.register_date"
          type="date"
          placeholder="请选择时间"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="发证日期:" prop="issue_date">
        <el-date-picker class="elWidth" v-model="data.issue_date" type="date" placeholder="请选择时间" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="所有人:" prop="owers_name">
        <el-input class="elWidth" v-model="data.owers_name"></el-input>
      </el-form-item>
      <el-form-item label="地址:" prop="address">
        <el-input class="elWidth" v-model="data.address"></el-input>
      </el-form-item>
      <el-form-item label="使用性质:" prop="use_nature">
        <el-input class="elWidth" v-model="data.use_nature"></el-input>
      </el-form-item>
      <el-form-item label="核载质量:" prop="nuclear_carry">
        <el-input class="elWidth" v-model="data.nuclear_carry"></el-input>
      </el-form-item>
      <el-form-item label="保险到期日期:" prop="insurance_valid_date">
        <el-date-picker
          class="elWidth"
          v-model="data.insurance_valid_date"
          type="date"
          placeholder="请选择时间"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="保险公司及机构:" prop="insurance_org">
        <el-input class="elWidth" v-model="data.insurance_org"></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="行驶证(主页): " class="long" prop="driving_license_img[0]">
            <ImageUploader
              ref="uploader1"
              :uploadKey="`vehicle_license:${data.plate_no ? data.plate_no : '车牌号'}:行驶证`"
              v-model="data.driving_license_img[0]"
              @change="(url)=>successUpload(url,'face')"
              class="img-wrapper"
            >
            </ImageUploader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行驶证(副页): " class="long" prop="driving_license_img[1]">
            <ImageUploader
              ref="uploader2"
              :uploadKey="`vehicle_license:${data.plate_no ? data.plate_no : '车牌号'}:行驶证`"
              v-model="data.driving_license_img[1]"
              @change="(url)=>successUpload(url,'back')"
              class="img-wrapper"
              :disabled="!data.driving_license_img[0]"
              :whyDisable="!data.driving_license_img[0] ? '请先上传主页' : ''"
            >
            </ImageUploader>
          </el-form-item>
        </el-col>
      </el-row>
    
      <el-form-item label="图像资料: " class="long" prop="force_insurance_img">
        <ImageUploaderGroup
          ref="uploaderGroup"
          :max="2"
          :uploadKey="`vehicle_license:${data.plate_no ? data.plate_no : '车牌号'}:保单`"
          v-model="data.force_insurance_img"
          @successUpload="successUpload2"
          :disabled="!data.driving_license_img[0]"
          :whyDisable="!data.driving_license_img[0] ? '请先上传行驶证' : ''"
        >
        </ImageUploaderGroup>

        <!-- <ImageUploader
              class="elWidth"
              :uploadKey="`vehicle_opera:${data.plate_no}`"
              v-model="data.force_insurance_img"
              style="width: 200px; height: 120px"
            ></ImageUploader> -->
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="commit">{{ mode === "add" ? addModeOkText : modifyModeOkText }} </el-button>
      <el-button @click="show = false">{{ $ct("label.cancel") }}</el-button>
    </template>
  </PonyDialog>
</template>

<script>
import SelectTreeInput from "../../../../components/common/SelectTreeInput";
import DictionarySelect from "../../../../components/common/DictionarySelect";
import { mapActions } from "vuex";
import ImageUploader from "@/components/common/ImageUploader";
import ImageUploaderGroup from "@/components/common/ImageUploaderGroup";
import log from "../../../videoPlayStandard/component/videoPlayerStandard/mxin/log";

export default {
  name: "carInfo",
  components: {
    ImageUploaderGroup,
    ImageUploader,
    DictionarySelect,
    SelectTreeInput,
  },
  data() {
    const defaultData = {
      register_date: "",
      issue_date: "",
      register_type: "",
      address: "",
      insurance_org: "",
      id: null,
      plate_no: "",
      vin: "",
      nuclear_carry: "", // 车辆容积  2020 10 23 yzy
      owers_name: null,
      use_nature: null,
      driving_license_img: [],
      insurance_valid_date: null,
      force_insurance_img: [],
      vehicle_model: "",
      engineNumber: "",
      source: "ocr",
      deptGroup: null,
      dept_id: "",
      company_id: "",
    };
    return {
      show: false,
      mode: "add",
      loading: false,
      addModeTitle: this.$ct("label.addCar"),
      addModeOkText: this.$ct("label.addConfirm"),
      modifyModeTitle: this.$ct("label.modifyCar"),
      modifyModeOkText: this.$ct("label.modifyConfirm"),
      data: JSON.parse(JSON.stringify(defaultData)),
      defaultData,
      rules: {
        plate_no: [
          {
            required: true,
            message: "请输入车牌号",
            trigger: "blur",
          },
        ],
        register_type: [
          {
            required: true,
            message: "请输入车辆类型",
            trigger: "blur",
          },
        ],
        vehicle_model: [
          {
            required: true,
            message: "请输入品牌型号",
            trigger: "blur",
          },
        ],
        vin: [
          {
            required: true,
            message: "请输入vin",
            trigger: "blur",
          },
        ],
        engineNumber: [
          {
            required: true,
            message: "请输入发动机号",
            trigger: "blur",
          },
        ],
        register_date: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        issue_date: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        owers_name: [
          {
            required: true,
            message: "请输入所有人",
            trigger: "blur",
          },
        ],
        use_nature: [
          {
            required: true,
            message: "请输入使用性质",
            trigger: "blur",
          },
        ],
        address: [
          {
            required: true,
            message: "请输入地址",
            trigger: "blur",
          },
        ],
        nuclear_carry: [
          {
            required: true,
            message: "请输入核载质量",
            trigger: "blur",
          },
        ],
        insurance_valid_date: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        insurance_org: [
          {
            required: true,
            message: "请输入保险公司及机构",
            trigger: "blur",
          },
        ],
        "driving_license_img[0]": [
          {
            required: true,
            message: "请上传文件",
            trigger: ["change", "blur"],
          },
        ],
        "driving_license_img[1]": [
          {
            required: true,
            message: "请上传文件",
            trigger: ["change", "blur"],
          },
        ],
        force_insurance_img: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change",
            type: "array",
          },
        ],
        deptGroup: [
          {
            required: true,
            message: this.$ct("messageInfo.2"),
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    "data.deptGroup": function (val) {
      if (!val) return;
      Object.assign(this.data, {
        dept_id: val.value,
        company_id: val.parentNode.value,
      });
      // this.getDriverList();
      // this.simRemoteMethod();
      // this.lineRemoteMethod();
    },
  },
  methods: {
    deptCondition(treeNode) {
      if (treeNode.type !== 3) {
        this.$message({
          type: "info",
          message: this.$ct("messageInfo.8"),
          showClose: true,
        });
        return false;
      } else {
        return true;
      }
    },
    async showModal(rowData) {
      let vehicleId = rowData ? rowData.id : null;
      this.data = JSON.parse(JSON.stringify(this.defaultData));
      this.show = true;

      if (vehicleId) {
        this.mode = "modify";
        this.initData(vehicleId);

        // await this.$nextTick(()=>{})
        // this.$utils.assign(this.data, rowData);
        // this.data.deptGroup = await this.$refs["departmentInput"].fillOtherProperty(this.data.dept_id);
      } else {
        this.mode = "add";
      }
    },
    async initData(vehicleId) {
      let res = await this.$api.selectVehicleAllById({ vehicleId });
      if (res && res.RS == 1) {
        //特殊的Object.assign
        this.$utils.assign(this.data, res.VehicleListAll);
        this.data.deptGroup = await this.$refs["departmentInput"].fillOtherProperty(this.data.dept_id);
      }
    },
    successUpload(url,type) {
      let host =
        process.env.NODE_ENV == "development"
          ? "http://**************:8085"
          : window.location.origin == "http://************:8085"
          ? "http://**************:8085"
          : window.location.origin;
      let url2 = host + url;
      this.getDataByImg(url2, type);
    },
    successUpload2() {
      this.$refs["form"].validateField("force_insurance_img");
    },
    async getDataByImg(url, type) {
      if (!url) return;
      let result = await this.$api.ocrRecognition({
        operateType: 1,
        url,
      });
      if (result && result.status == 200) {
        this.$utils.assign(this.data, result.data[type]);
        if (result.data.face && type == 'face') {
          this.data.plate_no = result.data.face.licensePlateNumber;
          this.data.issue_date = result.data.face.issueDate;
          this.data.register_date = result.data.face.registrationDate;
          this.data.use_nature = result.data.face.useNature;
          this.data.register_type = result.data.face.vehicleType;
          this.data.vin = result.data.face.vinCode;
          this.data.vehicle_model = result.data.face.model;
          this.data.owers_name = result.data.face.owner;
        }
        if (result.data.back && type == 'back') {
          this.data.nuclear_carry = result.data.back.permittedWeight;
        }
        this.$refs["form"].validate(() => {});
      } else {
        this.$message.error(result.message || "识别错误");
      }
    },
    async reuploadImages() {
      try {
        // 只重新上传行驶证主页
        if(this.data.driving_license_img[0]) {
          const uploader1 = this.$refs.uploader1;
          if(uploader1.$refs.uploader) {
            const uploadInstance = uploader1.$refs.uploader;
            const file = uploadInstance.uploadFiles[0];

            if(file && file.raw) {
              // 构造上传参数
              const formData = new FormData();
              formData.append('file', file.raw);
              formData.append('key', `vehicle_license:${this.data.plate_no ? this.data.plate_no : '车牌号'}:行驶证`);

              // 直接调用上传接口
              const response = await fetch('/ponysafety2/a/fileupload/common', {
                method: 'POST',
                body: formData
              });
              
              const result = await response.json();
              if(result.rs === 1) {
                // 只更新图片地址，不触发 OCR
                this.data.driving_license_img[0] = result.url;
              } else {
                throw new Error('上传失败');
              }
            }
          }
        }

        return true;
      } catch(err) {
        this.$message.error('文件重新上传失败');
        return false;
      }
    },
    async commit() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) {
          return this.$message.error("必填信息不能为空！");
        }

        const uploadResult = await this.reuploadImages();
        if(!uploadResult) {
          return;
        }

        let result = await (this.mode === "add" ? this.addCar() : this.modifyCar());
        if (result) {
          this.$emit("refresh");
          this.show = false;
        }
      });
    },
    async addCar() {
      try {
        let res = await this.$api.addSysVehicleInfo(this.data);
        if (res.RS === 1) {
          this.$success(this.$ct("label.addSuccess"));
          return true;
        } else {
          throw new Error(this.$ct("messageInfo.10") + res.Reason);
          return false;
        }
      } catch (e) {
        this.$message.error(e.message || "操作失败");
        return false;
      }
    },
    async modifyCar() {
      // console.log(Object.assign(this.paramAdapter(this.data), { terminal_bind_list: this.terminalTable }), 'params');
      if (!this.hasPermission("car:update")) return this.$error("修改失败,无权限！");
      try {
        let res = await this.$api.modifySysVehicleInfo(this.data);
        if (res.RS === 1) {
          this.$success(this.$ct("label.modifySuccess"));
          return true;
        } else {
          throw new Error(this.$ct("messageInfo.11") + res.Reason);
          return false;
        }
      } catch (e) {
        this.$error(e.message);
        return false;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.form-group {
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 0;
  padding: 10px;
  overflow: auto;
  .elWidth {
    width: 160px;
  }
  /deep/ .el-form-item {
    &.long {
      width: 100%;
    }
  }

  /deep/ .vadio {
    width: 100%;
  }

  /deep/ .half {
    width: 50%;
  }
}
.img-wrapper {
  height: 120px;
  width: 200px;
  margin: 5px 5px 5px 0px;
}
</style>
