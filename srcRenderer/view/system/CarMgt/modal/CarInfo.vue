<template>
  <PonyDialog
    v-model="show"
    :title="mode === 'add' ? addModeTitle : modifyModeTitle"
    class="car-modal"
    :width="890"
    content-style="max-height:650px;overflow:auto;padding:0"
  >
    <el-form :model="data" :rules="rules" size="mini" ref="form" label-width="100px">
      <el-tabs v-model="activeTab" type="border-card" style="height: 500px; border: none; box-shadow: none">
        <el-tab-pane name="basic" :label="$ct('label.baseInfo')" class="form-group">
          <el-form-item prop="plate_no" :label="$ct('label.plateNo') + ':'">
            <el-input
              class="elWidth"
              v-model="data.plate_no"
              :disabled="mode === 'modify' && !hasPermission('car:editPlateNo')"
            ></el-input>
          </el-form-item>
          <el-form-item prop="plate_color" :label="$ct('label.plateColor') + ':'">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.plate_color">
              <el-option v-for="(item, index) in plateColor" :key="index" :label="item.label" :value="+item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="alias"
            :label="$ct('label.carAlias') + ':'"
            :rules="{
              required: data.industry_type == 8,
              message: '请输入车辆别名',
              trigger: 'blur',
            }"
          >
            <el-input class="elWidth" v-model="data.alias"></el-input>
            <el-popover placement="top-start" width="210" trigger="hover" content="别名可在车辆树上设置展示">
              <i class="pony-iconv2 pony-bangzhu" slot="reference" style="cursor: pointer"></i>
            </el-popover>
          </el-form-item>
          <el-form-item prop="deptGroup" :label="$ct('label.group') + ':'">
            <SelectTreeInput
              class="elWidth"
              v-model="data.deptGroup"
              type="department"
              ref="departmentInput"
              :placeholder="$ct('label.deptChoosePlease')"
              :title="$ct('label.deptChoosePlease')"
              :withParent="true"
              :condition="deptCondition"
            ></SelectTreeInput>
          </el-form-item>
          <el-form-item prop="industry_type" :label="$ct('label.industry') + ':'" v-if="!isCLZCL">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.industry_type">
              <el-option
                v-for="(item, index) in industryType"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="trans_type" :label="$ct('label.transportIndustry') + ':'">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.trans_type">
              <!--                            <el-option v-for="(item, index) in transType" :key="index" :label="item.value + ' - ' + item.label" :value="item.value">-->
              <el-option v-for="(item, index) in transType" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="type"
            :label="$ct('label.carTypes') + ':'"
            v-if="!isCLZCL && +data.industry_type !== 2 && +data.industry_type !== 8 && +data.industry_type !== 18"
          >
            <!-- <el-select
              :placeholder="$ct('label.choosePlease')"
              v-model="data.type"
            >
              <el-option
                v-for="(item, index) in vehicleType"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select> -->
            <SelectTreeInput
              class="elWidth"
              v-model="vehicleTypeSelectValue"
              ref="vehicleType"
              type="vehicleType"
              :withParent="true"
              :condition="treeDeptCondition"
              placeholder="请选择车辆类型"
              title="请选择车辆类型"
            >
            </SelectTreeInput>
          </el-form-item>
          <el-form-item prop="trail_type" :label="$ct('label.carTypes') + ':'" v-if="isCLZCL || +data.industry_type === 2">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" multiple collapse-tags v-model="data.trail_type">
              <el-option v-for="item in carKindType" :key="item.value" :label="item.label" :value="+item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="concrete_type" :label="$ct('label.carTypes') + ':'" v-if="+data.industry_type === 8">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.concrete_type">
              <el-option v-for="(item, index) in concreteKindType" :key="index" :label="item.label" :value="+item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="concrete_type" :label="$ct('label.carTypes') + ':'" v-if="+data.industry_type === 18">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.concrete_type">
              <el-option v-for="(item, index) in cleanKindType" :key="index" :label="item.label" :value="+item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="energyType" label="能源类型:">
            <el-select class="elWidth" placeholder="请选择能源类型" v-model="data.energyType">
              <el-option v-for="(item, index) in energyTypeList" :key="index" :label="item.label" :value="+item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="energyCapacity" :label="data.energyType == 0 ? '油箱容量:' : data.energyType==1 ? '电池容量:' : '气体容积:'">
            <el-input
              class="elWidth"
              v-model="data.energyCapacity"
              :placeholder="data.energyType == 0 ? '请输入油箱容量' : data.energyType==1 ? '请输入电池容量': '请输入气体容积'"
            >
              <template slot="append">{{ data.energyType == 0 ? "L" : data.energyType==1 ?'KWh':"m³" }}</template>
            </el-input>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item prop="vin" :label="$ct('label.carVIN') + ':'">
            <el-input class="elWidth" v-model="data.vin"></el-input>
          </el-form-item>
          <el-form-item prop="vin" :label="$ct('label.allowedDrivers') + ':'">
            <el-select
              class="elWidth"
              v-model="data.allow_bind_driver"
              multiple
              filterable
              :placeholder="$ct('label.deptChoosePlz')"
              collapse-tags
              :loading="driverRemoteLoading"
            >
              <el-option v-for="item in driverList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="trail_devices" :label="$ct('label.carDevices') + ':'" v-if="isCLZCL || +data.industry_type === 2">
            <el-select
              class="elWidth"
              :placeholder="$ct('label.choosePlease')"
              multiple
              collapse-tags
              v-model="data.trail_devices"
            >
              <el-option v-for="item in carTrailDevice" :key="item.value" :label="item.label" :value="+item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="trail_tonnage" :label="$ct('label.carLoad') + ':'" v-if="isCLZCL || +data.industry_type === 2">
            <el-input-number class="elWidth" v-model="data.trail_tonnage" min="0:" style="width: 100%"></el-input-number>
          </el-form-item>
          <!-- 到期时间隐藏 -->
          <!-- <el-form-item :label="$ct('label.expireTime') + ':'" >
                        <el-date-picker v-model="data.fee_end" type="date" style="width: 100%;" :placeholder="$ct('label.datePlz')" value-format="yyyy-MM-dd" :editable="false">
                        </el-date-picker>
                    </el-form-item> -->
<!--          <el-form-item label="车厢容积(m³):">-->
          <el-form-item label="核载质量(kg):">
            <el-input class="elWidth" style="width: 100%" v-model="data.nuclear_carry"></el-input>
          </el-form-item>
          <el-form-item label="车辆重量(吨):">
            <el-input class="elWidth" style="width: 100%" v-model="data.weight"> </el-input>
          </el-form-item>
          <el-form-item label="轴数:">
            <el-input class="elWidth" v-model="data.axle"></el-input>
          </el-form-item>
          <el-form-item label="车长(m):">
            <el-input class="elWidth" v-model="data.length"></el-input>
          </el-form-item>
          <el-form-item label="车高(m):">
            <el-input class="elWidth" v-model="data.height"></el-input>
          </el-form-item>
          <el-form-item label="车宽(m):">
            <el-input class="elWidth" v-model="data.width"></el-input>
          </el-form-item>
          <el-form-item label="挂车车牌号:">
            <el-input class="elWidth" v-model="data.trailer_no"></el-input>
          </el-form-item>
          <el-divider></el-divider>
          <el-form-item label="服务开始时间:">
            <el-date-picker
              class="elWidth"
              v-model="data.fee_begin"
              type="date"
              style="width: 100%"
              placeholder="请选择开始时间"
              :disabled="!hasPermission('carExpirationTime:change')"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="服务结束时间:">
            <el-date-picker
              class="elWidth"
              v-model="data.fee_end"
              type="date"
              style="width: 100%"
              placeholder="请选择结束时间"
              :disabled="!hasPermission('carExpirationTime:change')"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="车辆来源:">
            <el-select class="elWidth" placeholder="请选择车辆来源" v-model="data.sourceType">
              <el-option v-for="item in carSourceType" :key="item.value" :label="item.label" :value="+item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="搅拌站：" prop="mixing_station_ids" v-if="+data.industry_type === 8">
            <SelectTreeInput
              class="elWidth"
              v-model="jiaobanSelectValue"
              ref="jiaoban"
              type="jiaobanzhantree"
              :condition="treeFilterData"
              :withParent="true"
              title="请选择搅拌站"
            >
            </SelectTreeInput>
          </el-form-item>
          <el-form-item label="车辆使用类型" v-if="sinotransUsageStatus && hasPermission('zwy:sinotransUsage')">
            <el-select v-model="data.sinotransUsage">
              <el-option label="短途" :value="1"></el-option>
              <el-option label="长途" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$ct('label.remark') + ':'" class="long">
            <el-input v-model="data.remark" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane
          name="device"
          :label="$ct('label.terminalConfig')"
          class="form-group"
          v-if="hasPermission('car:editTerminal')"
        >
          <el-row :gutter="0" style="width: 100%">
            <el-col :span="8">
              <el-form-item prop="channel_count" label="接入方式:">
                <el-select class="elWidth" v-model="data.inputType" style="margin-left: 10px" placeholder="请选择">
                  <el-option v-for="(item, index) in inputTypeList" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="channel_count">
                <el-checkbox v-model="data.sourceData">原车数据推送</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="init_mile" label="初始里程:">
                <el-input v-model="data.init_mile" style="width: 140px; margin-left: 10px">
                  <template slot="append">米</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-row>
                <el-col>
                  <el-form-item label-width="30px">
                    <el-checkbox v-model="data.videoDevice">视频设备</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <el-form-item prop="channel_count" label="视频通道:">
                    <el-input-number
                      v-model="data.channel_count"
                      :min="0"
                      :max="16"
                      style="width: 100%"
                      class="elWidth"
                      v-show="false"
                    ></el-input-number>
                    <el-select
                      v-model="channel_valid"
                      multiple
                      collapse-tags
                      style="margin-left: 10px; width: 310px"
                      placeholder="请选择"
                      :disabled="!data.videoDevice"
                    >
                      <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index"> </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item prop="channel_count" label="对讲通道:">
                    <el-select
                      v-model="data.channelTalkback"
                      style="margin-left: 10px; width: 100px"
                      placeholder="请选择"
                      :disabled="!data.videoDevice"
                    >
                      <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index + 1">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="channel_count" label="ADAS前向:">
                    <el-select
                      v-model="data.channelAdasFront"
                      style="margin-left: 10px; width: 100px"
                      placeholder="请选择"
                      :disabled="!data.videoDevice"
                    >
                      <el-option
                        v-for="(item, index) in channel_valid"
                        :key="index"
                        :label="`通道${item + 1}`"
                        :value="item + 1"
                      >
                      </el-option>
<!--                      <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index + 1"></el-option>-->
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="channel_count" label="广播通道:">
                    <el-select
                      v-model="data.channelBroadcast"
                      style="margin-left: 10px; width: 100px"
                      placeholder="请选择"
                      :disabled="!data.videoDevice"
                    >
                      <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index + 1">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="channel_count" label="DMS:">
                    <el-select
                      v-model="data.channelDsm"
                      style="margin-left: 10px; width: 100px"
                      placeholder="请选择"
                      :disabled="!data.videoDevice"
                    >
                      <el-option
                        v-for="(item, index) in channel_valid"
                        :key="index"
                        :label="`通道${item + 1}`"
                        :value="item + 1"
                      >
                      </el-option>
<!--                      <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index + 1"></el-option>-->
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item prop="channel_valid_temp" label="温度传感器:" v-if="isShowTemp">
                    <el-input-number
                      v-model="data.channel_valid_temp"
                      :min="0"
                      :max="16"
                      style="width: 100px"
                      class="elWidth"
                      v-show="false"
                    ></el-input-number>
                    <el-select v-model="channel_valid_temp" multiple collapse-tags style="margin-left: 10px;width: 310px;" placeholder="请选择">
                      <el-option v-for="(item, index) in 8" :key="index" :label="`通道${index + 1}`" :value="index"> </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-table
                border
                class="el-table--ellipsis"
                :data="data.channel_valid_name"
                max-height="170"
                style="width: 100%; margin-bottom: 10px"
              >
                <el-table-column type="index" label="序号" width="80"> </el-table-column>
                <el-table-column label="逻辑通道" min-width="100" prop="no">
                  <template slot-scope="{ row }">
                    {{ `通道${row.no}` }}
                  </template>
                </el-table-column>
                <el-table-column label="名称" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name" :disabled="!data.videoDevice"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-form-item prop="channel_count" label="终端设备:" class="vadio">
            <div class="add-btn" @click="addTerminal">
              <i class="el-icon-plus"></i>
            </div>
          </el-form-item>
          <div style="width: 100%">
            <el-table border class="el-table--ellipsis" :data="terminalTable" style="width: 100%; margin-bottom: 10px">
              <el-table-column min-width="30">
                <template slot-scope="scope">
                  <i class="pony-iconv2 pony-shanchu" @click="clearRow(scope.$index, scope.row)"></i>
                </template>
              </el-table-column>
              <el-table-column :label="$ct('label.terminalType')" min-width="130">
                <template slot-scope="scope">
                  <span style="color: #ff5359">*</span>
                  <el-select
                    v-model="scope.row.terminal_type_id"
                    popper-class="car-modal-popper"
                    filterable
                    :placeholder="$ct('label.terminalTypePlz')"
                  >
                    <el-option v-for="(item, index) in terminalType" :key="index" :label="item.label" :value="item.value">
                      <div class="option-contain" style="width: 300px">
                        <span class="option-label">{{ item.label }}</span>
                        <span class="extra" :title="item.protocol">{{ item.protocol }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="支持业务" min-width="130" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div v-if="scope.row.terminal_type_id">
                    <el-tag type="success" v-if="scope.row['business'].length > 2">
                      {{ scope.row["business"][0] }}
                    </el-tag>
                    <el-tag type="success" v-if="scope.row['business'].length > 2">
                      {{ scope.row["business"][1] }}
                    </el-tag>
                    <el-tag
                      v-else
                      type="success"
                      v-for="(item, index) in scope.row['business']"
                      :key="index"
                      style="margin-right: 3px"
                      >{{ item }}
                    </el-tag>

                    <el-popover v-if="scope.row['business'].length > 2" placement="top" trigger="hover">
                      <span style="margin-right: 3px" v-for="(item, index) in scope.row['business']" :key="index">
                        <el-tag type="success" v-if="index > 1">{{ item }}</el-tag>
                      </span>
                      <el-tag slot="reference" type="success"> +{{ scope.row["business"].length - 2 }} </el-tag>
                    </el-popover>
                  </div>
                  <div v-else style="color: #61697c">请选择终端类型</div>
                </template>
              </el-table-column>
              <el-table-column label="终端手机号" min-width="130">
                <template slot-scope="scope">
                  <div>
                    <span style="color: #ff5359">*</span>
                    <el-input v-model="scope.row.terminal_code" placeholder="请输入终端手机号"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="终端ID" min-width="130">
                <template slot-scope="scope">
                  <div>
                    <span style="color: #ff5359">*</span>
                    <el-input v-model="scope.row.device_id" placeholder="请输入终端ID"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="终端型号" min-width="130">
                <template slot-scope="scope">
                  <!-- <el-input v-model="scope.row.terminal_desc" placeholder="请输入终端型号"></el-input> -->
                  <el-select
                    v-model="scope.row.terminal_desc"
                    filterable
                    popper-class="car-modal-popper"
                    placeholder="请输入终端型号"
                  >
                    <el-option v-for="(item, index) in terminalDesc" :key="index" :label="item.label" :value="item.value">
                      <div class="option-contain" style="width: 200px">
                        <span class="option-label">{{ item.label }}</span>
                        <!-- <span class="extra" :title="item.protocol">{{
                          item.protocol
                        }}</span> -->
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="SIM卡ICCID号" min-width="160">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.sim_code" placeholder="请输入SIM卡ICCID号"></el-input>
                  </div>
                </template>
                <!-- <template slot-scope="scope">
                                    <el-select v-model="scope.row.sim_code" :disabled="
                      mode === 'modify' && !hasPermission('car:editSim')
                    " filterable remote popper-class="car-modal-popper" clearable @clear="simList = []" :remote-method="simRemoteMethod" :loading="remoteLoading" placeholder="请选择或输入sim卡号">
                                        <el-option v-for="(item, index) in simList" :key="index" :label="item.label" :value="item.value">
                                            <div class="option-contain">
                                                <span class="option-label">{{ item.label }}</span>
                                                <span class="extra" :title="item.info">{{
                          item.info
                        }}</span>
                                            </div>
                                        </el-option>
                                    </el-select>
                                </template> -->
              </el-table-column>
              <el-table-column label="IMEI号" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.device_imei" placeholder="请输入IMEI号"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane name="license" :label="$ct('label.carOperation')" class="form-group">
          <el-form-item :label="$ct('label.transportLicense') + ':'">
            <el-input class="elWidth" v-model="data.trans_card_no"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.licenseFrom') + ':'">
            <el-input class="elWidth" v-model="data.trans_card_org"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.operationStatus') + ':'">
            <DictionarySelect class="elWidth" v-model="data.vehicle_opera_status" code="vehicle_opera_status"> </DictionarySelect>
          </el-form-item>
          <el-form-item :label="$ct('label.validateStart') + ':'">
            <el-date-picker
              class="elWidth"
              v-model="data.trans_card_start_date"
              type="date"
              style="width: 100%"
              :placeholder="$ct('label.datePlz')"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$ct('label.validateEnd') + ':'">
            <el-date-picker
              class="elWidth"
              v-model="data.trans_card_end_date"
              type="date"
              style="width: 100%"
              :placeholder="$ct('label.datePlz')"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$ct('label.checkResult') + ':'">
            <DictionarySelect class="elWidth" v-model="data.vehicle_verify_status" code="vehicle_verify_status">
            </DictionarySelect>
          </el-form-item>
          <el-form-item :label="$ct('label.attachmentPic') + ':'" class="long">
            <ImageUploader
              class="elWidth"
              v-bind="uploadCondition"
              :uploadKey="`vehicle_opera:${data.plate_no}`"
              v-model="data.opera_permit_img"
              style="width: 200px; height: 120px"
            ></ImageUploader>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane name="driving" :label="$ct('label.drivingLicense')" class="form-group">
          <el-form-item :label="$ct('label.owner') + ':'">
            <el-input class="elWidth" v-model="data.owers_name"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.useNature') + ':'">
            <el-input class="elWidth" v-model="data.use_nature"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.annualExamination') + ':'">
            <el-date-picker
              class="elWidth"
              v-model="data.annual_valid_date"
              type="date"
              style="width: 100%"
              :placeholder="$ct('label.datePlz')"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$ct('label.attachmentPic') + ':'" class="long">
            <ImageUploaderGroup
              class="elWidth"
              :max="4"
              v-bind="uploadCondition"
              :uploadKey="`vehicle_license:${data.plate_no}:行驶证`"
              v-model="data.driving_license_img"
            >
            </ImageUploaderGroup>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane name="extra" :label="$ct('label.insurance')" class="form-group">
          <el-form-item :label="$ct('label.insuranceNO') + ':'">
            <el-input class="elWidth" v-model="data.insurance_no"></el-input>
          </el-form-item>
          <el-form-item prop="tireModelMain" label="保险费用(元):">
            <el-input class="elWidth" v-model="data.insuranceFee"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.insuranceValidDate') + ':'">
            <el-date-picker
              class="elWidth"
              v-model="data.insurance_valid_date"
              type="date"
              style="width: 100%"
              :placeholder="$ct('label.datePlz')"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item :label="$ct('label.attachmentPic') + ':'" class="long">
            <ImageUploaderGroup
              class="elWidth"
              :max="5"
              v-bind="uploadCondition"
              :uploadKey="`vehicle_license:${data.plate_no}:保单`"
              v-model="data.force_insurance_img"
            >
            </ImageUploaderGroup>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane name="others" label="其他" class="form-group">
          <el-divider>成本关联</el-divider>
          <el-form-item label="保养费用（元/年）:">
            <el-input class="elWidth" v-model="data.repairFee"></el-input>
          </el-form-item>
          <el-form-item label="里程折旧率(%/km):">
            <el-input class="elWidth" v-model="data.olderRateMile"></el-input>
          </el-form-item>
          <el-form-item label="时间折旧率(%/天):">
            <el-input class="elWidth" v-model="data.olderRateTime"></el-input>
          </el-form-item>
          <el-form-item prop="tireModelMain" label="轮胎损耗费用(N元/公里):">
            <el-input class="elWidth" v-model="data.tireFee"></el-input>
          </el-form-item>
          <el-form-item label="车价(元):" style="height: 56px">
            <el-input class="elWidth" v-model="data.carPriceOri"></el-input>
          </el-form-item>
          <el-form-item label="油卡:" style="height: 56px">
            <el-input class="elWidth" v-model="data.oilCard"></el-input>
          </el-form-item>
          <el-divider>其他</el-divider>
          <el-form-item prop="engineNumber" label="发动机编号:">
            <el-input class="elWidth" v-model="data.engineNumber"></el-input>
          </el-form-item>
          <el-form-item prop="engineModel" label="发动机型号:">
            <el-input class="elWidth" v-model="data.engineModel"></el-input>
          </el-form-item>
          <el-form-item prop="tireModelMain" label="发动机排量:">
            <el-input class="elWidth" v-model="data.displacement"></el-input>
          </el-form-item>
          <el-form-item prop="gearboxModel" label="变速箱型号:">
            <el-input class="elWidth" v-model="data.gearboxModel"></el-input>
          </el-form-item>
          <el-form-item prop="rearAxleSpeedRatioCurrent" label="现车后桥速比:">
            <el-input class="elWidth" v-model="data.rearAxleSpeedRatioCurrent"></el-input>
          </el-form-item>
          <el-form-item prop="rearAxleSpeedRatioOriginal" label="原车后桥速比:">
            <el-input class="elWidth" v-model="data.rearAxleSpeedRatioOriginal"></el-input>
          </el-form-item>
          <el-form-item prop="tireModelMain" label="主车轮胎型号:">
            <el-input class="elWidth" v-model="data.tireModelMain"></el-input>
          </el-form-item>

          <el-form-item prop="drive_line_id" :label="$ct('label.workRoad') + ':'">
            <el-select
              class="elWidth"
              v-model="data.drive_line_id"
              filterable
              popper-class="car-modal-popper"
              clearable
              @clear="lineList = []"
              :loading="lineRemoteLoading"
              :placeholder="$ct('label.roadName')"
            >
              <el-option v-for="(item, index) in lineList" :key="index" :label="item.label" :value="item.value">
                <div class="line-option">
                  <span class="option-label">{{ item.label }}</span>
                  <span class="extra" :title="item.start">{{ $ct("expression.0", [item.start]) }}</span>
                  <span class="extra" :title="item.end">{{ $ct("expression.1", [item.end]) }}</span>
                  <span class="extra" :title="item.mile">{{ $ct("expression.2", [item.mile]) }}</span>
                  <span class="extra" :title="item.remark">{{ item.remark }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="vehicle_product" :label="$ct('label.carManufacturer') + ':'">
            <el-input class="elWidth" v-model="data.vehicle_product"></el-input>
          </el-form-item>
          <el-form-item prop="areaGroup" :label="$ct('label.carRegisterPlace') + ':'">
            <SelectTreeInput
              class="elWidth"
              v-model="data.areaGroup"
              prefixCls="chinArea"
              :condition="areaCondition"
              :withParent="true"
              ref="regionInput"
              type="area"
              :placeholder="$ct('label.regionChoosePlz')"
              :title="$ct('label.regionChoosePlz')"
            >
            </SelectTreeInput>
          </el-form-item>
          <el-form-item prop="brand" :label="$ct('label.brand') + ':'">
            <DictionarySelect class="elWidth" v-model="data.brand" code="vehicle_brand" @change="modelChange"> </DictionarySelect>
          </el-form-item>
          <el-form-item prop="vehicle_model" label="车辆型号:">
            <!-- <DictionarySelect
             v-model="data.vehicle_model"
              code="vehicle_model">
            </DictionarySelect> -->
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.vehicle_model">
              <el-option v-for="(item, index) in vehicleModel" :key="index" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="trans_goods" :label="$ct('label.transportGoods') + ':'">
            <el-input class="elWidth" v-model="data.trans_goods"></el-input>
          </el-form-item>

          <el-form-item prop="black_white" :label="$ct('label.white&blackList') + ':'">
            <el-select class="elWidth" :placeholder="$ct('label.choosePlease')" v-model="data.black_white">
              <el-option :label="$ct('label.unset')" :value="null" v-if="!data.black_white"></el-option>
              <el-option :label="$ct('label.blackList')" :value="2"></el-option>
              <el-option :label="$ct('label.whiteList')" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$ct('label.carTypeCode') + ':'">
            <DictionarySelect class="elWidth" v-model="data.area_sli_type" code="area_sli_type"> </DictionarySelect>
          </el-form-item>
          <el-form-item :label="$ct('label.lightBoxCode') + ':'">
            <el-input class="elWidth" v-model="data.light_box_no"></el-input>
          </el-form-item>

          <el-form-item :label="$ct('label.installer') + ':'">
            <el-input class="elWidth" v-model="data.install_person"></el-input>
          </el-form-item>
          <el-form-item :label="$ct('label.installTime') + ':'">
            <el-date-picker
              class="elWidth"
              v-model="data.install_date"
              type="date"
              style="width: 100%"
              :placeholder="$ct('label.datePlz')"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label=" 业户名称:">
            <el-input class="elWidth" v-model="data.owner"></el-input>
          </el-form-item>
          <el-form-item label="报废到期时间:">
            <el-date-picker
              class="elWidth"
              v-model="data.scrap_date"
              type="date"
              style="width: 100%"
              placeholder="请选择报废到期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="其他附件：" class="long">
            <ImageUploaderGroup class="elWidth" :max="4" v-bind="uploadCondition" v-model="data.trailer_image_url">
            </ImageUploaderGroup>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="commit">{{ mode === "add" ? addModeOkText : modifyModeOkText }} </el-button>
      <el-button @click="show = false">{{ $ct("label.cancel") }}</el-button>
    </template>
  </PonyDialog>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-07-23 16:20:49
 * @LastEditors: yezy
 * @LastEditTime: 2019-07-23 16:20:49
 * @Description: 老旧页面重构
 */
import SelectTreeInput from "../../../../components/common/SelectTreeInput";
import DictionarySelect from "../../../../components/common/DictionarySelect";
import { mapActions } from "vuex";
import ImageUploader from "@/components/common/ImageUploader";
import ImageUploaderGroup from "@/components/common/ImageUploaderGroup";

export default {
  name: "carInfo",
  components: {
    ImageUploaderGroup,
    ImageUploader,
    DictionarySelect,
    SelectTreeInput,
  },
  data() {
    const defaultData = {
      id: null,
      plate_no: null,
      vin: null,
      alias: null,
      install_person: null,
      vehicle_product: null,
      area_id: null,
      driver_line_id: null,
      remark: null,
      trans_type: 20,
      industry_type: 0,
      plate_color: 1,
      type: null,
      sim_code_one: null,
      sim_code_two: null,
      sim_code_tree: null, // three ? mdzz
      code_one: null,
      code_two: null,
      code_tree: null,
      // terminal_type_id_one: null,
      // terminal_type_id_two: null,
      // terminal_type_id_tree: null,
      // terminal_type:[],
      dept_id: null,
      company_id: null,
      trail_type: [],
      trail_devices: [],
      trailer_image_url: [],
      concrete_type: 0,
      energyType: 0,
      energyCapacity: null,
      channel_count: 4,

      channelBroadcast: 1,
      channelTalkback: 1,
      trailer_no: "",
      channel_valid: "",
      channel_valid_temp: "",
      trail_tonnage: null,
      allow_bind_driver: [],
      trans_goods: null,
      opera_permit_img: null,
      mixing_station_ids: [], //搅拌站ids
      nuclear_carry: 1, // 车辆容积  2020 10 23 yzy

      trans_card_no: null,
      trans_card_org: null,
      trans_card_start_date: null,
      trans_card_end_date: null,
      vehicle_opera_status: null,
      vehicle_verify_status: null,

      deptGroup: null,
      areaGroup: null,
      black_white: null,

      light_box_no: null,
      owers_name: null,
      use_nature: null,
      annual_valid_date: null,
      driving_license_img: [],
      insurance_no: null,
      insurance_valid_date: null,
      force_insurance_img: [],

      area_sli_type: null,
      brand: "",
      owner: "",
      vehicle_model: "",
      weight: "",
      axle: "",
      scrap_date: null,
      fee_end: moment().add(1, "years").format("YYYY-MM-DD"),
      fee_begin: moment().format("YYYY-MM-DD"),

      inputType: 1, // 接入方式
      videoDevice: false, // 视频设备
      sourceData: false, // 视频设备
      dataPush: false, // 数据推送
      channelDsm: null, // DMS
      channelAdasFront: null, // ADAS
      sinotransUsage: "", //中外运类型,
      init_mile: "",
      install_date: moment().format("YYYY-MM-DD"),
      engineNumber: null,
      engineModel: null,

      gearboxModel: null,
      rearAxleSpeedRatioCurrent: null,
      rearAxleSpeedRatioOriginal: null,
      tireModelMain: null,
      length: "",
      width: "",
      height: "",
      insuranceFee: "",
      displacement: "",
      tireFee: "",
      repairFee: "",
      carPriceOri: "",
      olderRateTime: "",
      olderRateMile: "",
      channel_valid_name: [],
      oilCard: "",
      sourceType: 0,
    };
    return {
      // isShowTemp:false,
      activeTab: "basic",
      show: false,
      mode: "add",
      loading: false,
      addModeTitle: this.$ct("label.addCar"),
      addModeOkText: this.$ct("label.addConfirm"),
      modifyModeTitle: this.$ct("label.modifyCar"),
      modifyModeOkText: this.$ct("label.modifyConfirm"),
      simList: [],
      remoteLoading: false,
      driverList: [],
      driverRemoteLoading: false,
      lineList: [],
      lineRemoteLoading: false,
      data: JSON.parse(JSON.stringify(defaultData)),
      defaultData,
      jiaobanSelectValue: null,
      vehicleTypeSelectValue: null,
      vehicleTypeName: "",
      rules: {
        fenceId: [
          {
            required: true,
            message: this.$ct("label.fensePlz"),
            trigger: "change",
          },
        ],
        plate_no: [
          {
            required: true,
            message: this.$ct("messageInfo.0"),
            trigger: "blur",
          },
        ],
        plate_color: [
          {
            required: true,
            message: this.$ct("messageInfo.1"),
            trigger: "change",
          },
        ],
        deptGroup: [
          {
            required: true,
            message: this.$ct("messageInfo.2"),
            trigger: "change",
          },
        ],
        industry_type: [
          {
            required: true,
            message: this.$ct("messageInfo.3"),
            trigger: "change",
          },
        ],
        type: [
          {
            required: true,
            message: this.$ct("messageInfo.4"),
            trigger: "change",
          },
        ],
        trans_type: [
          {
            required: true,
            message: this.$ct("messageInfo.5"),
            trigger: "change",
          },
        ],
        energyType: [
          {
            required: true,
            message: "请选择能源类型",
            trigger: "change",
          },
        ],
      },
      terminalTable: [
        {
          // terminalType:[],type:null,terminalTypeId: 0, terminalType: 'terminal_type_id_one', terminalCode: 'code_one', simCode:'sim_code_one'
        },
      ],
      transType: [],
      vehicleType: [],
      plateColor: [],
      industryType: [],
      terminalType: [],
      terminalDesc: [],
      carKindType: [],
      carTrailDevice: [],
      carSourceType: [],
      concreteKindType: [],
      cleanKindType:[],
      energyTypeList: [
        {
          label: "燃油车",
          value: 0,
        },
        {
          label: "纯电动",
          value: 1,
        },
        {
          label: "气体机",
          value: 3,
        },
      ],
      channel_valid: [],
      channel_valid_temp: [],
      vehicleModel: [],
      // 接入方式
      inputTypeList: [
        {
          value: 1,
          label: "设备直连",
        },
        {
          value: 2,
          label: "808转发",
        },
        {
          value: 3,
          label: "809接入",
        },
        {
          value: 4,
          label: "中交兴路",
        },
        {
          value: 5,
          label: "CSPC数据转发",
        },
      ],
      typelist: [20, 21, 22, 23],
      sinotransUsageStatus: false,
    };
  },
  computed: {
    isCLZCL: function () {
      return "clzcl" === this.$store.state.main.company;
    },
    uploadCondition: function () {
      return {
        disabled: !this.data.plate_no,
        whyDisable: this.$ct("messageInfo.6"),
      };
    },
    isShowTemp() {
      return this.terminalTable.some((item) => {
        if (item.business) return item.business.some((val) => val == "温度管控");
      });
    },
  },
  watch: {
    "data.deptGroup": function (val) {
      if (!val) return;
      Object.assign(this.data, {
        dept_id: val.value,
        company_id: val.parentNode.value,
      });
      this.getDriverList();
      this.simRemoteMethod();
      this.lineRemoteMethod();
    },
    "data.areaGroup": function (val) {
      Object.assign(this.data, {
        area_id: val.value,
      });
    },
    "data.industry_type": function (val) {
      if (val == 14) {
        this.vehicleTypeSelectValue = {
          label: "大型普通货车",
          value: "21",
        };
        Object.assign(this.data, {
          type: "21",
        });
      }
    },
    terminalTable: {
      handler(val) {
        val.forEach((item) => {
          if (item.terminal_type_id) {
            this.terminalType.some((child) => {
              if (child.value == item.terminal_type_id) {
                item.business = child.business;
                return true;
              }
            });
          }
        });
      },
      // immediate: true,
      deep: true,
    },
    jiaobanSelectValue: function (newVal) {
      if (!newVal) return;
      Object.assign(this.data, {
        mixing_station_ids: [newVal.value],
      });
    },
    vehicleTypeSelectValue: function (newVal) {
      if (!newVal) return;
      Object.assign(this.data, {
        type: newVal.value,
      });
      this.sinotransUsageStatus = this.typelist.includes(Number(newVal.value));
      if (!this.sinotransUsageStatus) {
        this.data.sinotransUsage = "";
      }
    },
    channel_valid: {
      handler(val) {
        if (val.length > 0) {
          let arr = [];
          val.forEach((number) => {
            let value = number + 1;
            let index = this.data.channel_valid_name.findIndex((it) => it.no == value);
            if (index == -1) {
              arr.push({
                no: value,
                name: `通道${value}`,
              });
            } else {
              arr.push(this.data.channel_valid_name[index]);
            }
          });
          this.data.channel_valid_name = arr.sort((a, b) => a.no - b.no);
        } else {
          this.data.channel_valid_name = [];
        }
      },
      deep: true,
    },
  },
  methods: {
    async modelChange() {
      let result = await this.$api.getCodeTypeList({
        code: "vehicle_model",
        description: this.data.brand,
      });
      this.vehicleModel = result.map((item) => {
        const value = parseInt(item.value);
        return {
          label: item.name,
          value: isNaN(value) ? item.value : value,
        };
      });
    },
    // terminalTypeChange(val){
    //     console.log(val);
    //     // val.forEach(item=>{
    //     //     this.terminalType.some(child=>{
    //     //         if(child.value == val){
    //     //             item.business = child.business
    //     //             return true
    //     //         }
    //     //     })
    //     // })
    //     console.log(this.terminalTable);
    // },
    clearRow(index, row) {
      this.terminalTable.splice(index, 1);
      // Object.assign(this.data, {
      //     [row.terminalType]: null,
      //     [row.terminalCode]: null,
      //     [row.simCode]: null,
      // })
    },
    async simRemoteMethod(query) {
      this.remoteLoading = true;
      let result = await this.$api.getSysSimInfoAll({
        count: 30,
        dept_id_list: [this.data.dept_id],
        iccid: "",
        manufactor: "",
        operator: "",
        page: 1,
        sign: "",
        sim_no: query,
        sorting: -1,
      });
      this.remoteLoading = false;
      if (result.Rs === 1) {
        this.simList = result.sys_sim_info_list.map((item) => {
          return {
            label: item.code,
            value: item.code,
            info: item.plate_no,
          };
        });
      } else {
        this.simList = [];
      }
    },
    async lineRemoteMethod() {
      this.lineLoading = true;
      let result = await this.$api.selectLineInfe({
        company_id: this.data.company_id,
        dept_id: null,
        name: null,
        page: 1,
        count: 1000,
        sorting: -1,
      });
      this.lineLoading = false;
      if (result.RS === 1) {
        this.lineList = result.SysDriveLineList.map((item) => {
          return {
            label: item.name,
            value: item.id,
            start: item.start_location,
            end: item.end_location,
            mile: item.mile,
            remark: item.remark,
          };
        });
      } else {
        this.lineList = [];
      }
    },
    ...mapActions("dictionary", ["getFormatListByCode", "getValueLabelMapByCode"]),
    async initDictionary() {
      let [
        vehicleType,
        plateColor,
        transType,
        industryType,
        carKindType,
        carTrailDevice,
        concreteKindType,
        cleanKindType,
        protocolType,
        terminalType,
        vehicleModel,
        terminalDesc,
        carSourceType,
      ] = await Promise.all([
        this.getFormatListByCode("vehicle_type"),
        this.getFormatListByCode("plate_color"),
        this.getFormatListByCode("trans_type"),
        this.initUserIndustryList(),
        this.getFormatListByCode("trail_type"),
        this.getFormatListByCode("trail_device"),
        this.getFormatListByCode("erp_concrete_vehicle_type"),
        this.getFormatListByCode("clean_vehicle_type"),
        this.getValueLabelMapByCode("protocol_type"),
        // Promise.all([
        //     this.$api.getTerminalTypeList({type: 1}),
        //     this.$api.getTerminalTypeList({type: 2}),
        //     this.$api.getTerminalTypeList({type: 3}),
        // ]),
        this.$api.getTerminalType(),
        this.getFormatListByCode("vehicle_model"),
        this.getFormatListByCode("terminal_model_desc"),
        this.getFormatListByCode("vehicle_source_type"),
      ]);
      // console.log(terminalType,'terminalType');
      terminalType = terminalType.map((ter) => {
        return {
          label: ter.name,
          value: ter.id,
          producer: ter.product_name,
          protocol: protocolType[ter.protocol_type],
          business: ter.terminal_business,
        };
      });
      Object.assign(this, {
        vehicleType,
        plateColor,
        transType,
        industryType,
        carKindType,
        carTrailDevice,
        concreteKindType,
        cleanKindType,
        terminalType,
        vehicleModel,
        terminalDesc,
        carSourceType,
      });
    },
    async initUserIndustryList() {
      let res = await Promise.all([
        this.$api.getUserIndustryBind({ user_id: "-1" }),
        this.$api.getUserIndustryBind({ user_id: "0" }),
      ]);
      this.data.industry_type = res[0].data[0].value;
      return res[1].data.map((item) => {
        return {
          label: item.name,
          value: item.value,
          disabled: !res[0].data.some((ci) => {
            return ci.value === item.value;
          }),
        };
      });
    },
    // 判断选中节点层级
    areaCondition(treeNode) {
      if (treeNode.type < 3) {
        this.$message({
          type: "warning",
          showClose: true,
          message: this.$ct("messageInfo.7"),
        });
        return false;
      }
      return true;
    },
    deptCondition(treeNode) {
      if (treeNode.type !== 3) {
        this.$message({
          type: "info",
          message: this.$ct("messageInfo.8"),
          showClose: true,
        });
        return false;
      } else {
        return true;
      }
    },
    treeFilterData(treeNode) {
      if (treeNode.type < 2) {
        this.$warning("请选择搅拌场！");
        return false;
      }
      return true;
    },
    treeDeptCondition(treeNode) {
      return true;
    },
    async showModal(rowData) {
      let vehicleId = rowData ? rowData.id : null;

      this.data = JSON.parse(JSON.stringify(this.defaultData));
      if (vehicleId) {
        this.mode = "modify";
        this.vehicleTypeName = rowData.vehicle_type;
        this.terminalTable = JSON.parse(JSON.stringify(rowData.terminal_bind_list));
        this.initData(vehicleId);
        // this.jiaobanSelectValue = JSON.parse(this.data.mixing_station_ids).map(item => {
        //     return {
        //         label: '',
        //         value: item
        //     }
        // })
      } else {
        this.mode = "add";
        this.channel_valid = [0, 1, 2, 3];
        this.channel_valid_temp = [];
        // console.log('add');
        this.terminalTable = [{}];
        this.vehicleTypeSelectValue = null;
        this.jiaobanSelectValue = null;
      }
      this.show = true;
    },
    async initData(vehicleId) {
      let request = [this.$api.selectVehicleAllById({ vehicleId }), this.$api.getVehicleAllowedDriver({ vehicle_id: vehicleId })];
      let res = await Promise.all(request);
      if (res[0].RS == 1 && res[1].status === 200) {
        //特殊的Object.assign
        this.$utils.assign(this.data, res[0].VehicleListAll);

        // this.channel_valid_old=this.data.channel_valid  //保留截取前的channel_valid
        // this.data.channel_valid=this.data.channel_valid.substring(0,this.data.channel_count)
        // this.data.channel_valid.split("")
        this.channel_valid = [];
        this.channel_valid_temp = [];

        if (this.data.channel_valid) {
          this.data.channel_valid.split("").forEach((item, index) => {
            if (item == "1") {
              this.channel_valid.push(index);
            }
          });
        }
        if (this.data.channel_valid_temp) {
          this.data.channel_valid_temp.split("").forEach((item, index) => {
            if (item == "1") {
              this.channel_valid_temp.push(index);
            }
          });
        }
        this.data.plate_color = +this.data.plate_color;
        this.data.deptGroup = await this.$refs["departmentInput"].fillOtherProperty(this.data.dept_id);

        this.data.areaGroup = await this.$refs["regionInput"].fillOtherProperty(this.data.area_id);
        this.vehicleTypeSelectValue = {
          label: this.vehicleTypeName,
          value: this.data.type,
        };
        if (this.data.industry_type == 8) {
          // this.jiaobanSelectValue = {
          //     label:'',
          //     value:this.data.mixing_station_ids[0]
          // }
          this.jiaobanSelectValue = await this.$refs["jiaoban"].fillOtherProperty(this.data.mixing_station_ids[0]);
        }

        // 多选树节点存储
        // let selectArr = []
        //     for(var i=0;i<this.data.mixing_station_ids.length;i++){
        //         if(!this.data.mixing_station_ids.length)return
        //         selectArr = this.data.mixing_station_ids.map(item => {
        //                 return { value: item, label: '' ,}
        //         })
        //     }
        // this.jiaobanSelectValue = selectArr
        this.data.allow_bind_driver = res[1].data;

        this.getDriverList();
        this.simRemoteMethod();
        this.lineRemoteMethod();
      }
    },
    async getDriverList() {
      if (!this.data.deptGroup) {
        return;
      }
      if (this.data.deptGroup) {
        this.driverRemoteLoading = true;
      }
      let res = await this.$api.getSimpleDriverInfo({
        company_id: this.data.company_id,
        dept_id: this.data.dept_id,
      });
      this.driverRemoteLoading = false;
      if (res.status === 200) {
        this.driverList = res.data.map((item) => {
          return {
            label: item.driver_name,
            value: item.driver_id,
          };
        });
      } else {
        this.$message({
          type: "error",
          showClose: true,
          message: this.$ct("messageInfo.9"),
        });
      }
    },

    commit() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) {
          return this.$message.error("必填信息不能为空！");
        }
        let flag = this.terminalTable.some((item) => {
          if (item.terminal_code && item.device_id) {
            item.terminal_code = item.terminal_code.replace(/^0+/, "");
            if (item.terminal_type_id && item.terminal_code.replace(/\s+/g, "") && item.device_id.replace(/\s+/g, ""))
              return false;
            return true;
          } else {
            return true;
          }
        });
        if (flag) return this.$message.error("请检查终端类型,终端号,终端ID是否均已填写！");
        let channel_valid_list = [];
        for (let i = 0; i < 16; i++) {
          if (this.channel_valid.indexOf(i) <= -1) {
            channel_valid_list[i] = "0";
          } else {
            channel_valid_list[i] = "1";
          }
        }
        let channel_temp_list = [];
        for (let i = 0; i < 8; i++) {
          if (this.channel_valid_temp.indexOf(i) <= -1) {
            channel_temp_list[i] = "0";
          } else {
            channel_temp_list[i] = "1";
          }
        }
        this.data.channel_valid = channel_valid_list.join("");
        this.data.channel_valid_temp = channel_temp_list.join("");
        if (this.data.plate_no) {
          this.data.plate_no = this.data.plate_no.replace(/^\s*|\s*$/g, "");
        }
        let result = await (this.mode === "add" ? this.addCar() : this.modifyCar());
        if (result) {
          this.$emit("refresh");
          this.show = false;
        }
      });
    },
    paramAdapter(params) {
      let temp = JSON.parse(JSON.stringify(params));
      if (this.isCLZCL) {
        temp.industry_type = 2;
      }
      temp.terminal_id_one_code = temp.code_one;
      temp.terminal_id_two_code = temp.code_two;
      temp.terminal_id_tree_code = temp.code_tree;
      temp.sim_id_one_code = temp.sim_code_one;
      temp.sim_id_two_code = temp.sim_code_two;
      temp.sim_id_tree_code = temp.sim_code_tree;
      // 几个会少一天的日期转换[2020年6月5日 14:03:41 | yuqf]
      if (temp.trans_card_start_date != null && temp.trans_card_start_date != "") {
        temp.trans_card_start_date = this.DateFormat(temp.trans_card_start_date);
      }
      if (temp.trans_card_end_date != null && temp.trans_card_end_date != "") {
        temp.trans_card_end_date = this.DateFormat(temp.trans_card_end_date);
      }
      if (temp.scrap_date != null && temp.scrap_date != "") {
        temp.scrap_date = this.DateFormat(temp.scrap_date);
      }
      return temp;
    },
    async addCar() {
      try {
        let res = await this.$api.addSysVehicleInfo(
          Object.assign(this.paramAdapter(this.data), {
            terminal_bind_list: this.terminalTable,
          })
        );
        if (res.RS === 1) {
          this.$success(this.$ct("label.addSuccess"));
          return true;
        } else {
          throw new Error(this.$ct("messageInfo.10") + res.Reason);
          return false;
        }
      } catch (e) {
        this.$message.error(e.message || "操作失败");
        return false;
      }
    },
    async modifyCar() {
      // console.log(Object.assign(this.paramAdapter(this.data), { terminal_bind_list: this.terminalTable }), 'params');
      if (!this.hasPermission("car:update")) return this.$error("修改失败,无权限！");
      try {
        let res = await this.$api.modifySysVehicleInfo(
          Object.assign(this.paramAdapter(this.data), {
            terminal_bind_list: this.terminalTable,
          })
        );
        if (res.RS === 1) {
          this.$success(this.$ct("label.modifySuccess"));
          //跟新vuex里缓存的数据
          this.$store.commit("mediaPlatform/setChannelNo", {
            id: this.data.id,
            value: { data: this.data.channel_count },
          });
          return true;
        } else {
          throw new Error(this.$ct("messageInfo.11") + res.Reason);
          return false;
        }
      } catch (e) {
        this.$error(e.message);
        return false;
      }
    },
    addTerminal() {
      this.terminalTable.push({});
    },
  },
  created() {
    this.initDictionary();
  },
};
</script>

<style scoped lang="scss">
.car-modal-popper {
  .option-contain {
    color: #6e6e6e;
    width: 220px;
    display: flex;
    justify-content: space-between;

    > .option-label {
      color: #6e6e6e;
      flex-shrink: 0;
      margin-right: 5px;
    }

    > .extra {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .line-option {
    color: #6e6e6e;
    width: 600px;
    display: flex;
    justify-content: space-between;

    > .option-label {
      width: 20%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #6e6e6e;
      flex-shrink: 0;
      margin-right: 5px;
    }

    > .extra {
      width: 20%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.car-modal {
  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    margin-top: 4px;
    border: 1px solid #2880e2;
    color: #2880e2;
    border-radius: 50%;
  }

  /deep/ {
    .el-table {
      .cell {
        padding: 0;
      }

      td {
        padding: 0;
      }

      .el-input__inner {
        border: none !important;
        border-radius: 0;
      }
    }

    .elWidth {
      width: 150px !important;
    }
  }

  /deep/ .vehicle_type_level0 {
    display: none !important;
  }

  /deep/ .vehicle_type_level1 {
    display: none !important;
  }

  .form-group {
    display: flex;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 0;
    padding: 10px;
    overflow: auto;

    /deep/ .el-form-item {
      &.long {
        width: 100%;
      }
    }

    /deep/ .vadio {
      width: 100%;
    }

    /deep/ .half {
      width: 50%;
    }
  }
}

/deep/ .el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin-top: 1px;
  margin-bottom: 14px;
}
</style>
