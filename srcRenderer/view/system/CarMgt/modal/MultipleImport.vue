<template>
  <PonyDialog v-model="show" :title="$ct('label.multipleImport')" class="multiple-import" :loading="loading"
    :hasFooter="false" :width="1000" contentStyle="padding:0;height:550px">
    <Layout :content-loading="table.loading">
      <template slot="query">
        <div class="query-item">
          <el-upload style="float:left;" action="/ponysafety2/a/vehicle/readExcelVehixleToTable"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            :show-file-list="false" :before-upload="() => { table.loading = true }" :on-success="handleSuccess"
            :on-error="handleError">
            <el-button type="primary" size="mini">
              <i class="pony-iconv2 pony-sousuo"></i>{{ $ct('label.chooseFilePlz') }}
            </el-button>
          </el-upload>
          <el-button type="primary" size="mini" style="margin-left: 10px" @click="confirmImport" :loading="table.loading">
            <i class="pony-iconv2 pony-pdf"></i>{{ $ct('label.confirm') }}
          </el-button>
        </div>
        <div class="break-item">
          <span>{{ $ct('expression.0', [table.data.length]) }}</span>
        </div>
        <div class="query-item">
          <el-button type="primary" size="mini" @click="exportExample" :loading="exportExampleLoading">
            <i class="pony-iconv2 pony-excel"></i>{{ $ct('label.exportBaseTemplate') }}
          </el-button>
          <el-button type="primary" size="mini" @click="table.data = []">
            <i class="pony-iconv2 pony-shanchu"></i>{{ $ct('label.clear') }}
          </el-button>
          <el-button type="primary" size="mini" @click="exportTableList" :loading="exportLoading">
            <i class="pony-iconv2 pony-excel"></i>{{ $ct('label.export') }}
          </el-button>
        </div>
      </template>
      <template slot="content">
        <el-table :empty-text="table.data.length ? $ct('label.noData') : ''" :data="table.data" height="100%" border
          ref="carTable" highlight-current-row style="width: 100%">
          <el-table-column type="index" :label="$ct('label.index')" width="50"></el-table-column>
          <el-table-column :label="$ct('label.operate')">
            <template slot-scope="scope">
              <el-button type="text" size="mini" icon="pony-iconv2 pony-shanchu"
                @click="removeRow(scope.$index)"></el-button>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="异常原因" min-width="110" width="" show-overflow-tooltip></el-table-column>
<!--          <el-table-column :label="$ct('label.group')" align="left">
            <template slot-scope="scope">
              <span>{{ scope.row.company_name + '>>' + scope.row.dept_name }}</span>
            </template>
          </el-table-column>-->
          <el-table-column prop="company_name" label="归属企业"></el-table-column>
          <el-table-column prop="dept_name" label="归属部门"></el-table-column>
          <el-table-column prop="plate_no" :label="$ct('label.plateNoAlias')"></el-table-column>
          <el-table-column prop="plate_color" label="车牌颜色"></el-table-column>
          <el-table-column prop="vin" :label="$ct('label.vin')" min-width="100" width=""></el-table-column>
<!--          <el-table-column prop="inputTypeView" label="接入方式" min-width="100" width=""></el-table-column>-->
          <el-table-column prop="inputType" label="接入方式" min-width="110" width="">
            <template slot-scope="scope">
              <span v-if="scope.row.inputType == 1">设备直连</span>
              <span v-else-if="scope.row.inputType == 2">808转发</span>
              <span v-else-if="scope.row.inputType == 3">809接入</span>
              <span v-else-if="scope.row.inputType == 4">中交兴路</span>
              <span v-else-if="scope.row.inputType == 5">G7</span>
            </template>
          </el-table-column>
          <el-table-column prop="energyTypeView" label="能源类型" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="terminal_type_name_one" :label="$ct('label.terminalTypes')" min-width="120" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="code_one" :label="$ct('label.mainTerminalNo')" min-width="110" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sim_code_one" :label="$ct('label.mainSimCode')" min-width="120" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceId" label="终端ID" min-width="110" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceImei" label="终端IMEI" min-width="110" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="videoDevice" label="是否视频设备" min-width="110" width="" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.videoDevice === true ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="channelCount" :label="$ct('label.channelCount')" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="channelTalkback" label="对讲通道" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="channelBroadcast" label="广播通道" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="transIndustryView" :label="$ct('label.transIndustry')" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="vehicleIndustryView" :label="$ct('label.vehicleIndustry')" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="vehicleTypeView" :label="$ct('label.vehicleType')" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="repairFee" label="保养费用" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="olderRateMile" label="里程折旧率(%/km)" min-width="120" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="olderRateTime" label="时间折旧率(%/天)" min-width="120" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tireFee" label="轮胎损耗费(N元/公里)" min-width="140" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="carPriceOri" label="车价(元)" min-width="100" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="oilCard" label="油卡" min-width="100" width="" show-overflow-tooltip></el-table-column>
<!--          <el-table-column prop="status" :label="$ct('label.status')">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0">{{ $ct('label.waitForImport') }}</span>
              <span v-else-if="scope.row.status == 1">{{ $ct('label.importSuccess') }}</span>
              <span v-else-if="scope.row.status == -1">{{ $ct('label.importFailure') }}</span>
            </template>
          </el-table-column>-->
          <el-table-column prop="feeBegin" label="服务开始时间" min-width="110" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="feeEnd" label="服务到期时间" min-width="110" width="" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="110" width="" show-overflow-tooltip></el-table-column>
          

        </el-table>
      </template>
    </Layout>
  </PonyDialog>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/8/7 15:09
 * @LastEditors: xieyj
 * @LastEditTime: 2021/9/16 15:09
 * @Description:
 */
const rules = [
  {
    value: 'plate_no',
    name: '车牌号'
  },
  {
    value: 'company_name',
    name: '车辆归属'
  },
  {
    value: 'dept_name',
    name: '车辆归属'
  },
  {
    value: 'vehicleIndustryView',
    name: '车辆行业'
  },
  {
    value: 'transIndustryView',
    name: '运输行业'
  },
  {
    value: 'vehicleTypeView',
    name: '车型'
  }
]
export default {
  name: "multipleImport",
  data() {
    return {
      loading: false,
      show: false,
      table: {
        loading: false,
        data: [],
      },
      exportLoading: false,
      exportExampleLoading: false
    }
  },
  methods: {
    showModal() {
      this.show = true;
    },
    handleSuccess(res) {
      this.table.loading = false;
      this.$message({ type: 'success', showClose: true, message: this.$ct('expression.1', [res.length]) });
      this.table.data = res;
      this.$nextTick(() => {
        this.$refs['carTable'].doLayout()
      })
    },
    handleError(err) {
      this.$message({ type: 'error', showClose: true, message: this.$ct('label.parseFailure') })
      this.table.loading = false;
    },
    removeRow(index) {
      this.table.data.splice(index, 1);
    },
    async confirmImport() {
      if (this.table.data.length === 0) {
        return this.$message({ type: 'info', showClose: true, message: this.$ct('messageInfo.0') })
      }
      for (let i = 0, num = this.table.data.length; i < num; i++) {
        for (let j = 0, num1 = rules.length; j < num1; j++) {
          if (this.table.data[i][rules[j].value] == '' || this.table.data[i][rules[j].value] == '[未匹配到]') {
            return this.$warning('请完善' + rules[j].name + '!')
          }
        }
      }
      this.table.loading = true;
      let res = await this.$api.excelTableTosql(this.table.data);
      this.table.loading = false;
      if (res) {
        this.table.data = res.VehicleToTables;
        this.$message({ type: 'success', showClose: true, message: res.Reason });
        this.$emit('refresh')
        this.show = false
      } else {
        this.$message({ type: 'success', showClose: true, message: this.$ct('label.systemError') });
      }
    },
    exportExample() {
      this.exportExampleLoading = true
      this.$utils.excelExport("/ponysafety2/a/vehicle/excelvehicledemofile", JSON.stringify([]),
        this.$ct('messageInfo.1') + ".xlsx");
      this.exportExampleLoading = false
    },
    async exportTableList() {
      if (this.table.data.length === 0) {
        this.$message({ type: 'info', showClose: true, message: this.$ct('messageInfo.2') })
        return
      }
      this.exportLoading = true;
      await this.$utils.excelExport("/ponysafety2/a/vehicle/excelvehicletable", JSON.stringify(this.table.data),
        this.$ct('label.carData') + ".xlsx");
      this.exportLoading = false;
    }
  }
}
</script>

<style scoped lang="scss">
.multiple-import {
  .pony-iconv2 {
    font-size: 12px;
  }
}
</style>
