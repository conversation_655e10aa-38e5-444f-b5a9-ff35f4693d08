<template>
	<div class="carFueCalendar">
		<div class="top">
			<div class="choose">
				<div class="left">
                    <span class="icon">
                        <i class="pony-iconv2 pony-qiye"></i>
                    </span>
					<span>{{ companyName }}>>{{ deptName }}</span>
					<span class="icon">
                        <i class="pony-iconv2 pony-chepai"></i>
                    </span>
					<span>{{ plateNo }}</span>
				</div>
				<div class="center">
					<div class="btn" @click="changeMonth(0)"><i class="pony-iconv2 pony-jiantouzuo"></i></div>
					<div>
						<span>{{ date.year }}</span>&nbsp;&nbsp;&nbsp;<span>{{ monthList[Number(date.month)] }}</span>
					</div>
					<div class="btn" @click="changeMonth(1)"><i class="pony-iconv2 pony-jiantouyou"></i></div>
				</div>
				
			</div>
			<div class="week">
				<div class="week-list" v-for="item in weekList" :key="item.value">{{ item.label }}</div>
			</div>
		</div>
		<div class="bottom">
			<CarFuelCalendarItem class="item"
			                     :class="{'today': today == item.fullText, 'cur': !item.cur}"
			                     v-for="(item,index) in dateArray" :key="index"
			                     :dayInfo="dateArray[index]"></CarFuelCalendarItem>
		</div>
	</div>
</template>

<script>
import CarFuelCalendarItem from "./components/CarFuelCalendarItem";
import moment from "moment";

export default {
	name: "carFuelCalendar",
	components: {
		CarFuelCalendarItem
	},
	data() {
		return {
			date: {
				year: '',
				month: '',
				day: ''
			},
			today: '',
			dateArray: [],
			weekList: [
				{
					value: '1',
					label: '星期一'
				},
				{
					value: '2',
					label: '星期二'
				},
				{
					value: '3',
					label: '星期三'
				},
				{
					value: '4',
					label: '星期四'
				},
				{
					value: '5',
					label: '星期五'
				},
				{
					value: '6',
					label: '星期六'
				},
				{
					value: '7',
					label: '星期日'
				}
			],
			monthList: {
				1: '一月',
				2: '二月',
				3: '三月',
				4: '四月',
				5: '五月',
				6: '六月',
				7: '七月',
				8: '八月',
				9: '九月',
				10: '十月',
				11: '十一月',
				12: '十二月'
			},
			plateNo: '',// 车牌号
			companyName: '', //公司
			deptName: '', // 车队
			vehicleId: ''
		}
	},
	mounted() {

	},
	watch: {
		date: {
			handler() {
				this.getRenderData()
				this.getCarDayInfo()
			},
			deep: true
		},
		'$route': {
			handler(val,oldVal) {
				if (val.name === 'carFuelCalendar') {
					this.vehicleId = this.$route.query.vehicleId
					let date = moment(parseInt(this.$route.query.end)).format('YYYY-MM-DD')
					this.getNowDate(date)
				}
			},
			deep: true,
			immediate: true
		}
	},
	methods: {
		// 获取当前的日期
		getNowDate(date) {
			this.date.year = date.split('-')[0]
			this.date.month = date.split('-')[1]
			this.date.day = date.split('-')[2]
			const curDate = new Date() // 当前日期对象
			let nowYear = curDate.getFullYear()
			let owMonth = curDate.getMonth() + 1
			let nowDay = curDate.getDate() < 10 ? '0' + curDate.getDate() : curDate.getDate()
			this.today = `${nowYear}-${owMonth}-${nowDay}`
		},

		// 生成日历
		getRenderData() {
			this.dateArray = []
			const [nums, first] = this.getDayNum()
			// 下个月第一天为1号
			let nextMonthDay = 1
			// 上个月的天数
			let prevMothDay = this.getLastMonthDays()
			// 本月第一天为1号
			let n = 1
			// 日历共6行
			for (let i = 0; i < 6; i++) {
				const temp = []
				// 周一到周日
				for (let j = 1; j <= 7; j++) {
					// 日历第一行，需要判断是否为上个月的日期
					if (i === 0) {
						// 如果星期数小于本月第一天的星期数，则为上个月
						if (j < first) {
							temp.unshift({
								isSelect: false,
								cur: false,
								text: prevMothDay,
								fullText: `${this.getLastDateString(Number(this.date.year), Number(this.date.month), Number(prevMothDay))}`
							})
							prevMothDay = prevMothDay - 1
						} else { // 本月
							temp.push({
								isSelect: false,
								cur: true,
								text: n < 10 ? '0' + n : n,
								fullText: `${this.date.year}-${this.date.month}-${n < 10 ? '0' + n : n}`
							})
							n = n + 1
						}
					} else {
						const t = n++
						//  不是第一行 需要判断是否为下一月,如果n>本月天数则为下一月
						if (t > nums) {
							temp.push({
								isSelect: false,
								cur: false,
								text: nextMonthDay < 10 ? '0' + nextMonthDay : nextMonthDay,
								fullText: `${this.getNextDateString(Number(this.date.year), Number(this.date.month), nextMonthDay < 10 ? '0' + nextMonthDay : nextMonthDay)}`
							})
							nextMonthDay = nextMonthDay + 1
						} else {
							temp.push({
								isSelect: false,
								cur: true,
								text: t < 10 ? '0' + t : t,
								fullText: `${this.date.year}-${this.date.month}-${t < 10 ? '0' + t : t}`
							})
						}
					}
				}
				this.dateArray = this.dateArray.concat(temp)
			}
		},

		// 获取当前月份对应的天数和1号对应的周几
		getDayNum() {
			const y = this.date.year
			const m = this.date.month
			return [new Date(y, m, 0).getDate(), new Date(y, m - 1, 1).getDay()]
		},

		// 获取上个月有多少天
		getLastMonthDays() {
			const y = this.date.year
			const m = this.date.month
			// 获取上个月1号的时间
			const lastDate = new Date(y, m - 1, 1)
			return new Date(lastDate.getFullYear(), lastDate.getMonth(), 0).getDate()
		},

		// 获取当前月有多少天
		getNowMonthDays() {
			const y = this.date.year
			const m = this.date.month
			const lastDate = new Date(y, m, 1)
			return new Date(lastDate.getFullYear(), lastDate.getMonth(), 0).getDate()
		},

		// 上一天的日期 判断是否为上一年
		getLastDateString(year, month, date) {
			if (month === 1) {
				year = year - 1
				month = 12
			} else {
				month = month - 1 < 0 ? 12 : month - 1
			}
			return `${year}-${month}-${date}`
		},

		// 下一天 判断是否为上一年
		getNextDateString(year, month, date) {
			if (month === 12) {
				year = year + 1
				month = 1
			} else {
				month = month + 1 > 12 ? 1 : month + 1
			}
			return `${year}-${month}-${date}`
		},

		// 改变月份 type 0 上一月 1 下一月
		changeMonth(type) {
			let newDate = ''
			if (type === 0) {
				newDate = this.getLastDateString(Number(this.date.year), Number(this.date.month), Number(this.date.day))
			} else {
				newDate = this.getNextDateString(Number(this.date.year), Number(this.date.month), Number(this.date.day))
			}
			this.date.year = Number(newDate.split('-')[0])
			this.date.month = Number(newDate.split('-')[1])
			this.date.day = Number(newDate.split('-')[2]) < 10 ? '0' + Number(newDate.split('-')[2]) : Number(newDate.split('-')[2])
		},

		async getCarDayInfo() {
			let nowMonth = this.getNowMonthDays()
			const res = await this.$api.fuelConsumptionCalendar({
				vehicleId: this.vehicleId,
				start: `${this.date.year}-${this.date.month}-01`,
				end: `${this.date.year}-${this.date.month}-${nowMonth}`
			})
			if (res.status !== 200) return
			this.deptName = res.data.deptName
			this.plateNo = res.data.plateNo
			this.companyName = res.data.companyName
			let dayList = res.data.dayList
			let arr = JSON.parse(JSON.stringify(this.dateArray))
			arr.forEach((item) => {
				let year = item.fullText.split('-')[0]
				let month = this.monthList[Number(item.fullText.split('-')[1])]
				let day = item.fullText.split('-')[2]
				dayList.forEach((list) => {
					if (list.mile !== '0.0' || list.time !== '0.0' || list.fuelAvg !== '-' || list.fuelAvgRun !== '-' || list.fuel !== '0.0') {
						if (list.year == year && list.month == month && list.day == day) {
							item.fuel = list.fuel
							item.fuelAvg = list.fuelAvg
							item.fuelAvgRun = list.fuelAvgRun
							item.mile = list.mile
							item.time = list.time
						}
					}
				})
			})
			this.dateArray = arr
		}
	}
}
</script>

<style scoped lang="scss">
.carFueCalendar {
	width: 100%;
	height: 100%;
	padding: 14px;
	background-color: var(--background-color-calendar);

	.top {
		width: 1892px;
		height: 135px;
		background-color: var(--background-color-calendar-content);
		border-radius: 20px;
		padding: 0 24px;
		border: 1px solid var(--color-calendar-border-transparent);


		.choose {
			position: relative;
			height: 75px;
			display: flex;
			align-items: center;
			border-bottom: 1px solid var(--color-calendar-border);

			.left {
				position: absolute;
				left: 0;
				font-size: 16px;
				font-weight: 800;
				color: var(--color-calendar-base);
				display: flex;
				align-items: center;
				line-height: 16px;

				.icon {
					line-height: 20px;
					display: inline-block;
					width: 25px;
					height: 25px;
					border-radius: 3px;
					text-align: center;
					margin: 0 10px;
					background-color: var(--border-color-extra-light);

					i {
						font-size: 24px;
						line-height: 24px;
						font-weight: 400;
						color: var(--color-text-secondary)
					}
				}
			}

			.center {
				width: 256px;
				height: 38px;
				position: absolute;
				display: flex;
				align-items: center;
				justify-content: space-between;
				left: 50%;
				transform: translateX(-50%);
				font-size: 18px;
				font-weight: 800;
				line-height: 10px;
				color: var(--color-calendar-center);

				.btn {
					height: 35px;
					width: 35px;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #0062ff;
					border-radius: 8px;
					color: #fff;
					cursor: pointer;
				}
			}
		}

		.week {
			height: 62px;
			display: flex;
			align-items: center;
			justify-content: space-around;

			.week-list {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 254px;
				font-size: 16px;
				font-weight: normal;
				line-height: 10px;
				color: #7c8db5;
			}
		}
	}

	.bottom {
		width: 1892px;
		height: 680px;
		display: flex;
		flex-wrap: wrap;
		background-color: var(--background-color-calendar-day);
		border-radius: 20px;
		margin-top: 14px;
		padding: 20px 0 20px 25px;
		overflow: auto;
		border: 1px solid var(--color-calendar-border-transparent);

		.item {
			width: 254px;
			height: 212px;

			&:nth-child(7n) {
				margin-right: 0;
			}

			&:nth-child(-n+7) {
				margin-top: 0;
			}
		}

		.today {
			border: 2px solid #0062ff;
		}

		.cur {
			opacity: 0.4;
		}
	}
}

</style>
