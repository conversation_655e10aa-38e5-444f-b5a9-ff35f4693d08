<template>
  <Layout tag="div" :asideWidth="290" :contentLoading="table.loading" class="crossReport small-popup">

    <template slot="aside">
      <div class="query-top">
        <el-tabs v-model="tool.activeQuery" stretch type="border-card">
          <el-tab-pane :label="$ct('chooseObj')" name="vehicle">
            <el-tabs v-model="tool.activeTab" stretch style="padding: 5px 10px 0;">
              <el-tab-pane :label="$ct('car')" name="vehicleTree">
                <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" style="padding: 10px 0;"
                  @check="selectVehicleNodes" node-key="id">
                </ElementTree>
              </el-tab-pane>
              <el-tab-pane :label="$ct('driver')" name="driverTree">
                <ElementTree type="driverTree" ref="driverTree" :checkMode="true" style="padding: 10px 0;"
                  @check="selectDeptNodes" node-key="id">
                </ElementTree>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane :label="$ct('chooseroad')" name="fense">
            <el-tabs v-model="tool.areaActivetab" stretch style="padding: 5px 10px 0;">
              <el-tab-pane label="限行区" name="xianxingqu">
                <LeftFenceTreeFilter @change="changeFenceShow" :source="8" type="xianxingquCross" ref="xianxingqu"></LeftFenceTreeFilter>
              </el-tab-pane>
              <el-tab-pane label="辖区" name="xiaqu">
                <LeftFenceTreeFilter @change="changeFenceShow" :source="16" type="xiaquMgtCross" ref="xiaqu"></LeftFenceTreeFilter>
              </el-tab-pane>
              <el-tab-pane label="围栏" name="weilan">
                <ElementTree  type="fenseParams:4" :checkMode="true" @check="selectFenceNodes" ref="weilan"></ElementTree>
              </el-tab-pane>
              <el-tab-pane label="区域围栏" name="areafence">
	<ElementTree type="areaRoadFenceTree" :checkMode="true" @check="selectAreaFenceNodes" ref="areaFence"></ElementTree>
  </el-tab-pane>
              
            </el-tabs>

            <!-- <ElementTree type="xianxingquCross" :checkMode="true" @check="selectNodes" ref="xnqRef"></ElementTree> -->
            <!-- <FenseFilter ref="fenseFilter" @change="changeFenceShow" :source="4"></FenseFilter> -->
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="query-bottom bg bg--lighter">
        <StartEndTime :timeTitle="[$ct('startTime'), $ct('endTime')]" v-model="timeSelect" :timeLimitForCur="1"
          valueFormat="timestamp" :isLimit="true">
        </StartEndTime>

        <div class="query-item">
          <span>
            <el-checkbox v-model="queryList.left_light">{{ $ct('leftl') }}</el-checkbox>
          </span>
          <span>
            <el-checkbox v-model="queryList.right_light">{{ $ct('rightl') }}</el-checkbox>
          </span>
        </div>

        <div class="query-item">
          <span>
            <el-checkbox v-model="setting.stract.state">{{ $ct('wayspeed') }}</el-checkbox>
          </span>
          <el-input-number v-model="setting.stract.value" :disabled="!setting.stract.state" :max="120" :step="1"
            placeholder="（km/h)" style="width: 100%">
          </el-input-number>
        </div>
        <div class="query-item">
          <span>
            <el-checkbox v-model="setting.left.state">{{ $ct('leftspeed') }}</el-checkbox>
          </span>
          <el-input-number v-model="setting.left.value" :disabled="!setting.left.state" :max="120" :step="1"
            placeholder="（km/h)" style="width: 100%">
          </el-input-number>
        </div>
        <div class="query-item">
          <span>
            <el-checkbox v-model="setting.right.state">{{ $ct('rightspeed') }}</el-checkbox>
          </span>
          <el-input-number v-model="setting.right.value" :disabled="!setting.right.state" :max="120" :step="1"
            placeholder="（km/h)" style="width: 100%">
          </el-input-number>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="searchCurrentDetail"
            :loading="table.loading">{{ $ct('query') }}
          </el-button>
        </div>
      </div>
    </template>

    <el-tabs slot="content" v-model="tool.showModel" type="border-card">
      <el-tab-pane :label="$ct('map')" name="map" style="padding-top: 5px">
        <SimpleMap ref="simplemap" @mapReady="generateMapObj" mapConfig="simple">
          <div class="filterSelect mbg">
            <i class="pony-iconv2 pony-paixu"></i>
            <el-select v-model="table.currentSort" @change="handleCommand" placeholder="请选择">
              <el-option :label="$ct('pingci')" value="pinci"></el-option>
              <el-option :label="$ct('alarmCount')" value="alarm_count"></el-option>
              <el-option :label="$ct('thoughCount')" value="cross_count"></el-option>
              <el-option :label="$ct('sbspeed')" value="cross_speed"></el-option>
              <el-option :label="$ct('sbleft')" value="left_speed"></el-option>
              <el-option :label="$ct('sbright')" value="right_speed"></el-option>
              <el-option :label="$ct('leftl')" value="left_light"></el-option>
              <el-option :label="$ct('rightl')" value="right_light"></el-option>
            </el-select>
          </div>
        </SimpleMap>
      </el-tab-pane>
      <el-tab-pane :label="$ct('form')" name="table">
        <div class="dfb" style="width: 100%; height: 40px; padding-left: 10px;">
          <div class="break-item">
            <el-button @click.prevent="exportReportTable" size="mini" type="primary" :loading="exportLoading">{{
              $ct('excel') }}</el-button>
          </div>
          <div class="query-item">
            <el-pagination background small :current-page.sync="activeSource.page" :page-size="activeSource.size"
              layout="prev, pager, next, total" :total="activeSource.data.length">
            </el-pagination>
          </div>
        </div>
        <el-tabs style="height: calc(100% - 40px)" v-model="tool.activeTable" type="border-card">
          <el-tab-pane :label="activeTitle + $ct('statistics')" name="vehicleGather" style="padding-top: 10px">
            <el-table class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini"
              :data="formatList" @sort-change="sortCurrentProp" height="100%" style="width: 100%">
              <el-table-column type="index" :index="(index) => index + 1 + pageStart" :label="$ct('index')"
                width="80"></el-table-column>
              <el-table-column prop="plate_no" :label="queryList.type ? $ct('driver') : $ct('plateNo')"
                min-width="100"></el-table-column>
              <el-table-column prop="dept_name" :label="$ct('unit')" show-overflow-tooltip min-width="200"
                header-align="center" align="left"></el-table-column>
              <el-table-column prop="cross_speed" :label="$ct('sbspeed')" min-width="120" sortable></el-table-column>
              <el-table-column prop="left_speed" :label="$ct('sbleft')" min-width="120" sortable></el-table-column>
              <el-table-column prop="right_speed" :label="$ct('sbright')" min-width="120" sortable></el-table-column>
              <el-table-column prop="left_light" :label="$ct('leftl')" min-width="120" sortable></el-table-column>
              <el-table-column prop="right_light" :label="$ct('rightl')" min-width="120" sortable></el-table-column>
              <el-table-column prop="alarm_count" :label="$ct('alarmCount')" min-width="120" sortable></el-table-column>
              <el-table-column prop="cross_count" :label="$ct('thoughCount')" min-width="120" sortable></el-table-column>
              <el-table-column label="详情" min-width="50">
                <template slot-scope="{ row }">
                  <el-button type="text" title="详情" size="mini" @click="showDetail(row)">
                    <i class="pony-iconv2 pony-xiangqing"></i>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane :label="$ct('roadstatistics')" name="mapDate" style="padding-top: 10px">
            <el-table class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini"
              :data="formatList" @sort-change="sortCurrentProp" height="100%" style="width: 100%">
              <el-table-column type="index" :index="(index) => index + 1 + pageStart" :label="$ct('index')"
                width="80"></el-table-column>
                <el-table-column prop="dept_name" label="归属公司" show-overflow-tooltip header-align="center"
                align="left" min-width="200"></el-table-column>
              <el-table-column prop="fense_name" :label="$ct('roadname')" show-overflow-tooltip header-align="center"
                align="left" min-width="200"></el-table-column>
              <el-table-column prop="cross_speed" :label="$ct('sbspeed')" min-width="120" sortable></el-table-column>
              <el-table-column prop="left_speed" :label="$ct('sbleft')" min-width="120" sortable></el-table-column>
              <el-table-column prop="right_speed" :label="$ct('sbright')" min-width="120" sortable></el-table-column>
              <el-table-column prop="left_light" :label="$ct('leftl')" min-width="120" sortable></el-table-column>
              <el-table-column prop="right_light" :label="$ct('rightl')" min-width="120" sortable></el-table-column>
              <el-table-column prop="alarm_count" :label="$ct('alarmCount')" min-width="120" sortable></el-table-column>
              <el-table-column prop="cross_count" :label="$ct('thoughCount')" min-width="120" sortable></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane :label="activeTitle + $ct('details')" name="adjective" style="padding-top: 10px">
            <el-table class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini"
              :data="formatList" @sort-change="sortCurrentProp" height="100%" style="width: 100%">
              <el-table-column type="index" :index="(index) => index + 1 + pageStart" :label="$ct('index')"
                width="80"></el-table-column>
              <el-table-column prop="fense_name" :label="$ct('road')" show-overflow-tooltip header-align="center"
                align="left" min-width="150"></el-table-column>
              <el-table-column prop="dept_name" :label="$ct('unit')" show-overflow-tooltip min-width="200"
                header-align="center" align="left"></el-table-column>
              <el-table-column prop="plate_no" :label="$ct('plateNo')" min-width="120"></el-table-column>
              <el-table-column prop="driver_name" :label="$ct('driver')" min-width="120"></el-table-column>
              <el-table-column prop="event_name" :label="$ct('speedtype')" min-width="120"></el-table-column>
              <el-table-column prop="speed" :label="$ct('velocity')" min-width="120" sortable></el-table-column>
              <el-table-column prop="light" :label="$ct('sblight')" min-width="120" sortable></el-table-column>
              <el-table-column prop="date_time" :label="$ct('speedTime')" min-width="120" sortable></el-table-column>
              <el-table-column :label="$ct('operate')" min-width="100">
                <template slot-scope="{row}">
                  <el-button type="text" size="mini" @click.native.stop="jumpOnPlayBack(row)">
                    <i class="pony-iconv2 pony-guijihuifang"></i>
                  </el-button>
                </template>
              </el-table-column>

            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>

    <popup ref='cross' :minWidth="220" :auto-close='false' :close-on-click='false'>
      <div class="crossPopup" v-if="table.currentCross">
        <div class="crossPopup__header bor--b dfb">
          <span class="fon--m">{{ table.currentCross.fense_name }}</span>
        </div>
        <div class="crossPopup__content">
          <ul class="bor--b">
            <li><i style="font-size: 22px" class="pony-iconv2 pony-zuozhuan"></i><span>{{ table.currentCross.left_speed
            }}</span></li>
            <li><i style="font-size: 22px" class="pony-iconv2 pony-zhihang"></i><span>{{ table.currentCross.cross_speed
            }}</span></li>
            <li style="border: 0"><i style="font-size: 22px" class="pony-iconv2 pony-youzhuan"></i><span>{{
              table.currentCross.right_speed }}</span></li>
          </ul>
          <ul>
            <li><i class="pony-iconv2 pony-zuozhuanxiangdeng"></i><span>{{ table.currentCross.left_light }}</span></li>
            <li><i class="pony-iconv2 pony-youzhuanxiangdeng"></i><span>{{ table.currentCross.right_light }}</span></li>
            <li style="border: 0">
              <span>{{ table.currentCross.alarm_count }} / {{ table.currentCross.cross_count }}</span>
              <el-tooltip class="item" effect="dark" :content="$ct('crossCount')" placement="top">
                <el-button type="text" icon="pony-iconv2 pony-bangzhu"></el-button>
              </el-tooltip>
            </li>
          </ul>
        </div>
      </div>
    </popup>

  </Layout>
</template>

<script>
import L from '@/assets/lib/leaflet-bmap'
import LeftFenceTreeFilter from '@/view/business/components/LeftFenceTreeFilter';
import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
import FenseFilter from './components/FenseFilter'
import { getFenseLayerByCustom, getArrayDifference } from '@/view/monitor/util/monitorUtil'
import popup from '../../monitor/components/Popup'
import StartEndTime from '@/components/common/StartEedTime'
import moment from "moment";
const ExportJsonExcel = require('js-export-excel')
export default {
  name: 'crossReport',
  components: { SimpleMap, FenseFilter, popup, StartEndTime, LeftFenceTreeFilter },
  data() {
    return {
      _currentFeature: null,

      _currentBindLayer: {},

      setting: {
        stract: {
          state: true,
          min: 5,
          value: 40,
          // value: 10,
        },
        left: {
          state: true,
          min: 5,
          value: 20
          // value: 10,
        },
        right: {
          state: true,
          min: 5,
          // value: 30
          value: 10,
        }
      },

      timeSelect: [
        moment().subtract(1, 'days').startOf('day').valueOf(),
        moment().subtract(1, 'days').endOf('day').valueOf()
      ],

      queryList: {
        type: 0,                    // 0 车  1 司机
        object_ids: [],
        fense_ids: [],
        left_speed: null,
        cross_speed: null,
        right_speed: null,
        right_light: true,
        left_light: true,
        restricted_area_ids: []
      },

      tool: {
        activeQuery: 'vehicle',
        areaActivetab: 'xianxingqu',
        activeTab: 'vehicleTree',
        activeTable: 'vehicleGather',
        showModel: 'map'
      },


      table: {
        loading: false,
        currentCross: null,
        currentSort: 'cross_count',
        vehicleGather: {
          data: [],
          page: 1,
          size: 30
        },
        mapDate: {
          data: [],
          page: 1,
          size: 30
        },
        adjective: {
          data: [],
          page: 1,
          size: 30
        },
      },
      vehicleIdsList: [],
      driverIdsList: [],
      exportLoading: false,
      areaFenceIds:[]
    };
  },

  computed: {
    activeTitle() {
      return this.queryList.type ? this.$ct('driver') : this.$ct('car')
    },
    extandParmas() {
      return {
        start_time: this.timeSelect[0],
        end_time: this.timeSelect[1],
        type: this.tool.activeTab == 'vehicleTree' ? 0 : 1
      }
    },
    activeSource() {
      return this.table[this.tool.activeTable]
    },
    pageStart() {
      return (this.activeSource.page - 1) * this.activeSource.size
    },
    formatList() {
      return this.activeSource.data.slice(this.pageStart, this.pageStart + this.activeSource.size)
    }
  },

  watch: {
    'tool.showModel': function (newVal) {
      if (newVal == 'map') this.reflashMap()
    },
  'tool.areaActivetab': function (old,newVal) {
      if (old == 'weilan') {
        this.$refs['weilan'].$refs.tree.setCheckedKeys([])
			  this.changeFenceShow([])
      }else if(old =='areafence'){
        this.changeFenceShow(this.areaFenceIds)
      }else {
        this.$refs[old].clearTree()
      }
    },
  
  },

  mounted() {
  
  },

  methods: {
    selectVehicleNodes(current, { checkedNodes }) {
      this.vehicleIdsList = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
    },
    selectAreaFenceNodes(current, { checkedNodes }){
	let currentNodes = checkedNodes.filter(item => item.type == 5).map(item => item.id)
	this.areaFenceIds = currentNodes
	this.changeFenceShow(currentNodes)
  },
    selectFenceNodes(current, { checkedNodes }) {
      // this.vehicleIdsList = checkedNodes
      //   .filter((item) => item.type == 4)
      //   .map((item) => item.id);
      let currentNodes = checkedNodes.filter(item => item.type == 2).map(item => item.id)
      this.changeFenceShow(currentNodes)

    },
    selectDeptNodes(current, { checkedNodes }) {
      this.driverIdsList = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
    },
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes.filter(item => item.type == 6).map(item => item.id)
      this.changeFenceShow(currentNodes)
    },
    jumpOnPlayBack(row) {
      let startTime = moment(row.date_time).subtract(2, 'minute')
      let endTime = moment(row.date_time).add(2, 'minute')
      let parmas = {
        vehicleId: row.vehicle_id,
        startTime: startTime,
        endTime: endTime,
        fense_id: row.fense_id
      }
      this.$router.push({
        path: '/home/<USER>',
        query: parmas
      })
    },

    cleanUp() {
      this.table.loading = false
      let list = ['vehicleGather', 'mapDate', 'adjective']
      list.forEach(item => {
        this.table[item].page = 1
        this.table[item].data = []
      })
      this._currentFeature.clearLayers()
      this.$refs.cross.close()
    },

    async searchCurrentDetail() {
      this.queryList.object_ids = this.tool.activeTab == 'vehicleTree' ? this.vehicleIdsList : this.driverIdsList
      if (!this.queryList.object_ids.length) {
        this.$warning(this.tool.activeTab == 'vehicleTree' ? this.$ct('choosecar') : this.$ct('choosedriver'));
        return
      }
      // if(!this.queryList.fense_ids.length) {
      //     this.$warning(this.$ct('choosefense'));
      //     return
      // }
      let parmas = JSON.parse(JSON.stringify(this.queryList))
      if (this.setting.stract.state) parmas.cross_speed = this.setting.stract.value
      if (this.setting.left.state) parmas.left_speed = this.setting.left.value
      if (this.setting.right.state) parmas.right_speed = this.setting.right.value
      Object.assign(parmas, this.extandParmas)

      this.cleanUp()
      this.table.loading = true
      //  let parmas = {"start_time":"2021-08-14","end_time":"2021-08-19","type":0,"object_ids":["155","156","157","158","159","161","160","162","163","170","169","164","165","166","168","172","175","178","177","167","171","179","173","176","174","145","142","140","147","143","146","34","139","137","144","148","149","141","133","132","131","130","136","113","138"],"fense_ids":["3afff7ae-9c71-4735-ad74-c2928d101255","30cbc818-6130-4f12-94f8-44189f90e64f","54ded4f9-2699-43e8-8618-a0b9ddc34f94","584bc789-fbfa-4647-8e95-4b7b894d8e7b","6418f272-cee2-4716-aa61-2fb670582301","71bd071e-dcc1-461d-ac42-a64a4711c048","72987d2f-8ed9-48d7-bf11-d0230a017844","5e30c051-0ac8-4f30-aab3-1e669c9b7e99"],"alarmAndSpeed":{"401":40,"402":20,"403":30},"_t":1579080826246}
      let result = await this.$api.intersectionViolationInquiryV2(parmas)
      if (!result || result.status != 200) {
        this.table.loading = false
        this.$error(result.message || this.$ct('queryfail'));
        return
      }
      if (!result.data.adjective.length) {
        this.table.loading = false
        this.$warning(this.$ct('noData'));
        return
      }
      this.table.adjective.data = result.data.adjective || []
      this.table.vehicleGather.data = result.data.vehicleGather || []

      result.data.mapDate.forEach(item => {
        if (!item.alarm_count || !item.cross_count) {
          item.pingci = 0
        } else {
          item.pingci = item.alarm_count / item.cross_count
        }
      })
      this.table.mapDate.data = result.data.mapDate || []

      this.changePopupState()
      this.table.loading = false
    },

    handleCommand(command) {
      this.table.currentSort = command
      this.changePopupState()
    },

    changePopupState() {
      if (!this.table.mapDate.data.length) return
      if (this._currentFeature) {
        this._currentFeature.clearLayers()
      }
      let sortList = JSON.parse(JSON.stringify(this.table.mapDate.data))
      sortList = sortList.sort((a, b) => b[this.table.currentSort] - a[this.table.currentSort])
      sortList.forEach((item, index) => {
        let marker = this._currentBindLayer[item.fense_id]
        if (!marker) return
        let icon
        if (index < 10) {
          icon = new L.DivIcon({
            html: `<div class="leaflet-cross" style="height: 32px; width: 32px;
                            background-color: #dd474b;color:#fff">
                            ${index + 1}</div>`,
            className: 'leaflet-center-text'
          })
          marker.setZIndexOffset(10000)
        } else {
          icon = new L.DivIcon({
            html: `<div class="leaflet-cross" style="height: 25px; width: 25px;
                            background-color: #384e6d;color:#fff">
                            ${index + 1}</div>`,
            className: 'leaflet-center-text'
          })
          marker.setZIndexOffset(1000)
        }
        marker.setIcon(icon)
        this._currentFeature.addLayer(marker)
      })
      this.reflashMap()
    },

    changeFenceShow(value) {
      let diffentList = getArrayDifference(this.queryList.fense_ids, value)
      let addList = []
      diffentList.forEach(item => {
        let currentLayer = this._currentBindLayer[item]
        if (!currentLayer) {
          addList.push(item)
          return
        }
      })
      this.queryList.fense_ids = JSON.parse(JSON.stringify(value))
      if (addList.length) {
        this.getFenceDetail(addList)
      }
    },

    async getFenceDetail(list) {
      let result = await this.$api.queryFenseInfoAssets({ fence_id_list: list })
      if (!result || result.status != 200) {
        this.$error(result.message || this.$ct('queryfail'));
        return
      }
      result.data.forEach(fense => {
        let pointList = []
        fense.fence_point.forEach(item => {
          let latlng = item.split(',')
          pointList.push([Number(latlng[0]), Number(latlng[1])])
        })
        let layer = getFenseLayerByCustom({
          shape: fense.shape,
          pointList: pointList,
          radius: fense.fence_range,
          option: 'default'
        })
        let icon = new L.DivIcon({
          html: `<div class="leaflet-cross" style="background-color: #384e6d;"></div>`,
          className: 'leaflet-center-text'
        })
        let marker = L.marker(layer.center, { icon: icon })
        marker.id = fense.id
        marker.on('click', (e) => {
          this.openCurrentPopup(e.target.id)
        })
        this._currentBindLayer[fense.id] = marker
      })
    },

    openCurrentPopup(id) {
      let target = this._currentBindLayer[id]
      let result = this.table.mapDate.data.find(item => item.fense_id == id)
      if (!target || !result) return
      this.table.currentCross = result
      this.$nextTick(() => {
        this.$refs.cross.open(target)
      })
    },

    exportReportTable() {
      let sheetOne = []
      let options = {
        fileName: `${moment(this.timeSelect[0]).format('YYYY-MM-DD HH:mm:ss')} ~ ${moment(this.timeSelect[1]).format('YYYY-MM-DD HH:mm:ss')} ${this.$ct('carroadspeed')}`,
        datas: []
      }
      if(!this.activeSource.data.length) return this.$warning('暂无数据导出')
      this.exportLoading = true
      if (this.table.vehicleGather.data.length&&this.tool.activeTable=='vehicleGather') {
        let excelBody = []
        this.table.vehicleGather.data.forEach((item, index) => {
          let array = []
          array.push(
            index + 1, item.plate_no, item.dept_name, item.cross_speed,
            item.left_speed, item.right_speed, item.left_light, item.right_light, item.alarm_count, item.cross_count
          )
          excelBody.push(array)
        })
        let title = this.queryList.type ? this.$ct('driver') : this.$ct('plateNo')
        options.datas.push({
          sheetName: this.activeTitle + this.$ct('statistics'),
          sheetData: excelBody,
          sheetHeader: [this.$ct('index'), title, this.$ct('unit'), this.$ct('sbspeed'), this.$ct('sbleft'), this.$ct('sbright'), this.$ct('leftl'), this.$ct('rightl'), this.$ct('alarmCount'), this.$ct('thoughCount')],
          columnWidths: ['3', '8', '20', '8', '8', '10', '8', '8', '8', '8']
        })
      }
      if (this.table.mapDate.data.length&&this.tool.activeTable=='mapDate') {
        let excelBody = []
        this.table.mapDate.data.forEach((item, index) => {
          let array = []
          array.push(
            index + 1,item.dept_name, item.fense_name, item.cross_speed,
            item.left_speed, item.right_speed, item.left_light, item.right_light, item.alarm_count, item.cross_count
          )
          excelBody.push(array)
        })
        options.datas.push({
          sheetName: this.$ct('roadstatistics'),
          sheetData: excelBody,
          sheetHeader: [this.$ct('index'), '归属公司','路口名称', this.$ct('sbspeed'), this.$ct('sbleft'), this.$ct('sbright'), this.$ct('leftl'), this.$ct('rightl'), this.$ct('alarmCount'), this.$ct('thoughCount')],
          columnWidths: ['3', '8', '8', '8', '8', '8', '8', '8', '8']
        })
      }
      if (this.table.adjective.data.length&&this.tool.activeTable=='adjective') {
        let excelBody = []
        this.table.adjective.data.forEach((item, index) => {
          let array = []
          array.push(
            index + 1, item.fense_name, item.dept_name, item.plate_no,
            item.driver_name, item.event_name, item.speed, item.light, item.date_time
          )
          excelBody.push(array)
        })
        options.datas.push({
          sheetName: this.activeTitle + this.$ct('details'),
          sheetData: excelBody,
          sheetHeader: [this.$ct('index'), this.$ct('roadname'), this.$ct('unit'), this.$ct('plateNo'), this.$ct('driver'), this.$ct('speedtype'), this.$ct('velocity'), this.$ct('sblight'), this.$ct('speedTime')],
          columnWidths: ['3', '10', '20', '8', '8', '10', '8', '8', '10']
        })
      }
      ExportJsonExcel(options).saveExcel();
      this.exportLoading = false;
    },


    sortCurrentProp(column) {
      if (!column.order || !column.prop) return
      if (column.order == 'ascending') {
        this.activeSource.data.sort((a, b) => +(moment(a[column.prop]).valueOf()) - +(moment(b[column.prop]).valueOf()))
      } else {
        this.activeSource.data.sort((a, b) => +(moment(b[column.prop]).valueOf()) - +(moment(a[column.prop]).valueOf()))
      }
    },

    generateMapObj() {
      this._currentBindLayer = {}
      this.$refs.cross.init(this.$refs['simplemap']._map)
      this._currentFeature = new L.FeatureGroup().addTo(this.$refs['simplemap']._map)
    },

    reflashMap() {
      setTimeout(() => {
        this.$refs['simplemap'].reflashMap()
        let bounds = this._currentFeature.getBounds()
        if (bounds._northEast) {
          this.$refs['simplemap']._map.fitBounds(bounds, 17)
        }
      }, 100)
    },

    async handleJumpLink() {
      let parmas = this.$route.query?.parmas
      if (!parmas) return
      parmas = JSON.parse(parmas)
      // console.log('parmas', parmas)
      if (!parmas.object_ids) return
      this.table.loading = true
      let $ztree = this.$refs['vehicleTree']
      await $ztree.waitForInit
      if (parmas.startTime) {
        this.timeSelect = [
          moment(parmas.startTime).valueOf(),
          moment(parmas.endTime).valueOf()
        ]

      }
      if (parmas.object_ids.length) {
        $ztree.$refs["tree"].setCheckedKeys(parmas.object_ids);
        this.vehicleIdsList = parmas.object_ids

      }
      // if(parmas.fense_ids && parmas.fense_ids.length) {
      //     this.$refs['fenseFilter'].initFilterValue(parmas.fense_ids)
      // }
      this.queryList.restricted_area_ids = parmas.restricted_area_ids
      this.tool.showModel = 'table'
      await this.$nextTick()
      await this.searchCurrentDetail()
      this.$router.push('/home/<USER>')
      this.table.loading = false
    },
    //详情跳转
    async showDetail(row) {
      let list = [JSON.stringify(row.vehicle_id)]
      Object.assign(row, this.extandParmas)
      this.table.loading = true
      this.table.adjective.page = 1
      let res = await this.$api.intersectionViolationInquiryV2({
        object_ids: list,
        start_time: row.start_time,
        end_time: row.end_time,
        fense_ids: this.queryList.fense_ids,
        restricted_area_ids: this.restricted_area_ids,
        type: row.type,
        cross_speed: row.cross_speed,
        left_light: row.left_light,
        left_speed: row.left_speed,
        right_light: row.right_light,
        right_speed: row.right_speed,
      })
      if (!res || res.status != 200) {
        this.table.loading = false
        this.$error(res.message || this.$ct('queryfail'));
        return
      }
      if (!res.data.adjective.length) {
        this.table.loading = false
        this.$warning(this.$ct('noData'));
        return
      }
      this.table.adjective.data = res.data.adjective || []
      this.tool.activeTable = 'adjective'
      this.table.loading = false
    },
  },

  activated() {
    this.handleJumpLink()
  },
}

</script>

<style lang='scss' scoped>
.crossReport {
  .query-top {
    height: calc(100% - 315px);

    /deep/ .el-tabs__content {
      overflow-y: auto !important;
    }
  }

  .query-bottom {
    margin-top: 5px;
    height: 300px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }
}

.crossPopup {
  &__header {
    height: 40px;
  }

  &__content {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    ul {
      width: 100%;
      display: flex;
      justify-content: flex-start;
    }

    li {
      width: 70px;
      padding: 3px 0;
      line-height: 25px;
      display: flex;
      flex-wrap: wrap;

      align-items: center;
      justify-content: center;
      border-right: solid 1px var(--border-color-base);
      font-size: 15px;

      span {
        width: 100%;
        text-align: center;
      }
    }
  }
}

.filterSelect {
  z-index: 10;
  position: absolute;
  top: 15px;
  right: 8px;
  border-radius: 4px;
  width: max-content;
  height: max-content;

  i {
    padding: 0 5px;
  }
}

.el-tabs__content {
  overflow-y: auto;
}
</style>
