<template>
  <Layout tag="div" class="lmsWayBill" :contentLoading="table.loading" :has-color="true">
    <template slot="aside">
      <div class="query-top">
        <ElementTree type="vehicle" :checkMode="true" @check="selectNodes" node-key="id" ref="vehicleTree"> </ElementTree>
      </div>
    </template>

    <template slot="query">
      <div class="query-item">
        <StartEndTime
          showType="heng"
          itemHeight="35"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="datetime"
          titleStyle="font-size:12px;margin-right:5px;display:inline-block;text-align: justify;
                text-align-last: justify;"
        >
        </StartEndTime>
      </div>
      <div class="query-item">
        <span>SAP订单号：</span>
        <el-autocomplete
          class="inline-input"
          v-model="queryList.sapCode"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'sapCode');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>承运商名称：</span>
        <el-select v-model="queryList.carrierName" multiple collapse-tags clearable>
          <el-option v-for="(item, index) in carrierNameList" :key="item.value" :label="item.name" :value="item.value">
            <div class="option-text">{{ item.name }}</div>
          </el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>车牌号：</span>
        <el-autocomplete
          class="inline-input"
          v-model="queryList.vehicle"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'vehicle');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>物料：</span>
        <el-autocomplete
          class="inline-input"
          v-model="queryList.materiel"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'materiel');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>发货方：</span>
        <el-autocomplete
          class="inline-input"
          v-model="queryList.sender"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'sender');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>物料类型：</span>
        <el-select v-model="queryList.materialType" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="1" label="固体"></el-option>
          <el-option :value="2" label="液体"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>配送方式：</span>
        <el-select v-model="queryList.sendType" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="1" label="配送"></el-option>
          <el-option :value="2" label="自提"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>是否危化品：</span>
        <el-select v-model="queryList.ghs" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option
        ></el-select>
      </div>
      <div class="query-item">
        <span>是否录制视频：</span>
        <el-select v-model="queryList.transcribe" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>
          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>是否录制成功：</span>
        <el-select v-model="queryList.recordingSuccessful" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>
          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>观看状态：</span>
        <el-select v-model="queryList.viewStatus" style="width:100px">
          <el-option :value="null" label="全部"></el-option>
          <el-option :value="1" label="已观看"></el-option>
          <el-option :value="0" label="未观看"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>状态：</span>
        <el-select v-model="queryList.status" placeholder="请选择" style="width: 100px">
          <el-option label="全部" :value="null"></el-option>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span style="width: 50px;"></span>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="search" :loading="table.loading">查询</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="operateTableSetting">表格显示配置</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="exportDataInfo" :loading="exportLoading">导出</el-button>
      </div>
    </template>
    <el-table
      ref="table"
      class="el-table--ellipsis el-table--radius"
      slot="content"
      border
      stripe
      highlight-current-row
      size="mini"
      :data="formatList"
      height="100%"
    >
      <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" min-width="50"></el-table-column>
      <el-table-column label="操作" width="50">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" title="录像查看" @click="toJump(row)">
            <i class="pony-iconv2 pony-bofangquan"></i>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableSettingList"
        :key="index"
        :min-width="item.size + 'px'"
        header-align="center"
        :align="item.align"
        :prop="item.key"
        :label="item.name"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <span v-if="item.key == 'status'">{{ row.status || row.status == 0 ? statusObj[row.status] : "" }}</span>

          <span v-if="item.key == 'recordingDuration'">{{ row.recordingDuration | formatTime }}</span>
          <span v-if="item.key == 'sendTime'">{{ TimeFormat(row.sendTime) }}</span>
          <span v-if="item.key == 'receiverTime'">{{ TimeFormat(row.receiverTime) }}</span>
          <span v-if="item.key == 'sendType'">{{ row.sendType == 1 ? '配送' : row.sendType == 2 ? '自提' : '' }}</span>
          <span v-if="item.key == 'materialType'">{{ row.materialType == 1 ? '固体' : row.materialType == 2 ? '液体' : ''
            }}</span>
          <span v-if="item.key == 'ghs'">{{ row.ghs ? '是' : '否' }}</span>
          <span v-if="item.key == 'transcribe'">{{ row.transcribe ? '是' : '否' }}</span>
          <span v-if="item.key == 'viewStatus '">{{ row.viewStatus  ? '已观看' : '未观看' }}</span>
          <span v-if="item.key == 'recordingSuccessful'">{{ row.recordingSuccessful? '是' : '否' }}</span>
          <span v-if="item.key == 'failureReason'">{{row.transcribe?row.recordingSuccessful?"": row.failureReason? 'G7视频录制失败' : '待上传':"" }}</span>


          <span v-if="item.type !== 1">{{ row[item.key] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <template slot="footer">
      <div class="query-item">
        <el-pagination
          small
          background
          :current-page.sync="table.page"
          :page-size="table.size"
          layout="prev, pager, next, total"
          :total="table.list.length"
        >
        </el-pagination>
      </div>
    </template>
    <!-- 表格显示配置弹窗 -->
    <TableShowConfigList
      ref="tableShowConfigList"
      v-model="tableSettingList"
      :list="allSettingList"
      @tableValue="getSetValue"
      pageName="lmsWayBill"
      :isSave="true"
      :defaultSetting="defaultSettingList"
      @change="settable"
    >
    </TableShowConfigList>
  </Layout>
</template>

<script>
const ExportJsonExcel = require("js-export-excel");
import StartEndTime from "@/components/common/StartEedTime";
import TableShowConfigList from "@/view/report/components/TableShowConfigList";
import { allSettingList, defaultSettingList, statusList, statusObj } from "./lmsWaybill.js";

export default {
  name: "WayBillLMS",
  components: {
    StartEndTime,
    TableShowConfigList,
  },
  data() {
    return {
      // isSearch 用于标识是否由CSPC故障明细页面导航进来的
      isSearch: 0,
      table: {
        loading: false,
        list: [],
        page: 1,
        size: 30,
        total: 0,
      },
      dealDataList: ["trashWeight", "trashWeightGross", "trashWeightTare", "trashWeightNet"],
      selectStartEndTime: [moment().startOf("day").valueOf(), moment().endOf("day").valueOf()],
      tableSettingList: [],
      allSettingList,
      defaultSettingList,
      queryList: {
        vehicleIds: [],
        status: null,
        start: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        end: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        sapCode: null,
        carrierName: null,
        vehicle: null,
        materiel: null,
        materialType: null,
        sender: null,
        sendType: null,
        ghs: null,
        transcribe: null,
        recordingSuccessful:null,
        viewStatus:null
      },
      typeListPoint: [],
      statusList,
      statusObj,
      exportLoading: false,
      allData: [],
      carrierNameList: [],
    };
  },
  watch: {
    selectStartEndTime: function (newVal, oldVal) {
      this.queryList.start = newVal[0] ? moment(newVal[0]).format("YYYY-MM-DD HH:mm:ss") : "";
      this.queryList.end = newVal[1] ? moment(newVal[1]).format("YYYY-MM-DD HH:mm:ss") : "";
    },
  },
  filters: {
    formatTime(value) {
      if (!value && value != 0) return "";
      if (value == 0) return 0;
      var secondTime = parseInt(value);
      var minuteTime = 0;
      var hourTime = 0;
      var dayTime = 0;
      var result = "";
      if (secondTime > 60) {
        minuteTime = parseInt(secondTime / 60);
        secondTime = parseInt(secondTime % 60);
        if (minuteTime > 60) {
          hourTime = parseInt(minuteTime / 60);
          minuteTime = parseInt(minuteTime % 60);
          if (hourTime > 24) {
            dayTime = parseInt(hourTime / 24);
            hourTime = parseInt(hourTime % 24);
          }
        }
      }
      if (secondTime > 0) {
        minuteTime += 1;
      }
      if (minuteTime >= 0) {
        result = "" + parseInt(minuteTime) + " 分钟 " + result;
      }
      if (hourTime > 0) {
        if (minuteTime == 0) {
          result = "" + parseInt(hourTime) + " 小时 ";
        } else {
          result = "" + parseInt(hourTime) + " 小时 " + result;
        }
      }
      if (dayTime > 0) {
        result = "" + parseInt(dayTime) + " 天 " + "" + parseInt(hourTime) + "小时";
      }
      return result;
    },
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },
    formatList() {
      return this.table.list.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  mounted() {
    this.getType();
    this.getAllData();
    this.$route;
  },
  activated() {
    this.handleJumpLink();
  },
  methods: {
    querySearch(queryString, cb, type) {
      let map = new Map();
      let allData = [];
      if (this.allData.length) {
        allData = this.allData
          .filter((item) => {
            return !map.has(item[type].toString()) && map.set(item[type].toString());
          })
          .map((item) => {
            return {
              name: item[type],
              value: item[type],
            };
          });
      }
      var results = queryString ? allData.filter(this.createFilter(queryString)) : allData;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) != -1;
      };
    },
    async getAllData() {
      this.allData = [];
      const res = await this.$api.sgsCspcBillList({
        vehicleIds: [],
        status: null,
        start: null,
        end: null,
        sapCode: null,
        carrierName: null,
        vehicle: null,
        materiel: null,
        materialType: null,
        sender: null,
        sendType: null,
        ghs: null,
        transcribe: null,
        viewStatus:null
      })
      if (res.status == 200 && res.data.length) {
        this.allData = res.data.filter((item) => item.status != -2);
        let map = new Map();
        if (this.allData.length) {
          this.carrierNameList = this.allData
            .filter((item) => {
              return !map.has(item["carrierName"].toString()) && map.set(item["carrierName"].toString());
            })
            .map((item) => {
              return {
                name: item["carrierName"],
                value: item["carrierName"],
              };
            });
        }
      }
    },
    async handleJumpLink() {
      let parmas = this.$route.query;
      if (!parmas || !parmas.id) return;
      if (parmas.vehicleId) {
        await this.$refs["vehicleTree"].waitForInit;
        this.$refs["vehicleTree"].$refs["tree"].setCheckedKeys([parmas.vehicleId]);
        let node = this.$refs["vehicleTree"].$refs["tree"].getNode(parmas.vehicleId);
        node.expand(null, true);
        this.queryList.vehicleIds = [parmas.vehicleId];
      }
      if (parmas.endBillTime || parmas.startBillTime) {
        this.selectStartEndTime = [moment(parmas.startBillTime).valueOf(), moment(parmas.endBillTime).valueOf()];
      }
      this.queryList.id = parmas.id;

      this.$nextTick(async () => {
        await this.search();
        await this.$router.push("/home/<USER>");
        // this.table.list = this.table.list.filter(item=>item.read==0)
      });
    },
    toJump(row) {
      let obj = {
        vehicleId: row.vehicleId,
        start: row.sendTime ? moment(row.sendTime).valueOf() : moment().startOf("day").valueOf(),
        end: row.receiverTime ? moment(row.receiverTime).valueOf() : moment().endOf("day").valueOf(),
        read: -1,
      };
      this.$router.push({
        path: "/home/<USER>",
        query: obj,
      });
    },
    settable() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    // 点击设置表格按钮
    operateTableSetting() {
      this.$refs.tableShowConfigList.showModel();
    },
    getSetValue(val) {
      if (!val) {
        this.tableSettingList = this.defaultSettingList;
      } else {
        this.tableSettingList = val;
      }
    },
    selectNodes(current, { checkedNodes }) {
      this.queryList.vehicleIds = checkedNodes.filter((item) => item.type == 4).map((item) => item.id);
    },

    async search() {
      this.table.list = [];
      this.table.loading = true;
      const res = await this.$api.sgsCspcBillList(this.queryList);
      this.queryList.id = "";
      this.table.loading = false;
      if (res.status !== 200) {
        return this.$error(res.messgae || "查询失败！");
      }
      if (res.data.length === 0) {
        return this.$warning("暂无数据!");
      }
      this.table.list = res.data.filter((item) => item.status != -2);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    async getType() {
      this.typeListPoint = [];
      let resultList = await Promise.all([
        // this.$api.getSysDictByCode({code: 'waste_collect_trash_type'}),
        this.$api.getSysDictByCode({ code: "waste_collect_point_type" }),
      ]);
      if (resultList[0]) {
        this.typeListPoint = resultList[0];
      }
      // if (resultList[1]) {
      //     this.typeListPoint = resultList[1]
      // }
    },
    async exportDataInfo() {
      if (this.table.list.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }
      let sheetHeader = [];
      this.tableSettingList.forEach((item) => {
        sheetHeader.push(`${item.name}@${item.key}@8000@000000`);
      });
      let excelBody = this.table.list.map((row) => {
        return Object.assign({}, row, {
          status: (row.status || row.status == 0) ? this.statusObj[row.status] : '',
          sendType: row.sendType == 1 ? '配送' : row.sendType == 2 ? '自提' : '',
          materialType: row.materialType == 1 ? '固体' : row.materialType == 2 ? '液体' : '',
          ghs: row.ghs ? '是' : '否',
          transcribe: row.transcribe ? '是' : '否',
          recordingSuccessful: row.recordingSuccessful? '是' : '否',
          failureReason:row.recordingSuccessful?"": row.failureReason? 'G7视频录制失败' : '待上传',
          viewStatus: row.viewStatus  ? '已观看' : '未观看',
          receiverTime: this.TimeFormat(row.receiverTime),
          sendTime: this.TimeFormat(row.sendTime),
          recordingDuration: this.$options.filters.formatTime(row.recordingDuration),
        });
      });
      let params = {};
      let paramsList = [];
      params = {
        sheetName: "LMS运单",
        title: "LMS运单",
        headers: sheetHeader,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.exportLoading = true;

      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "LMS运单" + ".xlsx");
      this.exportLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.lmsWayBill {
  .query-top {
    height: calc(100% - 50px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .query-list {
    display: flex;
  }
}
/deep/ .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-select .el-tag__close.el-icon-close {
  top: -7px;
  right: -4px;
}

.option-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
