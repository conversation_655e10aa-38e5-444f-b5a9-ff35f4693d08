<template>
  <Layout :has-color="true" :contentLoading="table.loading" class="LockStatusQuery">
    <template slot="aside">
      <div class="query-top">
        <ElementTree type="vehicle" ref="tree" :checkMode="true" @check="selectNodes"></ElementTree>
      </div>
    </template>
    <template slot="query">
      <div class="query-item">
        <StartEndTime
          showType="heng"
          itemHeight="35"
          :timeTitle="['开始时间:','送达时间:']"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="datetime"
          titleStyle="font-size:12px;margin-right:5px;display:inline-block;text-align: justify;
                text-align-last: justify;"
        >
        </StartEndTime>
      </div>
      <div class="query-item">
        <span>SAP订单号：</span>
        <el-autocomplete
          class="inline-input"
          v-model="query.sapCode"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'sapCode');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>承运商名称：</span>
        <el-select v-model="query.carrierName" multiple collapse-tags clearable>
          <el-option v-for="(item, index) in carrierNameList" :key="item.value" :label="item.name" :value="item.value">
            <div class="option-text" :title="item.name">{{ item.name }}</div>
          </el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>车牌号：</span>
        <el-autocomplete
          class="inline-input"
          v-model="query.vehicle"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'vehicle');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>开始时间地址：</span>
        <el-autocomplete
          class="inline-input"
          v-model="query.startLoc"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'startLoc');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>结束时间地址：</span>
        <el-autocomplete
          class="inline-input"
          v-model="query.endLoc"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'endLoc');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>物料：</span>
        <el-autocomplete
          class="inline-input"
          v-model="query.materiel"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'materiel');
            }
          "
          placeholder="请输入内容"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <span>物料类型：</span>
        <el-select v-model="query.materialType" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="1" label="固体"></el-option>
          <el-option :value="2" label="液体"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>配送方式：</span>
        <el-select v-model="query.sendType" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="1" label="配送"></el-option>
          <el-option :value="2" label="自提"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>是否危化品：</span>
        <el-select v-model="query.ghs" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>

          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option
        ></el-select>
      </div>
      <div class="query-item">
        <span>是否有轨迹：</span>
        <el-select v-model="query.gps" style="width: 100px">
          <el-option :value="null" label="全部"></el-option>
          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="queryTimelist" :loading="table.loading">查询</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="operateTableSetting">表格显示配置</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="exportDataInfo()">导出</el-button>
      </div>
    </template>

    <template slot="content">
      <el-table
        ref="breaktable"
        slot="content"
        class="table"
        border
        stripe
        highlight-current-row
        size="mini"
        :data="formatList"
        height="100%"
        style="width: 100%"
      >
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="70"> </el-table-column>
        <el-table-column label="操作" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button
              style="width: auto"
              @click="toTrackPlayback(scope.row, 1)"
              type="text"
              size="small"
              :disabled="!scope.row.endTime"
              >回放</el-button
            >
            <el-button
              style="width: auto"
              @click="toTrackPlayback(scope.row, 0)"
              type="text"
              size="small"
              :disabled="!scope.row.returnTime"
              >返厂回放</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableSettingList"
          :key="index"
          :min-width="item.size + 'px'"
          header-align="center"
          :align="item.align"
          :prop="item.key"
          :label="item.name"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <el-button type="text" @click="toJumpLMS(row)" style="width: auto; cursor: pointer" v-if="item.key == 'sapCode'">{{
              row.sapCode
            }}</el-button>
            <span v-if="item.key == 'sendType'">{{ row.sendType == 1 ? "配送" : row.sendType == 2 ? "自提" : "" }}</span>
            <span v-if="item.key == 'materialType'">{{
              row.materialType == 1 ? "固体" : row.materialType == 2 ? "液体" : ""
            }}</span>
            <span v-if="item.key == 'ghs'">{{ row.ghs ? "是" : "否" }}</span>

            <span v-if="item.type !== 1">{{ row[item.key] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template slot="footer">
      <div class="query-item">
        <el-pagination
          background
          small
          :pager-count="5"
          :current-page.sync="pager.current"
          layout="prev, pager, next, total"
          :page-size="pager.size"
          :total="pager.total"
        >
        </el-pagination>
      </div>
    </template>
    <TableShowConfigList
      ref="tableShowConfigList"
      v-model="tableSettingList"
      :list="allSettingList"
      @tableValue="getSetValue"
      pageName="malfunctionDetails"
      :isSave="true"
      :defaultSetting="defaultSettingList"
      @change="settable"
    >
    </TableShowConfigList>
  </Layout>
</template>
<script>
import { allSettingList, defaultSettingList } from "./MalfunctionDetailsTable.js";
import StartEndTime from "@/components/common/StartEedTime";
import TableShowConfigList from "@/view/report/components/TableShowConfigList";
export default {
  name: "malfunctionDetails",
  components: { StartEndTime, TableShowConfigList },
  data() {
    return {
      table: {
        loading: false,
        data: [],
      },
      selectStartEndTime: [moment().subtract(1,'day').startOf("day").valueOf(), moment().subtract(1,'day').endOf("day").valueOf()],

      query: {
        vehicleIds: [],
        operateType: 30,
        start: moment().subtract(1,'day').startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        end: moment().subtract(1,'day').endOf("days").format("YYYY-MM-DD HH:mm:ss"),
        sapCode: null,
        carrierName: null,
        vehicle: null,
        materiel: null,
        materialType: null,
        sendType: null,
        ghs: null,
        startLoc: null,
        endLoc: null,
        gps: null,
      },
      allSettingList,
      defaultSettingList,
      pager: {
        current: 1,
        size: 30,
        total: 0,
      },
      exportLoading: false,
      tableSettingList: [],
      carrierNameList: [],
    };
  },

  computed: {
    pageStart() {
      return (this.pager.current - 1) * this.pager.size;
    },
    formatList() {
      return this.table.data.slice((this.pager.current - 1) * this.pager.size, this.pager.current * this.pager.size);
    },
  },
  watch: {
    selectStartEndTime: function (newVal, oldVal) {
      this.query.start = newVal[0] ? moment(newVal[0]).format("YYYY-MM-DD HH:mm:ss") : "";
      this.query.end = newVal[1] ? moment(newVal[1]).format("YYYY-MM-DD HH:mm:ss") : "";
    },
  },
  async mounted() {
    await this.getAllData();
  },
  methods: {
    querySearch(queryString, cb, type) {
      let map = new Map();
      let allData = [];
      if (this.allData.length) {
        allData = this.allData
          .filter((item) => {
            if (item[type]) {
              return !map.has(item[type].toString()) && map.set(item[type].toString());
            }
          })
          .map((item) => {
            return {
              name: item[type],
              value: item[type],
            };
          });
      }
      var results = queryString ? allData.filter(this.createFilter(queryString)) : allData;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) != -1;
      };
    },
    async getAllData() {
      this.allData = [];
      const res = await this.$api.cspcOperate({
        vehicleIds: [],
        operateType: 30,
        start: null,
        end: null,
        sapCode: null,
        carrierName: null,
        vehicle: null,
        materiel: null,
        materialType: null,
        sendType: null,
        ghs: null,
        startLoc: null,
        endLoc: null,
        gps: null,
      });
      if (res.status == 200 && res.data.length) {
        this.allData = res.data.map((item) => {
          let obj = {};
          for (let key in item) {
            obj[key] = item[key];
          }

          for (let key in item.sgsCspcBillRecord) {
            if (!obj.hasOwnProperty(key)) {
              obj[key] = item.sgsCspcBillRecord[key];
            }
          }
          return obj;
        });
      }
      const listRes = await this.$api.getCarrierList();
      if (listRes.status == 200 && listRes.data.length) {
        this.carrierNameList = listRes.data.map((item) => {
          return {
            name: item,
            value: item,
          };
        });
      }
    },
     // 点击设置表格按钮
     operateTableSetting() {
      this.$refs.tableShowConfigList.showModel();
    },
    getSetValue(val) {
      if (!val) {
        this.tableSettingList = this.defaultSettingList;
      } else {
        this.tableSettingList = val;
      }
    },
    settable() {
      this.$nextTick(() => {
        this.$refs.breaktable.doLayout();
      });
    },
    // 点击SAP订单号跳转至LMS运单并根据订单号进行查询
    toJumpLMS(row) {
      let obj = {
        startBillTime: row.sgsCspcBillRecord.sendTime,
        endBillTime: row.sgsCspcBillRecord.receiverTime,
        vehicleId: row.sgsCspcBillRecord.vehicleId,
        id: row.sgsCspcBillRecord.id,
      };
      this.$router.push({
        path: "/home/<USER>",
        query: obj,
      });
    },
    // 点击回放跳转至轨迹回放
    toTrackPlayback(row, type) {
      let obj = {
        vehicleId: row.vehicleId,
        startTime: type ? row.startTime : row.endTime,
        endTime: type ? row.endTime : row.returnTime,
      };
      this.$router.push({
        path: "/home/<USER>",
        query: obj,
      });
    },

    //   初始查询
    // treeMounted($tree) {
    //     let rootNodes = $tree.data.map($tree.getNode);
    //     this.currentNodes = rootNodes[0].data.id;
    //     this.queryfacelist();
    // },

    // 查询
    async queryTimelist(page) {
      this.table.data = [];
      this.pager.total = 0;

      this.table.loading = true;
      let res = await this.$api.cspcOperate(this.query);
      if (!res || res.status !== 200) {
        this.$error(res.message);
        this.table.loading = false;
        return;
      }
      if (!res.data || !res.data.length) {
        this.$warning("未查询到数据");
        this.table.loading = false;
        return;
      }
      this.table.data = res.data.map((item) => {
        let obj = {};
        for (let key in item) {
          obj[key] = item[key];
        }

        for (let key in item.sgsCspcBillRecord) {
          if (!obj.hasOwnProperty(key)) {
            obj[key] = item.sgsCspcBillRecord[key];
          }
        }
        return obj;
      });
      this.pager.total = res.data.length;
      this.table.loading = false;
      this.$nextTick(() => {
        this.$refs["breaktable"].doLayout();
      });
    },
    //导出表格
    async exportDataInfo() {
      if (this.table.data.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }

      let sheetHeader = [];
      this.tableSettingList.forEach((item) => {
        sheetHeader.push(`${item.name}@${item.key}@8000@000000`);
      });
      let excelBody = this.table.data.map((row) => {
        return Object.assign({}, row, {
          sendType: row.sendType == 1 ? "配送" : row.sendType == 2 ? "自提" : "",
          materialType: row.materialType == 1 ? "固体" : row.materialType == 2 ? "液体" : "",
          ghs: row.ghs ? "是" : "否",
        });
      });
      let params = {};
      let paramsList = [];
      params = {
        sheetName: "运单送货轨迹明细",
        title: "运单送货轨迹明细",
        headers: sheetHeader,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.exportLoading = true;

      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "运单送货轨迹明细" + ".xlsx");
      this.exportLoading = false;
    },
    // 多选树
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes.filter((item) => item.type >= 4).map((item) => item.id);
      this.query.vehicleIds = currentNodes;
    },
  },
};
</script>
<style lang="scss" scoped>
.LockStatusQuery {
  .query-top {
    height: 100%;
  }

  .query-bottom {
    margin-top: 5px;
    /* height: 125px; */
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > div {
        flex-grow: 1;
      }

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }
  .image {
    position: relative;
    height: 40px;
    width: 40px;
    // right: 0;
    // top: 50%;
    // margin-top: -25px;
    // margin: 10px auto;
    // margin: 0 18px;
    margin: auto;
    .mask {
      height: 40px;
      position: absolute;
      left: 0;
      top: 0px;
      width: 40px;
      background-color: rgba(0, 0, 0, 0.5);
      line-height: 40px;
      text-align: center;
      font-size: 20px;
      pointer-events: none;
      color: #fff;
    }
  }
}
/deep/ .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-select .el-tag__close.el-icon-close {
  top: -7px;
  right: -4px;
}

.option-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
