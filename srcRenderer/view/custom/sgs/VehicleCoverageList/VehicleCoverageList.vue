<template>
  <Layout tag="div" class="lmsWayBill" :contentLoading="table.loading" :has-color="true">
    <template slot="query">
      <div class="query-item">
        <span>承运商名称：</span>
        <el-select v-model="queryList.carrierName" multiple collapse-tags clearable>
          <el-option v-for="(item, index) in carrierNameList" :key="item.value" :label="item.name" :value="item.value">
            <div class="option-text">{{ item.name }}</div>
          </el-option>
        </el-select>
      </div>
      
      <div class="query-item">
        <span>车牌号：</span>
        <el-autocomplete
          class="inline-input"
          v-model="queryList.vehicle"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'vehicle');
            }
          "
          placeholder="请输入车牌号"
        ></el-autocomplete>
      </div>
      <div class="query-item">
        <StartEndTime
          showType="heng"
          itemHeight="35"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="date"
          titleStyle="font-size:12px;margin-right:5px;display:inline-block;text-align: justify;
                  text-align-last: justify;"
        >
        </StartEndTime>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="search" :loading="table.loading">查询</el-button>
      </div>

      <div class="query-item">
        <el-button type="primary" @click="exportDataInfo" :loading="exportLoading">导出</el-button>
      </div>
    </template>
    <el-table
      ref="table"
      class="el-table--ellipsis el-table--radius"
      slot="content"
      border
      stripe
      highlight-current-row
      size="mini"
      :data="formatList"
      height="100%"
    >
      <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" min-width="50"></el-table-column>
      <el-table-column prop="vehicle" label="主车牌号" width="150"></el-table-column>
      <el-table-column prop="carrierName" label="承运商名称" width="350" show-overflow-tooltip></el-table-column>
      <el-table-column prop="time" label="首次监控日期" width="180" sortable></el-table-column>
      <el-table-column min-width="100"></el-table-column>
    </el-table>

    <template slot="footer">
      <div class="query-item">
        <el-pagination
          small
          background
          :current-page.sync="table.page"
          :page-size="table.size"
          layout="prev, pager, next, total"
          :total="table.list.length"
        >
        </el-pagination>
      </div>
    </template>
  </Layout>
</template>

<script>
const ExportJsonExcel = require("js-export-excel");
import StartEndTime from "@/components/common/StartEedTime";
export default {
  name: "vehicleCoverageList",
  components: {
    StartEndTime,
  },
  data() {
    return {
      table: {
        loading: false,
        list: [],
        page: 1,
        size: 30,
        total: 0,
      },
      selectStartEndTime: [moment().subtract(1,'day').startOf("day").valueOf(), moment().subtract(1,'day').endOf("day").valueOf()],
      tableSettingList: [],
      queryList: {
        start: moment().subtract(1,'day').startOf("day").format("YYYY-MM-DD"),
        end: moment().subtract(1,'day').endOf("day").format("YYYY-MM-DD"),
        carrierName: null,
        vehicle: null,
        operateType:50
      },
      exportLoading: false,
      allData: [],
      carrierNameList: [],
    };
  },
  watch: {
    selectStartEndTime: function (newVal, oldVal) {
      this.queryList.start = newVal[0] ? moment(newVal[0]).format("YYYY-MM-DD") : "";
      this.queryList.end = newVal[1] ? moment(newVal[1]).format("YYYY-MM-DD") : "";
    },
  },
  filters: {
    formatTime(value) {
      if (!value && value != 0) return "";
      if (value == 0) return 0;
      var secondTime = parseInt(value);
      var minuteTime = 0;
      var hourTime = 0;
      var dayTime = 0;
      var result = "";
      if (secondTime > 60) {
        minuteTime = parseInt(secondTime / 60);
        secondTime = parseInt(secondTime % 60);
        if (minuteTime > 60) {
          hourTime = parseInt(minuteTime / 60);
          minuteTime = parseInt(minuteTime % 60);
          if (hourTime > 24) {
            dayTime = parseInt(hourTime / 24);
            hourTime = parseInt(hourTime % 24);
          }
        }
      }
      if (secondTime > 0) {
        minuteTime += 1;
      }
      if (minuteTime >= 0) {
        result = "" + parseInt(minuteTime) + " 分钟 " + result;
      }
      if (hourTime > 0) {
        if (minuteTime == 0) {
          result = "" + parseInt(hourTime) + " 小时 ";
        } else {
          result = "" + parseInt(hourTime) + " 小时 " + result;
        }
      }
      if (dayTime > 0) {
        result = "" + parseInt(dayTime) + " 天 " + "" + parseInt(hourTime) + "小时";
      }
      return result;
    },
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },
    formatList() {
      return this.table.list.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  mounted() {
    this.getAllData();
  },
  activated() {},
  methods: {
    querySearch(queryString, cb, type) {
      let map = new Map();
      let allData = [];
      if (this.allData.length) {
        allData = this.allData
          .filter((item) => {
            return !map.has(item[type].toString()) && map.set(item[type].toString());
          })
          .map((item) => {
            return {
              name: item[type],
              value: item[type],
            };
          });
      }
      var results = queryString ? allData.filter(this.createFilter(queryString)) : allData;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) != -1;
      };
    },
    async getAllData() {
      this.allData = [];
      const res = await this.$api.getCarrierList()
      if (res.status == 200 && res.data.length) {
        this.carrierNameList = res.data.map(item=>{
          return {
            name: item,
            value:item
          }
        })
        // let map = new Map();
        // if (this.allData.length) {
        //   this.carrierNameList = this.allData
        //     .filter((item) => {
        //       return !map.has(item["carrierName"].toString()) && map.set(item["carrierName"].toString());
        //     })
        //     .map((item) => {
        //       return {
        //         name: item["carrierName"],
        //         value: item["carrierName"],
        //       };
        //     });
        // }
      }
      let allRes = await this.$api.cspcOperate({
        start:null,
        end: null,
        carrierName: null,
        vehicle: null,
        operateType:50
      });
      if(allRes.status == 200 && allRes.data.length){
        this.allData = allRes.data
      }
    },
    async search() {
      this.table.list = [];
      this.table.loading = true;
      const res = await this.$api.cspcOperate(this.queryList);
      this.queryList.id = "";
      this.table.loading = false;
      if (res.status !== 200) {
        return this.$error(res.messgae || "查询失败！");
      }
      if (res.data.length === 0) {
        return this.$warning("暂无数据!");
      }
      this.table.list = res.data.filter((item) => item.status != -2);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    async exportDataInfo() {
      if (this.table.list.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }
      let sheetHeader = ["主车牌号@vehicle@8000@00000", "承运商名称@carrierName@20000@00000", "首次监控日期@time@10000@00000"];

      let excelBody = this.table.list.map((row) => {
        return Object.assign({}, row, {});
      });
      let params = {};
      let paramsList = [];
      params = {
        sheetName: "普货车辆覆盖清单",
        title: "普货车辆覆盖清单",
        headers: sheetHeader,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.exportLoading = true;

      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "普货车辆覆盖清单" + ".xlsx");
      this.exportLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.lmsWayBill {
  .query-top {
    height: calc(100% - 50px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .query-list {
    display: flex;
  }
}
/deep/ .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-select .el-tag__close.el-icon-close {
  top: -7px;
  right: -4px;
}

.option-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
