<template>
    <Layout :has-color="true" :contentLoading="table.loading" class="LockStatusQuery">
        <template slot="aside">
            <div class="query-top">
                <ElementTree type="vehicle" ref="tree" :checkMode="true" @check="selectNodes">
                </ElementTree>
            </div>
            <div class="query-bottom bg bg--light box-shadow">
                <StartEndTime itemHeight="35"
                              v-model="selectStartEndTime" valueFormat="timestamp" timeType="date" :isLimit="true"
                              :timeLimitForCur="1" :timeLimit="30">
                </StartEndTime>
                <div class="query-item">
                    <span>变化量≥:</span>
                    <el-input-number v-model="search.change" :min="10" :max="100" style="width: 100%"></el-input-number>
                    %
                </div>
                <div class="query-item">
                    <el-button type="primary" style="width: 100%" @click="searchList()" :loading="table.loading">查询</el-button>
                </div>
            </div>
        </template>
        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="exportDataInfo()" :loading="exportLoading">导出
                </el-button>
            </div>
            <div class="break-item"></div>
            <div>
                <el-pagination background small :pager-count="5" :current-page.sync="pager.current"
                               layout="prev, pager, next, total" :page-size="pager.size" :total="pager.total">
                </el-pagination>
            </div>
        </template>
        <template slot="content">
            <el-table ref="breaktable" slot="content" class="el-table--ellipsis el-table--radius" border stripe
                      highlight-current-row size="mini" :data="formatList" height="100%"
                      style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="70">
                </el-table-column>
                <el-table-column prop="companyName" label="操作" min-width="50">
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" title="数据趋势">
                            <i class="pony-iconv2 pony-youhao" @click="detail(scope.row)"></i>
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="dept" label="单位" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="plateNo" label="车牌号" min-width="60" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="before" label="加油前液位（%）" min-width="80" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="after" label="加油后液位（%）" min-width="80" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="change" label="加油量（%）" min-width="80" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="changeMeasure" label="加油量（L）" min-width="80" show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{Math.floor(scope.row.changeMeasure)}}
                    </template>
                </el-table-column>
                <el-table-column prop="time" label="加油时间" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="location" label="加油位置" min-width="180" show-overflow-tooltip>
                </el-table-column>
            </el-table>
        </template>
        <RefuelDialog ref="refuelDialog"></RefuelDialog>
    </Layout>
</template>
<script>
import StartEndTime from "@/components/common/StartEedTime";
import RefuelDialog from "./components/RefuelDialog";
import moment from "moment";

export default {
    name: 'Refuel',
    components: {StartEndTime, RefuelDialog},
    data() {
        return {
            table: {
                loading: false,
                data: [],
            },
            pager: {
                current: 1,
                size: 30,
                total: 0,
            },
            status: true,
            search: {
                // 能源类型(0:油车, 1:电车)
                energyType: 0,
                // 油量电量变化类型(0:变大, 1:变小)
                changeType: 0,
                ids: [],
                start: '',
                end: '',
                change: 10
            },
            selectStartEndTime: [
                moment().subtract(7, "days").startOf("day").valueOf(),
                moment().subtract(1, "days").endOf("day").valueOf()
            ],
            exportLoading: false,
            vehicle: []
        };
    },
    watch: {
        selectStartEndTime: {
            handler: function (newVal, oldVal) {
                this.search.start = moment(newVal[0]).format('YYYY-MM-DD')
                this.search.end = moment(newVal[1]).format('YYYY-MM-DD')
            },
            immediate: true
        },
    },
    computed: {
        pageStart() {
            return (this.pager.current - 1) * this.pager.size;
        },
        formatList() {
            return this.table.data.slice(
             (this.pager.current - 1) * this.pager.size,
             this.pager.current * this.pager.size
            );
        },
    },
    mounted() {
        this.searchList()
    },
    methods: {
        // 多选树
        selectNodes(current, {checkedNodes}) {
            this.search.ids = checkedNodes
             .filter((item) => item.type == 4)
             .map((item) => item.id);
            if (checkedNodes.length === 1) {
                this.vehicle = checkedNodes.filter(item => item.type == 4).map(item => item.name)
            }
        },
        async searchList() {
            if (!this.search.start && !this.search.end) {
                this.$warning('请选择起止时间')
                return
            }
            // if (!this.search.ids.length) {
            //     this.$warning('请选择车辆!')
            //     return
            // }
            if (typeof this.search.change !== 'number' || this.search.change < 10) {
                return this.$warning('请输入正确的变化量！')
            }
            this.table.data = []
            this.pager.total = 0
            this.table.loading = true;
            let res = await this.$api.refuel(this.search, true);
            if (!res || res.status !== 200) {
                if(res.status == 999)return
                this.$error(res.message || "查询失败");
                this.table.loading = false;
                return
            }
            if (!res.data || !res.data.length) {
                this.$warning("未查询到数据");
                this.table.loading = false;
                return;
            }
            if (res.status === 200) {
                this.table.data = res.data
                this.pager.total = res.data.length
                this.table.loading = false
            }
            this.$nextTick(() => {
                this.$refs.breaktable.doLayout()
            })
        },
        detail(row) {
            let day = moment(row.time).format('YYYY-MM-DD')
            this.$refs.refuelDialog.showDialog({
                plateNo: row.plateNo,
                vehicleId: row.vehicleId,
                start: day,
                end: day
            })
        },
        async exportDataInfo() {
            if (this.table.data.length == 0) {
                this.$warning("没有数据可以导出");
                return;
            }
            let sheetHeader = [
                "单位@dept@8000@000000",
                "车牌号@plateNo@8000@000000",
                "加油前液位（%）@before@8000@000000",
                "加油后液位（%）@after@8000@000000",
                "加油量（%）@change@8000@000000",
                "加油时间@time@8000@000000",
                "加油位置@location@8000@000000",
            ]
            
            let excelBody = this.table.data.map(item => {
                return {
                    dept: item.dept,
                    plateNo: item.plateNo,
                    before: item.before,
                    after: item.after,
                    change: item.change,
                    time: item.time,
                    location: item.location,
                }
            })
            
            let params = {}
            let paramsList = [];
            
            let vehicle = ""
            if (this.search.ids.length == 1) {
                vehicle = this.vehicle[0]
            }
            let fileName = `${moment(this.search.start).format(
             "YYYY-MM-DD"
            )} ~ ${moment(this.search.end).format(
             "YYYY-MM-DD"
            )}--${vehicle}加油记录报表`
            let title = `${vehicle}加油记录报表(${moment(this.search.start).format(
             "YYYY-MM-DD"
            )} ~ ${moment(this.search.end).format(
             "YYYY-MM-DD"
            )} )`
            
            params = {
                sheetName: "加油记录报表",
                title: title,
                headers: sheetHeader,
                dataList: excelBody,
            }
            paramsList.push(params)
            this.exportLoading = true;
            
            await this.$utils.jsExcelExport(JSON.stringify(paramsList), fileName + '.xlsx')
            this.exportLoading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.LockStatusQuery {
    .query-top {
        height: calc(100% - 184px);
    }
    
    .query-bottom {
        margin-top: 5px;
        height: 180px;
        padding: 10px;
        
        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
            
            > div {
                flex-grow: 1;
            }
            
            > span {
                width: 65px;
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
</style>
