<template>
   <PonyDialog
            :show="show"
            :loading="loading"
            :title="title"
            :hasFooter="false"
            :allowClose="false"
            smallIndex
            :isFullscreen="true"
            :bottom="true" :draggable="false" :allFull="true"
            contentStyle="padding:10px;background-color:var(--background-color-light);max-height:max-content"
        >
        
        <div slot="header" style="line-height:35px">
            <span>{{title}}</span>
            <el-tag style="vertical-align: 2px;margin-left:5px" size="medium">{{dealList[rowData.status]}}</el-tag>
            
            
        </div>
        <div class="table">
            <el-table ref="breaktable" slot="content" class="el-table--ellipsis el-table--radius" border stripe
                        @row-click="tableclick"
                      highlight-current-row size="mini" :data="dataList" height="calc(100% - 40px)"
                      style="width: 100%">
                <el-table-column prop="time" label="时间" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <!-- <el-table-column prop="value_rpm" label="转速(r/min)" min-width="60" show-overflow-tooltip>
                </el-table-column> -->
                <el-table-column prop="value_speed" label="车速(km/h)" min-width="60" show-overflow-tooltip>
                    <template slot="header"><span>车速(km/h)<i class="pony-iconv2 pony-guijihuifang headicon" title="轨迹回放" @click="toJump('/home/<USER>')"></i></span></template>
                </el-table-column>
                <el-table-column prop="value_energy" label="车速(km/h)" min-width="60" show-overflow-tooltip>
                    <template slot="header"><span>油量(%)<i class="pony-iconv2 pony-youhao headicon" title="历史明细" @click="toJump('/home/<USER>')"></i></span></template>
                </el-table-column>
            </el-table>
            <div class="buttonDiv">
              <el-button
                style="margin-left:5px;margin-top:3px"
                type="primary"
                class="fr"
                size="mini"
                @click="showText(1)"
                >确认</el-button
              >
              <el-button
                  style="margin-left:5px;margin-top:3px"
                  type="primary"
                  class="fr"
                  size="mini"
                  @click="showText(3)"
                  >误报</el-button
              >
              <el-button
                  style="margin-left:5px;margin-top:3px"
                  type="primary"
                  class="fr"
                  size="mini"
                  @click="changeRow(0)"
                  >下一条</el-button
              >
              <el-button
                  style="margin-left:5px;margin-top:3px"
                  type="primary"
                  class="fr"
                  size="mini"
                  @click="changeRow(1)"
                  >上一条</el-button
              >
              <el-button
            style="margin-top:3px"
                class="fr"
                type="primary"
                size="mini"
                @click="show = false"
                >关闭</el-button
            >
            </div>
        </div>
        <div class="right">
            <div class="map">
                <TimeBox ref="timeBox"></TimeBox>
                <SimpleMap ref="simplemapref" mapConfig="justmap" :mapLittleTool="['scale']" @mapReady="generateMapObj" :noDelay="true"></SimpleMap>
                <Popup ref='vehicle' :minWidth="350" :maxWidth="400" :auto-close='false' :close-on-click='false'>
                    <div class="monitorpupop" v-if="currentInfo">
                        <div class="monitorpupop__header bor--b dfb">
                            <span class="fon--m" style="font-size: 14px;">{{ plateNo }}</span>
                        </div>
                        <div class="monitorpupop__content dfbw">
                            <DataRow v-for="(item, index) in setData" :key="index"
                                type="popup"
                                :size="item.size"
                                width="75px"
                                :title="item.name"
                                :value="currentInfo[item.key]"
                                :tip="item.tip"
                                :color="item.key"
                                >
                            </DataRow>
                        </div>
                    </div>
                </Popup>
            </div>
            <div class="chart" ref="container"></div>
        </div>
        <PonyDialog
            :show="textPony.show"
            :loading="textPony.loading"
            :title="textPony.title"
            :width="700"
            contentStyle="padding:30px 10px 10px 10px;background-color:var(--background-color-light);height:500px"
            @confirm="handleConfirm"
            @close="textPony.show = false"
            :okText="textPony.title"
        >
        <div class="textModel">
          <span v-for="(item,index) in ttsmodelist.slice(0,19)" :title="item.configDesc" :key="index" :class="currentTTS == index + 1?'active':''" @click="changeTTS(index+1)">{{item.configDesc}}</span>
          <el-dropdown
              v-if="ttsmodelist.length>20"
              @command="changeTTS"
              trigger="click"
              >
              <span style="padding:3px 4px"><i class="pony-iconv2 pony-gengduo"></i></span>
              <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item,index) in ttsmodelist" :key="index" :command="index+1">{{item.configDesc}}</el-dropdown-item>
              </el-dropdown-menu>
          </el-dropdown>

          <span style="padding:3px 4px"><i class="pony-iconv2 pony-jia" @click="jumpToTTS()" ></i></span>
        </div>
        <div class="form">
          <el-form :model="textPony" label-width="80px">
            <el-form-item label="情况说明: " prop="remark">
              <el-input type="textarea" v-model="textPony.remark" :rows="5"></el-input>
            </el-form-item>
          </el-form>
        </div>
        </PonyDialog>
        </PonyDialog>

</template>

<script>
import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
import Popup from '@/view/monitor/components/Popup'
import DataRow from '@/view/monitor/components/DataRow'
import { getMapBasicIconRequire } from "@/view/monitor/util/monitorUtil";
import moment from "moment";
import TimeBox from "./TimeBox";

const option = {
    grid: {
        left: 70,
        top: 60,
        right: 70,
        bottom: 80,
    },
    tooltip: {
        trigger: 'axis',
        formatter: function (params) {
            let htmlStr = ''
            htmlStr += params[0].axisValueLabel + '<br/>';//x轴的名称
            for (let i = 0; i < params.length; i++) {
                let param = params[i];
                let seriesName = param.seriesName
                let value = param.value[1] ? Math.floor(param.value[1]) : '-'
                let color = param.color;//图例颜色
                htmlStr += `<div style="display: flex;justify-content: space-between;align-items:center">
                               <div>
                                  <span style="display: inline-block; background-color: ${color}; width: 10px; height: 10px;border-radius: 50%; margin-right: 10px"></span>
                                  ${seriesName}
                               </div>
                               ${value}${i == 0 ? '%' : 'km/h'}
                            </div>`;
            }
            return htmlStr
        },
        axisPointer: {
            type: 'cross',
            animation: false,
            label: {
                backgroundColor: '#505765',
            },
            
        },

        
    },
    legend: {
        data: ['油量', '车速'],
        right: 10,
    },
    dataZoom: [
        {
            show: true,
            realtime: true,
        },
    ],
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            axisLine: {onZero: false},
            data: [],
            axisPointer: {
                type:'line',
                        triggerTooltip: false,  value: 0,
                        lineStyle: { color: '#888888', opacity: 0.5, width: 3 },
                        handle: { show: true,margin:120 }
                    }
        },
    ],
    
    yAxis: [
        {
            name: '油量(%)',
            type: 'value',
            min: 0,
            max: 100,
            splitNumber: 5
        },
        {
            name: '车速(km/h)',
            alignTicks: true,
            type: 'value',
            min: 0,
            max: 150,
            splitNumber: 5
        },
    ],
    series: [
        {
            name: '油量',
            type: 'line',
            lineStyle: {
                width: 1,
            },
            itemStyle: {
                color: 'rgb(149, 199, 126)'
            },
            emphasis: {
                focus: 'series',
            },
            areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(149, 199, 126, 1)'
                    },
                    {
                        offset: 1,
                        color: 'rgba(149, 199, 126, 0)'
                    }
                ])
            },
            markArea: {
                silent: true,
                itemStyle: {
                    opacity: 0.3,
                    color: 'rgba(255, 118, 118)'
                },
                data: [],
            },
            data: [],
            connectNulls: true
        },
        {
            name: '车速',
            type: 'line',
            yAxisIndex: 1,
            lineStyle: {
                width: 1,
            },
            itemStyle: {
                color: 'rgb(80, 114, 196)'
            },
            emphasis: {
                focus: 'series',
            },
            areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(61, 132, 247, 1)'
                    },
                    {
                        offset: 1,
                        color: 'rgba(61, 132, 247, 0)'
                    }
                ])
            },
            data: [],
            connectNulls: true
        },
        {
            name: '异常时间',
            type: 'line',
            markLine: {
                label: {
                    formatter: '异常时间'
                },
                lineStyle:{
                    width:2,
                    type:'solid'
                },
                symbol:'none',//去掉箭头
                data: []
            }
        }
    ],
};
export default {
    name: 'detailShow',
    components:{SimpleMap,Popup,DataRow,TimeBox},

    data() {
        return {
            show:false,
            loading: false,
            dataList:[],
            plateNo:'',
            title:'',
            allInfo:{},
            _markerCluster:null,
            _linePointMarker:null,
            vehicleMarkerInfo:null,
            currentInfo:null,
            gpsList:[],
            search: {
                start: null,
                end: null,
                vehicleId: null
            },
            ttsmodelist: [],
            currentTTS:0,

            textPony:{
              show:false,
              remark:'',

            },
            setData:[
                    { name: '事件时间', key: 'time', size: 'all' },
                    { name: '油量减少', key: 'changePercent', size: 'all',tip:'%' },
                    { name: '事件位置', key: 'location', size: 'all' },
                ],
            _chartInstance: null,
            time: null,
            bigMarker:null,
            dealList:['未处理','偷油','异常','误报'],
            rowData:{}

        };
    },

    mounted() {
    
    },

    methods: {
        async showText(type){
          // type:3 误报  1: 确认
          if(type == 1){
            this.textPony.title = '确认'
          }else {
            this.textPony.title = '误报'
          }
          this.textPony.type = type
          this.textPony.show = true
          this.textPony.remark = ''
          await this.getTTSModelList()
          if(this.ttsmodelist.length){
              this.currentTTS = 1
              
          }
          this.changeTTS(this.currentTTS)

        },
         jumpToTTS(){
                this.$router.push({
                    path: "/home/<USER>",
                    query: {
                    tab: "themeRuler:fuelMould",
                    },
                });
            },
        async handleConfirm(){
          await this.handleFuel(this.textPony.type)
          this.textPony.show = false
          this.textPony.remark = ''
        },
        changeRow(type){
            this.$emit('changeRow',type)
        },
        changeTTS(index){
                this.currentTTS = index
                let remark
                if(index){
                    remark = this.ttsmodelist[index-1] ? this.ttsmodelist[index-1].configValue : ''
                }else {
                    remark = ''
                    // remark = `尊敬的${this.detailList.driver_name}驾驶员，您今日已触发${this.detailList.event_type_name}事件${this.detailList.today_type_count}次`
                }
                this.textPony.remark = remark

                // if(remark.includes('{x}')){
                //     let str = `${this.detailList.today_type_count}`
                //     this.messageModal.data.content = remark.replace(/{x}/g,str)

                // }else {
                //     this.messageModal.data.content = remark
                // }
            },
        async getTTSModelList() {
                let result = await this.$api.getTTS({configType:121})
                if (!result || result.status != 200 || !result.data || !result.data.length) return
                this.ttsmodelist = result.data

            },
        toJump(path){
            let needList = {
                vehicleId: this.search.vehicleId,
                startTime: path == '/home/<USER>' ? this.search.start:moment(this.search.start).startOf('days').format('YYYY-MM-DD HH:mm:ss'),
                endTime: path == '/home/<USER>' ? this.search.end:moment(this.search.end).endOf('days').format('YYYY-MM-DD HH:mm:ss'),
            }
            this.$router.push({
                path,
                query:needList
            })
        },
        showModal(row){
            this.rowData = JSON.parse(JSON.stringify(row))
            this.show = true
            this.search.vehicleId = row.vehicleId
            this.plateNo = row.plateNo
            this.search.start = moment(row.timeBefor).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')
            this.search.end = moment(row.time).add(60,'minutes').format('YYYY-MM-DD HH:mm:ss')
            this.time = moment(row.time).format('YYYY-MM-DD HH:mm:ss')
            this.title = `${row.plateNo} (${this.search.start} ~ ${this.search.end})`
            this.getDetail()
            this.$nextTick(() => {
                this._chartInstance = this.$echarts.init(this.$refs.container)
            })
        },
        async handleFuel(status){
            this.loading = true
            const res = await this.$api.fuelRefuelOperate({
                time: this.rowData.time,
                vehicleId: this.rowData.vehicleId,
                status,
                remark: this.textPony.remark
            })
            if (res.status !== 200) {
                this.loading = false
                return this.$error(res.message || '处理失败！')
            }
            this.$success('处理成功！')
            this.$emit('change')
            this.loading = false
        },
        async getDetail() {
            this.loading = true
            const res = await this.$api.refuelDetail(this.search)
            if (res.status !== 200) {
                this.loading = false
                return this.$error(res.message || '查询失败！')
            }
            this.loading = false

            this.dataList = res.data.merge
            option.xAxis[0].data = res.data.merge.map(item => item.time)
            option.series[0].data = res.data.merge.map(item => {
                return [item.time, item.value_energy === '-' ? null : item.value_energy]
            })
            option.series[1].data = res.data.merge.map(item => {
                return [item.time, item.value_speed === '-' ? null : item.value_speed]
            })
            // const energyList = [...new Set(option.series[0].data.map(item => item[1]))]
            const speedList = [...new Set(option.series[1].data.map(item => item[1]))]
            option.yAxis[0].max = 100
            option.yAxis[1].max = Math.max(...speedList)
            option.yAxis[0].interval = 20
            option.yAxis[1].interval = Math.max(...speedList) / 5
    
            option.series[2].markLine.data =  [{
                xAxis: this.time
            }]
           this.gpsList = res.data.Gps
            this.handleGspPoint(res.data.Gps)
            this.$nextTick(() => {
                this.$refs.breaktable.doLayout()
            })
            this._chartInstance.setOption(option)
            this._chartInstance.on('click', {seriesIndex: 1}, (params)=> {
                this.tableToCurrent(params.value[0])
            });
        },
        bigMarkerChange(data){
            let index = this.gpsList.findIndex(item=>item.time == data.time)
            if(index == -1)return
            if(!this.bigMarker){
                this.bigMarker = L.marker([data.lat,data.lng], {
                    icon: L.icon({
                        iconUrl: "./static/imgNewVersion/monitor/car_run.png",
                        iconAnchor: [13, 13],
                        iconSize: 26,
                        rotationAngle: Number(data.dire),
                        rotationOrigin: "center center",
                        // title: data.dire
                    }),
                    zIndexOffset: 310,
                }).addTo(this.$refs["simplemapref"]._map)
                
                this.bigMarker.time = data.time
                this.bigMarker.setRotationAngle(Number(data.dire))
                this.bigMarker.on('mouseover', (e) => {
                    this.$refs.timeBox.showBox(e.target.time,e.containerPoint.x,e.containerPoint.y)
                })
                this.bigMarker.on('mouseout', () => {
                    this.$refs.timeBox.hidden()
                })
                // this._markerCluster.addLayer(this.bigMarker);

            }else {
                this.bigMarker.setLatLng([data.lat,data.lng])
                this.bigMarker.setRotationAngle(Number(data.dire))
                this.bigMarker.time = data.time
            }
        },
         handleGspPoint(list) {
            list = list.filter(item => item.lng && item.lat)
            if (!list.length) return;
            let path = list.map(item=>[item.lat,item.lng])
            list.forEach(item => {
                let marker = L.marker([item.lat,item.lng], {
                    icon: L.icon({
                        iconUrl: "./static/imgNewVersion/monitor/jiantou.png",
                        iconAnchor: [7, 7],
                        iconSize: 14,
                        rotationOrigin: "center center",
                        title: item.dire
                    }),
                    zIndexOffset: 290,
                })
                marker.time = item.time
                marker.setRotationAngle(item.dire)
                marker.on("click", (e) => {
                    // this.bigMarkerChange(item)
                    this.tableToCurrent(item.time)
                    // this.control.activeGpsTime = item.gpsTime;
                })
                marker.on('mouseover', (e) => {
                    this.$refs.timeBox.showBox(e.target.time,e.containerPoint.x,e.containerPoint.y)
                })
                marker.on('mouseout', () => {
                    this.$refs.timeBox.hidden()
                })
                // this.markers.push(marker)
                this._linePointMarker.addLayer(marker);
            })
            let startMarker = L.marker([list[0].lat,list[0].lng], {
                icon: getMapBasicIconRequire["start"],
                zIndexOffset: 300,
            });
            this._markerCluster.addLayer(startMarker);

            let endMarker = L.marker([list[list.length - 1].lat,list[list.length - 1].lng], {
                icon: getMapBasicIconRequire["end"],
                zIndexOffset: 300,
            });
            this._markerCluster.addLayer(endMarker);
            list.forEach(item=>{
                let markerList = []
                if(item.exType){
                    let icon = L.icon({
                        iconUrl: "./static/imgNewVersion/monitor/map-abnormal-text.png",
                        iconSize: [26, 26],
                    });
                    let marker = L.marker([item.lat,item.lng], {
                        icon,
                        zIndexOffset: 300,
                    });
                    marker.on('click', (e) => {
                        this.openCurrentInfo(item)
                    })
                    markerList.push(marker)
                }
                this._markerCluster.addLayers(markerList);
            })
            let pathLine = L.polyline(path, {
                stroke: true,
                color: '#5385f6',
                weight: 4,
            });
            this._markerCluster.addLayer(pathLine);
            this.$refs['simplemapref']._map.fitBounds(this._markerCluster.getBounds())
           
        },
        tableclick(row){
            let index = this.dataList.findIndex(item=>item.time == row.time)
            this.changeIndexChart(index)
        },
        changeIndexChart(index){
            option.xAxis[0].axisPointer.value = index
            this._chartInstance.setOption(option)
            this.bigMarkerChange(this.dataList[index])
        },
        tableToCurrent(data){
            //type是true,说明是time作为查询标准
            let index = this.dataList.findIndex(item=>item.time == data)
            if (index < 0) return
            this.$refs['breaktable'].bodyWrapper.scrollTop = (index * 32)
            this.$refs['breaktable'].setCurrentRow(this.dataList[index]);
            this.changeIndexChart(index)
            this.bigMarkerChange(this.dataList[index])

            this.$nextTick(() => {
			    this.$refs['breaktable'].doLayout()
            })
        },
        openCurrentInfo(data){
            this.currentInfo = data
            this.$nextTick(() => {
            var latLng = new L.LatLng(data.lat, data.lng);
                this.$refs.vehicle.open(latLng)
                this.$refs['simplemapref']._map.setView(latLng, 19, {paddingBottomRight: [10, 300]})
            })
        },
      
        clearMap(){
            this.rowData = null
            this.allInfo = {}
            this.$refs.vehicle.close()
            this.currentInfo = null
            this.search = {
                start: null,
                end: null,
                vehicleId: null
            }
            if (this._markerCluster) {
                this._markerCluster.clearLayers()
            }
            if (this._linePointMarker) {
                this._linePointMarker.clearLayers()
            }
            
        },
        generateMapObj(){
            let $matcismap = this.$refs['simplemapref']._map
            this.$refs.vehicle.init($matcismap)
            this._markerCluster = new L.MarkerClusterGroup({
                showCoverageOnHover: false,
                maxClusterRadius: 40
            }).addTo($matcismap)
            this._linePointMarker = L.LayerGroup.collision({margin: 5});
            this._linePointMarker.addTo($matcismap);
            $matcismap.on('zoomend',(e) =>{
                if(this.$refs['timeBox']){
                    this.$refs['timeBox'].hidden()
                }
            })
        },
         close() {
            this._chartInstance.dispose()
            this._chartInstance = null
        }
    },
};
</script>

<style lang="scss" scoped>
.table {
    float: left;
    width: 30%;
    height: 100%;
    .headicon {
        color: var(--color-primary);
        margin-left: 3px;
        cursor: pointer;
    }
}
.right {
    float: left;
    width: calc(70% - 10px);
    height: 100%;
    margin-left: 10px;
    .map{
        position: relative;
        height: calc(55% - 5px);
         .monitorpupop {
        &__header {
            height: 45px;
            .fon--m {
                cursor: pointer;
            }
        }
        &__content {
            padding: 10px 0;
        }
        &__fotter {
            height: 40px;
            i {
                font-size: 20px;
                cursor: pointer;
                margin-right: 12px;
            }
        }
    }
    }
    .chart {
        height: calc(45% - 5px);
        margin-top: 10px;
        background-color: var(--background-color-base);
    }
}
.textModel {
  padding-left: 80px;
  span {
      display: inline-block;
      padding: 3px 8px;
      color: var(--color-text-secondary);
      border: 1px solid var(--border-color-base);
      border-radius: 5px;
      margin-right: 3.8px;
      margin-bottom: 5px;
      max-width: 117.5px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      cursor: pointer;
  }
  .active {
      background-color: rgba(42, 128, 224,.1);
      color: var(--color-primary);
      border: 1px solid var(--color-primary);
  }

}
</style>
