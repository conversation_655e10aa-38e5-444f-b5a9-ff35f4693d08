<template>
		<div class="fense-content">
				<ElementTree
						class="fense-top"
						type="project"
						ref="fenseTree"
						:checkMode="true"
						@mounted="getBasicTreeDataInfo"
						@node-click="nodeClick"
						@check="nodeCheck"
						:shezhiShow="false"
				>
				</ElementTree>
				<div class="item">
						<div>仅展示有运单</div>
						<el-switch
								v-model="isProjectWork" @change="changeTree">
						</el-switch>
				</div>
				<div class="item">
						<div>展示搅拌站</div>
						<el-switch
								v-model="isMixingStation">
						</el-switch>
				</div>
				<div class="fense-bottom">
						<div class="card">
								<span>{{ $ct('choose') }}:</span>
								<span>{{ checkedFenseList.length }}</span>
						</div>
						<div class="card">
								<span>{{ $ct('all') }}:</span>
								<span>{{ fenseTotalNumber }}</span>
						</div>
						<div class="card">
								<span>{{ $ct('formal') }}:</span>
								<span>{{ ableNumber }}</span>
						</div>
						<div class="card">
								<span>{{ $ct('temporary') }}:</span>
								<span>{{ unableNumber }}</span>
						</div>
				</div>
		</div>
</template>

<script>
import L from '@/assets/lib/leaflet-bmap'
import {getArrayDifference} from '@/view/monitor/util/monitorUtil'
import {generateNewVersionFenseLayer, getProjectLayer} from '../../page/FenseCollect/util/fensebasicsetting'
import * as api from "../../../../axios/api";

export default {
		name: 'MapProject',
		data() {
				return {
						_mapObj: null,
						_fenseLayerGroup: null,
						_mixingStationLayerGroup: null,
						_fenseBindLayerObj: null,
						checkedFenseList: [],
						fenseTotalNumber: 0,
						waitForInit: null,
						_promiseResolve: null,
						ableNumber: 0,
						unableNumber: 0,
						timing: null,
						isProjectWork: false,
						isMixingStation: false
				}
		},

		created() {
				this.waitForInit = new Promise((resolve => {
						this._promiseResolve = resolve
				}))
		},

		beforeCreate() {
				this.$options._i18Name = 'treeMapLayer'
		},

		async mounted() {
				this.setTiming()
		},
		watch: {
				isMixingStation(val) {
						this.change(val)
				},
        isProjectWork(val) {
            this.$refs['fenseTree'].$refs['tree'].setCheckedKeys([])
            this._fenseLayerGroup.clearLayers()
            this.checkedFenseList = []
            this._fenseBindLayerObj = {}
        }
		},

		methods: {
				changeTree(val) {
						if (val) {
								this.$refs['fenseTree'].initTree('projectOnly')
						} else {
								this.$refs['fenseTree'].initTree('project')
						}

				},
				async change(val) {
						if (val) {
								let list = this.treeObj.store._getAllNodes().filter(node => node.data.type == 3).map(item => item.data.id)

								let result = await this.$api.queryFenseInfoAssetsV2({fense_id_list: list})
								if (result.status !== 200) {
										return this.$error('查询搅拌站失败！')
								} else if (result.data.page_data.length === 0) {
										return this.$warning('暂无数据！')
								}
								result.data.page_data.forEach(fense => {
										let layerList = generateNewVersionFenseLayer(fense, true)
										layerList.forEach(item => {
												this._mixingStationLayerGroup.addLayer(item)
										})
								})
						} else {
								if (this._mixingStationLayerGroup) {
										this._mixingStationLayerGroup.clearLayers()
								}
						}
				},
				setTiming() {
						this.timing = setInterval(async () => {
								// let proList = [
								// 	this.$store.dispatch('ztreeData/refreshTreeData', 'project'),
								// 	this.$store.dispatch('ztreeData/refreshTreeData', 'projectOnly')
								// ]
								// Promise.all(proList).then((res)=>{
								// 	if(this.isProjectWork){
								// 		this.$refs['fenseTree'].initTree('projectOnly')
								// 	}else {
								// 		this.$refs['fenseTree'].initTree('project')
								// 	}
								// })
								if (this.isProjectWork) {
										this.$refs['fenseTree'].initTree('projectOnly')
								} else {
										this.$refs['fenseTree'].initTree('project')
								}

						}, 8 * 60 * 1000)
				},
				changeTreeState(state) {
						let firstNode = this.$refs['fenseTree'].ztreeObj.getNodes()[0]
						if (!firstNode) return
						this.$refs['fenseTree'].ztreeObj.checkNode(firstNode, state, true, true)
				},
				nodeClick(data, node, $node) {
						if (data.checked) {
								this.fitCurrentBounds(data.id)
						}
				},

				nodeCheck(data, {checkedNodes}) {
						let ids = checkedNodes.filter(node => node.type == 4).map(fense => fense.id)
						this.handleFenseChange(ids)
						let firstNode = this.treeObj.data.map(this.treeObj.getNode);
						this.$emit('change', 'maproute', firstNode.checked && !firstNode.indeterminate)
				},

				// 更改选中数据时刷新
				handleFenseChange(list) {
						if (!list.length) {
								if (this._fenseLayerGroup) this._fenseLayerGroup.clearLayers()
								this.checkedFenseList = []
								this._fenseBindLayerObj = {}
								return
						}
						// 找出没有加载的数据
						let difference = getArrayDifference(this.checkedFenseList, list)
						if (!difference.length) return
						let addList = []
						difference.forEach(item => {
								let currentLayerList
								// 判断图层的数量如果没有图层，新增车辆
								if (Object.keys(this._fenseBindLayerObj).length) {
										currentLayerList = this._fenseBindLayerObj[item]
								} else {
										addList.push(item)
										return
								}
								if (!currentLayerList || !currentLayerList.length) {
										addList.push(item)
										return
								}
								if (this.checkedFenseList.includes(item)) {
										currentLayerList.forEach(layer => this._fenseLayerGroup.removeLayer(layer))
										delete this._fenseBindLayerObj[item]
								} else {
										currentLayerList.forEach(layer => this._fenseLayerGroup.addLayer(layer))
										if (difference.length == 1) {
												this.fitCurrentBounds(item)
										}
								}
						})

						this.checkedFenseList = list
						if (addList.length) this.getFenseDetail(addList)
				},

				// 获取详细信息
				async getFenseDetail(list) {
						let result = await this.$api.queryFenseInfoAssets({fence_id_list: list})
						if (!result || result.status != 200) {
								this.$error(result.message || '查询出错')
								return
						}
						result.data.forEach(fense => {
								if (!fense.fence_list.point_list.length) return
								let layerList = getProjectLayer(fense)
								layerList.forEach(item => {
										this._fenseLayerGroup.addLayer(item)
								})
								this._fenseBindLayerObj[fense.id] = layerList
						})
						if (list.length == 1) {
								this.fitCurrentBounds(list[0])
						}
				},

				// 聚焦
				fitCurrentBounds(id) {
						let currentLayerList = this._fenseBindLayerObj[id]
						if (!currentLayerList || !currentLayerList.length) return
						this._mapObj.fitBounds(currentLayerList[0].getBounds())
				},

				// 工程项目
				init(map) {
						this._mapObj = map
						this._fenseBindLayerObj = {}
						this._fenseLayerGroup = new L.FeatureGroup().addTo(this._mapObj)
						this._mixingStationLayerGroup = new L.FeatureGroup().addTo(this._mapObj)
				},

				// 清除地图图层
				cleanUp() {
						if (this._fenseLayerGroup) {
								this._fenseLayerGroup.clearLayers()
								this._fenseLayerGroup = null
						}
						if (this._mixingStationLayerGroup) {
								this._mixingStationLayerGroup.clearLayers()
								this._mixingStationLayerGroup = null
						}
						this.checkedFenseList = []
						this._fenseBindLayerObj = null
						this._mapObj = null
						clearInterval(this.timing)
						this.timing = null
				},

				// 获取总数
				getBasicTreeDataInfo(obj, data) {
						this.treeObj = obj
						this.fenseTotalNumber = data.gdTotalNumber
						this.ableNumber = data.ableNumber
						this.unableNumber = data.unableNumber
						this._promiseResolve();
				}
		},

		beforeDestroy() {
				this.cleanUp()
		}
}

</script>

<style lang='scss' scoped>
.fense-content {
		height: 100%;
		width: 100%;
		position: relative;
		display: flex;
		flex-direction: column;

		.fense-top {
				height: calc(100% - 114px);
				flex-grow: 1;
		}

		.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-shrink: 0;
				height: 32px;
				line-height: 32px;
				padding: 0 10px;
				//background-color: var(--border-color-light);
		}

		.fense-bottom {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-around;
				align-items: center;
				border-top: solid 1px var(--border-color-base);
				height: 50px;
				padding-top: 10px;

				.card {
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: center;
						width: 50%;

						span {
								&:first-child {
										margin-right: 10px;
								}
						}
				}
		}
}
</style>

