<template>
  <div class="leafletmap">
    <div class="leafletmap__loading" v-loading="mapLoading" v-show="mapLoading"></div>

    <div class="leafletmap__main" ref="leaflet"></div>
    <div class="pdfViewBackground" ref="pdfview"></div>

    <slot></slot>

    <ul
      class="leafletmap__fixed"
      v-if="!windowModel"
      :style="{ right: direction == 'rtl' ? 'unset' : '5px', left: direction == 'ltr' ? 'unset' : '5px' }"
    >
      <li @click="initDrawControl">
        <i class="pony-iconv2 pony-gongju"></i>
        <span>{{ $ct("map_tool") }}</span>
        <span v-if="currentDrawer.control.sign != 'map_default'">( {{ $ct(`${currentDrawer.control.sign}`) }} )</span>
      </li>
      <slot name="tool"></slot>
    </ul>

    <!-- currentDrawer.includeList if 包含，为了刚开始没那么卡 -->
    <DialogDrawer
      :width="mapConfigStyle ? '215px' : '300px'"
      :title="$ct(`${currentDrawer.control.sign}`)"
      :isFullscreen="false"
      :direction="direction"
      :allowClose="0"
      :show="currentDrawer.ifShow"
      v-show="currentDrawer.switch"
      @close="currentDrawer.switch = false"
    >
      <div class="control-close" slot="header">
        <i
          class="el-icon-arrow-left"
          :title="$ct('return_tool')"
          v-show="currentDrawer.control.sign != 'map_default'"
          @click="clearCurrentDrawer"
        >
        </i>
        <i class="el-icon-arrow-down" :title="$ct('hide_tool')" @click="currentDrawer.switch = false"> </i>
      </div>

      <div class="control-body" v-show="currentDrawer.control.sign == 'map_default'">
        <div class="control-card" v-for="(item, index) in filterMapSetting" :key="index">
          <div class="control-tile">
            {{ item.name }}
          </div>
          <ul class="control-click" v-if="item.key != 'mapPermisson'">
            <li v-for="(child, childIndex) in item.children" :key="childIndex" @click="handleCurrentEvent(child)">
              <!-- <div class="clickbtn"><span>{{ child.name }}</span></div> -->
              <!-- 添加新功能的时候记得传宽度 -->
              <maptoolbox :width="child.width" :name="child.name"> </maptoolbox>
            </li>
          </ul>
          <!-- 页面上总有些**操作 ，这里吧那几个特出情况写配置就真的很烦 -->
          <div v-else class="control-switch">
            <div v-if="sepcialMapSetting.includes('lukuang')" class="switch-content">
              <span>路况</span>
              <el-switch @change="changeTimeLayer" v-model="control.lukuang"></el-switch>
            </div>
            <div v-if="sepcialMapSetting.includes('mapgd')" class="switch-content">
              <span>工地</span>
              <span class="dfc">
                <i @click="handleCurrentEvent({ sign: 'mapgd', source: 'mapgd' })" class="pony-iconv2 pony-xiangqing"></i>
                <el-switch @change="handleTreeBABAEvent('mapgd')" v-model="control.mapgd"></el-switch>
              </span>
            </div>
            <!-- <li v-if="sepcialMapSetting.includes('mapgd')">
                            <span>工地</span>
                            <span class="dfc">
                                <i @click="handleCurrentEvent({sign: 'mapgd', source: 'mapgd'})"
                                   class="pony-iconv2 pony-xiangqing"></i>
                                <el-switch @change="handleTreeBABAEvent('mapgd')" v-model="control.mapgd"></el-switch>
                            </span>
                        </li> -->
            <div v-if="sepcialMapSetting.includes('mapxnc')" class="switch-content">
              <span>消纳场</span>
              <span class="dfc">
                <i @click="handleCurrentEvent({ sign: 'mapxnc', source: 'mapxnc' })" class="pony-iconv2 pony-xiangqing"></i>
                <el-switch @change="handleTreeBABAEvent('mapxnc')" v-model="control.mapxnc"></el-switch>
              </span>
            </div>
            <div v-if="sepcialMapSetting.includes('maproute')" class="switch-content">
              <span>通行线路</span>
              <span class="dfc">
                <i @click="handleCurrentEvent({ sign: 'maproute', source: 'maproute' })" class="pony-iconv2 pony-xiangqing"></i>
                <el-switch @change="handleTreeBABAEvent('maproute')" v-model="control.maproute"></el-switch>
              </span>
            </div>
            <!-- <li v-if="sepcialMapSetting.includes('maproute')">
                            <span>通行线路</span>
                            <span class="dfc">
                                <i @click="handleCurrentEvent({sign: 'maproute', source: 'maproute' })"
                                   class="pony-iconv2 pony-xiangqing"></i>
                                <el-switch @change="handleTreeBABAEvent('maproute')"
                                           v-model="control.maproute"></el-switch>
                            </span>
                        </li> -->
            <div v-if="sepcialMapSetting.includes('mapcz')" class="switch-content">
              <span>场站</span>
              <span class="dfc">
                <i @click="handleCurrentEvent({ sign: 'mapcz', source: 'mapcz' })" class="pony-iconv2 pony-xiangqing"></i>
                <el-switch @change="handleTreeBABAEvent('mapcz')" v-model="control.mapcz"></el-switch>
              </span>
            </div>
            <div class="switch-content">
              <span>限行区</span>
              <span class="dfc">
                <el-switch @change="changeFenseState('xxqfense')" v-model="xxqfense.open"></el-switch>
              </span>
            </div>
            <!-- <li>
                            <span>限行区</span>
                            <span class="dfc">
                                <el-switch @change="changeFenseState('xxqfense')" v-model="xxqfense.open"></el-switch>
                            </span>
                        </li> -->
            <div class="switch-content">
              <span>辖区</span>
              <span class="dfc">
                <el-switch @change="changeFenseState('xqfense')" v-model="xqfense.open"></el-switch>
              </span>
            </div>
            <!-- <div class="switch-content">
                        <span>疫情区域</span>
                        <span class="dfc">
                            <el-switch @change="changeFenseState('yqfense')" v-model="yqfense.open"></el-switch>
                        </span>
                        </div> -->
            <!-- <li>
                            <span>疫情区域</span>
                            <span class="dfc">
                                <el-switch @change="changeFenseState('yqfense')" v-model="yqfense.open"></el-switch>
                            </span>
                        </li> -->
          </div>
        </div>
      </div>
      <NavigationTree
        ref="navigation"
        v-if="currentDrawer.includeList.includes('map_navigation')"
        v-show="currentDrawer.control.sign == 'map_navigation'"
        >>
      </NavigationTree>
      <FenseTreeLayer
        ref="fence"
        :isLimit="isLimit"
        @layer="fenceLayerChange"
        v-if="currentDrawer.includeList.includes('map_fence')"
        v-show="currentDrawer.control.sign == 'map_fence'"
      >
      </FenseTreeLayer>

      <AreaFenseTreeLayer
        ref="areaFence"
        :isLimit="isLimit"
        @layer="fenceLayerChange"
        v-if="currentDrawer.includeList.includes('map_area_fence')"
        v-show="currentDrawer.control.sign == 'map_area_fence'"
      >
      </AreaFenseTreeLayer>
      <InterTreeLayer
        ref="inter"
        v-if="currentDrawer.includeList.includes('map_inter')"
        v-show="currentDrawer.control.sign == 'map_inter'"
      >
      </InterTreeLayer>
      <LineTreeLayer
        ref="line"
        v-if="currentDrawer.includeList.includes('map_line')"
        v-show="currentDrawer.control.sign == 'map_line'"
      >
      </LineTreeLayer>

      <MapLineSearch
        ref="lineplan"
        v-if="currentDrawer.includeList.includes('map_linePlan')"
        v-show="currentDrawer.control.sign == 'map_linePlan'"
      >
      </MapLineSearch>
      <MapPoiSearch
        ref="search"
        v-if="currentDrawer.includeList.includes('map_search')"
        v-show="currentDrawer.control.sign == 'map_search'"
      >
      </MapPoiSearch>
      <MapProject
        ref="project"
        v-if="currentDrawer.includeList.includes('map_project')"
        v-show="currentDrawer.control.sign == 'map_project'"
      >
      </MapProject>
      <MapTagging
        ref="tagging"
        v-if="currentDrawer.includeList.includes('map_tagging')"
        v-show="currentDrawer.control.sign == 'map_tagging'"
      >
      </MapTagging>
      <MapView ref="view" v-if="currentDrawer.includeList.includes('map_view')" v-show="currentDrawer.control.sign == 'map_view'">
      </MapView>
      <MapPaint
        ref="paint"
        v-if="currentDrawer.includeList.includes('map_paint')"
        v-show="currentDrawer.control.sign == 'map_paint'"
      >
      </MapPaint>

      <!-- 三巨头 -->
      <FenseLineTreeLayer
        ref="maproute"
        v-if="currentDrawer.includeList.includes('maproute')"
        v-show="currentDrawer.control.sign == 'maproute'"
        @change="changeControlState"
      >
      </FenseLineTreeLayer>
      <FenseGdTreeLayer
        ref="mapgd"
        v-if="currentDrawer.includeList.includes('mapgd')"
        v-show="currentDrawer.control.sign == 'mapgd'"
        @change="changeControlState"
      >
      </FenseGdTreeLayer>
      <FenseXncTreeLayer
        ref="mapxnc"
        v-if="currentDrawer.includeList.includes('mapxnc')"
        v-show="currentDrawer.control.sign == 'mapxnc'"
        @change="changeControlState"
      >
      </FenseXncTreeLayer>
      <ChangZhanTree
        ref="mapcz"
        v-if="currentDrawer.includeList.includes('mapcz')"
        v-show="currentDrawer.control.sign == 'mapcz'"
        @change="changeControlState"
      >
      </ChangZhanTree>
      <RegionalRange
        ref="rangearea"
        v-if="currentDrawer.includeList.includes('map_rangearea')"
        v-show="currentDrawer.control.sign == 'map_rangearea'"
        @change="toRegionalRange"
      >
      </RegionalRange>
    </DialogDrawer>
    <Popup ref="tagging" :minWidth="350" :maxWidth="400" :closeOnClick="false" :closeButton="false">
      <div class="tagging" v-show="popup.show">
        <div class="tagging__header"><span> 取点信息 </span> <i class="pony-iconv2 pony-guanbi" @click="closePopup"></i></div>
        <div class="tagging__content">
          <div class="popup-item">
            经度<span class="latlng">{{ popup.data.latlng.split(",")[1] }}</span
            >纬度<span class="latlng">{{ popup.data.latlng.split(",")[0] }}</span>
          </div>
          <div class="popup-item">
            <span>地址</span>
            {{ popup.data.location }}
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
/**
 * http://mars2d.cn/go.html?id=13
 *  地图组件页面，被页面只写页面内每个按钮得调用不写交互
 *  地图内得个方法请去 mixins 里去找
 */
import L from "@/assets/lib/leaflet-bmap";
import * as MapDictionary from "./mapControl";
import mapchange from "./mapChange";
import _ from "lodash";
import html2canvas from "html2canvas";

import DialogDrawer from "@/components/common/DialogDrawer";
import RegionalRange from "@/view/monitor/components/RegionalRange";

// 各大组件
import FenseTreeLayer from "../maptreelayer/FenseTreeLayer";
import InterTreeLayer from "../maptreelayer/InterTreeLayer";
import LineTreeLayer from "../maptreelayer/LineTreeLayer";
import MapLineSearch from "../maptoollayer/MapLineSearch";
import MapPoiSearch from "../maptoollayer/MapPoiSearch";
import MapTagging from "../maptoollayer/MapTagging";
import MapView from "../maptoollayer/MapView";
import MapPaint from "../maptoollayer/MapPaint";
import MapProject from "../maptoollayer/MapProject";
import maptoolbox from "../maptoollayer/maptoolbox";

import NavigationTree from "../maptreelayer/NavigationTree";
// 三巨头 搞不懂
import FenseLineTreeLayer from "../maptreelayer/FenseLineTreeLayerV2";
import FenseGdTreeLayer from "../maptreelayer/GdTreeLayerV2";
import FenseXncTreeLayer from "../maptreelayer/XncTreeLayerV2";
import ChangZhanTree from "../maptreelayer/ChangZhanTreeV2";

import mapSwitch from "./mapSwitch";
import { mapState } from "vuex";
import Popup from "@/view/monitor/components/Popup";
import { getMapBasicIconRequire } from "@/view/monitor/util/monitorUtil";
import { getAddressByLatLngOrder } from "@/view/monitor/util/webApiUtil";
import AreaFenseTreeLayer from "../maptreelayer/AreaFenseTreeLayer";

require("./MaticsMapV2.scss");

export default {
  name: "maticsMap",
  components: {
    DialogDrawer,
    FenseTreeLayer,
    InterTreeLayer,
    LineTreeLayer,
    MapLineSearch,
    MapPoiSearch,
    MapTagging,
    FenseLineTreeLayer,
    FenseGdTreeLayer,
    FenseXncTreeLayer,
    ChangZhanTree,
    NavigationTree,
    MapView,
    MapPaint,
    MapProject,
    RegionalRange,
    maptoolbox,
    Popup,
    AreaFenseTreeLayer,
  },
  mixins: [mapchange, mapSwitch],
  data() {
    return {
      _map: null,

      mapLoading: false,

      currentMapTile: [],

      mapTileLayer: [],

      timeTileLayer: null,

      control: {
        toolState: true,
        lukuang: false,
        mapgd: false,
        mapxnc: false,
        maproute: false,
        mapcz: false,
      },

      mapControl: JSON.parse(JSON.stringify(MapDictionary.mapControl)),
      defaultConfig: JSON.parse(JSON.stringify(MapDictionary.defaultConfig)),
      routeRestraint: JSON.parse(JSON.stringify(MapDictionary.routeRestraint)),
      defaultSetting: JSON.parse(JSON.stringify(MapDictionary.mapSetting)),

      currentRouter: "",

      currentDrawer: {
        control: {
          sign: "map_default",
        },
        ifShow: true,
        switch: false,
        includeList: [],
      },

      _featureGroup: null,
      _boundsFilterLayer: null,
      _meatureLayer: null,

      waitForInit: null,
      _promiseResolve: null,

      _filterResolve: null,
      _extandLayer: null,
      popup: {
        show: false,
        data: {
          latlng: "",
          location: "",
        },
        marker: null,
      },
    };
  },

  props: {
    /**
     * 就是默认显示什么地图，目前没有人用到这个配置项，不来简化了
     */
    windowModel: {
      type: Boolean,
      default: false,
    },
    //围栏数量选择限制
    isLimit: {
      type: Boolean,
      default: false,
    },
    //isLimit为true时有效,限制围栏选择的数量
    limitNum: {
      type: Number,
      default: 20,
    },
    /**
     * 2020 12 22 13：56 项老板说直接全部改变  斯国一！
     */
    // defaultMap: {
    //     type: Array,
    //     default: function() {
    //         return ['GaoDe.Normal.Map']
    //         // return ['YX.Normal.Map', 'YX.Normal.Annotion']
    //     }
    // },

    /**
     *  地图得附加配置，这个是传给地图的
     *  东西错综复杂，去看官网，加载不同得插件还能获取额外得配置
     */
    mapSetting: {
      type: Object,
      default: function () {
        return {};
      },
    },
    noDelay: {
      type: Boolean,
      default: false,
    },

    /**
     *  地图显示工具得配置 只针对组件的
     *  默认值string['simple','matics','justmap']
     *  obj: { 自己去看setting }
     */
    mapConfig: {
      type: [Object, String],
      default: "matics",
    },

    mapLittleTool: {
      type: Array,
      default: function () {
        return [
          "scale", //  图例
          "coordinates", //  显示左下角得那个经纬度
          "zoomCustom", //  显示图层控制得那个加减号
          "attribution", //  地图版权信息
          "minimap", //  鹰眼图
        ];
      },
    },

    direction: {
      type: String,
      default: "rtl",
    },
  },

  created() {
    // 有些页面引入组件得时候确保组件已经生成完毕了
    this.waitForInit = new Promise((resolve) => {
      this._promiseResolve = resolve;
    });
    //  存下来是那个页面得，有些权限要照顾下
    this.currentRouter = this.$route.name;
  },

  computed: {
    ...mapState("auth", ["userInfo"]),
    ...mapState("map", ["currentMap", "systemCrsChange", "mapOptions"]),
    /**
     * 2020 12-18 14:32
     * 这里要面临三层筛选
     *  1、组件对外开放的功能筛选               defaultConfig
     *  2、用户的permission权限筛选            hasPermission（）
     *  3、针对指定路由不显示的筛选             这个目前就实时监控得有限制（胃疼操作）就上面那句话
     * 只有以上的筛选都合格的情况才能在组件中筛选出来
     */
    mapConfigStyle() {
      return typeof this.mapConfig == "string" && this.mapConfig == "justmap" ? this.mapConfig : "";
    },

    filterMapSetting() {
      // 拿到组件配置
      const mapconfig = typeof this.mapConfig == "string" ? this.defaultConfig[`${this.mapConfig}`] : this.mapConfig;
      // 由于地图切换bug，如果配置了百度地图的权限，则只显示百度地图的切换
      if (this.hasPermission("system:map:baidu")) {
        this.mapControl[0].children = this.mapControl[0].children.filter((item) => {
          return item.source === "Baidu";
        });
        this.$store.commit("map/changeSystemMap", ["Baidu.Normal.Map"]);
      }
      return this.mapControl
        .filter((cur) => mapconfig[cur.key] && mapconfig[cur.key].length)
        .map((item) => {
          let list = item.children.filter(
            (child) =>
              mapconfig[item.key].includes(child.source) && (child.permisson ? this.hasPermission(child.permisson) : true)
          );
          return Object.assign(item, {
            children: list,
          });
        });
    },

    sepcialMapSetting() {
      let filterList = this.filterMapSetting.find((item) => item.key == "mapPermisson");
      const filterRouteList = this.routeRestraint[this.currentRouter] || [];
      return filterList.children.filter((item) => !filterRouteList.includes(item.source)).map((root) => root.source);
    },
  },

  watch: {
    currentMap(newVal) {
      if (this.currentMapTile != newVal) {
        this.changeMapLayer(newVal);
      }
    },

    systemCrsChange(newVal) {
      // this.handleReallyAmazingEvent()
    },
  },

  mounted() {
    /**
     *  老夫实现声明
     *  这里面得方法可以放在地图得统一图层管辖里的，至于为什么非要手动去写，你去问那些把这些东西乱放得人
     */
    this.readyLeafletMap();
    if (this.noDelay) {
      this.changeMapLayerNoTime(this.currentMap);
    } else {
      this.changeMapLayer(this.currentMap);
    }
  },

  methods: {
    closePopup() {
      this.popup.show = false;
      if (this.popup.marker) this.popup.marker.remove();
      this.popup.marker = null;
      this.popup.data = {
        latlng: "",
        location: "",
      };
      this._map.off("click");
      if (this.$refs.tagging._map) this.$refs.tagging.close();
    },
    //地图工具中取点功能
    addMapPiont() {
      this.closePopup();
      this._map.on("click", async (e) => {
        if (this.popup.marker) this.popup.marker.remove();
        let marker = L.marker(e.latlng, {
          icon: getMapBasicIconRequire["pony-moren4"],
          zIndexOffset: 1000,
          draggable: false,
        }).addTo(this._map);
        this.popup.data.latlng = marker._latlng.lat + "," + marker._latlng.lng;
        this.popup.data.location = await getAddressByLatLngOrder({ latlng: [marker._latlng.lat, marker._latlng.lng] });
        // marker.marker_id = mark_id;
        if (!this.popup.data.location || !this.popup.data.latlng) {
          console.log(this.popup.data.location);
          this.$error("获取地址失败");
          marker.remove();
          marker = null;
          this.closePopup();
          this._map.off("click");
          return;
        }
        this.popup.marker = marker;
        this.$nextTick(() => {
          this.openTaggingMarker(marker);
        });
      });
    },
    openTaggingMarker(marker) {
      this.$refs["tagging"].init(this._map);
      this.$nextTick(() => {
        this.popup.show = true;
        this.$refs["tagging"].open(marker);
      });
    },
    clearCurrentDrawer() {
      this.$refs[this.currentDrawer.control.source].cleanUp();
      this.currentDrawer.control = { name: "地图工具", sign: "map_default" };
    },
    fenceLayerChange(list) {
      this.$emit("layer", list);
    },
    handleCurrentEvent(item) {
      const dictionary = this.defaultConfig.matics;
      switch (true) {
        //  这些是地图切换得事件
        case dictionary.mapChange.includes(item.source):
          this.currentMapTile = item.map;
          let layers = [];
          for (var x in this._map._layers) {
            if (this._map._layers[x].options.layerId === "drawMap") {
              let drawLayer = this._map._layers[x];
              for (let i in drawLayer._layers) {
                drawLayer._layers[i].disableEdit();
              }
            }
            layers.push(this._map._layers[x]);
          }
          this.readyLeafletMap(this._map.getCenter(), this._map.getZoom(), item.source === "Baidu" ? 19 : 18);
          this._map._layers = {};
          layers.forEach((item) => {
            item.addTo(this._map);
          });
          for (var x in this._map._layers) {
            if (this._map._layers[x].options.layerId === "drawMap") {
              let drawLayer = this._map._layers[x];
              for (let i in drawLayer._layers) {
                if (drawLayer._layers[i].options.isEdit === true) {
                  drawLayer._layers[i].enableEdit();
                }
              }
            }
          }
          this.changeMapLayer(item.map);
          break;
        //  这些事自创的地图组件，特殊方式引入
        case dictionary.bussiness.includes(item.source):
        case dictionary.mapPermisson.includes(item.source):
          this.checkInitToolSource(item);
          break;
        //  这个时候就可以奏乐了，拳TM垃圾功能
        //  重点来了，为了突出这玩意有点用，还要给他加点佐料
        case item.source == "manyou":
          this._map.off("click", L.Draw.Event.CREATED);
          break;
        case item.source == "lkenlarge":
        case item.source == "lknarrow":
        case item.source == "mapfilter":
          this.currentDrawer.control = { sign: "map_default", source: item.source };
          this._featureGroup["rectangle"].enable();
          break;
        case item.source == "ranging":
          let rangingtool = this._featureGroup["polyline"];
          rangingtool && rangingtool._enabled ? rangingtool.disable() : rangingtool.enable();
          break;
          // case item.source == 'paint':
          // this._meatureLayer._startMeasure()
          break;
        case item.source == "fullScreen":
          if (!this._map.isFullscreen()) this._map.toggleFullscreen();
          break;
        case item.source == "print":
          this.toPrintMapView();
          // if (!this._map.isFullscreen()) this._map.toggleFullscreen()
          break;
        case item.source == "screenshot":
          this.toScreenshotMapView();
          // if (!this._map.isFullscreen()) this._map.toggleFullscreen()
          break;
        case item.source == "rangearea":
          this.checkInitToolSource(item);
          break;
        case item.source == "search":
          this.checkInitToolSource(item);
          break;
        case item.source == "getPoint":
          this.addMapPiont();
          break;
      }
    },
    toPrintMapView() {
      this.$emit("changeClear");
      this.$print(this.$refs.leaflet);
    },
    async toScreenshotMapView() {
      this.$emit("changeClear");
      this.mapLoading = true;
      // const targetDom = document.querySelector('.pony-dialog')
      const targetDom = this.$refs.leaflet;

      // 画布的宽高
      let canvas = await html2canvas(targetDom, {
        allowTaint: false,
        useCORS: true,
        height: targetDom.clientHeight,
        width: targetDom.clientWidth,
      });
      await this.$nextTick();
      const container = this.$refs["pdfview"];
      while (container.hasChildNodes()) {
        container.removeChild(container.firstChild);
      }
      const dataImg = new Image();
      dataImg.src = canvas.toDataURL("image/png");
      container.appendChild(dataImg);
      const alink = document.createElement("a");
      alink.href = dataImg.src;
      alink.download = `实时监控地图截图.jpg`;
      alink.click();

      this.mapLoading = false;
    },
    async toRegionalRange(parmas) {
      let result = await this.$refs["rangearea"].showModal(parmas);
      if (!result) return;
      if (this._extandLayer) {
        this._extandLayer.clearLayers();
        this._extandLayer = null;
      }
      // 如果需要这个结果的话,可以使用
      this.$emit("changeArea", result.result);
      this._extandLayer = result.result.addTo(this._map);

      this._map.fitBounds(this._extandLayer.getBounds());
      this._extandLayer.on("dblclick", (e) => {
        if (this._extandLayer) {
          this._extandLayer.clearLayers();
          this._extandLayer = null;
        }
      });
    },
    // 进入二级页面执行的方法
    checkInitToolSource(item, showNow = true, showBox = true) {
      this.currentDrawer.switch = showBox;
      if (showNow) {
        this.currentDrawer.control = item;
      }
      if (item.sign == "map_rangearea") {
        this.$nextTick(() => {
          this.toRegionalRange({});
        });
      }
      if (!this.currentDrawer.includeList.includes(item.sign)) {
        this.currentDrawer.includeList.push(item.sign);
        this.$nextTick(() => {
          this.$refs[`${item.source}`].init(this._map);
        });
      }
    },

    async handleTreeBABAEvent(type) {
      let state = this.control[type];
      let curDom = this.$refs[type];
      if (!curDom) {
        this.checkInitToolSource({ sign: type, source: type }, false);
      }
      await this.$nextTick();
      const readyDom = this.$refs[type];
      await readyDom.waitForInit;
      this.$nextTick(() => {
        readyDom.changeTreeState(state);
      });
    },

    changeControlState(type, state) {
      this.control[type] = state;
    },

    /**
     *  改变地图路况网得显示状态
     */
    changeTimeLayer() {
      let key = this.currentMap[0].split(".");
      if (key[0] == "Baidu") {
        if (this.timeTileLayer) {
          this._map.removeLayer(this.timeTileLayer);
          this.timeTileLayer = null;
        } else {
          this.timeTileLayer = L.tileLayer
            .chinaProvider("Baidu.Time.Map", {
              maxZoom: 18,
              minZoom: 3,
              tms: true,
            })
            .addTo(this._map);
        }
      } else if (key[0] == "Gaode") {
        if (this.timeTileLayer) {
          this._map.removeLayer(this.timeTileLayer);
          this.timeTileLayer = null;
        } else {
          this.timeTileLayer = L.tileLayer
            .chinaProvider("Gaode.Time.Map", {
              maxZoom: 18,
              minZoom: 3,
              tms: true,
            })
            .addTo(this._map);
        }
      } else {
        this.$warning("此地图尚未支持路况显示");
        this.control.lukuang = !this.control.lukuang;
        return;
      }
    },
    getZoom() {
      let type = this.userInfo?.area_type;
      if (!type) return this.defaultSetting.zoom;
      let zoomIndex = [6, 8, 12, 13, 15];
      return zoomIndex[type - 1] ? zoomIndex[type - 1] : 12;
    },
    readyLeafletMap(location, zoom, max) {
      let userSet = this.userInfo?.area_latlng;

      let zoomSet = zoom ? zoom : this.getZoom();
      let maxZoom = max ? max : this.defaultSetting.maxZoom;
      let center = [];
      if (userSet) {
        let latlng = userSet.split(",");
        center = [+latlng[0], +latlng[1]];
      } else {
        center = [30.1956, 120.2073];
      }
      if (location && zoom) {
        center = [location.lat, location.lng];
      }
      if (this._map) {
        this._map.remove();
      }
      this._map = L.map(
        this.$refs.leaflet,
        Object.assign(this.defaultSetting, this.mapSetting, { center: center, zoom: zoomSet, maxZoom: maxZoom })
      );

      const zoomControl = L.control.zoom({
        position: "bottomleft",
      });
      if (this.mapLittleTool.includes("zoomCustom")) {
        this._map.addControl(zoomControl);
      }
      this.addExtandConfig(this._map, this.mapLittleTool);

      this.$emit("mapReady", this._map);
      this._promiseResolve(this._map);

      this._meatureLayer = L.control
        .measure({
          position: "topleft",
          activeColor: "red",
          completedColor: "red",
          primaryLengthUnit: "meters",
          secondaryLengthUnit: "kilometers",
          secondaryAreaUnit: "sqmeters",
        })
        .addTo(this._map);

      this._featureGroup = {
        rectangle: new L.Draw.Rectangle(this._map, { metric: ["km", "km"], shapeOptions: { color: "red" } }),
        polyline: new L.Polyline.Measure(this._map),
      };

      this._map.on(L.Draw.Event.CREATED, (e) => {
        const { source } = this.currentDrawer.control;
        switch (source) {
          case "lkenlarge":
          case "lknarrow":
            this.changeMapBounds(e.layer);
            break;
          case "mapfilter":
            if (this._boundsFilterLayer) {
              this.$warning("筛选区域已达上限！");
              return;
            }
            this._boundsFilterLayer = e.layer;
            this.handleFilterEvent();
            this._filterResolve(e.layer);
            break;
        }
      });
    },

    changeMapBounds(layer) {
      let mapzoom = this._map.getZoom();
      let center = layer.getBounds().getCenter();
      mapzoom = this.currentDrawer.control.source == "lkenlarge" ? mapzoom + 1 : mapzoom - 1;
      this._map.setView(center, mapzoom);
      this._map.removeLayer(layer);
      this._map.off("click", L.Draw.Event.CREATED);
    },

    handleFilterEvent() {
      this._map.addLayer(this._boundsFilterLayer);
      let icon = new L.DivIcon({ html: '<i style="margin: -5px;" class="leaflet-layer_close el-icon-close"></i>' });
      let closeMarker = L.marker(this._boundsFilterLayer.getBounds()._northEast, { icon: icon }).addTo(this._map);
      let _this = this;
      closeMarker.on("click", (e) => {
        _this._map.removeLayer(_this._boundsFilterLayer);
        _this._map.removeLayer(closeMarker);
        _this._map.off("click", L.Draw.Event.CREATED);
        _this._boundsFilterLayer = null;
      });
    },

    /**
     * 这里是地图切换至于是不是要页面重载，到时候再说
     */
    changeMapLayer: _.debounce(async function (list) {
      if (!this._map) {
        this.$warning("地图基础创建的有问题");
        return;
      }
      if (this.mapTileLayer.length) {
        this.mapTileLayer.forEach((item) => item.remove());
        this.mapTileLayer = [];
      }
      //  如果知识单纯得那种地图得话将不受本次操作得
      this.mapLoading = true;
      let mapKeyTile = list[0].split(".")[0];
      let justmapConfig = this.defaultConfig["justmap"].mapChange;
      let layerList;
      // if (this.mapConfig == 'justmap' && !justmapConfig.includes(mapKeyTile)) {
      //     layerList = await this.changeTileLayer(this._map, ['GaoDe.Normal.Map'], false)
      // } else {
      //     layerList = await this.changeTileLayer(this._map, list, true)
      // }
      layerList = await this.changeTileLayer(this._map, list, true);
      layerList.forEach((item) => {
        this.mapTileLayer.push(item.addTo(this._map));
      });

      this.$nextTick(() => {
        this._map.invalidateSize();
        this.mapLoading = false;
      });
    }, 200),
    async changeMapLayerNoTime(list) {
      if (!this._map) {
        this.$warning("地图基础创建的有问题");
        return;
      }
      if (this.mapTileLayer.length) {
        this.mapTileLayer.forEach((item) => item.remove());
        this.mapTileLayer = [];
      }
      //  如果知识单纯得那种地图得话将不受本次操作得
      this.mapLoading = true;
      let mapKeyTile = list[0].split(".")[0];
      let justmapConfig = this.defaultConfig["justmap"].mapChange;
      let layerList;
      if (this.mapConfig == "justmap" && !justmapConfig.includes(mapKeyTile)) {
        layerList = await this.changeTileLayer(this._map, ["GaoDe.Normal.Map"], false);
      } else {
        layerList = await this.changeTileLayer(this._map, list, true);
      }
      layerList.forEach((item) => {
        this.mapTileLayer.push(item.addTo(this._map));
      });

      this.$nextTick(() => {
        this._map.invalidateSize();
        this.mapLoading = false;
      });
    },

    /**
     *  为了偷懒呀~ 没错，这就是偷鸡 (投机)！！
     *  我验证了下，目前得这个效率也还可以哈（手动滑稽）
     *
     *  我坚信肯定有类似得这种机制，但是老夫找不着啊！！！ 暂时找不到绝对得不同点，就以配置来判断吧，相信你们以后会有更好办法，哈哈哈哈哈
     *   {  LAO XIE WO XIANG XIN NI }
     *  Polygon:    getLatLngs()    setLatLngs()
     *  Polyline:   getLatLngs()    setLatLngs()
     *  Rectangle:  getLatLngs()    setLatLngs()
     *  Circle:     setLatLng()     getLatLng()
     */
    handleReallyAmazingEvent() {
      // console.time('changeCrs')
      this._map.eachLayer((layer) => {
        switch (true) {
          //  基础得marker 点
          case Boolean(layer._icon):
            this.changeMarkerLatLng(layer);
            break;
          //  图形
          case Boolean(layer._path):
            this.changeTuXingLatLng(layer);
            break;
        }
      });
      //  console.timeEnd('changeCrs')
    },

    changeMarkerLatLng(marker) {
      if (!marker) return;
      let latlng = marker.getLatLng();
      marker.setLatLng([latlng.lat, latlng.lng]);
    },

    changeTuXingLatLng(layer) {
      if (!layer) return;

      if (layer._mRadius) {
        let circleLatlng = layer.getLatLng();
        layer.setLatLng([circleLatlng.lat, circleLatlng.lng]);
      } else {
        let latlngList = [];
        // console.log('layer.getLatLngs()', layer.getLatLngs())
        if (layer.getLatLngs().length > 1) {
          latlngList = layer.getLatLngs().map((item) => {
            if (!item) return;
            return [item.lat, item.lng];
          });
        } else {
          latlngList = layer.getLatLngs()[0].map((item) => {
            if (!item) return;
            return [item.lat, item.lng];
          });
        }
        layer.setLatLngs(latlngList);
      }
    },

    initDrawControl() {
      let { control } = this.currentDrawer;
      control = control.sign ? control : { name: "地图工具", sign: "map_default" };
      this.currentDrawer.switch = !this.currentDrawer.switch;
    },

    /**
     *  这里得筛选没有过多得要求，只要list 中存在 latlng [lat, lng] 字段即可，
     *  默认得是举行筛选
     */
    async filterLatlngList(list, obj = { key: "latlng", value: "id" }) {
      if (!list) return [];
      this.handleCurrentEvent({ source: "mapfilter" });
      let layer = await new Promise((resolve) => {
        this._filterResolve = resolve;
      });
      // console.log(layer.getLatLngs(),'layer');
      let array = list
        .filter((v) => v[obj.key] && layer.getBounds().contains(v[obj.key]))
        .map((e) => {
          return e[obj.value];
        });
      return {
        array: array,
        layer: layer,
      };
    },
    /**
     *
     * 这个只用来获取,所选择的区域的经纬度的(矩形区域的4个点)
     */
    async getFilterLatlngList() {
      this.handleCurrentEvent({ source: "mapfilter" });
      let layer = await new Promise((resolve) => {
        this._filterResolve = resolve;
      });
      let array = layer.getLatLngs();
      return {
        layer,
        array: array[0],
      };
    },
    // reflashMap() {
    //     if (this._map) {
    //         setTimeout(() => {
    //             this._map.invalidateSize()
    //         }, 100)
    //     }
    // }
    async reflashMap() {
      await this.$utils.sleep(100);
      if (this._map) {
        this._map.invalidateSize();
      }
    },
  },
  activated() {
    this.currentRouter = this.$route.name;
    this.reflashMap();
  },
  beforeDestroy() {
    this._map.remove();
  },
};
</script>

<style lang="scss" scoped>
@import "./MaticsMapV2.scss";

.pdfViewBackground {
  position: relative;
  height: max-content;
  width: max-content;
  display: none;
}

.tagging {
  width: 100%;
  height: max-content;

  &__header {
    display: flex;
    align-items: center;
    height: 40px;
    border-bottom: solid 1px var(--border-color-lighter);
    justify-content: space-between;
  }

  &__content {
    padding: 10px 0;

    .popup-item {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;

      .latlng {
        max-width: 140px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }

      &:last-child {
        margin: 0;
      }
    }

    .icon {
      position: relative;

      .iconBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 279px;
        height: 30px;
        border: 1px solid var(--border-color-base);
        padding: 0 10px;
        line-height: 30px;

        .closeIcon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          opacity: 0.5;
          border-radius: 50%;
        }

        .iconImg {
          height: 100%;
          width: 30px;

          img {
            height: 100%;
            width: 100%;
          }
        }
      }

      .iconSelect {
        position: absolute;
        top: 30px;
        left: 32px;
        width: 279px;
        border: 1px solid var(--border-color-base);
        background-color: var(--background-color-base);
        z-index: 999;

        .box {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex-wrap: wrap;

          .item {
            width: 39px;
            height: 39px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }

  &__fotter {
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-top: solid 1px var(--border-color-lighter);

    i {
      font-size: 16px;
      cursor: pointer;
    }
  }
}
</style>
