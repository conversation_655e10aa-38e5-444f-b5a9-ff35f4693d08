export const mapControl = [
  //添加新功能记得加宽度，为了样式好看
  {
    name: "地图切换",
    icon: "el-icon-picture-outline",
    key: "mapChange",
    children: [
      {
        name: "高德地图",
        map: ["GaoDe.Normal.Map"],
        sign: "gaode",
        source: "GaoDe",
        width: "79px",
      },
      {
        name: "高德卫星",
        map: ["GaoDe.Satellite.Map", "GaoDe.Satellite.Annotion"],
        sign: "gaodeSatel",
        source: "GaoDe",
        width: "79px",
      },
      {
        name: "腾讯地图",
        map: ["Tencent.Normal.Map"],
        sign: "Tencent",
        source: "Tencent",
        width: "79px",
      },
      {
        name: "天地图",
        map: ["TianDiTu.Normal.Map", "TianDiTu.Normal.Annotion"],
        sign: "TianDiTuSatel",
        source: "TianDiTu",
        width: "79px",
      },
      {
        name: "天地图卫星",
        map: ["TianDiTu.Satellite.Map", "TianDiTu.Satellite.Annotion"],
        sign: "TianDiTu",
        source: "TianDiTu",
        width: "79px",
      },
      // { name: '宜兴地图', map: ['YX.Normal.Map', 'YX.Normal.Annotion'], sign: 'YX', source: 'YX', permisson: "yxmap" },
      // { name: '宜兴卫星图', map: ['YX.Satellite.Map', 'YX.Satellite.Annotion'], sign: 'YX', source: 'YX', permisson: "yxmap" },

      { name: "百度地图", map: ["Baidu.Normal.Map"], sign: "Baidu", source: "Baidu", width: "79px" },
      {
        name: "百度卫星",
        map: ["Baidu.Satellite.Map", "Baidu.Satellite.Annotion"],
        sign: "BaiduSatel",
        source: "Baidu",
        width: "79px",
      },
    ],
  },
  {
    name: "地图工具",
    icon: "el-icon-goods",
    key: "mapTool",
    children: [
      {
        name: "漫游",
        sign: "map_manyou",
        icon: "pony-roam",
        source: "manyou",
        width: "57px",
      },
      {
        name: "打印",
        sign: "map_print",
        icon: "",
        source: "print",
        width: "57px",
      },
      {
        name: "截图",
        sign: "map_screenshot",
        icon: "",
        source: "screenshot",
        width: "57px",
      },
      {
        name: "测距",
        sign: "map_ranging",
        icon: "pony-distance",
        source: "ranging",
        width: "57px",
      },
      {
        name: "测量",
        sign: "map_paint",
        icon: "pony-mianji",
        source: "paint",
        width: "56px",
      },
      {
        name: "全屏",
        sign: "map_fullScreen",
        icon: "pony-quanping",
        source: "fullScreen",
        width: "56px",
      },
      {
        name: "取点",
        sign: "map_getPoint",
        icon: "",
        source: "getPoint",
        width: "56px",
      },
      {
        name: "区域规划",
        sign: "map_rangearea",
        icon: "",
        source: "rangearea",
        width: "79px",
      },
      {
        name: "拉框放大",
        sign: "map_lkenlarge",
        icon: "pony-fangda1",
        source: "lkenlarge",
        width: "79px",
      },
      {
        name: "拉框缩小",
        sign: "map_lknarrow",
        icon: "pony-lakuangsuoxiao",
        source: "lknarrow",
        width: "79px",
      },

      {
        name: "城市导航",
        sign: "map_navigation",
        icon: "pony-quanping",
        source: "navigation",
        width: "79px",
      },
      {
        name: "我的视野",
        sign: "map_view",
        icon: "",
        source: "view",
        width: "79px",
      },
      {
        name: "地理信息查询",
        sign: "map_search",
        icon: "pony-dingweiweizhi",
        source: "search",
        width: "117px",
      },
    ],
  },
  {
    name: "业务模块",
    icon: "el-icon-menu",
    key: "bussiness",
    children: [
      {
        name: "公交线路",
        sign: "map_line",
        icon: "pony-guanglanduanliebiao",
        source: "line",
        width: "79px",
      },
      {
        name: "路径规划",
        sign: "map_linePlan",
        icon: "pony-gongjiaoxianlu",
        source: "lineplan",
        width: "79px",
      },
      {
        name: "工程项目",
        sign: "map_project",
        icon: "pony-shenhe-dianziweilan",
        source: "project",
        permisson: "monitor:gcxmtreelayer",
        width: "79px",
      },

      {
        name: "区域围栏",
        sign: "map_area_fence",
        icon: "pony-shenhe-dianziweilan",
        source: "areaFence",
        width: "79px",
      },

      {
        name: "兴趣点",
        sign: "map_inter",
        icon: "pony-hangdianxingqudian",
        source: "inter",
        width: "60px",
      },
      {
        name: "标注点",
        sign: "map_tagging",
        icon: "el-icon-star-off",
        source: "tagging",
        width: "60px",
      },

      {
        name: "围栏",
        sign: "map_fence",
        icon: "pony-shenhe-dianziweilan",
        source: "fence",
        width: "55px",
      },
    ],
  },
  {
    name: "显示设置",
    icon: "el-icon-menu",
    key: "mapPermisson",
    children: [
      {
        name: "路况",
        sign: "lukuang",
        icon: "pony-shenhe-dianziweilan",
        type: "switch_li",
        source: "lukuang",
      },
      {
        name: "工地",
        sign: "mapgd",
        icon: "pony-hangdianxingqudian",
        type: "switch_li",
        source: "mapgd",
        permisson: "monitor:gdterrlayer",
      },
      {
        name: "场站",
        sign: "mapcz",
        icon: "pony-guanglanduanliebiao",
        type: "switch_li",
        source: "mapcz",
        permisson: "monitor:changzhantree",
      },
      {
        name: "消纳场",
        sign: "mapxnc",
        icon: "pony-guanglanduanliebiao",
        type: "switch_li",
        source: "mapxnc",
        permisson: "monitor:xnctreelayer",
      },
      {
        name: "通行线路",
        sign: "maproute",
        icon: "pony-guanglanduanliebiao",
        type: "switch_li",
        source: "maproute",
        permisson: "monitor:txxltreelayer",
      },
    ],
  },
];

export const defaultConfig = {
  simple: {
    mapChange: ["GaoDe", "TianDiTu", "Baidu"],
    mapTool: ["ranging", "fullScreen", "rangearea", "search"],
    bussiness: [],
    mapPermisson: ["lukuang", "mapgd", "mapxnc", "maproute"],
  },
  addsearch: {
    mapChange: ["GaoDe", "TianDiTu", "Baidu"],
    mapTool: ["ranging", "fullScreen", "rangearea", "search"],
    bussiness: ["tagging"],
    mapPermisson: ["lukuang", "mapgd", "mapxnc", "maproute"],
  },
  matics: {
    mapChange: ["GaoDe", "TianDiTu", "YX", "Baidu"],
    mapTool: [
      "manyou",
      "lkenlarge",
      "lknarrow",
      "ranging",
      "paint",
      "fullScreen",
      "navigation",
      "view",
      "print",
      "screenshot",
      "rangearea",
      "search",
      "getPoint",
    ],
    bussiness: ["fence", "inter", "line", "lineplan", "tagging", "navigation", "view", "paint", "project", "areaFence"],
    mapPermisson: ["lukuang", "mapgd", "mapxnc", "maproute", "mapcz"],
  },
  justmap: {
    mapChange: ["GaoDe", "Tencent", "Baidu", "TianDiTu"],
    mapTool: ["ranging", "fullScreen", "rangearea", "search"],
    bussiness: ["tagging"],
    mapPermisson: [],
  },
};

/**
 * 又是一个恶心得万一，还路由互斥 这种东西都有得说！！！！！
 */
export const routeRestraint = {
  monitor: ["mapgd", "mapxnc", "maproute", "mapcz"],
  FenseMonitor: ["mapgd", "mapxnc", "maproute", "mapcz"],
};

export const mapSetting = {
  minZoom: 3,
  maxZoom: 18,
  tapTolerance: 25,
  zoomControl: false,
  attributionControl: false,
  // center: [30.1956, 120.2073],
  // center: [31.317909, 119.80185],
  zoom: 8,
};

export const mapBasicControl = {
  scale: L.control.scale({
    imperial: false,
  }),

  zoom: L.control.zoom({
    zoomInTitle: "放大",
    zoomOutTitle: "缩小",
    position: "topleft",
  }),
};
