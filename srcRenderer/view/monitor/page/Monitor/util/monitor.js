/*
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-09-26 19:54:20
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-16 11:03:49
 */
import * as PUBLICUTIL from '../../../util/monitorUtil'
import * as api from '@/axios/api'
import moment from 'moment'
import store from "../../../../../store";
import {transSecondToHMSCN} from "../../../util/monitorUtil";

const shitUserList = ['clzcl', 'zljt']

const initGsixData = async() => {
    let res = await api.getGsixInfo()
    let result = {}
    if(!res || res.status != 200 || !res.data.length) return
    res.data.forEach(item => {
        result[item.gsix_vehicle_id] = item
    })
    return result
}


const initVideoFormat = () => {
    let Obj = store.state.vehicle.videoByVehicleId
    let result = {}
    Object.values(Obj).forEach(item => {
        result[item.vehicle_id] = {
            video_code: item.code,
            video_remark: item.remark,
            video_name: item.value
        }
    })
    return result
}


const initDriverFormat = () => {
    let Obj = store.state.vehicle.driverByVehicleId
    let result = {}
    Object.values(Obj).forEach(item => {
        result[item.vehicle_id] = {
            driver_id: item.driver_id,
            driver_name: item.driver_name,
            driver_phone: item.driver_phone,
            driver_idcardno: item.id_card_no,
            driver_issDate: item.issuing_date,
            driver_issOrg: item.issuing_org,
            driver_licNum: item.licence_number,
            driver_licState: item.licence_status,
            driver_staffNo: item.staff_no
        }
    })
    return result
}


const initCollectFormat = async () => {
    let Obj = await store.dispatch('ztreeData/getTreeData', 'follow')
    let result = {}
    if(Obj && Obj.children) {
        Obj.children.forEach(item => {
            result[item.id] = true
        })
    }
    return result
}

const initClzclCustomFormat = async() => {
    let result = {}
    if(!shitUserList.includes(store.state.main.company)) return result
    let res = await api.getRecentTrailState()
    if(!res || res.status != 200 || !res.data[0]) return
    res.data.forEach(item => {
        result[item.vehicleId] = item.trailState
    })
    return result
}


const initDefaultVehicle = {
    collected: false,
    stand_doorState: '无',            // 车门状态
    stand_dianState: '无',            // 电路状态
    stand_oilState: '无',             // 油路状态
    stand_operateState: '无',         // 营运状态
    stand_inandoutarea: '无',         // 进出区域 / 线路
    stand_exporEventId: '无',         // 人工处理事件ID
    stand_oilNumber: '无',               // 油量
    stand_inandoutline: '无',         // 线路行驶
    stand_overspeedState: '无',       // 超速状态
    stand_lockState: '未定位',        // 定位状态

    stand_zhzState: '无',             // 装载状态
    stand_clzz: '-',                    // 车辆载重

    media_lostSignal: '无',         // 信号丢失
    media_fadeSignal: '无',         // 信号遮挡
    media_mainStroStatus: '正常',   // 主存储器状态
    media_auxStroStatus: '正常',    // 灾备存储器状态
    media_otherStroStatus: '无',    // 其他视频故障
    media_numBeyond: '无',          // 有无特殊报警录像达到存储阈值

    menci: '关',
    lengji: '开',
    wendu: '通道一：-7℃；通道二：-10℃；通道三：-5℃；通道四：-2℃；',
}

// 车辆信号状态位
const carStatus = {
    0: '右转向灯信号:',
    1: '左转向灯信号:',
    2: '制动信号:',
    3: '倒档信号:',
    4: '空挡信号:',
    5: '离合器状态:',
    6: '拖车开关:',
    7: '近光灯信号：',
    8: '远光灯信号:',
    9: '雾灯信号:',
    10: '示廓灯:',
    11: '喇叭信号:',
    12: '空调状态:',
    13: '缓速器工作:',
    14: 'ABS工作:',
    15: '加热器工作:',
}
const getCarStatus = (value) => {
    let arr = []
    if (value && typeof (value) == 'object' ) {
        for (let key in carStatus) {
            if (value.includes(Number(key))) {
                arr.push(carStatus[key] + '开')
            } else {
                arr.push(carStatus[key] + '关')
            }
        }
    }
    return arr
}
//底部表格灯光信号

const formatLightStatus = (list,obj) => {
    const statusObj = {
      light_signal_0: "关",
      light_signal_1: "关",
      light_signal_2: "关",
      light_signal_7: "关",
      light_signal_8: "关",
    };
  if (list) {
    list.forEach((item) => {
      let name = "light_signal_" + item;
      switch (item) {
        case 0:
          statusObj[name] = "开";
          break;
        case 1:
          statusObj[name] = "开";
          break;
        case 2:
          statusObj[name] = "开";
          break;
        case 7:
          statusObj[name] = "开";
          break;
        case 8:
          statusObj[name] = "开";
          break;
      }
    });
  }
  Object.assign(obj, statusObj);
  return obj;
};

// 客载状态
const loadState = {
    52: '空车',
    53: '半载',
    54: '满载',
}
const getLoadState = (value) => {
    let str = ''
    if (value && value !== '-') {
        for (let key in loadState) {
            if (value.includes(Number(key))) {
                return loadState[key]
            }
        }
        if (str === '') {
            return '无'
        }
    } else {
        return '无'
    }
}

// 胎压
const tireStatus = [
    '前左1:',
    '前左2:',
    '前右1:',
    '前右2:',
    '中左1:',
    '中左2:',
    '中左3:',
    '中右1:',
    '中右2:',
    '中右3:',
    '后左1:',
    '后左2:',
    '后左3:',
    '后右1:',
    '后右2:',
    '后右3:'
]
const getCarTireStatus = (value) => {
    if (value && value !== '-') {
        let arr = []
        let list = value.split(',')
        tireStatus.forEach((item,index) => {
            if (list[index] != -1) {
                arr.push(item + list[index])
            } else {
                arr.push(item + '无')
            }
        })
        return arr
    } else {
        return []
    }
}

// 卫星
const satellite = {
    18: 'GPS卫星进行定位',
    19: '北斗卫星进行定位',
    20: 'GLONASS卫星进行定位',
    21: 'Galileo卫星进行定位'}
const getSatelliteStatus = (value) => {
    let arr = []
    if (value && value !== '-') {
        for (let key in satellite) {
            if (value.includes(Number(key))) {
                arr.push('使用' + satellite[key])
            } else {
                arr.push('未使用' + satellite[key])
            }
        }
        return arr
    } else {
        return []
    }
}

// 门状态
const door = {
    13: '门1/前门',
    14: '门2/中门',
    15: '门3/后门',
    16: '门4/驾驶席门',
    17: '门5/自定义门'
}
const getDoorStatus = (value) => {
    let arr = []
    if (value) {
        for (let doorKey in door) {
            if (value.includes(Number(doorKey))) {
                arr.push(door[doorKey] + '开')
            } else {
                arr.push(door[doorKey] + '关')
            }
        }
        return arr
    } else {
        return []
    }
}

const workStatus = {
    0: '未作业',
    1: '作业中',
    10: '禁用',
    11: '启用'
}
const getWorkStatus = (value) => {
    if (value && value !== '-' || value === 0) {
        return workStatus[value]
    } else {
        return '-'
    }
}

// IO状态
const changeIOStauts = (value) => {
    if (value && value !== '-') {
       
        return 'AD0:' +  value[0] + ';AD1:' + value[1]
    } else {
        return '-'
    }
}

const noUserParmas = [
    'alarmStatus',
    // 'dayStartMile',
    'terminalStatus',
    // 'lat', 利用它做一下排序
    'lng',
    'nearestStation',
    'monthMile',
    // 'dayMile',
    'companyId',
    'deptId',
    'trans_operaPermitImg',
    'trans_vehicleOperaStatus',
    'companyName',
    'deptName'
]

const carStopType = ['行驶中', '停车', '熄火', '离线']

const mileFormat = (mile) => {
    if(!mile || !Number(mile))return '0.0公里'
    return (mile / 10000000) > 1?(mile / 10000000).toFixed(2) + '万公里':( mile / 1000 ).toFixed(2) + '公里'
}

const getCustomVehicleStyle = (vehicle) => {
    let {industry_type,concrete_state,plate,state,gpsSpeed,concrete_vehicle_type,rollerState,alias,stop_time,stop_type} = vehicle
    let content = `<div>${ plate }</div>`
    // if (PUBLICUTIL.checkCarRunState(state)) {
        content = content + `<div>${ gpsSpeed || 0 }km/h</div>`
    // }
    if(industry_type == 8 || industry_type == 18){
        content = ''
        let list = store.state.vehicle.stCarStatus.map(item => item.key)
        list.forEach((item) => {
            switch (item) {
                case 'alias':
                    if (alias && alias !== '-') {
                        if (content.length !== 0) content = content + '-'
                        content += `${alias}`
                    }
                    break
                case 'plate':
                    if (plate && plate !== '-') {
                        if (content.length !== 0) content = content + '-'
                        content += `${plate}`
                    }
                    break
                case 'concrete_vehicle_type':
                    if (concrete_vehicle_type && concrete_vehicle_type !== '-') {
                        if (content.length !== 0) content = content + '-'
                        content += getConcrete(concrete_vehicle_type)
                    }
                    break
                case 'rollerState':
                    if (rollerState && rollerState !== '-') {
                        if (content.length !== 0) content = content + '-'
                        content += rollerState.split('')[0]
                    }
                    break
                case 'concrete_state':
                    if (content.length !== 0) content = content + '-'
                    content += `<span style='${getCustomVehicleStyleContent(concrete_state)}'>${getCustomVehicleStatus(concrete_state)}</span>`
                    break
                case 'gpsSpeed':
                    if (gpsSpeed > 0 || stop_type === 0) {
                        if (content.length !== 0) content = content + '-'
                        if (PUBLICUTIL.checkCarRunState(state)) {
                            content += `${ gpsSpeed }km/h`
                        } else {
                            content += `0km/h`
                        }
                    }
                    break
                case 'carStatus':
                    if (gpsSpeed === 0 && stop_type && stop_type !== '-') {
                        if (content.length !== 0) content = content + '-'
                        if (stop_type === 0) {
                            content += `${carStopType[stop_type]}`
                        } else {
                            content += `${carStopType[stop_type]}${transSecondToHMSCN(stop_time)}`
                        }
                    }
            }
        })
    }
    if(industry_type == 14){
      content = `${plate}-${vehicle.jianhua56_isbill}-<span style='${getCustomVehicleStyleContentJianhua(vehicle.jianhua56_isload)}'>${vehicle.jianhua56_isload}</span>-${ gpsSpeed || 0 }km/h`
    }
    return content
}
const getCustomVehicleStyleContent = (state) =>{
    let color = '#fff'
    let backgroundColor = 'rgba(112,128,144,.5)'
    switch (state) {
        case '正供':
            color = 'rgb(10, 203, 103)'
            backgroundColor='rgba(216, 247, 231,.2)'
            break;
        case '出厂':
            color = '#2638FE'
            backgroundColor='rgba(38, 56, 254,.2)'
            break;
        case '回厂':
            color = '#67CFB9'
            backgroundColor='rgba(103, 207, 185,.2)'
            break;
        case '在工地':
            color = '#927AFE'
            backgroundColor='rgba(146, 122, 254,.2)'
            break;
        case '供毕':
            color = '#63BCC9'
            backgroundColor='rgba(90, 188, 201,.2)'
            break;
        default:
            break;
    }
    let content = `color:${color};background-color:${backgroundColor};padding:2px 4px;margin-left:3px;border-radius:3px;font-size:12px`
    return content
}
const getCustomVehicleStyleContentJianhua = (state) =>{
  let color = '#fff'
  let backgroundColor = 'rgba(112,128,144,.5)'
  switch (state) {
      case '重载':
          color = '#ff5359'
          backgroundColor='rgba(255, 83, 89,.2)'
          break;
      case '空载':
          color = '#63ba47'
          backgroundColor='rgba(99, 186, 71,.2)'
          break;
      default:
          break;
  }
  let content = `color:${color};background-color:${backgroundColor};padding:2px 4px;margin-left:3px;border-radius:3px;font-size:12px`
  return content
}
const getCustomVehicleStatus = (state) => {
    let status
    switch (state) {
        case '正供':
            status = '正'
            break;
        case '出厂':
            status = '出'
            break;
        case '回厂':
            status = '回'
            break;
        case '在工地':
            status = '工'
            break;
        case '供毕':
            status = '毕'
            break;
        default:
            status = '-'
            break;
    }
    return status
}
const drumState = ['未知', '正转', '反转', '停转']
const getDrumState = (state) => {
    let status
    if (state && state !== '-') {
        status = Number(state.split('|')[0])
        status = drumState[status]
    } else {
        status = '-'
    }
    return status
}

// 车辆类型
const getConcrete = (value) => {
    let str = ''
    if (value && value !== '-') {
        str = value.substr(0, 1)
    } else {
        return '-'
    }
    return str
}
//gps报警
const formatGpsAlarm = (alarmStatus,obj) =>{
        let value = {};
        if(alarmStatus){
             alarmStatus.forEach((item) => {
               let name = "gpsAlarm_" + item;
               value[name] = 1;
             });
             Object.assign(obj, value);
             return obj;
        }

}

//弹窗报警
const getAlarmStr = (obj) =>{
    let str = '';
      if (obj.hasOwnProperty("media_261")==true){
        if (obj.media_261 == 1) {
          str += "视频信号丢失报警,";
        }
    }
    if (obj.hasOwnProperty("media_262") == true) {
      if (obj.media_262 == 1) {
        str += "视频信号遮挡报警,";
      }
    }
    if (obj.hasOwnProperty("media_main_263") == true) {
      if (obj.media_main_263 == 1) {
        str += "主存储单元故障报警,";
      }
    }
        if (obj.hasOwnProperty("media_aux_263") == true) {
          if (obj.media_aux_263 == 1) {
            str += "灾备存储单元故障报警,";
          }
        }if (obj.hasOwnProperty("media_264") == true) {
          if (obj.media_264 == 1) {
            str += "其他视频设备故障报警,";
          }
        }if (obj.hasOwnProperty("media_265") == true) {
          if (obj.media_265 == 1) {
            str += "客车超员报警,";
          }
        }if (obj.hasOwnProperty("media_266") == true) {
          if (obj.media_266 == 1) {
            str += "异常驾驶行为报警,";
          }
        }if (obj.hasOwnProperty("media_267") == true) {
          if (obj.media_267 == 1) {
            str += "特殊报警录像达到存储阈值报警,";
          }
        }
    if(obj.gpsAlarm){
        obj.gpsAlarm.forEach(item=>{
            let alarmValue = store.state.dictionary.alarm[item];
            // console.log(alarmValue);
            str += alarmValue&&alarmValue.name?alarmValue.name+',':''
        })
    }
    str = str.substring(0,str.length-1)
    return str;
}


/**
 *
 * @param {初始化最后位置信息需要转经纬度得哦} item
 * @param {实时监控的数据转换已经初始化了,不需要添加默认字段,不然会覆盖原有的字段} init
 */
const initVehicleBasicData = (item,init = true) => {
    item.acc = (item.terminalStatus & 0x1) === 1
    if(init){
        Object.assign(item, initDefaultVehicle)
    }
    if(item.lat) {
        item.latlng = [item.lat, item.lng]
    }
    if(shitUserList.includes(store.state.main.company)) {
        item.trailState = 4
    }
    item.working_time_value = item.working_time // 作业时长
    item.working_time = item.working_time?PUBLICUTIL.transSecondToHMSCN(item.working_time):'-'
    item.format_dept = item.companyName + '>>' + item.deptName
    initWsVehicleBasicData(item)
}


const initVehicleBasicData2 = (item) => {
    item.acc = (item.terminalStatus & 0x1) === 1
    if(item.lat) {
        item.latlng = [item.lat, item.lng]
    }
    if(shitUserList.includes(store.state.main.company)) {
        item.trailState = 4
    }
    item.working_time_value = item.working_time // 作业时长
    item.working_time = item.working_time?PUBLICUTIL.transSecondToHMSCN(item.working_time):'-'
    item.format_dept = item.companyName + '>>' + item.deptName
    // console.log(item.mile,item.dayStartMile)
    initWsVehicleBasicData(item)
}


const computedOffLineTime = (value) => {
    if(!value) return '从未上线'
    var time = (new Date().getTime() - value)
    var days = parseInt(time / (1000 * 60 * 60 * 24))
    var hours = parseInt((time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    var minutes = parseInt((time % (1000 * 60 * 60)) / (1000 * 60))
    let content = ''
    if(days) {
        content += days + ' 天 '
    } else {
        content += '0 天 '
    }
    if(hours) {
        content += hours + ' 小时 '
    } else {
        content += '0 小时 '
    }
    return content + minutes + " 分钟 "
}
const computedOffLineTimeValue = (value) => {
    if (value) {
        return (new Date().getTime() - value)
    } else {
        return 0
    }
}
// 初始化VehicleBasic数据
const initWsVehicleBasicData = (item) => {
    // console.log(item.ext_status, 'item.ext_status')
    item.state = PUBLICUTIL.getVehicleState(item); //这里会得到一个'vehicle_0_0'的字符准
    item.offline_time = "在线";
    item.offline_time_value = 0;
    if (PUBLICUTIL.checkCarOffLine(item.state)) {
      item.offline_time = computedOffLineTime(item.gpsTime);
      item.offline_time_value = computedOffLineTimeValue(item.gpsTime);
    }
    item.gpsSinglep = PUBLICUTIL.getGpsSingleState(item.gpsSingle);
    item.gsmSinglep = PUBLICUTIL.getGsmSingleState(item.gsmSingle);
    item.dayMile = (item.mile - item.dayStartMile) / 1000 + "km";
    item.dayMile_value = (item.mile - item.dayStartMile) / 1000;
    item.mile_value = 0;
    if (item.mile) {
      item.mile_value = item.mile;
      item.mile = mileFormat(item.mile);
    }
    item.format_gpsTime = item.gpsTime
      ? moment(item.gpsTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    item.format_acc = item.acc ? "开" : "关";
    item.format_dire = PUBLICUTIL.getVehicleDireState(item.dire);
    item.format_state = PUBLICUTIL.vehicleCustomStateExplain(item);
    formatLightStatus(item.signal_status, item);
    item.ext_status = getCarStatus(item.ext_status); // 信号状态
    item.mini_tire_pressure_status = getCarTireStatus(item.mini_tire_pressure); // 胎压
    item.satelliteStatus = getSatelliteStatus(item.terminalStatusArray); // 卫星
    item.doorStatus = getDoorStatus(item.terminalStatusArray); // 门
    item.mini_io_status =
      item.mini_io_status == 1
        ? "深度休眠状态"
        : item.mini_io_status == 2
        ? "休眠状态"
        : "-"; // IO状态
    item.mini_analog = changeIOStauts(item.mini_analog); // 模拟量
    item.driving_state = item.terminalStatusArray
      ? item.terminalStatusArray.includes(22)
        ? "行驶"
        : "停止"
      : "停止"; // 行驶状态
    item.forward_collision_warning = item.terminalStatusArray
      ? item.terminalStatusArray.includes(50)
        ? "有"
        : "无"
      : "无"; // 前撞预警
    item.deviation_warning = item.terminalStatusArray
      ? item.terminalStatusArray.includes(51)
        ? "有"
        : "无"
      : "无"; // 偏离预警
    item.load_state = getLoadState(item.terminalStatusArray); // 客载状态
    item.encryption = item.terminalStatusArray
      ? item.terminalStatusArray.includes(5)
        ? "加密"
        : "未加密"
      : "未加密"; // 经纬度已经保密插件加密
    item.position = item.terminalStatusArray
      ? item.terminalStatusArray.includes(1)
        ? "已定位"
        : "未定位"
      : "未定位";
      item.gps_module = item.terminalStatusArray
      ? item.terminalStatusArray.includes(55)
        ? "打开"
        : "关闭"
      : "关闭";  // 定位是否打开
      item.positioned = item.positioned?'已定位':'未定位'
    item.coldchain_job_status = getWorkStatus(item.coldchain_job_status) // 工作状态

    item.driver_name = item.driver_name ? item.driver_name : "";
    item.ftt_doorstate = item.ftt_doorstate ? item.ftt_doorstate : "";
    item.ftt_ysj = item.ftt_ysj ? item.ftt_ysj : "";
    item.ftt_tempdesc = item.ftt_tempdesc ? item.ftt_tempdesc : "";
    item.coldchain_rota_speed_value =
      item.coldchain_rota_speed === "-" ? 0 : Number(item.coldchain_rota_speed);
    item.coldchain_oil_consum_value =
      item.coldchain_oil_consum === "-" ? 0 : Number(item.coldchain_oil_consum);
    PUBLICUTIL.deleteUnUseAble(noUserParmas, item);
    formatGpsAlarm(item.gpsAlarm,item);
    item.gpsSingleDes = item.gpsSinglep==1?`${item.gpsSingle}-弱`:item.gpsSinglep==2?`${item.gpsSingle}-中`:item.gpsSinglep>=3?`${item.gpsSingle}-强`:'无'
    item.gsmSingleDes =
      item.gsmSinglep == 1
        ? `${item.gsmSingle}-弱`
        : item.gsmSinglep == 2
        ? `${item.gsmSingle}-中`
        : item.gsmSinglep >= 3
        ? `${item.gsmSingle}-强`
        : "无";
    item.rollerState = getDrumState(item.rollerState)
    item.canUploadTime = item.canUploadTime?moment(item.canUploadTime).format("YYYY-MM-DD HH:mm:ss") :"";
    item.popupAlarm = getAlarmStr(item)
    item.wash_water_level =  (typeof item.wash_water_level == 'number')?Math.round(item.wash_water_level / 65535 *100) + '%':item.wash_water_level;
  }
const getPopupSetting = [
    // { name: '部门', key: 'format_dept', size: 'all' },
    { name: '司机', key: 'driver_name', size: 'half' },
    { name: '电话', key: 'driver_phone', size: 'half' },
    { name: '速度', key: 'gpsSpeed', size: 'half', tip: 'km/h' },
    { name: 'ACC状态', key: 'format_acc', size: 'half' },
    { name: '里程', key: 'mile', size: 'half' },
    { name: '设备', key: 'terminalNo', size: 'half' },
    // { name: '位置', key: 'location', size: 'all' },
]

const numList = ['gpsSpeed','simCode','terminalNo','height','pulseSpeed','working_time','mile','coldchain_rota_speed','fuelEconomy','dayMile','offline_time','gpsSingleDes','gsmSingleDes','coldchain_oil_consum','latlng']
const tableConvert = {
    'working_time': 'working_time_value',
    'mile': 'mile_value',
    'coldchain_rota_speed': 'coldchain_rota_speed_value',
    'coldchain_oil_consum': 'coldchain_oil_consum_value',
    'dayMile': 'dayMile_value',
    'offline_time': 'offline_time_value',
    'gpsSingleDes':'gpsSingle',
    'gsmSingleDes':'gsmSingle',
    'latlng':'lat'
}
const getTableSetting = [
  {
    name: "企业车队",
    key: "format_dept",
    size: "280",
    align: "left",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "vehicle_info",
    id: "format_dept",
  },
  {
    name: "车牌号",
    key: "plate",
    size: "100",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "vehicle_info",
    id: "plate",
  },
  {
    name: "状态",
    key: "format_state",
    size: "80",
    align: "center",
    type: 1,
    icon: 1,
    typeKey: "state",
    children: [],
    parent_id: "vehicle_info",
    id: "format_state",
  },
  {
    name: "定位时间",
    key: "format_gpsTime",
    size: "140",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "format_gpsTime",
  },
  {
    name: "GPS速度(Km/h)",
    key: "gpsSpeed",
    size: "120",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "gpsSpeed",
  },

  {
    name: "方向",
    key: "format_dire",
    size: "100",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "format_dire",
  },
  {
    name: "ACC",
    key: "format_acc",
    size: "100",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "format_acc",
  },
  {
    name: "VSS速度",
    key: "pulseSpeed",
    size: "100",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "pulseSpeed",
  },
  {
    name: "经纬度",
    key: "latlng",
    size: "300",
    align: "center",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "latlng",
  },
  {
    name: "地址",
    key: "location",
    size: "350",
    align: "left",
    type: 1,
    icon: 0,
    children: [],
    parent_id: "gps_info",
    id: "location",
  },
];

const getVehicleBasicStting = {
    'basic': [
        { name: '企业部门', key: 'format_dept', size: 'all' },
        { name: '车辆状态', key: 'format_state', size: 'half' },
        { name: 'ACC状态', key: 'format_acc', size: 'half' },
        { name: 'SIM卡号', key: 'simCode', size: 'all' },
        { name: '设备号码', key: 'terminalNo', size: 'all' },
        { name: '终端ID', key: 'deviceId', size: 'all' },
        { name: 'IMEI号码', key: 'deviceImei', size: 'all' },
        { name: '速度', key: 'gpsSpeed', size: 'half', tip: 'km/h' },
        { name: '车型', key: 'vehicleType', size: 'half' },
        { name: '行业', key: 'transType', size: 'half' },
        { name: '车牌颜色', key: 'color', size: 'half' },
        { name: '里程', key: 'mile', size: 'all' },
        { name: '位置', key: 'location', size: 'all' },
    ],
    'vehicle': [
        { name: 'VIN码', key: 'vin', size: 'all' },
        { name: '通道数', key: 'channelCount', size: 'half' },
        { name: '经纬度', key: 'latlng', size: 'all' },
        { name: '方向', key: 'format_dire', size: 'half' },
        { name: '高度', key: 'height', size: 'half' },
        { name: '油量', key: 'stand_oilNumber', size: 'half' },
        { name: '车门', key: 'stand_doorState', size: 'half' },
        { name: '电路', key: 'stand_dianState', size: 'half' },
        { name: '油路', key: 'stand_oilState', size: 'half' },
        { name: '营运状态', key: 'stand_operateState', size: 'half' },
        { name: '超速状态', key: 'stand_overspeedState', size: 'half' },
        { name: '线路行驶', key: 'stand_inandoutline', size: 'half' },
        { name: '装载状态', key: 'stand_zhzState', size: 'half' },
        { name: '车辆载重', key: 'stand_clzz', size: 'half', tip: '吨' },
        { name: '车厢温度', key: 'mini_temp', size: 'half', tip: '°' },
        { name: 'IO状态', key: 'mini_io_status', size: 'half'},
        { name: '行驶状态', key: 'driving_state', size: 'half'},
        { name: '紧急查车系统采集的前撞预警', key: 'forward_collision_warning', size: 'roota',width: '200px'},
        { name: '经纬度是否保密插件加密', key: 'encryption', size: 'roota',width: '200px'},
        { name: '模拟量', key: 'mini_analog', size: 'roota'},
        { name: '车道偏离预警', key: 'deviation_warning', size: 'half', width: '120px'},
        { name: '客载状态', key: 'load_state', size: 'half'},
        { name: '网络信号强度', key: 'gsmSingle', size: 'half', width: '120px'},
        { name: '定位', key: 'position', size: 'half'},
        { name: '人工确认报警事件ID', key: 'stand_exporEventId', size: 'roota' },
        { name: '记录仪信号', key: 'stand_single', size: 'roota' },
        { name: '电子运单', key: 'mini_waybill', size: 'roota' },
    ],
    'gsix': [
        { name: '大气压值', key: 'gsix_pressure', size: 'rooth', tip: 'kPa' },
        { name: '发动机净输出扭矩', key: 'gsix_engine', size: 'rooth', tip: '%' },
        { name: '摩擦扭矩', key: 'gsix_pressure', size: 'rooth', tip: '%' },
        { name: '发动机转速', key: 'gsix_engine', size: 'rooth', tip: 'rpm' },
        { name: '反应剂余量', key: 'gsix_rest', size: 'rooth', tip: '%' },
        { name: 'SCR入口温度', key: 'gsix_src_temp1', size: 'rooth', tip: "deg C" },
        { name: 'SCR出口温度', key: 'gsix_src_temp2', size: 'rooth', tip: "deg C" },
        { name: 'DPF压差', key: 'gsix_dpf', size: 'rooth', tip: 'kPa' },
        { name: '发动机冷却液温度', key: 'gsix_cool_temp', size: 'rooth', tip: "deg C" },
        { name: '油箱液位', key: 'gsix_oil_rate', size: 'rooth' , tip: "%" },
        { name: '进气量', key: 'gsix_gas', size: 'rooth', tip: "kg/h" },
        { name: '发动机燃料流量', key: 'gsix_oil', size: 'rooth', tip: "L/h" },
        { name: 'SCR上游NOx传感器输出', key: 'gsix_src_upper', size: 'roota', tip: "ppm" },
        { name: 'SCR下游NOx传感器输出', key: 'gsix_src_lower', size: 'roota', tip: "ppm" },
    ],
    'obdb': [
        { name: 'OBD协议状态', key: 'gsix_protocol', size: 'rooth' },
        { name: 'MIL状态', key: 'gsix_mil', size: 'rooth' },
        { name: '软件标定识别号', key: 'gsix_scn', size: 'rooth' },
        { name: '标定验证码CVN', key: 'gsix_cvn', size: 'rooth' },
        { name: 'IUPR值', key: 'gsix_iupr', size: 'rooth' },
        { name: '故障码总数', key: 'gsix_errors_count', size: 'rooth' },
        { name: '故障码信息列表', key: 'gsix_errors_list', size: 'rooth' },
    ],
    'obdz': [
        { name: '催化转化器监控', key: 'cms' },
        { name: '加热催化转化器监控', key: 'hcms' },
        { name: '蒸发系统监控', key: 'esms' },
        { name: '二次空气系统监控', key: 'sasms' },
        { name: 'A/C系统制冷剂监控', key: 'srms' },
        { name: '排气传感器监控', key: 'egsms' },
        { name: '排气传感器加热器监控', key: 'egshms' },
        { name: 'EGB/VVT系统监控', key: 'egr' },
        { name: '冷启动辅助系统监控', key: 'csasms' },
        { name: '增压压力控制系统监控', key: 'bpcsms' },
        { name: 'DFF监控', key: 'dpfms' },
        { name: 'NOx催化还原/吸附器监控', key: 'nox' },
        { name: 'NMHC样话催化器监控', key: 'nmhc' },
        { name: '失火监控监控', key: 'mms' },
        { name: '燃油系统监控', key: 'fsms' },
        { name: '总和成分监控', key: 'ccms' },
    ],
    'screen': [
        { name: '信号丢失', key: 'media_lostSignal', size: 'roota' },
        { name: '信号遮挡', key: 'media_fadeSignal', size: 'roota' },
        { name: '主存储器', key: 'media_mainStroStatus', size: 'roota' },
        { name: '灾备存储', key: 'media_auxStroStatus', size: 'roota' },
        { name: '其他视频设备故障', key: 'media_otherStroStatus', size: 'roota', width: '120px' },
        { name: '录像达到存储阈值', key: 'media_numBeyond', size: 'roota', width: '120px' },
    ],
    'driver': [
        { name: '当前司机', key: 'driver_name', size: 'roota' },
        { name: '司机工号', key: 'driver_staffNo', size: 'roota' },
        { name: '司机手机号', key: 'driver_phone', size: 'roota' },
        { name: '身份证号', key: 'driver_idcardno', size: 'roota' },
        { name: '发证日期', key: 'driver_issDate', size: 'roota' },
        { name: '发证机构', key: 'driver_issOrg', size: 'roota' },
        { name: '从业资格证', key: 'driver_licNum', size: 'roota' },
        { name: '证照状态', key: 'driver_licState', size: 'roota' },
    ],
    'onLineDriver': [
        { name: '驾驶员姓名', key: 'on_driver_name', size: 'roota' },
        { name: '身份证号', key: 'on_driver_idcardno', size: 'roota' },
        { name: '发证机构名称', key: 'on_driver_issOrg', size: 'roota' },
        { name: '从业资格证编码', key: 'on_driver_licNum', size: 'roota' },
        { name: '证件有效期', key: 'on_driver_expirationDate', size: 'roota' },
    ],
    'trans': [
        { name: '运输货物', key: 'trans_Goods', size: 'roota' },
        { name: '运输许可证号', key: 'trans_CardNo', size: 'roota' },
        { name: '道路运输证发证机构', key: 'trans_CardOrg', size: 'roota', width: '120px' },
        { name: '道路运输证有效期起', key: 'trans_CardStartDate', size: 'roota', width: '120px' },
        { name: '道路运输证有效期止', key: 'trans_CardEndDate', size: 'roota', width: '120px' },
        { name: '车辆审验状态', key: 'trans_vehicleVerifyStatus', size: 'roota', width: '120px' },
    ],
    'gps': [
        { name: '北斗卫星数量', key: 'bdsListNum', size: 'all' },
        { name: 'GPS卫星数量', key: 'gpsListNum', size: 'all' },
        { name: 'GLONASS卫星数量', key: 'glonassListNum', size: 'all', width: '120px'},
        { name: 'GALILEO卫星数量', key: 'galileoListNum', size: 'all', width: '120px' },
    ]
}

const satelliteDetail = [
    { name: '卫星编号', key: 'satelliteNo', size: 'half' },
    { name: '仰角', key: 'elevation', size: 'half', tip: '°' },
    { name: '方位角', key: 'azimuth', size: 'half', tip: '°' },
    { name: '载噪比', key: 'ratio', size: 'half', tip: 'BHz' },
]

const rollerStatusValue = {
    0: '未知',
    1: '正转',
    2: '反转',
    3: '停转'
}



export {
    initVehicleBasicData,
    initDriverFormat,
    initClzclCustomFormat,
    initVideoFormat,
    initGsixData,
    getPopupSetting,
    getVehicleBasicStting,
    getTableSetting,
    getCustomVehicleStyle,
    initWsVehicleBasicData,
    initVehicleBasicData2,
    initCollectFormat,
    shitUserList,
    rollerStatusValue,
    satelliteDetail,
    numList,
    tableConvert,
    initDefaultVehicle,
    getCustomVehicleStyleContentJianhua
}
