<!--
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-09-29 10:15:01
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-10 15:10:09
 -->
<template>
  <div class="monitorpupop">
    <div class="monitorpupop__header bor--b dfb">
      <span @click="popupEvent(5)" class="fon--m" style="font-size: 14px;">{{ vehicleInfo.plate }}</span>
      <span>{{ vehicleInfo.format_gpsTime }}</span>
      <span>{{ vehicleInfo.format_state }}</span>
      <span class="dfb">
        <singleState style="margin-right: 10px;" singleType="gps" :singleState="vehicleInfo.gpsSinglep"></singleState>
        <singleState singleType="gsm" :singleState="vehicleInfo.gsmSinglep"></singleState>
      </span>
    </div>
    <div class="monitorpupop__content dfbw">

      <DataRow v-for="(item, index) in setting" :key="index" type="popup" :size="item.size" width="65px"
               :title="$ct(`${item.key}`)" :value="vehicleInfo[item.key]" :tip="item.tip" :color="item.key">
      </DataRow>

    </div>
    <div class="monitorpupop__fotter bor--t dfs fon--m" v-if="button">
      <i v-if="hasPermission('monitor:locklinebtn')" :title="$ct('lockline')" @click="popupEvent(0)"
         class="pony-iconv2 pony-genzong"></i>
      <i v-if="hasPermission('monitor:onlineVideo') && vehicleInfo.video_code" :title="$ct('onlineVideo')"
         @click="popupEvent(1)" class="pony-iconv2 pony-bofangquan"></i>
      <i v-if="hasPermission('monitor:hlsVideo') && vehicleInfo.video_code" title="播放器2" @click="popupEvent(10)"
         class="pony-iconv2 pony-bofangquan"></i>
      <i v-if="hasPermission('monitor:jessibucaVideo') && vehicleInfo.video_code" title="播放器3" @click="popupEvent(13)"
         class="pony-iconv2 pony-bofangquan"></i>
      <i v-if="hasPermission('monitor:playbackbtn')" :title="$ct('playback')" @click="popupEvent(2)"
         class="pony-iconv2 pony-guijihuifang"></i>
      <i v-if="hasPermission('monitor:commandbtn')" :title="$ct('command')" @click="popupEvent(3)"
         class="pony-iconv2 pony-zhilingxiafa"></i>
      <i v-if="hasPermission('monitor:morebtn')" :title="$ct('more')" @click="popupEvent(5)"
         class="pony-iconv2 pony-gengduo"></i>
      <i v-if="hasPermission('monitor:extanddetail') && !hasPermission('monitor:other')" @click="popupEvent(7)"
         class="pony-iconv2 pony-xiangqing" :title="$ct('extanddetail')"></i>
      <i v-if="hasPermission('monitor:morebtn')" :title="$ct('lock')" @click="popupEvent(4)"
         :class="['pony-iconv2', locking ? 'pony-suoding' : 'pony-jiesuo']"></i>
      <i v-if="hasPermission('monitor:collectbtn')" :title="$ct('collect')" @click="popupEvent(6)"
         :class="['pony-iconv2', vehicleInfo.collected ? 'pony-yishoucang' : 'pony-shoucang']"></i>
      <i v-if="hasPermission('monitor:futiancold') && !hasPermission('monitor:other')" title="冷链" @click="popupEvent(8)"
         class="pony-iconv2 pony-xuehua"></i>
      <i title="TBOX" @click="popupEvent(9)" v-if="hasPermission('monitor:TBOX') && vehicleInfo.energyType == 0"
         class="pony-iconv2 pony-shujuxiang"></i>
      <i title="电车" @click="popupEvent(11)" v-if="!hasPermission('monitor:other') && vehicleInfo.energyType == 1"
         class="pony-iconv2 pony-dian"></i>
      <i title="运单详情" @click="popupEvent(12)"
         v-if="!hasPermission('monitor:other') && hasPermission('monitor:wayBillDetail')"
         class="pony-iconv2 pony-yundanbianhao"></i>
      <i title="围栏" @click="draw" v-if="hasPermission('monitor:project')" class="pony-iconv2 pony-gongcheng"></i>
    </div>
  </div>
</template>

<script>
import DataRow from '../../../components/DataRow'
import singleState from '../../../components/SingleState'
import {operateERPProject} from "../../../../../axios/api";

export default {
  name: 'monitorPopup',
  components: {singleState, DataRow},
  data() {
    return {
      map: null,
      mapLayerGroup: null,
      vehicle: '',
      latlng: ''
    };
  },

  props: {
    vehicleInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
    setting: {
      type: Array,
      default: function () {
        return []
      }
    },
    locking: {
      type: Boolean,
      default: false
    },
    button: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    popupEvent(type) {
      this.$emit('popupEvent', type, this.vehicleInfo.id)
    },
    init(val, latlng, vehicleId) {
      this.map = val
      this.latlng = latlng
      this.vehicleId = vehicleId
    },
    draw() {
      this.mapLayerGroup = new L.FeatureGroup().addTo(this.map)
      L.circle([this.latlng.lat, this.latlng.lng], {radius: 200}).addTo(this.mapLayerGroup);
      this.add()
    },
    add() {
      this.$confirm('是否手动调整围栏？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$api.operateERPProject({
          operateType: 11,
          vehicleId: this.vehicleId,
          range: 200,
          pointList: [this.latlng.lng + ',' + this.latlng.lat]
        })
        if (res.status === 200) {
          this.$success('调整成功！')
        } else {
          this.$error(res.message || '调整失败！')
        }
        this.map.removeLayer(this.mapLayerGroup)
      }).catch(() => {
        this.map.removeLayer(this.mapLayerGroup)
      });
    }
  }
}

</script>

<style lang='scss' scoped>
.monitorpupop {
  &__header {
    height: 45px;

    .fon--m {
      cursor: pointer;
    }
  }

  &__content {
    padding: 10px 0;
  }

  &__fotter {
    height: 40px;

    i {
      font-size: 20px;
      cursor: pointer;
      margin-right: 10px;
    }
  }
}
</style>
