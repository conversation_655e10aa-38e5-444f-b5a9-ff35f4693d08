<template>
    <PonyDialog v-model="show" :title="$ct('setting.title')" class="warp" :width="640" content-style="height:461px; overflow-y:auto">

        <el-tabs type="border-card" v-model="activeTab" style="height: 100%">
            <el-tab-pane :label="$ct(`setting.tabList[0]`)" name="table" style="padding-top: 10px">
                <TransferPlusDraggle v-model="defaultSettingList" :titles="$ct('setting.formTitle')" :list="allSettingList" :filterableSearch=true  ref="tableConfig">
                </TransferPlusDraggle>
            </el-tab-pane>
            <el-tab-pane :label="$ct('setting.tabList[1]')" name="popup" style="padding: 10px 0 0 10px">
                <el-checkbox-group v-model="lockedPopup">
                    <el-checkbox :class="popup.size" v-for="popup in getPopupSetting['checkedDisabled']" :label="popup.key" :key="popup.key" :disabled="true">
                        {{ $ct(`table.${popup.key}`) }}
                        <!-- {{popup.name}} -->
                    </el-checkbox>
                </el-checkbox-group>
                <div v-for="item in isIndeterminateArr" :key="item">
                    <el-checkbox :indeterminate="isIndeterminateObj[item]" v-model="checkAllObj[item]" @change="handleCheckAllChange($event,item)">{{ $ct(`table.${item}`) }}
                    </el-checkbox>
                    <el-checkbox-group v-model="checkedPopupObj[item]" @change="handleCheckedChange($event,item)">
                        <el-checkbox :class="popup.size" v-for="popup in getPopupSetting[item]" :label="popup.key" :key="popup.key">
                            {{ $ct(`table.${popup.key}`) }}
                            <!-- {{popup.name}} -->
                        </el-checkbox>
                    </el-checkbox-group>

                </div>
            </el-tab-pane>
            <el-tab-pane :label="$ct('setting.tabList[2]')" name="other" style="padding: 10px 10px 0 10px;">
                <div class="setting-item">
                    <div class="item-title">{{ $ct('setting.cluster.message1') }}</div>
                    <div class="item-content">
                        <span>{{ $ct('setting.cluster.message2') }}</span>
                        <el-slider :max="80" :step="20" v-model="otherSide.value" show-stops></el-slider>
                        <span>{{ $ct('setting.cluster.message3') }}</span>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane :label="$ct('setting.tabList[3]')" name="name" style="padding: 10px 10px 0 10px;" v-if="hasPermission('monitor:STCarStatus')">
                <TransferDraggle ref="carStatusConfig" v-model="carStatusList" :filterableSearch=true :list="defaulList" :defaultSetting="defaulList" dataType="list"></TransferDraggle>
            </el-tab-pane>
        </el-tabs>

        <template slot="footer">
            <el-button size="mini" type="primary" v-show="activeTab != 'other'" @click="clearDeFault">
                {{ $ct('setting.returnDefault') }}
            </el-button>
            <el-button size="mini" type="primary" v-show="activeTab != 'other'" @click="show = false">
                {{ $ct('setting.close') }}
            </el-button>
            <el-button size="mini" type="primary" @click="changeTableSetting">{{ $ct('setting.commit') }}</el-button>
        </template>

    </PonyDialog>
</template>

<script>
import { getTableSetting, getPopupSetting } from '../util/monitor'
import TransferPlus from '@/components/common/TransferPlus';
import TransferPlusDraggle from '@/components/common/TransferPlusDraggle'

import TransferDraggle from "../../../../../components/common/TransferDraggle";



export default {
    name: 'monitorTable',
    components: { TransferPlus, TransferDraggle, TransferPlusDraggle },
    data() {
        const defaultCheckedArr = {
            basicInfo: [],
            gpsInfo:[],
            statisticsInfo:[],
            vehicleStaus: [],
            lockStatus: []
        }
        return {
            show: false,

            defaultSettingList: [],

            activeTab: 'table',
            configListKey:[],
            allSettingList: [
                    {
                        id: 'vehicle_info',
                        name: '车辆信息',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '企业车队', key: 'format_dept', size: '280', align: "left", type: 1, icon: 0, children: [], parent_id: 'vehicle_info', id: 'format_dept' },
                            { name: '车牌号', key: 'plate', size: '100', align: "center", type: 1, icon: 0, children: [], parent_id: 'vehicle_info', id: 'plate' },
                            { name: '状态', key: 'format_state', size: '80', align: "center", type: 1, icon: 1,typeKey: "state", children: [], parent_id: 'vehicle_info',id: 'format_state' },
                            { name: '车型', key: 'vehicleType', size: '90', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'vehicleType' },
                            { name: '行业', key: 'transType', size: '110', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'transType' },
                            { name: '车牌颜色', key: 'color', size: '100', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'color' },
                            { name: '视频设备', key: 'video_name', size: '120', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'video_name' },
                            { name: 'VIN码', key: 'vin', size: '120', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'vin' },
                            { name: 'SIM卡号', key: 'simCode', size: '160', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'simCode' },
                            { name: '终端号', key: 'terminalNo', size: '120', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'terminalNo' },
                            { name: '运输台数', key: 'unloadVSload', size: '100', align: "center", type: 1, icon: 0, children: [],parent_id: 'vehicle_info',id: 'unloadVSload' },
                        ]
                    },
                    {
                        id: 'driver_info',
                        name: '驾驶员信息',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '司机', key: 'driver_name', size: '80', align: "center",  type: 1, icon: 0, children: [],parent_id: 'driver_info',id: 'driver_name' },
                            { name: '司机电话', key: 'driver_phone', size: '110', align: "center",type: 1, icon: 0, children: [],parent_id: 'driver_info',id: 'driver_phone' },
                            { name: '司机身份证号', key: 'driver_idcardno', size: '150', align: "center", type: 1, icon: 0, children: [],parent_id: 'driver_info',id: 'driver_idcardno' },
                        ]
                    },
                    {
                        id: 'gps_info',
                        name: '定位信息',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '定位时间', key: 'format_gpsTime', size: '140', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'format_gpsTime' },
                            { name: 'GPS速度(Km/h)', key: 'gpsSpeed', size: '120', align: "center", type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'gpsSpeed'  },
                            { name: '方向', key: 'format_dire', size: '100', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'format_dire' },
                            { name: 'ACC', key: 'format_acc', size: '100', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'format_acc'},
                            { name: 'VSS速度', key: 'pulseSpeed', size: '100', align: "center", type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'pulseSpeed' },
                            { name: '经纬度', key: 'latlng', size: '300', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'latlng' },
                            { name: '地址', key: 'location', size: '350', align: "left",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'location'},
                            { name: '里程', key: 'mile', size: '90', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'mile'},
                            { name: '海拔', key: 'height', size: '80', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'height' },
                            { name: '定位信号', key: 'gpsSingleDes', size: '80', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'gpsSingleDes' },
                            { name: '通讯信号', key: 'gsmSingleDes', size: '80', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'gsmSingleDes' },
                            { name: '定位模块', key: 'gps_module', size: '80', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'gps_module' },
                            { name: '曲率半径', key: 'curvature', size: '90', align: "center",  type: 1, icon: 0, children: [],parent_id: 'gps_info',id: 'curvature' },

                        ]
                    },
                    {
                        id: 'driving_info',
                        name: '运行统计',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '作业时长', key: 'working_time', size: '115', align: "center",  type: 1, icon: 0, children: [],parent_id: 'driving_info',id: 'working_time' },
                            { name: '离线时长', key: 'offline_time', size: '130', align: "center", type: 1, icon: 0, children: [],parent_id: 'driving_info',id: 'offline_time'  },
                            { name: '日里程', key: 'dayMile', size: '120', align: "center",  type: 1, icon: 0, children: [],parent_id: 'driving_info',id: 'dayMile'  },
                            { name: '运单', key: 'jianhua56_isbill', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'driving_info', id: "jianhua56_isbill" },
                        ]
                    },
                    {
                        id: 'alarm_info',
                        name: '报警状态',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '视频信号丢失报警', key: 'media_261', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_261" },
                            { name: '视频信号遮挡报警', key: 'media_262', size: '160', align: "center",type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_262" },
                            { name: '主存储单元故障报警', key: 'media_main_263', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_main_263" },
                            { name: '灾备存储单元故障报警', key: 'media_aux_263', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_aux_263" },
                            { name: '其他视频设备故障报警', key: 'media_264', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_264" },
                            { name: '客车超员报警', key: 'media_265', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_265" },
                            { name: '异常驾驶行为报警', key: 'media_266', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_266" },
                            { name: '特殊报警录像达到存储阈值报警', key: 'media_267', size: '180', align: "center",type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "media_267" },
                            { name: '紧急报警', key: 'gpsAlarm_201', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_201" },
                            { name: '超速报警', key: 'gpsAlarm_202', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_202" },
                            { name: '疲劳驾驶报警', key: 'gpsAlarm_203', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_203" },
                            { name: '危险预警', key: 'gpsAlarm_204', size: '120', align: "center",type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_204" },
                            { name: 'GNSS 模块发生故障', key: 'gpsAlarm_205', size: '120', align: "center",type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_205" },
                            { name: 'GNSS 天线未接或被剪断', key: 'gpsAlarm_206', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_206" },
                            { name: 'GNSS 天线短路', key: 'gpsAlarm_207', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_207" },
                            { name: '终端主电源欠压', key: 'gpsAlarm_208', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_208" },
                            { name: '终端主电源掉电', key: 'gpsAlarm_209', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_209" },
                            { name: '终端 LCD 或显示器故障', key: 'gpsAlarm_210', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_210" },
                            { name: 'TTS 模块故障', key: 'gpsAlarm_211', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_211" },
                            { name: '摄像头故障', key: 'gpsAlarm_212', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_212" },
                            { name: '道路运输证 IC 卡模块故障', key: 'gpsAlarm_213', size: '160', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_213" },
                            { name: '超速预警', key: 'gpsAlarm_214', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_214" },
                            { name: '疲劳驾驶预警', key: 'gpsAlarm_215', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_215" },
                            { name: '违规行驶预警', key: 'gpsAlarm_216', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_216" },
                            { name: '胎压预警', key: 'gpsAlarm_217', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_217" },
                            { name: '右转盲区异常报警', key: 'gpsAlarm_218', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_218" },
                            { name: '当天累计驾驶超时', key: 'gpsAlarm_219', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_219" },
                            { name: '超时停车', key: 'gpsAlarm_220', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_220" },
                            { name: '进出区域', key: 'gpsAlarm_221', size: '120', align: "center",type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_221" },
                            { name: '进出路线', key: 'gpsAlarm_222', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_222" },
                            { name: '路段行驶时间不足/过长', key: 'gpsAlarm_223', size: '180', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_223" },
                            { name: '路线偏离报警', key: 'gpsAlarm_224', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_224" },
                            { name: '车辆 VSS 故障', key: 'gpsAlarm_225', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_225" },
                            { name: '车辆油量异常', key: 'gpsAlarm_226', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_226" },
                            { name: '车辆被盗', key: 'gpsAlarm_227', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_227" },
                            { name: '车辆非法点火', key: 'gpsAlarm_228', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_228" },
                            { name: '车辆非法位移', key: 'gpsAlarm_229', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_229" },
                            { name: '碰撞预警', key: 'gpsAlarm_230', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_230" },
                            { name: '侧翻预警', key: 'gpsAlarm_231', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_231" },
                            { name: '车辆非法开门', key: 'gpsAlarm_232', size: '120', align: "center", type: 1, icon: 2, children: [], parent_id: 'alarm_info', id: "gpsAlarm_232" },
                        ]
                    },
                    {
                        id: 'sensor_info',
                        name: '传感器',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '载重(渣土)', key: 'muck_nuclear_carry', size: '100', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "muck_nuclear_carry" },
                            { name: '载重(物流)', key: 'jianhua56_isload', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "jianhua56_isload" },
                            // { name: '载重(福田)', key: 'ftt_zaiz', size: '120', align: "center", type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "ftt_zaiz" },
                            { name: '门磁', key: 'ftt_doorstate', size: '80', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "ftt_doorstate" },
                            { name: '冷机', key: 'ftt_ysj', size: '80', align: "center", type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "ftt_ysj"},
                            { name: '温度', key: 'ftt_tempdesc', size: '280', align: "center", type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "ftt_tempdesc" },
                            { name: '水位百分比', key: 'wash_water_level', size: '100', align: "center", type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "wash_water_level" },
                            { name: '转速', key: 'coldchain_rota_speed', size: '80', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "coldchain_rota_speed" },
							              { name: 'CAN最新时间', key: 'canUploadTime', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "canUploadTime" },
                            { name: '发动机总里程', key: 'engine_mile', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sensor_info', id: "engine_mile" },
                        ]
                    },
					          {
                        id: 'signal_info',
                        name: '信号线状态',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '右转向灯信号', key: 'light_signal_0', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "light_signal_0" },
                            { name: '左转向灯信号', key: 'light_signal_1', size: '120', align: "center", type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "light_signal_1" },
                            { name: '制动信号', key: 'light_signal_2', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "light_signal_2" },
                            { name: '近光灯信号', key: 'light_signal_7', size: '120', align: "center", type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "light_signal_7"},
                            { name: '远光灯信号', key: 'light_signal_8', size: '120', align: "center", type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "light_signal_8" },
                            { name: 'IO状态', key: 'mini_io_status', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'sign_info', id: "mini_io_status" },
                        ]
                    },
                    {
                        id: 'engine_info',
                        name: '发动机数据',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '发动机车速', key: 'carSpeed', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "carSpeed" },
                            // { name: '发动机转速', key: 'coldchain_rota_speed', size: '80', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "coldchain_rota_speed" },
                            { name: '发动机累积怠速时间', key: 'idleDuration', size: '155', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "idleDuration" },
                            { name: '发动机累计运行时间', key: 'runningDuration', size: '155', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "runningDuration" },
                            { name: '发动机冷却水温度', key: 'coolantTemp', size: '145', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "coolantTemp" },
                            { name: '冷却剂液位', key: 'coolantPosition', size: '130', align: "center",  type: 1, icon: 0, children: [], parent_id: 'engine_info', id: "coolantPosition" },
                        ]
                    },
                    {
                        id: 'energy_info',
                        name: '能量消耗',
                        type: 0,
                        disabled: false,
                        children: [
                            // { name: '发动机总油耗', key: 'coldchain_oil_consum', size: '80', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "coldchain_oil_consum" },
                            // { name: '发动机平均燃油经济性', key: 'fuelEconomy', size: '130', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "fuelEconomy" },
                            { name: '油耗', key: 'coldchain_oil_consum', size: '80', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "coldchain_oil_consum" },
                            { name: '平均燃油经济性', key: 'fuelEconomy', size: '130', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "fuelEconomy" },
                            { name: '发动机瞬时燃油经济性', key: 'instantFuelEconomy', size: '150', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "instantFuelEconomy" },
                            { name: '发动机累积怠速燃油消耗量', key: 'idleoilConsumption', size: '190', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "idleoilConsumption" },
                            { name: '剩余油量百分比', key: 'oilPosition', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "oilPosition" },
                            { name: '后处理尿素消耗量', key: 'adblueConsumption', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "adblueConsumption" },
                            { name: '尿素液位', key: 'adbluePosition', size: '100', align: "center",  type: 1, icon: 0, children: [], parent_id: 'energy_info', id: "adbluePosition" },
                        ]
                    },
                    {
                        id: 'drivering_info',
                        name: '行驶状态',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: 'ABS激活', key: 'abs', size: '100', align: "center",  type: 1, icon: 0, children: [], parent_id: 'drivering_info', id: "abs" },
                            { name: '油门开度', key: 'throttlePosition', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'drivering_info', id: "throttlePosition" },
                            { name: '离合器踏板开关信号', key: 'clutchPedalSiqnal', size: '170', align: "center",  type: 1, icon: 0, children: [], parent_id: 'drivering_info', id: "clutchPedalSiqnal" },
                            { name: '刹车踏板开关信号', key: 'brakePedalSignal', size: '150', align: "center",  type: 1, icon: 0, children: [], parent_id: 'drivering_info', id: "brakePedalSignal" },
                        ]
                    },
                    {
                        id: 'vehicleBody_info',
                        name: '车身状态',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: '蓄电池电压', key: 'batteryVoltage', size: '120', align: "center",  type: 1, icon: 0, children: [], parent_id: 'vehicleBody_info', id: "batteryVoltage" },
                        ]
                    },
                    {
                        id: 'AEB_info',
                        name: '设备状态信息',
                        type: 0,
                        disabled: false,
                        children: [
                            { name: 'CANBOX通讯状态', key: 'canbox_message_status', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'AEB_info', id: "canbox_message_status" },
                            { name: '发动机ECU通讯状态', key: 'engine_ecu_status', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'AEB_info', id: "engine_ecu_status" },
                            { name: '发动机工作状态', key: 'engine_work_status', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'AEB_info', id: "engine_work_status" },
                            { name: 'AEB主机状态', key: 'aeb_host_status', size: '140', align: "center",  type: 1, icon: 0, children: [], parent_id: 'AEB_info', id: "aeb_host_status" },
                        
                        ]
                    },
                    

            ],
            isIndeterminateObj: {
                basicInfo: false,
                gpsInfo:false,
                statisticsInfo:false,
                vehicleStaus: false,
                lockStatus: false
            },
            checkAllObj: {
                basicInfo: false,
                gpsInfo:false,
                statisticsInfo:false,
                vehicleStaus: false,
                lockStatus: false
            },
            checkedPopup: [],
            defaultCheckedArr,
            checkedPopupObj: JSON.parse(JSON.stringify(defaultCheckedArr)),

            lockedPopup: ['format_dept', 'location'],

            getPopupSettingNew: [
                { name: '部门', key: 'format_dept', size: 'all' },
                { name: '司机', key: 'driver_name', size: 'half' },
                { name: '电话', key: 'driver_phone', size: 'half' },
                { name: '速度', key: 'gpsSpeed', size: 'half', tip: 'km/h' },
                { name: 'ACC', key: 'format_acc', size: 'half' },
                { name: '里程', key: 'mile', size: 'half' },
                { name: '设备', key: 'terminalNo', size: 'half' },
                { name: '载重', key: 'muck_nuclear_carry', size: 'half' },
                { name: '篷布', key: 'muck_cloth_cover', size: 'half' },
                { name: '举升', key: 'muck_hopper_lift', size: 'half' },
                // { name: '罐体', key: 'muck_status_rotation', size: 'half'},
                { name: '方向', key: 'format_dire', size: 'half' },
                { name: '车型', key: 'vehicleType', size: 'half' },
                { name: '行业', key: 'transType', size: 'half' },
                { name: '海拔', key: 'height', size: 'half' },
                { name: 'SIM卡', key: 'simCode', size: 'half' },
                { name: '终端类型', key: 'terminalTypeName', size: 'half' },
                { name: '定位', key: 'positioned', size: 'half' },
                { name: '工作时长', key: 'working_time', size: 'half' },
                { name: '位置', key: 'location', size: 'all' },
                { name: '门磁', key: 'moutai_door_magnetism', size: 'rooth' },
                { name: '施解封状态', key: 'moutai_lock_lockStatus', size: 'rooth' },
                { name: '电压', key: 'moutai_lock_power', size: 'rooth', tip: '毫伏' },
                { name: '电子锁密码', key: 'moutai_lock_password', size: 'rooth' },
                { name: '锁开关状态', key: 'moutai_lock_onStatus', size: 'rooth' },
                { name: '防拆开关状态', key: 'moutai_lock_brokenStatus', size: 'rooth' },
                { name: '电量', key: 'moutai_lock_batteryPercent', size: 'rooth' },
                { name: '充电状态', key: 'moutai_lock_chargeState', size: 'rooth' },
                { name: '运动传感器状态', key: 'moutai_lock_motionState', size: 'rooth' },
                { name: '滚筒状态', key: 'rollerState', size: 'rooth' },
                { name: '日里程', key: 'dayMile', size: 'rooth' },
                { name: '报警', key: 'popupAlarm', size: 'all' },
				        {name: '作业状态', key: 'coldchain_job_status', size: 'rooth'},
				        {name: '运输台数', key: 'unloadVSload', size: 'rooth',},
                {name: '运单', key: 'jianhua56_isbill', size: 'rooth',},
                { name: '载重(物流)', key: 'jianhua56_isload', size: 'rooth' },
						],
            getPopupSettingAll: {
                'checkedDisabled': [
                    { name: '企业车队', key: 'format_dept', size: 'rooth' },
                    { name: '位置', key: 'location', size: 'rooth' },
                ],
                'basicInfo': [
                    { name: '司机', key: 'driver_name', size: 'rooth' },
                    { name: '司机电话', key: 'driver_phone', size: 'rooth' },
                    { name: '设备', key: 'terminalNo', size: 'rooth' },
                    { name: '车型', key: 'vehicleType', size: 'rooth' },
                    { name: '行业', key: 'transType', size: 'rooth' },
                    { name: 'SIM卡', key: 'simCode', size: 'rooth' },
                    { name: '终端类型', key: 'terminalTypeName', size: 'rooth' },
				            {name: '作业状态', key: 'coldchain_job_status', size: 'rooth'},
				            {name: '运输台数', key: 'unloadVSload', size: 'rooth',},
                    {name: '运单', key: 'jianhua56_isbill', size: 'rooth',},
                ],
                'gpsInfo':[
                    { name: 'GPS速度', key: 'gpsSpeed', size: 'rooth', tip: 'km/h' },
                    { name: 'ACC', key: 'format_acc', size: 'rooth' },
                    { name: '里程', key: 'mile', size: 'rooth' },
                    { name: '方向', key: 'format_dire', size: 'rooth' },
                    { name: '海拔', key: 'height', size: 'rooth' },
                    { name: '定位', key: 'positioned', size: 'rooth' },
                    { name:'报警',key: 'popupAlarm',size:'rooth' },
                ],
                'statisticsInfo':[
                    { name: '工作时长', key: 'working_time', size: 'rooth' },
                    { name: '日里程', key: 'dayMile', size: 'rooth' },
                ],
                'vehicleStaus': [
                    { name: '载重', key: 'muck_nuclear_carry', size: 'rooth' },
                    { name: '载重(物流)', key: 'jianhua56_isload', size: 'rooth' },
                    { name: '篷布', key: 'muck_cloth_cover', size: 'rooth' },
                    { name: '举升', key: 'muck_hopper_lift', size: 'rooth' },
                    // { name: '罐体', key: 'muck_status_rotation', size: 'rooth'},
                    { name: '门磁', key: 'moutai_door_magnetism', size: 'rooth' },
                    { name: '滚筒状态', key: 'rollerState', size: 'rooth' },
                ],
                'lockStatus': [
                    { name: '施解封状态', key: 'moutai_lock_lockStatus', size: 'rooth' },
                    { name: '电压', key: 'moutai_lock_power', size: 'rooth', tip: '毫伏' },
                    { name: '电子锁密码', key: 'moutai_lock_password', size: 'rooth' },
                    { name: '锁开关状态', key: 'moutai_lock_onStatus', size: 'rooth' },
                    { name: '防拆开关状态', key: 'moutai_lock_brokenStatus', size: 'rooth' },
                    { name: '电量', key: 'moutai_lock_batteryPercent', size: 'rooth' },
                    { name: '充电状态', key: 'moutai_lock_chargeState', size: 'rooth' },
                    { name: '运动传感器状态', key: 'moutai_lock_motionState', size: 'rooth' },
                ]
            },

            otherSide: {
                value: 40,
            },
            carStatusList: [],
            defaulList: [
                { name: '别名', key: 'alias' },
                { name: '车牌号', key: 'plate' },
                { name: '车辆类型', key: 'concrete_vehicle_type' },
                { name: '滚筒状态', key: 'rollerState' },
                { name: '运货单状态', key: 'concrete_state' },
                { name: '速度', key: 'gpsSpeed' },
                { name: '车辆状态', key: 'carStatus' },
            ]
        };
    },

    beforeCreate() {
        this.$options._i18Name = 'monitor'
    },

    computed: {
        isIndeterminateArr() {
            return Object.keys(this.isIndeterminateObj)
        },
        getPopupSetting(){
            let obj = {}
            for(let key in this.getPopupSettingAll){
                let configValue = this.getPopupSettingAll[key].filter(item=>this.configListKey.includes(item.key))
                if(configValue.length){
                    obj[key] = configValue
                }
            }
            return obj
        }
    },
    async mounted() {
        let res = await this.$api.getUserIndustryBind({ user_id: '-1' })
        let interFaceConfig = res.data.map(item=>{
            return this.$api.operateUserTableConfig({operate_type: 2, user_id: `industry_${item.value}`, code: 'monitor_popup_industry'})
        })
        let resultConfig = await Promise.all(interFaceConfig)
        this.configListKey = [...new Set(resultConfig.map(item=>{
            return (item && item.data.config_value) ? JSON.parse(item.data.config_value).map(it=>it.key):[]
        }).flat())]
        // this.defaultSettingList.forEach(item => {
        // 		item.name = this.$ct(`table.${item.key}`)
        // })
        this.carStatusList = JSON.parse(JSON.stringify(this.$store.state.vehicle.stCarStatus))
    },
    methods: {
        openModal(obj) {
            this.show = true
            this.defaultSettingList = obj.table.map(item => item.key)
            let checkedKey = obj.popup.map(item => item.key)
            this.checkedPopupObj = JSON.parse(JSON.stringify(this.defaultCheckedArr))
            checkedKey.forEach(item => {
                this.isIndeterminateArr.forEach(val => {
                    if (this.getPopupSetting[val].find(it => it.key == item)) {
                        this.checkedPopupObj[val].push(item)
                        this.handleCheckedChange(this.checkedPopupObj[val], val)
                    }
                })
            })

            // this.checkedPopup = obj.popup.map(item => item.key)
            // this.checkedPopupObj.basicInfo = obj.popup.map(item => item.key)

        },

        async changeTableSetting() {
            let newSetting
						let allSettings  = []
						this.allSettingList.map((item) => {
                item.children.map(list => allSettings.push(list))
            })
            switch (this.activeTab) {
                case 'table':
                    newSetting = this.defaultSettingList.map(item => allSettings.find(cur => cur.key == item.key))
                    this.commitTableSetting('monitor_bottom_table', newSetting)
                    break;
              case 'popup':
                    let checkedArr = Object.values(this.checkedPopupObj).flat().concat(this.lockedPopup)
                    checkedArr = Array.from(new Set(checkedArr));
                    // let index = checkedArr.indexOf('location')
                    // let location = checkedArr.splice(index, 1)
                    // checkedArr.push(location)
                    newSetting = checkedArr.map(item => this.getPopupSettingNew.find(cur => cur.key == item))
                    if (newSetting.length > 20) {
                        this.$warning('最多选择20个！')
                        return
                    }
                    this.commitTableSetting('monitor_popup', newSetting)
                    break;
                case 'other':
                    newSetting = this.otherSide.value
                    this.commitTableSetting('monitor_other', newSetting)
                    break;
                case 'name':
                    newSetting = []
                    this.$store.commit('vehicle/setShCarStatus', this.carStatusList)
                default:
                    break;
            }
            this.$emit('change', this.activeTab, JSON.parse(JSON.stringify(newSetting)))
            this.show = false
        },

        clearDeFault() {
            if (this.activeTab == 'table') {
                this.defaultSettingList = getTableSetting
								this.$refs.tableConfig.clearDeFault()
            } else if (this.activeTab === 'name') {
                this.$refs.carStatusConfig.clearDeFault()
                this.$store.commit('vehicle/setShCarStatus', this.carStatusList)
            } else {
                let checkList = ['basicInfo','gpsInfo']
                checkList.forEach(item=>{
                    this.checkedPopupObj[item] = getPopupSetting.map(item => item.key)
                    this.isIndeterminateObj[item] = true
                    this.checkAllObj[item] = false
                })
                this.checkedPopupObj.basicInfo = getPopupSetting.map(item => item.key)
                this.checkedPopupObj.gpsInfo = getPopupSetting.map(item => item.key)
                let clearList = ['statisticsInfo','vehicleStaus','lockStatus']
                clearList.forEach(item=>{
                     this.checkedPopupObj[item] = []
                    this.isIndeterminateObj[item] = false
                    this.checkAllObj[item] = false
                })

            }
        },
        handleCheckedChange(value, type) {
            let checkedCount = value.length;
            this.checkAllObj[type] = checkedCount === this.getPopupSetting[type].map(item => item.key).length
            this.isIndeterminateObj[type] = checkedCount > 0 && checkedCount < this.getPopupSetting[type].map(item => item.key).length;
        },
        handleCheckAllChange(data, type) {
            if (data) {
                this.checkedPopupObj[type] = this.getPopupSetting[type].map(item => item.key)
            } else {
                this.checkedPopupObj[type] = []
            }
            this.isIndeterminateObj[type] = false

        },
        async commitTableSetting(type, list) {
            let result = await this.$api.operateUserTableConfig({
                operate_type: 1,
                user_id: '-1',
                code: type,
                config_value: JSON.stringify(list)
            })
            if (result && result.status == 200) {
                this.$success(result.message || this.$ct('setting.message.operateOk'))
            }
        }
    }
}

</script>

<style lang='scss' scoped>
.warp {
    .el-checkbox {
        line-height: 22px;

        &.half {
            width: 50%;
            margin: 0;
        }

        &.all {
            width: 100%;
        }

        &.rooth {
            width: 20.6%;
        }
    }

    /deep/ .el-tabs__content {
        overflow: auto;
    }
}

.el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0 10px;

    .el-checkbox {
        width: 100%;
        line-height: 30px;
        margin-right: 0;
        &.half {
            width: 50%;
            margin: 0;
        }

        &.all {
            width: 100%;
        }

        &.rooth {
            width: 19.7%;
        }
    }
}

.setting-item {
    width: 100%;

    .el-slider {
        width: calc(100% - 150px);
    }

    .item-title {
        font-size: 14px;
        line-height: 25px;
        margin: 10px 0;
        border-bottom: solid 1px var(--border-color);
    }

    .item-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}
</style>
