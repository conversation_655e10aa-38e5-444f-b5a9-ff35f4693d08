<template>
  <Layout :content-loading="loading" :has-color="true">
    <!-- <template slot="aside">
            <el-tabs stretch style="height: calc(100% - 10px);">
                <el-tab-pane label="车辆选择">
                    <ElementTree type="vehicle" ref="monitorTree" @check="selectNodes" :checkMode="true" ></ElementTree>
                </el-tab-pane>
                <el-tab-pane label="事件选择">
                    <div style="padding: 10px;">
                        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
                                     @change="handleCheckAllChange">事件类型
                        </el-checkbox>
                        <el-checkbox-group v-model="alarmType" @change="handleCheckedCitiesChange"
                                           style="padding-left: 10px">
                            <div class="type-item" v-for="(item, i) in typeList" :key="i">
                                <el-checkbox :label="item.value">{{item.label}}
                                    <span class="text text--danger">{{alarmEventCount[item.value] ? `(${alarmEventCount[item.value]})`: ''}}</span>
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </template> -->
    <template slot="query">
      <div class="project">
        <div class="top">
          <ProjectStatus :projectFilterList="projectFilterList" @riskDetail="riskDetail"></ProjectStatus>
        </div>
      </div>
      <div class="query-item">
        <span style="margin-right: 10px">车辆风险等级</span>
        <div class="level-button">
          <span v-for="(item, index) in riskLevelList" :key="index"
            :class="[queryList.riskLevelList.includes(index) ? 'active' + index : '']"
            @click="levelChange(index)">{{ item }}</span>
        </div>
      </div>
      <div class="query-item">
        <span style="margin-right: 10px">处理状态</span>
        <el-select v-model="queryList.dealType">
          <el-option :value="null" label="全部"></el-option>
          <el-option v-for="(item, key) in dealTypeMap" :value="parseInt(key)" :label="item" :key="key"></el-option>
        </el-select>
      </div>
      <!--            <div class="query-item">-->
      <!--                <el-button type="primary" @click="getMonitorData" size="mini">查询</el-button>-->
      <!--            </div>-->
      <div class="query-item">
        <span style="margin-right: 10px">车牌号</span>
        <el-input clearable size="mini" v-model="queryListPlateno" placeholder="请输入车牌号"></el-input>
      </div>
      <!-- <div class="query-item" >
                    <el-button type="primary" @click="sendTTS" v-if="hasPermission('riskMonitor:TTSissued')">TTS自动下发设置</el-button>
            </div> -->

      <div class="query-item">
        <el-button type="primary" @click="jumpToAlarmMgt(0)">历史明细查询</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="jumpToAlarmMgt(1)">设置</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="getMonitorData()" :loading="loading">刷新</el-button>
      </div>
      <div class="query-item" v-if="hasPermission('riskMonitor:overTime')">
        <el-button type="primary" @click="handleExpiredEvent()" :loading="overflowLoading">处理过期事件</el-button>
      </div>
      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination background small layout="prev, pager, next, total" :pager-count="5" :current-page.sync="table.page"
          :page-size="table.size" :total="lockAfterList.length">
        </el-pagination>
      </div>
    </template>
    <template slot="content">
      <el-table class="el-table--radius " stripe border :empty-text="lockAfterList.length ? '暂无数据' : ''" highlight-current-row
        :data="formatList" height="100%" ref="table" @row-dblclick="showDetail" style="width: 100%">
        <el-table-column label="锁定" width="45">
          <template slot-scope="{row}">
            <i class="blue-line" v-if="(row.times > nowData - 20 * 60 * 1000)"></i>
            <el-button type="text" title="锁定" size="mini">
              <i class="pony-iconv2 pony-suoding1" @click="lockList(row, 1)"
                v-if="lockListData.find(item => item.eventId == row.eventId)" title="解锁"></i>
              <i class="pony-iconv2 pony-jiesuo1" @click="lockList(row, 0)" style="color:var(--color-text-primary)" v-else
                title="锁定"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" min-width="60" show-overflow-tooltip>
          <template slot-scope="{$index}">
            <span>{{ (table.page - 1) * table.size + 1 + $index }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="50">
          <template slot-scope="{row}">
            <el-button type="text" title="详情" size="mini" @click="showDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="风险等级" min-width="80" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span
              :class="[row.riskLevel == 2 ? 'risk-level high-level' : row.riskLevel == 1 ? 'risk-level middle-level' : 'risk-level low-level']">{{ row.riskValue }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安全团队干预记录" min-width="100" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ row.isInterfered ? row.dealBy ? row.dealBy == 'system' ? '平台自动干预' : row.dealBy + '干预' : '安全员已干预' : '未干预' }}</span>
            <span class="tag" v-if="row.isInterfered">
              {{row.dealTypeView}}
              <!-- {{ dealTypeMap[row.dealType] }} -->
            </span>
          </template>
        </el-table-column>
        <el-table-column label="处理备注" min-width="100" show-overflow-tooltip prop="dealDesc"></el-table-column>
        <el-table-column prop="warnCodeView" label="事件类型" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="!scope.row.level || scope.row.level == 1">{{ scope.row.warnCodeView }}</span>
            <el-tag v-else :type="scope.row.level == 3 ? 'danger' : scope.row.level == 2 ? 'warning' : 'default'">
              {{ scope.row.warnCodeView }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="plateNo" label="车牌号" min-width="110" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ row.plateNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="driver" label="司机" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="单位" prop="dept" min-width="150" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="dealTypeView" label="企业处理状态" min-width="120" show-overflow-tooltip></el-table-column> -->


        <el-table-column prop="startTime" label="开始时间" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="endTime" label="结束时间" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="100" show-overflow-tooltip sortable></el-table-column>

      </el-table>
    </template>
    <RiskAlarmDetail ref="alarmDetail" :showTextOptions="false" :showAddTemplate="true" :showCanRun="false"
      @changeRow="changeRow" @lockRow="lockList" @changeEventLevel="changeEventLevel" :isCanLock="true" @changeDeal="getMonitorData"
      @changeEventType="changeEventType"></RiskAlarmDetail>
    <!-- <PonyDialog
        :show="ttsModel.show"
        title="TTS自动下发设置"
        :width="400"
        contentStyle="height:160px;padding-bottom:0"
        >
            <el-form :model="ttsModel.data" :rules="ttsModel.rules" label-width="135px" ref="form">
                <el-form-item label="自动下发TTS语音" prop="level">
                    
                    <div class="level-button">
                        <span
                        v-for="(item,index) in riskLevelList"
                        :key="index"
                        :class="[ttsModel.levelList.includes(index) ? 'active':'']"
                        @click="ttslevelChange(index)"
                        >{{item+'风险'}}</span>
                    </div>
                </el-form-item>
                <el-form-item label="自动下发时间" prop="time">
                    <el-time-picker
                        style="width:210px"
                        :clearable="false"
                        is-range
                        value-format="HH:mm:ss"
                        v-model="ttsModel.data.time"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围">
                    </el-time-picker>
                    <i class="pony-iconv2 pony-xinzeng" style="color: var(--color-primary)" @click="addshowTime"></i>
                </el-form-item>
                <el-form-item label="" v-if="ttsModel.showTime2">
                    <el-time-picker
                        style="width:210px"
                        :clearable="false"
                        is-range
                        value-format="HH:mm:ss"
                        v-model="ttsModel.data.time2"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围">
                    </el-time-picker>
                    <i class="pony-iconv2 pony-shanchu" style="color: var(--color-primary)" @click="ttsModel.showTime2 = false"></i>
                </el-form-item>
            </el-form>
            <template slot="footer">
                <el-button type="primary" @click="clear" :disabled="!(userTTsconfig && userTTsconfig.id)">清除配置</el-button>
                <el-button type="primary" @click="commitTTS">确认</el-button>
                <el-button @click="ttsModel.show = false">取消</el-button>
            </template>
        </PonyDialog> -->
    <PonyDialog :show="riskDetailParams.show" :title="riskDetailParams.title" :has-footer="false" :width="750"
      contentStyle="height:450px" @close="riskDetailParams.show = false">
      <el-tabs v-model="activeDetailTab" v-if="!riskDetailParams.sus" type="border-card">
        <el-tab-pane label="今日" name="today">
          <el-table class="el-table--radius" stripe border :empty-text="riskDetailParams.list.length ? '暂无数据' : ''"
            :data="riskDetailParams.list" height="100%" ref="risktable" style="width: 100%">
            <el-table-column type="index" label="序号" min-width="50">
              <template slot-scope="{$index}">
                <span>{{ 1 + $index }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="dept" label="单位" show-overflow-tooltip min-width="150"></el-table-column>

            <el-table-column prop="plateNo" label="车牌号" show-overflow-tooltip min-width="80"></el-table-column>
            <el-table-column prop="score" label="当前分值" min-width="70"></el-table-column>
            <el-table-column label="违规标签" min-width="220" align="left">
              <template slot-scope="{row}">
                <span class="scope-label" v-for="(item, index) in row.labels" :key="item"
                  :style="{ 'color': colorList[(index + 1) % 4], 'background-color': 'rgba' + rgbList[(index + 1) % 4] }">{{ alarmEventName[item] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="事件明细" min-width="65">
              <template slot-scope="{row}">
                <el-button type="text" @click="toJump(row.vehicleId)">跳转</el-button>
                <!-- <span class="scope-label">跳转</span> -->
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="当前" name="current" lazy>
          <el-table class="el-table--radius" stripe border :empty-text="riskDetailParams.list.length ? '暂无数据' : ''"
            :data="riskDetailParams.list" height="100%" ref="risktable" style="width: 100%">
            <el-table-column type="index" label="序号" min-width="50">
              <template slot-scope="{$index}">
                <span>{{ 1 + $index }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="dept" label="单位" show-overflow-tooltip min-width="150"></el-table-column>

            <el-table-column prop="plateNo" label="车牌号" show-overflow-tooltip min-width="80"></el-table-column>
            <el-table-column prop="score" label="当前分值" min-width="70"></el-table-column>
            <el-table-column label="违规标签" min-width="220" align="left">
              <template slot-scope="{row}">
                <span class="scope-label" v-for="(item, index) in row.labels" :key="item"
                  :style="{ 'color': colorList[(index + 1) % 4], 'background-color': 'rgba' + rgbList[(index + 1) % 4] }">{{ alarmEventName[item] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="事件明细" min-width="65">
              <template slot-scope="{row}">
                <el-button type="text" @click="toJump(row.vehicleId)">跳转</el-button>
                <!-- <span class="scope-label">跳转</span> -->
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <el-table v-else class="el-table--radius" stripe border :empty-text="riskDetailParams.list.length ? '暂无数据' : ''"
        :data="riskDetailParams.list" height="100%" ref="risktable" style="width: 100%">
        <el-table-column type="index" label="序号" min-width="50">
          <template slot-scope="{$index}">
            <span>{{ 1 + $index }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plateNo" label="车牌号" show-overflow-tooltip min-width="75"></el-table-column>
        <el-table-column prop="dept" label="单位" show-overflow-tooltip min-width="175"></el-table-column>
        <el-table-column label="事件" min-width="120" prop="warnCodeView"></el-table-column>
        <el-table-column label="详情" min-width="60">
          <template slot-scope="{row}">
            <el-button type="text" @click="toJumpShowDetail(row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="60">
          <template slot-scope="{row}">
            <el-button type="text" @click="lockList(row, 1)">取消关注</el-button>
          </template>
        </el-table-column>
      </el-table>
    </PonyDialog>
  </Layout>
</template>

<script>
/**
 * @Author: xieyj
 * @Email: xieyujie121113.163.com
 * @Date: 2021/1/15 10:10
 * @LastEditors: xieyj
 * @LastEditTime:
 * @Description:
 *
 */
import ProjectStatus from "./components/ProjectStatus"
import RiskAlarmDetail from "./components/RiskAlarmDetail";
import { DangerEvent, DangerEventProcessed, DangerEventEnd } from '@/service/wsService'
import { mapState } from 'vuex';


export default {
  name: 'riskMonitor',
  components: { ProjectStatus, RiskAlarmDetail },
  data() {
    return {
      currentRowIndex: null,
      riskDetailParams: {
        show: false,
        list: [],
        high_list: [],
        high_list_today: [],
        mid_list_today: [],
        suspend_list: [],
        title: "",
        sus: false,
      },
      ttsModel: {
        show: false,
        loading: true,
        showTime2: false,
        data: {
          level: '',
          time: [
            '00:00:00',
            "23:59:59"
          ],
          time2: [
            '00:00:00',
            "23:59:59"
          ]
        },
        levelList: [],
        rules: {
          level: [
            { required: true, message: '请选择风险等级', trigger: 'change' },
          ],
          time: [
            { required: true, message: '请选择下发时间', trigger: 'change' },
          ],
        }
      },
      currentRow: null,
      loading: false,
      selectedVehicle: [],
      isIndeterminate: false, //全选按钮的半选状态
      checkAll: true,  //全选状态
      alarmType: [], //事件类型选择的值
      typeList: [],
      dealTypeMap: [],//处理类型
      lockListData: [],
      subscription: [],
      timer: null,
      timerData: null,
      nowData: moment().valueOf(),
      vehicleStore: null,
      colorList: ['#ff6666', '#ff8866', '#2b80e0', '#7f70d1',],
      rgbList: ['(255,102,102,.1)', '(255,136,102,.1)', '(43,128,224,.1)', '(127,112,209,.1)'],
      activeDetailTab: "current",
      currentStatus: 1,
      showedIdList: [],//已经下一条的id
      //各报警事件数
      alarmEventCount: {},
      vehicleLevelList: [],
      userTTsconfig: null,
      alarmEventName: {},
      projectFilterList: [
        {
          title: '车辆',
          num: 0,
          downNum: 0,
          status: 0,
          icon: 'pony-daizhipai',
          color: '#20A971'
        },
        {
          title: '高风险车辆',
          num: 0,
          downNum: 0,
          status: 1,
          icon: 'pony-zaitu',
          color: 'var(--color-danger)'
        },
        {
          title: '中风险车辆',
          num: 0,
          downNum: 0,
          status: 2,
          icon: 'pony-zaitu',
          color: 'var(--color-warning)'
        },
        {
          title: '已关注事件',
          num: 0,
          downNum: 0,
          status: 3,
          icon: 'pony-baojing',
          color: 'var(--color-primary)'
        },
        {
          title: '未干预事件',
          num: 0,
          downNum: 0,
          // status:4,
          icon: 'pony-baojing',
          color: 'var(--color-danger)'
        },
      ],
      msgTemp: {
        events: [],
        processed: {},
        unEnd: {},
      },
      overflowLoading:false,
      riskLevelList: ['低', '中', '高'],
      queryListDealType: null,  //记录一下查的是那个类型
      queryListLevel: [0, 1, 2],  //记录一下查的是那个等级

      queryListPlateno: '',
      queryList: {
        dealType: null,
        riskLevelList: [0, 1, 2],
        vehicleIdList: [],
        eventTypeList: []
      },
      table: {
        list: [],
        page: 1,
        size: 30
      },
      followEventObj: {}
    };
  },
  watch: {
    // queryList: {
    // deep: true,
    // handler(value) {
    // 		this.table.list = []
    // 		this.projectFilterList[0].num = 0
    // 		this.projectFilterList[0].downNum = 0
    // 		this.projectFilterList[1].num = 0
    // 		this.projectFilterList[1].downNum = 0
    // 		this.projectFilterList[2].num = 0
    // 		this.projectFilterList[2].downNum = 0
    //         this.projectFilterList[4].num = 0
    // 		this.projectFilterList[4].downNum = 0
    // 		this.projectFilterList[3].num = 0
    // 		// this.projectFilterList[3].downNum = 0
    // 		this.getMonitorData()
    // }
    // },
    lockListData: {
      deep: true,
      handler(value) {
        this.riskDetailParams.suspend_list = value
        this.projectFilterList[3].num = this.riskDetailParams.suspend_list.length
        if (this.currentStatus == 3) {
          this.riskDetailParams.list = this.riskDetailParams.suspend_list
        }
      }
    },
    activeDetailTab(val) {
      if (val == 'today') {
        if (this.currentStatus == 1) {
          this.riskDetailParams.list = this.riskDetailParams.high_list_today
        } else {
          this.riskDetailParams.list = this.riskDetailParams.mid_list_today
        }

      } else {
        if (this.currentStatus == 1) {
          this.riskDetailParams.list = this.riskDetailParams.high_list
        } else {
          this.riskDetailParams.list = this.riskDetailParams.mid_list
        }
      }

    },
    'ttsModel.levelList': function (val) {
      let list = [false, false, false]
      if (val && val.length) {
        val.forEach(item => {
          list[item] = true
        })
        this.ttsModel.data.level = list
      } else {
        this.ttsModel.data.level = ''
      }
    },
    noIsInterferedTimerNum(val) {
      this.projectFilterList[4].num = val

    },
    noIsInterferedNum(val) {
      this.projectFilterList[4].downNum = val

    },

  },
  computed: {
    ...mapState('auth', ['userInfo']),

    formatList() {
      let start = (this.table.page - 1) * this.table.size;
      let end = this.table.page * this.table.size;
      if (end >= 30) {
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      }
      return this.lockAfterList.slice(start, end)
    },

    eventTypeCode: function () {
      return this.typeList.map(item => item.value)
    },


    finallyList() {
      return this.table.list.filter(item => {
        return this.alarmType.includes(item.warnCode) &&
          (item.isInterfered ? item.times + 20 * 60 * 1000 >= this.nowData : true) &&
          (!item.isInterfered ? item.times + 24 * 60 * 60 * 1000 >= this.nowData : true)
      });
    },
    monitorFilterList: function () {
      return this.finallyList.filter(item => {
        return item.plateNo.includes(this.queryListPlateno) &&
          (this.queryList.dealType == null ? 1 : this.queryList.dealType == 0 ? (item.dealType == this.queryList.dealType && !item.isInterfered) : this.queryList.dealType == 1 ? item.dealType != 0 : item.dealType == this.queryList.dealType) &&
          (this.queryList.riskLevelList.includes(item.riskLevel))
      });
    },
    lockAfterList() {
      let data = JSON.parse(JSON.stringify(this.monitorFilterList)).filter(item => !this.lockListData.find(it => it.eventId == item.eventId))
      return this.lockListData.concat(data)
    },
    //未干预事件数量
    noIsInterferedNum() {
      return this.finallyList.filter(item => !item.isInterfered).length
    },
    //未干预超过15分钟数量
    noIsInterferedTimerNum() {
      return this.finallyList.filter(item => !item.isInterfered && ((moment(item.createTime, 'YYYY-MM-DD HH:mm:ss').valueOf() + 15 * 60 * 1000) < this.nowData)).length
    },

  },
  async mounted() {
    let userTypeList = (await this.$api.queryAlarmEventRuleConfiguration({
      userId: -1
    })).data.values;
    let typeList = await this.$store.dispatch('dictionary/getFormatListByCode', 'danger_event_type');
    typeList = typeList.filter(item => {
      return userTypeList.includes(item.value);
    })
    let vehicleInfo = await this.$api.getAllVehicleInfo();
    let store = {};
    for (let i = 0, len = vehicleInfo.data.length; i < len; i++) {
      store[vehicleInfo.data[i]['vehicleId']] = vehicleInfo.data[i]
    }
    this.vehicleStore = store;
    this.typeList = typeList;
    this.alarmEventName = await this.$store.dispatch('dictionary/getValueLabelMapByCode', 'danger_event_type');
    this.alarmType = this.typeList.map(item => item.value)
    this.vehicleLevelList = (await this.$api.getCommonListByKey({
      key: 'vehicle_warn_color'
    })).data;

    this.getMonitorData()
    let res = await this.$api.getCommonListByKey({ key: "config_event_deal_type" })
    if (res.status !== 200) return this.$error('获取报警事件处理类型失败！')
    this.dealTypeMap = res.data
    this.$bus.$on('followEvent', (id) => {
      this.followEvent(id)
    })

  },

  methods: {
    followEvent(id) {
      let obj = this.lockAfterList.find(item => item.eventId == id)
      if (!obj) return
      this.lockList(obj, 0, false)
    },
    async handleExpiredEvent(){
      this.overflowLoading = true
      let res = await this.$api.eventHandleOvertime({})
      this.overflowLoading = false
      if (res && res.status == 200) {
        this.$success('处理超时事件成功！已处理'+(res.data ? res.data : 0)+'个事件')
      } else {
        this.$error('处理超时事件失败！')
      }
    },
    async getTTSConfig() {
      let userRes = await this.$api.getEventRulerConfig({
        version: 'TTSAutoSendDangerLevel'
      })
      if (userRes && userRes.status == 200 && userRes.data) {
        this.userTTsconfig = userRes.data[0]
        if (this.userTTsconfig.config_value) {
          let configObj = JSON.parse(this.userTTsconfig.config_value)
          this.ttsModel.data.level = configObj.send
          let list = []
          configObj.send.forEach((item, index) => {
            if (item) {
              list.push(index)
            }
          })
          this.ttsModel.levelList = list
          this.ttsModel.data.time = configObj.time && configObj.time.length ? [configObj.time[0].start, configObj.time[0].end] : ['00:00:00', '23:59:59']
          this.ttsModel.data.time2 = configObj.time && configObj.time.length > 1 ? [configObj.time[1].start, configObj.time[1].end] : ['00:00:00', '23:59:59']
        }
      }
    },
    addshowTime() {
      this.ttsModel.showTime2 = true
      this.ttsModel.data.time2 = ['00:00:00', "23:59:59"]
    },
    clear() {
      this.$confirm('确定清除配置?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let result = await this.$api.delTTS({ id: this.userTTsconfig.id })
        if (!result || result.RS != 1) {
          this.$error(result.message || "删除失败");
          return
        }
        this.$success('删除成功')
        this.userTTsconfig = null
        this.ttsModel.data.level = ''

        this.ttsModel.levelList = []
        this.ttsModel.data.time = ['00:00:00', '23:59:59']
        this.ttsModel.data.time2 = ['00:00:00', '23:59:59']
      })
    },
    commitTTS() {
      this.$refs['form'].validate(async valid => {
        if (!valid) return
        this.ttsModel.loading = true
        let time = []
        time.push({
          start: this.ttsModel.data.time[0],
          end: this.ttsModel.data.time[1]
        })
        if (this.ttsModel.showTime2) {
          time.push({
            start: this.ttsModel.data.time2[0],
            end: this.ttsModel.data.time2[1]
          })
        }
        let config_value = {
          send: this.ttsModel.data.level,
          time,
        }
        let params = {}
        if (this.userTTsconfig && this.userTTsconfig.id) {
          params = {
            obj_id: this.userTTsconfig.obj_id,
            obj_type: this.userTTsconfig.obj_type,
            config_type: this.userTTsconfig.config_type,
            config_value: JSON.stringify(config_value),
            id: this.userTTsconfig.id
          }
        } else {
          params = {
            obj_id: this.userInfo.id,
            obj_type: 0,
            config_type: 97,
            config_value: JSON.stringify(config_value),
          }
        }
        let res = await this.$api.insertSysConfig(params)
        if (res && res.RS == 1) {
          this.$success('配置成功')
          this.ttsModel.show = false
        } else {
          this.$error('配置失败')

        }
        this.ttsModel.loading = false

      })
    },
    async sendTTS() {
      await this.getTTSConfig()
      // this.ttsModel.levelList = []
      // this.ttsModel.data =
      //     {
      //         level:'',
      //         time:[
      //             '00:00:00',
      //             "23:59:59"
      //         ],
      //         time2:[
      //             '00:00:00',
      //             "23:59:59"
      //         ],
      //     },
      this.ttsModel.show = true
    },
    getNextRow() {
      //排序规则改变
      // this.showedIdList.push(this.currentRow.eventId)
      // let list = this.lockAfterList.filter(item=>!item.isInterfered && !this.showedIdList.includes(item.eventId)).sort((a,b)=>{moment(b.createTime,'YYYY-MM-DD HH:mm:ss').valueOf() - moment(a.createTime,'YYYY-MM-DD HH:mm:ss').valueOf()})
      let list = this.lockAfterList.filter(item => !item.isInterfered)
      list.sort(
        (a, b) =>
          moment(a.createTime, 'YYYY-MM-DD HH:mm:ss').valueOf() -
          moment(b.createTime, 'YYYY-MM-DD HH:mm:ss').valueOf()
      );
      if (!list.length) return false
      // let lastTime = list.find(item=>{moment(item.createTime,'YYYY-MM-DD HH:mm:ss').valueOf() + 10*60*1000 > moment().valueOf()})
      let heigLevel = list.find(item => item.level == 3)
      let midLevel = list.find(item => item.level == 2)
      // let lastTime = list.filter(item=>{moment(item.createTime,'YYYY-MM-DD HH:mm:ss').valueOf() + 10*60*1000 > moment().valueOf()})
      // let heigLevelList = list.filter(item=>item.level == 3)
      //.sort((a,b)=>a.level - b.level)
      // let midLevelList = list.filter(item=>item.level == 2)
      // let firstList = [...lastTime,...heigLevel,...midLevel]
      // let leaveList = list.filter(item=>!firstList.find(it=>it.eventId == item.eventId))
      // return [...firstList,...leaveList]
      // if(lastTime){
      //     return lastTime
      // }else 
      if (heigLevel) {
        return heigLevel
      } else if (midLevel) {
        return midLevel
      } else {
        return list[0]
      }

    },
    //type:1  上一条  0:下一条
    changeRow(type) {
      let current = this.formatList.findIndex(item => item.eventId == this.currentRow.eventId)
      let nextCurrent
      if (type) {
        if (this.table.page == 1 && current == 0) {
          this.$warning('已经是第一条了!')
          return
        } else {
          if (current == 0) {
            this.table.page = this.table.page -= 1
            nextCurrent = this.formatList[this.table.size - 1]
          } else {
            nextCurrent = this.formatList[current - 1]
          }
        }
      } else {
        nextCurrent = this.getNextRow()
        if (!nextCurrent) {
          this.$warning('已经是最后一条了!')
          return
        }
        let index = this.lockAfterList.findIndex(item => item.eventId == nextCurrent.eventId)
        this.table.page = Math.ceil((index + 1) / this.table.size)
        // if(current == this.formatList.length -1 && this.table.page == Math.ceil(this.lockAfterList.length/this.table.size)){
        //     this.$warning('已经是最后一条了!')
        //     return
        // }else {
        //     if(current == this.formatList.length -1){
        //         this.table.page = this.table.page +=1
        //         nextCurrent = this.formatList[0]
        //     }else {
        //         nextCurrent = this.formatList[current + 1]
        //     }

        // }
      }
      this.showDetail(nextCurrent)
      this.$refs['table'].setCurrentRow(nextCurrent)

    },
    // 跳转报警设置 1:点设置跳报警管理 0:跳报警事件
    jumpToAlarmMgt(path) {
      if (path) {
        this.$router.push({
          path: '/home/<USER>',
          query: { tab: 'event' }
        })
      } else {
        this.$router.push({
          path: '/home/<USER>',
        })
      }

    },
    toJump(id) {
      // let needList = {
      //     vehicleId:id
      // }
      // this.$router.push({
      //     path:'/home/<USER>',
      //     query:needList
      // })

      this.$router.push({
        path: '/home/<USER>',
        query: {
          vehicleId: id,
          startTime: moment().startOf('days').valueOf(),
          endTime: moment().endOf('days').valueOf(),
        }
      })
    },

    riskDetail(status) {
      this.riskDetailParams.show = true
      this.riskDetailParams.sus = false
      this.activeDetailTab = 'current'
      this.currentStatus = status
      switch (status) {
        case 1:
          this.riskDetailParams.title = "高风险车辆列表"
          this.riskDetailParams.list = this.riskDetailParams.high_list
          break;
        case 2:
          this.riskDetailParams.title = "中风险车辆列表"
          this.riskDetailParams.list = this.riskDetailParams.mid_list
          break;
        case 3:
          this.riskDetailParams.sus = true
          this.riskDetailParams.title = "已关注事件列表"
          this.riskDetailParams.list = this.riskDetailParams.suspend_list

          break;

      }
      setTimeout(() => {
        this.$nextTick(() => {
          this.$refs['risktable'].doLayout()
        });
      }, 0)

    },

    toJumpShowDetail(row) {
      this.riskDetailParams.show = false
      let rowData = this.table.list.find(item => item.eventId == row.eventId)
      this.showDetail(rowData)
    },
    // changeDeal(data){

    //   let tableObj = this.table.list.find(item => item.eventId == data.id)
    //   if (!tableObj)return
    //   let { dealType, remark, isInterfered } = data
    //   if (dealType || dealType === 0) {
    //       tableObj.dealType = dealType;
    //       let iskf = this.userInfo.approval_role ? this.userInfo.approval_role.split(',').includes('event_cse') : false
    //       if (isInterfered) {
    //         tableObj.isInterfered = true
    //         tableObj.dealBy = iskf ? this.userInfo.login_name : null
    //       }
    //       tableObj.dealTypeView = this.dealTypeMap[dealType]
    //       if (lastType === 12) {
    //         this.projectFilterList[3].num -= 1
    //       }
          
    //     }
    // },
    async showDetail(value) {
      // if(!value.you_can_handle)return
      // if(!this.currentRow)this.showedIdList = []
      this.currentRow = value;
      this.$refs['alarmDetail'].showModal(value.eventId, parseInt(value.vehicleId), false, true, false, value)
      return
      // value.readed = 1;
      try {
        let lastType = value.dealType
        let { dealType, remark, isInterfered } = await this.$refs['alarmDetail'].showModal(value.eventId, parseInt(value.vehicleId), false, true, false, value);
        if (dealType || dealType === 0) {
          value.dealType = dealType;
          let iskf = this.userInfo.approval_role ? this.userInfo.approval_role.split(',').includes('event_cse') : false
          if (isInterfered) {
            value.isInterfered = true
            value.dealBy = iskf ? this.userInfo.login_name : null
          }
          value.dealTypeView = this.dealTypeMap[dealType]
          if (lastType === 12) {
            this.projectFilterList[3].num -= 1
          }
          if (dealType == 12) {
            // this.changeSusList(1,value)
          }
        }
        //清除选中状态
        // this.$refs['table'].toggleRowSelection(value, false);
      } catch (e) {
        //点击了取消
      } finally {
        // this.restoreMsgQueue();
      }
    },
    // 等级修改
    levelChange(level) {
      if (this.queryList.riskLevelList.includes(level)) {
        let index = this.queryList.riskLevelList.findIndex(item => item == level)
        this.queryList.riskLevelList.splice(index, 1)
        return
      }
      this.queryList.riskLevelList.push(level)
      // this.getMonitorData()
    },
    ttslevelChange(level) {
      if (this.ttsModel.levelList.includes(level)) {
        let index = this.ttsModel.levelList.findIndex(item => item == level)
        this.ttsModel.levelList.splice(index, 1)
        return
      }
      this.ttsModel.levelList.push(level)
    },
    changeEventLevel(id) {
      let tableObj = this.table.list.find(item => item.eventId == id)
      if (tableObj) {
        tableObj.level = 3
      }
    },
    changeEventType(id, code, level) {
      let tableObj = this.table.list.find(item => item.eventId == id)
      if (tableObj) {
        tableObj.warnCode = code
        tableObj.warnCodeView = this.alarmEventName[code]
        // let levelObj = this.vehicleLevelList.find(item=>item.vehicleId == tableObj.vehicleId && item.type == code)
        // tableObj.level = levelObj ? levelObj.level :1
        tableObj.level = level
      }
    },
    //表格行的锁定解锁
    /**
     * status:1(解锁)  0(锁定)
     * isFollow: 是否需要调关注的接口,因为报警事件的接口已经调过了,这里调酒重复了
     */
    async lockList(row, status, isFollow = true) {
      let index = this.lockListData.findIndex(item => item.eventId == row.eventId)
      if (status) {
        if (index == -1) {
          return
        }
      } else {
        if (index != -1) {
          return
        }
      }
      if (isFollow) {
        let desc = status ? '取消关注' : '关注'
        let result = await this.$api.riskFollow({
          event_id: row.eventId,
          key: 'lock',
          value: status ? 0 : 1
        })
        if (result.status !== 200) {
          this.$error(result.message || desc + '失败')
          return;
        }
        if (!status) {
          row.isInterfered = true
        }
        this.$success('已' + desc)
      }

      if (status) {
        this.lockListData.splice(index, 1)
      } else {
        this.lockListData.push(row)
      }
      if (isFollow) {
        let res = await this.$api.operateUserVehicleFllow({
          operate_type: status ? -2 : 1,
          vehicle_id_list: [row.vehicleId]
        })
        if (!res || res.status != 200) {
          this.$warning(res.message || !status ? '关注车辆最多20辆!' : '取消关注失败')
          return
        }
      }

    },
    // selectNodes(data, {checkedNodes}) {
    //     let nodes = checkedNodes.filter(item => item.type === 4);
    //     this.selectedVehicle = nodes.map(item => item.id);
    // 				this.queryList.vehicleIdList = nodes.map(item => item.id)
    //     // this.pager.index = 1;
    // },
    async reflashData() {
      let params = {
        vehicleIdList: [],
        eventTypeList: this.alarmType,
        dealType: this.queryList.dealType == 0 ? null : this.queryList.dealType,
        riskLevelList: this.queryList.riskLevelList,
      }
      try {
        let res = await this.$api.riskMonitor(params)

        this.table.list = res.data.list
        this.table.list.forEach(item => {
          item.times = moment(item.createTime).valueOf()
        })
        this.riskDetailParams.high_list = res.data.vehicle_risk_high_now_list
        this.riskDetailParams.high_list_today = res.data.vehicle_risk_high_today_list

        this.riskDetailParams.mid_list = res.data.vehicle_risk_medium_now_list
        this.riskDetailParams.mid_list_today = res.data.vehicle_risk_medium_today_list

        this.projectFilterList[0].num = res.data.vehicle_online
        this.projectFilterList[0].downNum = res.data.vehicle_total

        this.projectFilterList[1].num = res.data.vehicle_risk_high_now
        this.projectFilterList[1].downNum = res.data.vehicle_risk_high_today

        this.projectFilterList[2].num = res.data.vehicle_risk_medium_now
        this.projectFilterList[2].downNum = res.data.vehicle_risk_medium_today

        this.lockListData = res.data.list_suspend
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })

      } catch (e) {
        console.log(e);
      }
    },
    async getMonitorData() {

      let params = {
        vehicleIdList: [],
        eventTypeList: this.alarmType,
        dealType: this.queryList.dealType == 0 ? null : this.queryList.dealType,
        riskLevelList: this.queryList.riskLevelList,
        // start:'2021-12-31 00:00:00',
        // end:'2021-12-31 23:59:59'
      }
      // if(!params.vehicleIdList.length){
      //     this.$warning('请选择车辆！')
      //     return
      // }
      // if(!params.eventTypeList.length){
      //     this.$warning('请选择事件！')
      //     return
      // }
      // if(!this.queryList.riskLevelList.length){
      //     this.$warning('请选择风险等级！')
      //     return
      // }
      try {
        this.loading = true
        let res = await this.$api.riskMonitor(params)
        if (!res || res.status != 200) {
          this.$error(res.message || '查询出错！')
          return
        }
        if (!res.data || !res.data.list.length) {
          // this.table.list = []
          this.$warning('未查询到相关风险数据！')
          // return
        }
        this.queryListDealType = this.queryList.dealType
        this.queryListLevel = JSON.parse(JSON.stringify(this.queryList.riskLevelList))
        this.table.list = res.data.list
        this.table.list.forEach(item => {
          item.times = moment(item.createTime).valueOf()
        })
        this.riskDetailParams.high_list = res.data.vehicle_risk_high_now_list
        this.riskDetailParams.high_list_today = res.data.vehicle_risk_high_today_list

        this.riskDetailParams.mid_list = res.data.vehicle_risk_medium_now_list
        this.riskDetailParams.mid_list_today = res.data.vehicle_risk_medium_today_list

        // this.riskDetailParams.suspend_list = res.data.list_suspend


        this.projectFilterList[0].num = res.data.vehicle_online
        this.projectFilterList[0].downNum = res.data.vehicle_total

        this.projectFilterList[1].num = res.data.vehicle_risk_high_now
        this.projectFilterList[1].downNum = res.data.vehicle_risk_high_today

        this.projectFilterList[2].num = res.data.vehicle_risk_medium_now
        this.projectFilterList[2].downNum = res.data.vehicle_risk_medium_today

        // this.projectFilterList[3].num = res.data.list_suspend.length
        // this.projectFilterList[3].downNum = res.data.vehicle_safe_yesterday
        this.lockListData = res.data.list_suspend
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
        await this.clearUp()
        this.subscription = [
          DangerEvent.subscribe(this.subscribeMsg),
          DangerEventProcessed.subscribe(this.onProcessed),
          DangerEventEnd.subscribe(this.eventEnd),
        ];

        this.setAlarmEventCountMap()
        this.timer = setInterval(() => {
          this.nowData = moment().valueOf()
          this.restoreMsgQueue();
        }, 2000)
        this.timerData = setInterval(() => {
          this.reflashData();
        }, 5 * 60 * 1000)
      } catch (e) {
        this.$error(e || '查询出错')
      } finally {
        this.loading = false

      }
    },
    setAlarmEventCountMap() {
      let temp = {};
      this.typeList.forEach(type => {
        temp[type.value] = this.table.list.filter(item => item.warnCode == type.value).length;
      })
      this.alarmEventCount = temp;
    },
    restoreMsgQueue() {
      if (this.msgTemp.events.length) {
        this.table.list.splice(0, 0, ...this.msgTemp.events.filter(item => this.eventTypeCode.includes(item.warnCode) && !this.table.list.find(it => it.eventId == item.eventId)));
        // this.$refs['table']?.setCurrentRow();
        this.msgTemp.events = [];
        this.setAlarmEventCountMap();
      }
      Object.entries(this.msgTemp.processed).forEach(([key, msg]) => {
        const event = this.table.list.find(item => item.eventId == key);
        if (event) {
          Object.assign(event, {
            // readed: 1,
            dealType: msg.dealType,
            dealTypeView: this.dealTypeMap[msg.dealType] || '未处理',
            isInterfered: true,
            // dealBy:msg.dealUser
            // attachment:msg.attachment
          })
          delete this.msgTemp.processed[key]
        }
      })
      Object.entries(this.msgTemp.unEnd).forEach(([key, msg]) => {
        const event = this.table.list.find(item => item.eventId == key);
        if (event) {
          // const eventParams = JSON.parse(msg.eventParams) || {};
          let endTime = moment(msg.endTime, 'YYYYMMDDHHmmss');

          // let diff = moment.duration(endTime.diff(moment(event.start_time, 'YYYY-MM-DD HH:mm:ss')));
          // let h = diff.hours(),
          //     m = diff.minutes(),
          //     s = diff.seconds();

          Object.assign(event, {
            endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
            // end_location: msg.endLocation,
            // fenseId: msg.fenseId,
            // fenseName: msg.fenseName,

            // time: `${h?h+this.$ct('label.hour'):''}${m?m+this.$ct('label.minute'):''}${s?s+this.$ct('label.second'):''}`,
            // avg_speed: eventParams.avgSpeed,
            // max_speed: eventParams.maxSpeed,
          })
          delete this.msgTemp.unEnd[key]
        }
      })
    },
    async subscribeMsg(msg) {
      const eventParams = JSON.parse(msg.eventParams) || {};

      let levelObj = this.vehicleLevelList.find(item => item.vehicleId == msg.vehicleId && item.type == msg.eventType)
      let level = levelObj ? levelObj.level : 1
      let temp = {
        vehicleId: msg.vehicleId,
        eventId: msg.eventId,
        warnCode: msg.eventType,
        plateNo: this.vehicleStore[msg.vehicleId].plate,
        startTime: msg.startTime,
        // start_timeE: msg.startTime,
        // start_location: msg.startAddress,
        endTime: msg.endTime,
        // end_timeE: msg.endTime,
        // end_location: msg.endAddress,
        dept: this.vehicleStore[msg.vehicleId].companyName + '>>' + this.vehicleStore[msg.vehicleId].deptName,
        // company_name: this.vehicleStore[msg.vehicleId].companyName,
        // dept_name: this.vehicleStore[msg.vehicleId].deptName,
        driver: msg.driverName,
        createTime: moment(eventParams.createTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss'),
        // fenseId: msg.fenseId,
        // fenseName: msg.fenseName,
        // readed: 0,
        level,
        dealType: msg.dealType,
        riskLevel: msg.riskLevel,
        riskValue: msg.riskValue,
        isInterfered: msg.dealDesc ? true : false,
        dealTypeView: this.dealTypeMap[msg.dealType] || '未处理',
        warnCodeView: this.alarmEventName[msg.eventType],
        times: moment(eventParams.createTime, 'YYYYMMDDHHmmss').valueOf()
        // attachment:msg.attachment,
        // time: eventParams.time,
        // you_can_handle:true,
        // avg_speed: eventParams.avgSpeed,
        // max_speed: eventParams.maxSpeed,
      }
      this.msgTemp.events.unshift(temp);
    },
    //报警事件处理推送 涉及的状态修改
    //todo 可能在数据多的账号会造成性能问题
    async onProcessed(msg) {
      this.msgTemp.processed[msg.eventId] = msg;
    },
    eventEnd(msg) {
      this.msgTemp.unEnd[msg.eventId] = msg;
    },
    // // //默认全选
    // checkAllVehicle($tree) {
    //     let rootNodes = $tree.data.map($tree.getNode);
    //     rootNodes.forEach(node => {
    //         if(node.data && node.data.expired)return
    //         node.setChecked(true, true)
    //     })
    //     this.selectedVehicle = $tree.getCheckedNodes(true).filter(item => item.type === 4).map(item => item.id);
    //     this.getMonitorData()
    // },
    //全选按钮状态改变
    handleCheckAllChange(val) {
      if (val) {
        this.alarmType = []
        this.typeList.forEach(item => {
          this.alarmType.push(item.value)
        })
      } else {
        this.alarmType = []
      }
      this.isIndeterminate = false;
    },
    /**
     * 注意：
     * element中的el-check的isIndeterminate属性与v-model的值共同展现的  全选按钮的样式：
     * isIndeterminate  v-model
     * true  true || true  false  样式为 -（半选）
     * false  true 样式为 √（全选）
     * false  false  样式为 不勾（不选）
     */
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.typeList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.typeList.length;
      this.alarmType = value
    },
    clearUp() {
      //销毁前取消订阅
      this.subscription.forEach(item => {
        item.unsubscribe();
      })
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (this.timerData) {
        clearInterval(this.timerData);
      }

    }
  },
  beforeDestroy() {
    this.clearUp()
    this.$bus.$off('followEvent')
  },
}

</script>

<style lang='scss' scoped>
.project {
  padding-right: 15px;
  float: left;
  width: 100%;
  height: 115px;
  overflow: hidden;

  .top {
    // width: 100%;
    margin-right: -35px;
    margin-top: 10px;
    overflow: hidden;
    height: 105px;

  }

  .projectTable {
    height: calc(100% - 10px);

    .query-item {
      height: 28px;
      margin: 10px 0;
    }

    .el-pagination {
      float: right;
    }

    .projectProcess {
      padding: 2px 5px;
      border-radius: 5px;
      background-color: rgba(40, 128, 226, .2);
      color: var(--color-primary);

      i {
        font-style: normal;
        color: #20A971;
      }
    }

  }

}

.level-button {
  span {
    padding: 3px 15px;
    // background-color: rgba(42, 128, 224,.1);
    cursor: pointer;
    border: 1px solid var(--border-color-lighter);
    border-radius: 5px;
    margin-right: 2px;
    font-size: 12px;
  }

  .active0 {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);

    background-color: rgba(42, 128, 224, .1);

  }

  .active1 {
    border: 1px solid var(--color-warning);
    color: var(--color-warning);

    background-color: rgba(242, 170, 59, .1);

  }

  .active2 {
    border: 1px solid var(--color-danger);
    color: var(--color-danger);

    background-color: rgba(255, 83, 89, .1);

  }
}

.risk-level {
  display: block;
  margin: 0 auto;
  width: 80px;
  //padding: 3px 25px;
  border-radius: 5px;
  font-size: 12px;
  color: #fff;

}
.tag {
  padding: 1px 5px;
  background-color: #c8dbf1;
  border-radius: 3px;
  margin-left: 3px;
  font-size: 12px;
  color: var(--color-primary);
}
.blue-line {
  position: absolute;
  left: 0;
  top: 0;
  display: inline-block;
  width: 3px;
  height: 100%;
  background-color: var(--color-primary);
}

.high-level {
  background-color: var(--color-danger);
}

.middle-level {
  background-color: var(--color-warning);
}

.low-level {
  background-color: var(--color-primary);
}

.scope-label {
  display: inline-block;
  padding: 0px 8px;
  border-radius: 5px;
  margin-right: 10px;
  margin-bottom: 3px;
  color: rgb(40, 128, 226);
  background-color: rgba(40, 128, 226, .2);
}

.el-tabs {
  /deep/.el-tabs__content {
    padding: 8px;
  }
}

.el-date-editor /deep/.el-range-separator {
  width: 12%;
}
</style>
