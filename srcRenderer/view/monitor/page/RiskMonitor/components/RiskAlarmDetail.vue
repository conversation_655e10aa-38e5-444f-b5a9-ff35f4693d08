<template>
    <PonyDialog v-model="show" class="alarm-detail-v2" :width="activeWidth" extra-width="350"
        :topWidth="true"
        :offsetWidth="1100"
        :allowClose="false"
        :content-style="'width:'+(activeWidth - 350)+'px;height:570px;padding:0'"  :loading="loading"
        :has-mask="false">
        <template slot="title">
            <!-- :offsetX="400" :offsetY="80" -->
            <span class="" :title="$ct('messageInfo.5')"
            style="display: inline-block;"
            @mousedown="copy(detailList.plate_no)"
                >
                <el-tag :type="rowData ? rowData.level == 3 ? 'danger' : rowData.level == 2 ? 'warning' : 'default':'default'"  style="cursor: pointer;">
                                    {{ detailList.plate_no }}
                                </el-tag>
                <!-- <i style="font-style:normal" class="pointer text text--link" @mousedown.stop="copy(detailList.plate_no)">{{ detailList.plate_no }}</i> -->
            </span>
                <!-- <span :class="[rowData ? rowData.riskLevel == 2?'risk-level high-level' : rowData.riskLevel == 1? 'risk-level middle-level':'risk-level low-level':'']">{{rowData ? rowData.riskValue: ''}}</span> -->
                <!-- <el-button type="primary" @click="readyExportPDF">截图分享</el-button> -->
            <div class="event-type" style="float:right">
                <span v-if="!rowData || !rowData.level || rowData.level == 1" style="cursor: pointer;">{{detailList.event_type_name}}</span>
                <el-tag v-else :type="rowData.level == 3 ? 'danger' : rowData.level == 2 ? 'warning' : 'default'" style="cursor: pointer;">
                    {{detailList.event_type_name}}<i class="pony-iconv2 pony-jiantou2-right event-change" @mousedown.stop="changeRiskLevel()" v-if="rowData.level == 2"></i>
                </el-tag>
                
            </div>
        </template>
        <div class="left-wrap">
            <div class="event-process" v-if="(detailList.alarmList && detailList.alarmList.length) || (detailList.alam_event_rec_medias && detailList.alam_event_rec_medias.length)">
                <div class="event-process-list" v-if="detailList.alarmList && detailList.alarmList.length">
                    <ul class="alarm-card-list" >
                        <li class="alarm-title"><i></i>事件过程</li>
                        
                        <li class="card-item" v-for="item in detailList.alarmList" :key="item.id" @click="viewMarker(item)">
                            <div class="card-main">
                                <ul class="top-line">
                                    <li >
                                        <i :class="TypeForClass(item.alarm_type)"></i>
                                        <span class="alarm-name">{{ TypeForName(item.alarm_type) }}</span>
                                    </li>
                                    <!-- <li>
                                        <span>车速:{{ !isNaN(item.speed) ? item.speed+'km/h':'-'}}</span>
                                    </li> -->
                                </ul>
                                <ul class="bottom-line">
                                    <!-- <li>
                                        <span>车速:{{ !isNaN(item.speed) ? item.speed+'km/h':'-'}}</span>
                                    </li> -->
                                    <p>车速:{{ !isNaN(item.speed) && item.speed != null ? item.speed+'km/h':'-'}}</p>

                                    <p class="alarm-time">{{ TimeFormat(item.alarm_time) }}</p>
                                </ul>
                            </div>
                        </li>
                        
                    </ul>
                </div>
                <div class="alarm-attach" v-if="detailList.alam_event_rec_medias && detailList.alam_event_rec_medias.length">
                    <ul>
                        <li v-for="(item,index) in detailList.alam_event_rec_medias" :key="index">
                            <!-- <div class="video-item" v-if="item.filetype == 2">
                                <i class="pony-iconv2 pony-shipin1"></i>
                            </div> -->
                            <VideoPlayerDetail
                                :muted="true"
                                ref="video2"
                                :controls="true"
                                :controlBar="controlBar"
                                :showClose="false"
                                :autoplay = "true"
                                v-if="item.substr(-3) == 'mp4'"
                                @ready="eventEmitter('ready', $event)"
                                :options="{sources: [{type: 'video/mp4', src: item}]}">
                            </VideoPlayerDetail>
                            <el-image
                                v-else
                                style="width: 100%; height: 100%"
                                fit="fill"
                                :src="item"
                                :preview-src-list="detailList.alam_event_rec_medias.filter(it=>it.substr(-3) != 'mp4')"
                                >
                            </el-image>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="event-detail">
                <ul class="alarm-info-list">
                    <li class="alarm-title" style="margin-left:-5px"><i></i>事件详情</li>

                    <li class="info-item">
                        <span>{{ $ct('label.eventType') }}：</span>
                        <span>{{ detailList.event_type_name }}
                            <i class="pony-iconv2 pony-xiugai" style="color:var(--color-primary);vertical-align: -2px;margin-left: 3px;" @click="changeEventType()"></i>
                            <span :title="$ct('label.todayCount')"
                                style="color:var(--color-danger)">（{{ '第'+detailList.today_type_count + $ct('label.count') }}）</span>
                        </span>
                    </li>
                    <li class="info-item short">
                        <span>{{ $ct('label.region') }}：</span>
                        <span>{{ detailList.fenseName || $ct('label.notSet') }}</span>
                    </li>
                    <li class="info-item">
                        <span>{{ $ct('label.company') }}：</span>
                        <span :title="(detailList.company_name + '>>' + detailList.dept_name) || $ct('label.notSet')">{{ (detailList.company_name + '>>' + detailList.dept_name) || $ct('label.notSet') }}</span>
                    </li>
                    <li class="info-item short">
                        <span>{{ $ct('label.mileage') }}：</span>
                        <span>{{ detailList.mile }} km</span>
                    </li>
                    <li class="info-item">
                        <span>{{ $ct('label.maxSpeed') }}：</span>
                        <span>{{ detailList.max_speed || 0 }} km/h</span>
                    </li>
                    <li class="info-item short">
                        <span>{{ $ct('label.avgSpeed') }}：</span>
                        <span>{{ detailList.avg_speed }} km/h</span>
                    </li>
                    <li class="info-item">
                        <span>{{ $ct('label.startTime') }}：</span>
                        <span>{{ detailList.start_time }}</span>
                    </li>
                    <li class="info-item short">
                        <span>{{ $ct('label.driveDuration') }}：</span>
                        <span>{{ detailList.time }}</span>
                    </li>
                    <li class="info-item">
                        <span>{{ $ct('label.endTime') }}：</span>
                        <span>{{ detailList.end_time || $ct('label.noEnd') }}</span>
                    </li>
                    <li class="info-item short">
                        <span>持续时长：</span>
                        <span>{{ detailList.time_sub }} </span>
                    </li>
                    <!-- <li class="info-item short">
                        <span>{{ $ct('label.mileage') }}：</span>
                        <span>{{ detailList.mile }} km</span>
                    </li> -->
                    <li class="info-item long">
                        <span>开始地址：</span>
                        <span>{{ (detailList.start_location) || '无' }}</span>
                    </li>
                    <li class="info-item long">
                        <span>结束地址：</span>
                        <span>{{ (detailList.end_location) || '无' }}</span>
                    </li>
                </ul>
                <ul class="alarm-info-phone">
                    <li class="alarm-title" style="margin:0 0 10px -5px"><i></i>联系方式</li>
                    <li v-for="(item,index) in detailList.phone_table" :key="index">
                        <span>{{item.role}}</span>
                        <span :title="item.name">{{item.name}}</span>
                        <span>
                            <i :title="item.phone" class="pony-iconv2 pony-dianhua" @click="toPhone(item)"></i>
                            <!-- <i class="pony-iconv2 pony-duanxin" title="发送短信" @click="toSend(item)"></i> -->
                            <i class="pony-iconv2 pony-duanxin" title="暂未开通"></i>

                            <i class="pony-iconv2 pony-weixin" title="暂未开通"></i>
                        </span>
                    </li>

                </ul>
                <ul class="alarm-info-safe">
                    <li class="alarm-title" style="margin:0 0 10px -5px">
                        <i></i>
                        安全干预
                        <el-popover
                            placement="top-start"
                            width="600"
                            trigger="click"
                            v-if="eventExplain[detailList.event_type]"
                            >
                            <div class="pop-content">
                                <h4>判断说明:</h4>
                                <p v-for="(it,ind) in eventExplain[detailList.event_type].ex" :key="ind+'ex'">{{it}}</p>
                                <h4>处理流程:</h4>
                                <p v-for="(it,ind) in eventExplain[detailList.event_type].pro" :key="ind+'pro'">{{it}}</p>
                            </div>
                            <span class="pony-iconv2 pony-bangzhu" slot="reference" style="color:var(--color-primary)" v-if="eventExplain[detailList.event_type]"></span>
                        </el-popover>
                        <span class="hasHandle" v-if="detailList.handleIcon">
                            <i class="pony-iconv2 pony-chenggong" style="vertical-align: -1px;"></i>
                            <i>{{ detailList.handleIcon }}</i>
                            <!-- <i class="gang"></i>
                            <i>分享</i> -->

                        </span>
                    </li>
                    <li class="button-item">
                        <!-- <span :class="currentTTS == 0?'active':''" @click="changeTTS(0)" v-if="!ttsmodelist.length">默认</span> -->
                        <span v-for="(item,index) in ttsmodelist.slice(0,5)" :title="item.configDesc" :key="index" :class="currentTTS == index + 1?'active':''" @click="changeTTS(index+1)">{{item.configDesc}}</span>
                        <el-dropdown
                            v-if="ttsmodelist.length>6"
                            @command="changeTTS"
                            trigger="click"
                            >
                            <span style="padding:3px 4px"><i class="pony-iconv2 pony-gengduo"></i></span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-for="(item,index) in ttsmodelist" :key="index" :command="index+1">{{item.configDesc}}</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>

                        <span style="padding:3px 4px"><i class="pony-iconv2 pony-jia" @click="jumpToTTS()" ></i></span>
                    </li>
                    <li>
                        <el-input type="textarea" :rows="3" v-model="messageModal.data.content" resize="none"
                         style="margin-bottom:20px"></el-input>
                    </li>
                </ul>
                <div class="commit-button">
                    <el-popover placement="left" width="560" trigger="click" ref="popover">
                        <el-table :data="gridData" height="250">
                            <el-table-column min-width="70" property="content" label="事件类型">
                                <template slot-scope="{row}">
                                    <span><i v-if="row.lastHandle" style="color:red;font-style:normal;margin-right:3px" class="pony-iconv2 pony-youbaojing"></i>{{row.desc}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column min-width="130" property="time" label="干预时间"></el-table-column>
                            <el-table-column min-width="100" property="type" label="操作"></el-table-column>
                            <el-table-column min-width="70" property="by" label="干预来源" show-overflow-tooltip></el-table-column>
                            <el-table-column min-width="120" property="content" label="内容" show-overflow-tooltip>
                            </el-table-column>
                        </el-table>
                        <el-button type="primary" slot="reference" style="margin-right:10px;position: relative;">24小时干预记录<i  class="remain" v-if="detailList.lastHandle"></i></el-button>
                    </el-popover>
                    <el-button type="primary" @click="sendTextCommand">干预</el-button>
                    <el-button type="primary" @click = "commit(5)">误报</el-button>
                    <el-button type="primary" @click = "commit(2)">无责</el-button>
                    <el-button type="primary" @click = "followEvent">关注</el-button>
                </div>
              
            </div>

        </div>
        <div class="right-wrap" slot="extra">
            <div class="video-wrap">
                <CheckSimOverFlow :vehicleId="params.vehicleId" :terminalCode="params.terminalCode">
                    <RealtimePlayer :vehicleId="params.vehicleId" :terminalCode="params.terminalCode" :chnN="chn"
                        @talkStart="talkStart"
                        :inputType="params.inputType"
                        :notSupportVideo="true"
                        :symbol="detailList.plate_no" :enableFullScreen="true" :autoPlay="autoPlay"
                        :disabled="!params.terminalCode" ref="video">
                    </RealtimePlayer>
                </CheckSimOverFlow>
                <div class="video-tip" v-show="params.terminalCode">
                    <div class="title">
                        <div v-show="$refs['video'] &&
                                            $refs['video'].player &&
                                            !$refs['video'].player.videoElm.paused">
                            <div class="remaining-time pointer bg video-tip-link" v-show="show && remainingTime">
                                <i class="el-icon-time"></i>
                                {{ remainingTime }}
                            </div>
                            <div v-stop-mouse-move="stopMouseMoveOptions"></div>
                        </div>
                        <el-dropdown @command="setChn">
                            <div class="pointer video-tip-link bg">
                                {{channelNoNameObj[chn] ? channelNoNameObj[chn] : $ct('label.chn')+chn}}
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-for="item in channelNoList" :key="item" :command="item">
                                    {{channelNoNameObj[item] ? channelNoNameObj[item] : $ct('label.chn')+item}}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <!-- <p class="bg pointer video-tip-link">实时视频</p> -->
                    </div>

                </div>
            </div>
            <div class="map-wrap" @mousedown.stop>
                <SimpleMap class="map" ref="map" mapConfig="justmap" :mapLittleTool="['scale']"></SimpleMap>
                <div class="monitor-btn"><i class="pony-iconv2 pony-shishijiankong" @click="linkToMonitor"></i></div>
                <div class="play-black-btn" @click="linkToPlayBack"></div>
                <div class="show-can-run" v-if="showCanRun">
                    <el-tooltip class="item" effect="dark" content="显示可通行路段" placement="bottom">
                        <el-switch @change="showCanRunLine" v-model="canRun.switch"></el-switch>
                    </el-tooltip>
                </div>
            </div>
        </div>
        <player ref="player" @played="changeBlue"></player>
        <template slot="footer">
            <slot name="extra-button">
            </slot>

            <div style="flex-grow:1"></div>
            <!-- <el-button type="primary" @click="commit" :disabled="dealType === 0">
                {{ $ct('label.deal') }}
            </el-button> -->
            <!--            <el-button size="mini" type="text" v-else>{{ $ct('label.hasDeal') }}</el-button>-->
            <el-button type="primary" @click="shareDetail">分享</el-button>
            <!-- <el-button type="primary" @click="changeRow(1)" :disabled="upDisabled">上一条</el-button> -->
            <el-button type="primary" @click="changeRow(0)" :disabled="downDisabled">下一条</el-button>
            <el-button type="border" @click="closeModal">{{ $ct('label.cancel') }}</el-button>
        </template>
        <div class="pdfViewBackground" ref="pdfview"></div>
        <!--	  新增文本弹窗-->
        <!-- <AddTTSMould ref="addTTSMould" @refresh="getTTSModelList()" :showNotice="true"></AddTTSMould> -->
        <PonyDialog width="450" @confirm="ttsModalConfirm" @close="closeTTsModal" title="请输语音下发内容" hasMask
            v-model="ttsmodal.show">
            <el-form ref="ttsform" :model="ttsmodal.data" :rules="ttsmodal.rules" label-width="80px">
                <el-form-item label="模板">
                    <el-select style="width: 100%" v-model="ttsmodal.data.type"
                        @change="ttsmodal.data.text = JSON.parse(JSON.stringify(ttsmodal.data.type))">
                        <el-option v-for="(item, index) in ttsmodal.modellist" :key="index" :label="item.configDesc"
                            :value="item.configValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="信息" prop="text">
                    <el-input type="textarea" :rows="5" v-model="ttsmodal.data.text"></el-input>
                </el-form-item>
            </el-form>
        </PonyDialog>
        <PonyDialog width="680" @confirm="eventModalConfirm" @close="eventShow = false" title="事件类型修改" hasMask
            v-model="eventShow" contentStyle="padding:15px;background-color:var(--background-color-light)">
            <ul class="eventList">
                <li v-for="item in typeList" :key="item.value" 
                    :class="change_event_type == item.value ? 'active':detailList.event_type == item.value ? 'disabled':''"
                    @click="changeCurrent(item.value)"
                    :title="item.label"
                    >
                    <i :class="item.className"></i>
                    <span class="alarm-name">{{ item.label }}</span>
                </li>
            </ul>
        </PonyDialog>
        <!--    文本弹窗 (现在是短信的文本,逻辑和文本下发差不多)-->
        <PonyDialog v-model="sendMsg.show" @close="sendMsg.show = false" :loading="sendMsg.loading" title="短信下发" :width="450"
            class="text">
            <el-form :model="sendMessageModal.data" :rules="sendMessageModal.rules" label-width="80px" ref="textForm"
                label-position="left">
                <el-form-item label="短信模板">
                    <el-select v-model="sendMsgModelresult" @change="changeSendMsgModal">
                        <!-- <el-option label="默认文本"
                            :value="`尊敬的${detailList.driver_name}驾驶员，您今日已触发${detailList.event_type_name}事件${detailList.today_type_count}次`">
                        </el-option> -->
                        <el-option v-for="(item, index) in sendMsgModelist" :key="item.id" :label="item.sms_name"
                            :value="item.config_value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="下发内容" prop="content">
                    <el-input type="textarea" :rows="5" v-model="sendMessageModal.data.content"></el-input>
                </el-form-item>
            </el-form>
            <template slot="footer">
                <el-button type="primary" @click="sendMsgCommand">发送</el-button>
                <el-button type="border" @click="sendMsg.show = false">取消</el-button>
            </template>
        </PonyDialog>
    </PonyDialog>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2020/4/3 15:51
     * @LastEditors: yezy
     * @LastEditTime: 2020/4/3 15:51
     * @Description:
     */
import VideoPlayerDetail from "@/components/alarmvideo/VideoPlayerDetail"

    import ImageUploaderGroup from "@/components/common/ImageUploaderGroup";
    import L from '@/assets/lib/leaflet-bmap'
    import {
        getAlarmMapIcon
    } from '@/view/monitor/util/monitorUtil'
    import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
    import BstandardUtil from '@/util/bstandard'
    import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/RealtimePlayer";
    import CheckSimOverFlow from "@/view/videoPlay/component/videoPlayerV2/component/CheckSimOverFlow";
    import AddTTSMould from "@/view/system/ThemeRuler/components/modal/addTTSMould.vue"
    import {
        generatePonylineFenseLayer
    } from '@/view/monitor/util/generateUtil'
    import html2canvas from 'html2canvas'
    import { eventExplain } from './eventExplain'
import { generateNewVersionFenseLayer } from '@/view/monitor/page/FenseCollect/util/fensebasicsetting'


    const startIcon = L.icon({
        iconUrl: require('../../../../../../static/img/map/start.png'),
        iconSize: [26, 26],
        className: 'ml_start_point'
    })
    const endIcon = L.icon({
        iconUrl: require('../../../../../../static/img/map/end1.png'),
        iconSize: [26, 26],
        className: 'ml_start_point'
    })

    export default {
        name: "alarmDetailV2",
        _i18Name: 'alarmDetail',
        components: {
            CheckSimOverFlow,
            RealtimePlayer,
            SimpleMap,
            ImageUploaderGroup,
            AddTTSMould,
            VideoPlayerDetail
        },
        props: {
            // 是否显示文本弹窗配置项
            showTextOptions: {
                default: true,
                required: false,
                type: Boolean
            },
            // 是否显示新增模板
            showAddTemplate: {
                default: false,
                required: false,
                type: Boolean
            },
            // showCanRun:{
            //     type: Boolean,
            //     default: true,
            // },
            //上一条是否禁止点击
            upDisabled:{
                type: Boolean,
                default: false,
            },
            //下一条是否禁止点击
            downDisabled:{
                type: Boolean,
                default: false,
            },
            //是否可以锁定事件
            isCanLock:{
                type:Boolean,
                default:false
            }

        },
        data() {
            return {
                eventExplain,
                currentTTS:0,
                gridData: [],
                rowData:null,
                // 文件上传
                // dialogImageUrl: '',
                // dialogVisible: false,
                fujian: true,
                fileList: [],
                typeList:[],
                show: false,
                loading: false,
                eventShow:false,
                change_event_type:'',
                params: {
                    vehicleId: '',
                    eventId: '',
                    version: 2,
                },
                showCanRun:false,
                controlBar:{
                    remainingTimeDisplay: false,
                    playToggle: true,
                    progressControl: true,
                    fullscreenToggle: true,
                    volumeMenuButton: {
                            inline: false,
                            vertical: true
                    },
                    subsCapsButton:false,
                    seekToLive:false,
                    liveDisplay:false,
                    durationDisplay:false,
                    customControlSpacer:false,
                    palybackRateMenuButton:false,
                    autoTrackButton:false

                },
                ttsmodal: {
                    modellist: [],

                    show: false,
                    _reject: null,
                    _resolve: null,
                    data: {
                        type: '',
                        text: ''
                    },
                    rules: {
                        text: [{
                            required: true,
                            message: '请输入消息内容',
                            trigger: 'blur'
                        }, ]
                    }
                },
                detailList: {},
                dealType: 0,
                dealTypeMap: {},
                vehicleLevelList:[],
                remark: null,
                remarkPrevious: null,
                templateId: 0,
                templateList: [{
                        label: this.$ct('label.custom'),
                        value: -1,
                        template: ''
                    },
                    {
                        label: this.$ct('label.defaultTemplate'),
                        value: 0,
                        template: ''
                    },
                ],
                gpsList: null,
                resultP: {
                    resolve: null,
                    reject: null,
                },
                mapInitialized: false,
                channelNo: 1,
                channelNoList:[],
                channelNoNameObj:{},
                chn: 2,
                autoPlay: false,
                autoClose: true,
                remainingTime: 0,
                stopMouseMoveOptions: {
                    target: document,
                    time: 300,
                    handler: this.stopVideo,
                    stateChange: this.onRemainingTime
                },
                canRun: {
                    switch: false,
                    _layer: null
                },
                //拍照，监听
                photo: {
                    show: false,
                    loading: false,
                },
                phone: {
                    show: false,
                    loading: false,
                },
                text: {
                    show: false,
                    loading: false,
                },
                sendMsg: {
                    show: false,
                    loading: false,
                },
                photoModal: {
                    pixelRatio: [
                        {value: 0, label: '最小分辨率'},

                        {
                            value: 1,
                            label: '320 * 240'
                        },
                        {
                            value: 2,
                            label: '640 * 480'
                        },
                        {
                            value: 3,
                            label: '800 * 600'
                        },
                        {
                            value: 4,
                            label: '1024 * 768'
                        },
                        {
                            value: 5,
                            label: '176 * 144'
                        },
                        {
                            value: 6,
                            label: '352 * 288'
                        },
                        {
                            value: 7,
                            label: '704 * 288'
                        },
                        {
                            value: 8,
                            label: '704 * 576'
                        },
                        {value: 255, label: '最大分辨率'},

                    ],
                    data: {
                        photoNum: 1,
                        channel_no: 1,
                        operate_cmd: 1,
                        interval: 2,
                        pixel: 1,
                        quality: 5,
                        light: 127,
                        contrast: 64,
                        chroma: 64,
                        saturation: 127,
                        save_flag: 1
                    },
                },
                options: [{
                    value: '01',
                    label: '服务'
                }, {
                    value: '10',
                    label: '紧急'
                }, {
                    value: '11',
                    label: '通知'
                }],
                defaultMsg:'',
                currentRow:null,
                sendMsgModelist:[],
                sendMsgModelresult:'',
                ttsmodelist: [],
                ttsmodelresult: '',
                messageModal: {
                    data: {
                        urgent: "01",
                        terminal_monitor: true,
                        terminal_tts: true,
                        add_show: false,
                        center: 0,
                        content: '',
                        usage:1
                    },
                    rules: {
                        content: [{
                            required: true,
                            message: '请输入消息内容',
                            trigger: 'blur'
                        }, ]
                    }
                },
                sendMessageModal: {
                    data: {
                        urgent: "01",
                        terminal_monitor: true,
                        terminal_tts: true,
                        add_show: false,
                        center: 0,
                        content: '',
                        usage:1
                    },
                    rules: {
                        content: [{
                            required: true,
                            message: '请输入消息内容',
                            trigger: 'blur'
                        }, ]
                    }
                },
                phoneModal: {
                    type: 1,
                    tel: '',
                    table:[]
                },
                phoneRules: {
                    tel: [{
                        required: true,
                        message: '请输入电话号码！',
                        trigger: 'blur'
                    }],
                },
                callResult:null,//呼叫成功的结果存储
                remarkPrompt: {
                    0: '已下发报警解除',
                    1: '已拍照',
                    2: '已监听',
                    3: '已下发TTS语音'
                },
                remarkAgain: null,
                markerStartEnd: null
            }
        },
        filters: {
            formatTime(ms) {
                if (ms) {
                    let fmt = '';
                    let minute = parseInt(ms, 10);
                    let second = 0;

                    if (minute <= 60) {
                        fmt = minute < 10 ? `0${minute}s` : `${minute}s`;
                    } else {
                        second = Math.floor(minute / 60);
                        second = second < 10 ? `0${second}` : second;
                        minute = Math.floor(minute % 60);
                        minute = minute < 10 ? `0${minute}` : minute;
                        fmt = `${second}m${minute}s`;
                    }
                    return fmt;
                } else {
                    return '- m - s'
                }
            },
        },
        computed:{
            activeWidth(){
                let width = 1200
                if(!this.detailList.alam_event_rec_medias || !this.detailList.alam_event_rec_medias.length){
                    width = width - 130
                }
                if(!this.detailList.alarmList || !this.detailList.alarmList.length){
                    width = width - 250
                }
                return width
            }
        },
        methods: {
            changeCurrent(value){
                if(value == this.detailList.event_type)return 
                this.change_event_type = value

            },
            changeEventType(){
                this.eventShow = true
                this.change_event_type = ''
                // this.change_event_type = this.detailList.event_type
            },
            async eventModalConfirm(){
                if(!this.change_event_type){
                    this.$warning('请选择事件')
                    return
                }
                this.loading = true

                let res = await this.$api.eventTypeModify({
                    event_id:this.rowData.eventId || this.rowData.event_id,
                    event_type:this.change_event_type,
                })
                if(!res || res.status != 200){
                    this.$error(res.message || '修改失败')
                    this.loading = false
                    return
                }
                this.$success('修改成功')
                this.getDetail();
                let levelObj = this.vehicleLevelList.find(item=>item.vehicleId == this.detailList.vehicle_id && item.type == this.change_event_type)
                this.rowData.level = levelObj ? levelObj.level :1

                this.$emit('changeEventType',this.rowData.eventId || this.rowData.event_id,this.change_event_type,levelObj ? levelObj.level :1)
                this.eventShow = false
                this.loading = false

            },
            speckText(text){
                let speechInstance = new window.SpeechSynthesisUtterance(text);
                speechInstance.lang = 'zh';
                speechInstance.volume = 100; //声音的音量
                speechInstance.rate = 1; //语速，数值，默认值是1，范围是0.1到10
                speechInstance.pitch = 1.5; // 表示说话的音高，数值，范围从0（最小）到2（最大）。默认值为1
                window.speechSynthesis.speak(speechInstance);

            },
            async changeRiskLevel(){
                let result = await this.$api.changeRiskLevel({
                    event_id: this.rowData.eventId
                })
                if (!result || result.status != 200) {
                    this.$error(result.message || '修改失败')
                    return
                }
                this.rowData.level = 3
                this.$emit('changeEventLevel',this.rowData.eventId)
            },
           
            changeSendMsgModal(val){
                if(!val)return
                let str = JSON.parse(val).content
                let reg = /[^\{\}]+(?=\})/g;
                let resultList = str.match(reg);
                resultList && resultList.length && resultList.forEach(item=>{
                    if(!this.detailList[item])return
                    str = str.replace(new RegExp("\\$\\{" + item + "\\}","gm"),this.detailList[item])
                })
                this.sendMessageModal.data.content = str
            },
            talkStart(){
                this.deviceCommit(7,'对讲','')
                this.$api.callCenterRec({
                        sendType:'EventVoIP',
                        vehicleId:this.detailList.vehicle_id,
                        desc:this.detailList.event_type_name,
                        time:this.detailList.start_time
                    })
            },
            async shareDetail(){
                let params = Object.assign(this.rowData,this.detailList)
                let res = await this.$api.eventShare(params)
                if(res && res.status == 200){
                    this.deviceCommit(14,'分享','')
                    this.$api.callCenterRec({
                        sendType:'EventShare',
                        vehicleId:this.detailList.vehicle_id,
                        desc:this.detailList.event_type_name,
                        url:res.data,
                        time:this.detailList.start_time
                    })
                    let copyText = `${res.data}
                    【${this.detailList.dept_name}${this.detailList.plate_no}${this.detailList.driver_name}于${moment(this.detailList.create_time).format('YYYY年MM月DD日HH时mm分ss秒')}产生 (今日第${this.detailList.today_type_count}次) ${this.detailList.event_type_name}事件】`
                    const target = document.createElement('div');
                    target.id = 'tempTarget';
                    target.style.opacity = '0';
                    target.innerText = copyText;
                    document.body.appendChild(target);

                    try {
                        let range = document.createRange();
                        range.selectNode(target);
                        window.getSelection().removeAllRanges();
                        window.getSelection().addRange(range);
                        document.execCommand('copy');
                        window.getSelection().removeAllRanges();
                        this.$warning('链接已复制到剪切板!')

                    } catch (e) {}
                    target.parentElement.removeChild(target);
                }else {
                    this.$error('分享出错')
                }
            },
            eventEmitter(e){

            },
            changeRow(type){
                this.$emit('changeRow',type)
            },
            changeTTS(index){
                this.currentTTS = index
                let remark
                if(index){
                    remark = this.ttsmodelist[index-1] ? this.ttsmodelist[index-1].configValue : ''
                }else {
                    remark = ''
                    // remark = `尊敬的${this.detailList.driver_name}驾驶员，您今日已触发${this.detailList.event_type_name}事件${this.detailList.today_type_count}次`
                }

                if(remark.includes('{x}')){
                    let str = `${this.detailList.today_type_count}`
                    this.messageModal.data.content = remark.replace(/{x}/g,str)

                }else {
                    this.messageModal.data.content = remark
                }
            },
            
            toCopy(phone){
                if(phone == '-'){
                    this.$warning('暂无可复制电话!')
                    return
                }
                this.copy(phone)
            },
            toSend(row){
                this.sendMsg.show = true
                this.currentRow = row
                this.sendMsgModelresult = this.defaultMsg
                this.sendMessageModal.data.content = ''
                if(this.defaultMsg){
                    this.changeSendMsgModal(this.defaultMsg)
                }

            },
            async toPhone(row){
                let phone = row.phone
                if(phone == '-'){
                    this.$warning('暂无可呼叫电话!')
                    return
                }
                try{
                    this.callResult = null
                    this.phone.loading = true
                    let content = `已电话呼叫${row.role}${row.name}（电话:${phone}`
                    this.deviceCommit(11,'呼叫',content)

                    let result = await this.$api.callCenterPhone({
                        phone,
                    })
                    if(!result || result.status != 200){
                        this.$error(result.message || '呼叫失败!')
                        this.phone.loading = false
                        return
                    }
                    this.phone.loading = false
                    this.$success('呼叫成功!')
                    this.callResult = result.data
                    this.$api.callCenterRec({
                        vehicleId:this.detailList.vehicle_id,
                        name:row.name,
                        phone,
                        role:row.role,
                        data:this.callResult,
                        desc:this.detailList.event_type_name,
                        time:this.detailList.start_time
                    })
                }catch(e){
                    this.phone.loading = false
                    this.$error('呼叫出错!')
                }
                // this.$warning('当前账户未开通网络呼叫,请联系管理员!')
            },
           
            jumpToTTS(){
                this.$router.push({
                    path: "/home/<USER>",
                    query: {
                    tab: "themeRuler:ttsMould",
                    },
                });
            },
            // 添加模板
            addItem() {
                this.$refs['addTTSMould'].showModal();
            },
            async getTTSModelList() {
                let result = await this.$api.getTTS()
                if (!result || result.status != 200 || !result.data || !result.data.length) return
                // this.ttsmodal.modellist = result.data
                // this.ttsmodal.data.type = result.data[0].configValue
                // this.ttsmodal.data.text = result.data[0].configValue

                // //下面的是文本下发需要赋值的字段，上面是视频里面tts语音下发（分开只是为了不混淆）
                // result.data.forEach(item => {
                //     this.templateList.push({
                //         value: item.id,
                //         label: item.configDesc,
                //         template: item.configValue
                //     })
                // });
                // this.ttsmodelist = result.data

                this.ttsmodelist = result.data.filter(item=>item.remark)

            },

            showModalTTS() {
                this.ttsmodal.show = true
                return new Promise((resolve, reject) => {
                    this.ttsmodal._reject = reject;
                    this.ttsmodal._resolve = resolve;
                })
            },

            closeTTsModal() {
                this.ttsmodal._reject = false;
                this.ttsmodal._resolve = false;
            },

            ttsModalConfirm() {
                this.$refs['ttsform'].validate(async (valid) => {
                    if (!valid) return
                    this.ttsmodal.show = false
                    this.ttsmodal._resolve(this.ttsmodal.data.text);
                })
            },
            // 点击处理图标
            // sendCommand(type) {
            //     switch (type) {
            //         case 'phone':
            //             this.phone.show = true
            //             break;
            //         case 'photo':
            //             this.photo.show = true
            //             break;
            //         case 'text':
            //             this.text.show = true
            //             this.ttsmodelresult =
            //                 `尊敬的${this.detailList.driver_name}驾驶员，您今日已触发${this.detailList.event_type_name}事件${this.detailList.today_type_count}次`
            //             this.messageModal.data.content =
            //                 `尊敬的${this.detailList.driver_name}驾驶员，您今日已触发${this.detailList.event_type_name}事件${this.detailList.today_type_count}次`
            //             break;
            //         default:
            //             break;
            //     }
            // },
            // 拍照指令下发
            // async sendPhotoCommand() {
            //     this.deviceCommit('拍照')
            //     BstandardUtil.init();
            //     this.photo.loading = true
            //     let modal = this.photoModal;
            //     let res = await this.$api.sendMiniStandardCommon({
            //         vehicle_terminal_list: [{
            //             vehicle_id: this.detailList.vehicle_id,
            //             terminal_no: (await this.$store.dispatch(
            //                 'mediaPlatform/getVehicleTerminalInfo', {
            //                     id: this.detailList.vehicle_id,
            //                 })).data,
            //         }],
            //         operate_key: 'RecordMedia',
            //         cmd_record_media: Object.assign(modal.data, {
            //             operate_cmd: modal.data.operate_cmd == 1 ? +modal.data.photoNum : modal.data
            //                 .operate_cmd,
            //         })
            //     })
            //     if (!res || res.status != 200) {
            //         this.photo.loading = false
            //         this.$warning(res.message)
            //         return
            //     }
            //     let wsRes = await BstandardUtil.waitForResponse(res.data);
            //     // modal.loading = false;
            //     if (wsRes && wsRes.task_state === 0) {
            //         this.$message({
            //             showClose: true,
            //             message: '指令下发成功~ 请前往多媒体查询结果！',
            //             type: 'success'
            //         })
            //         this.photo.show = false
            //         this.changeRemark(1)

            //     } else {
            //         this.$message({
            //             showClose: true,
            //             message: `指令下发失败，${wsRes.errormsg || '超时'}`,
            //             type: 'warning'
            //         })
            //     }
            //     this.photo.loading = false
            // },
            // 文本指令下发
            async sendTextCommand() {
                if(!this.messageModal.data.content){
                    this.$warning('请输入下发内容!')
                    return
                }
                this.deviceCommit(8,'TTS语音下发', this.messageModal.data.content)
                this.$message({
                        showClose: true,
                        message: '指令下发成功',
                        type: 'success'
                    })

                // this.changeRemark(3)
                BstandardUtil.init();
                // this.text.loading = true
                let one = this.messageModal.data.urgent === '01' ? 1 : this.messageModal.data.urgent === '10' ? 2 :
                    this.messageModal.data.urgent === '11' ? 3 : 0;
                let two = this.messageModal.data.terminal_monitor ? 4 : 0;
                let three = this.messageModal.data.terminal_tts ? 8 : 0;
                let four = this.messageModal.data.add_show ? 16 : 0;
                let five = this.messageModal.data.center ? 32 : 0;
                this.$api.sendMiniStandardCommon({
                    vehicle_terminal_list: [{
                        vehicle_id: this.detailList.vehicle_id,
                        terminal_no: (await this.$store.dispatch(
                            'mediaPlatform/getVehicleTerminalInfo', {
                                id: this.detailList.vehicle_id,
                            })).data,
                    }],
                    operate_key: 'SendSMS',
                    cmd_sms: {
                        type: one + two + three + four + five,
                        text: this.messageModal.data.content,
                        usage:this.messageModal.data.usage,
                        desc:this.detailList.event_type_name
                    }
                })
                // if (!res || res.status != 200) {
                //     this.text.loading = false
                //     //这里说让改成指令不管下发成功与否都提示下发成功2022-08-21 17：07
                //     // this.$success('指令下发成功')

                //     // this.$warning(res.message)
                //     return
                // }
                //这里说让改成指令不管下发成功与否都提示下发成功2022-08-21 17：07
                // let wsRes = await BstandardUtil.waitForResponse(res.data);
                // if (wsRes && wsRes.task_state === 0) {
                    // this.$message({
                    //     showClose: true,
                    //     message: '指令下发成功',
                    //     type: 'success'
                    // })

                    // this.text.show = false
                    // this.changeRemark(3)
                // } else {
                //     this.$message({
                //         showClose: true,
                //         message: `指令下发失败，${wsRes.errormsg || '超时'}`,
                //         type: 'warning'
                //     })
                // }
                // this.text.loading = false
            },
            async sendMsgCommand() {
                if(!this.sendMessageModal.data.content){
                    this.$warning('请输入下发内容!')
                    return
                }

                // this.deviceCommit(8,'TTS语音下发', this.sendMessageModal.data.content)
                let params = {}
                let reg = /[^\{\}]+(?=\})/g;
                let content = JSON.parse(this.sendMsgModelresult).content
                let resultList = content.match(reg);
                resultList && resultList.length && resultList.forEach(item=>{
                    params[item] = this.detailList[item] || ''
                })
                let sendRes = await this.$api.sendMessage({
                    phone: this.currentRow.phone,
                    params,
                    config_value:this.sendMsgModelresult
                })
                if(sendRes && sendRes.status == 200){
                    this.$message({
                        showClose: true,
                        message: '指令下发成功',
                        type: 'success'
                    })
                    this.deviceCommit(13,'短信',this.sendMessageModal.data.content)

                    this.sendMsg.show = false
                    this.$api.callCenterRec({
                        sendType:'MsgCenter',
                        content:this.sendMessageModal.data.content,
                        vehicleId:this.detailList.vehicle_id,
                        name:this.currentRow.name,
                        phone:this.currentRow.phone,
                        role:this.currentRow.role,
                        data:sendRes.data,
                        desc:this.detailList.event_type_name,
                        time:this.detailList.start_time
                    })
                }else {
                    this.$message({
                        showClose: true,
                        message: sendRes.message || '指令下发失败',
                        type: 'error'
                    })
                }
                
                
            },
            // 监听指令下发
            // async sendPhoneCommand() {
            //     this.deviceCommit('监听')
            //     BstandardUtil.init();
            //     this.phone.loading = true
            //     let res = await this.$api.sendMiniStandardCommon({
            //         vehicle_terminal_list: [{
            //             vehicle_id: this.detailList.vehicle_id,
            //             terminal_no: (await this.$store.dispatch(
            //                 'mediaPlatform/getVehicleTerminalInfo', {
            //                     id: this.detailList.vehicle_id,
            //                 })).data,
            //         }],
            //         operate_key: 'CallPhone',
            //         cmd_call_phone: {
            //             type: this.phoneModal.type,
            //             phone_no: this.phoneModal.tel
            //         }
            //     })
            //     if (!res || res.status != 200) {
            //         this.phone.loading = false
            //         this.$warning(res.message)
            //         return
            //     }
            //     let wsRes = await BstandardUtil.waitForResponse(res.data);
            //     if (wsRes && wsRes.task_state === 0) {
            //         this.$message({
            //             showClose: true,
            //             message: '指令下发成功',
            //             type: 'success'
            //         })
            //         this.phone.show = false
            //         this.changeRemark(2)
            //     } else {
            //         this.$message({
            //             showClose: true,
            //             message: `指令下发失败，${wsRes.errormsg || '超时'}`,
            //             type: 'warning'
            //         })
            //     }
            //     this.phone.loading = false
            // },
            // 改变备注框中的值
            changeRemark(type) {
                if (this.detailList.deal_type === 0) {
                    if (this.remark == null || this.remark.length === 0) {
                        this.remark = `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                    } else {
                        this.remark = this.remark + '|' +
                            `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                    }
                } else {
                    // if (this.remarkAgain == null || this.remarkAgain.length === 0) {
                    //     this.remarkAgain = `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                    // } else {
                    //     this.remarkAgain = this.remarkAgain + '|' +
                    //         `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                    // }
                }
            },
            // 暂时导出图片。听说导出PDF 有问题  【手动滑稽】
            async readyExportPDF() {
                this.loading = true

                const targetDom = document.querySelector('.pony-dialog')
                // 画布的宽高
                let canvas = await html2canvas(targetDom, {
                    allowTaint: false,
                    useCORS: true,
                    height: targetDom.clientHeight,
                    width: targetDom.clientWidth,
                });
                // 这两句话并没有什么卵用
                // canvas.style.width = parseFloat(canvas.style.width) * 0.8 + 'px'
                // canvas.style.height = parseFloat(canvas.style.height) * 0.8 + 'px'
                await this.$nextTick()
                const container = this.$refs['pdfview']
                while (container.hasChildNodes()) {
                    container.removeChild(container.firstChild)
                }
                const dataImg = new Image()
                dataImg.src = canvas.toDataURL('image/png')
                container.appendChild(dataImg)
                const alink = document.createElement("a");
                alink.href = dataImg.src;
                let time = moment().format('MM DD-HH mm')
                alink.download = `${this.detailList.plate_no}-${time}.jpg`;
                alink.click();

                this.loading = false
            },
            copy(innerText) {
                const target = document.createElement('div');
                target.id = 'tempTarget';
                target.style.opacity = '0';
                target.innerText = innerText;
                document.body.appendChild(target);

                try {
                    let range = document.createRange();
                    range.selectNode(target);
                    window.getSelection().removeAllRanges();
                    window.getSelection().addRange(range);
                    document.execCommand('copy');
                    window.getSelection().removeAllRanges();
                    this.$info(this.$ct('messageInfo.0'))
                } catch (e) {}
                target.parentElement.removeChild(target);
            },
            // detaillist数据来源
            async getDetail() {
                try {
                    this.loading = true;
                    let res = await this.$api.getAlarmEventRecDetailV2({
                        event_id: this.params.eventId,
                        vehicle_id: this.params.vehicleId,
                    });
                    if (res.status === 200) {
                        // 干预记录赋值[2022-01-12 14:26:03]
                        this.gridData = res.data.remark_table

                        this.templateList[1].template = res.data.ttl_msg_model
                        this.detailList = Object.assign(res.data, {
                            alarmList: res.data.alam_event_rec_detail,
                            vehicle_id: this.params.vehicleId,
                        });
                        await this.getTTSModelList()
                        
                        // let result = await this.$api.operateUserVehicleFllow({
                        //     operate_type: 2,
                        //     // vehicle_id_list: [this.rowData.vehicleId]
                        // })
                        // this.detailList.collected = false
                        // if(result && result.children && result.children.length){
                        //     let findObj =  result.children.find(item=>item.id == this.rowData.vehicleId)
                        //     if(findObj){
                        //         this.detailList.collected = true
                        //     }
                        // }

                        this.phoneModal.table = this.detailList.phone_table && this.detailList.phone_table.length ? this.detailList.phone_table :[]
                        this.ttsmodelist = this.ttsmodelist.filter(item=>JSON.parse(item.remark).type.includes(null) || JSON.parse(item.remark).type.includes(this.detailList.event_type)).sort((a,b)=>JSON.parse(b.remark).sort - JSON.parse(a.remark).sort)
                        if(this.ttsmodelist.length){
                            this.currentTTS = 1
                            
                        }
                   
                        this.remarkPrevious = res.data.deal_desc;
                        this.changeTTS(this.currentTTS)
                      
                        if (res.data.deal_type != 0) {
                            this.dealType = parseInt(res.data.deal_type);
                            // this.remark = res.data.deal_desc;
                            this.remark = res.data.remark_lite;
                        }
                        if(this.detailList.event_type == 14 || this.detailList.event_type == 15){
                            this.showCanRun = true
                        }
                        this.canRun.switch = false
                        let event_param = this.detailList.event_param ? JSON.parse(this.detailList.event_param) : {}
                        if(event_param.fenseId){
                          this.getFenseDetail([event_param.fenseId])
                        }

                        await this.showCanRunLine()
                        await this.initData();
                        // this.speckText(this.messageModal.data.content)
                    } else {
                        throw Error(res.reason)
                    }
                } catch (e) {
                    this.$error(e)
                } finally {
                    this.loading = false;
                }
            },
            alarmPlay(item) {
                if (!item.is_file) return
                this.$refs['player'].play({
		                alarmId: item.id,
		                timestamp: item.alarm_time,
		                label: item.plate_no,
		                vehicleId: this.detailList.vehicle_id,
		                terminalNo: item.terminal_no,
		                alarmSerial: item.alarm_serial
                },item.media_list)
            },
            async getFenseDetail(list) {
              let $simplemap = this.$refs['map']._map;
              $simplemap.hasLayer(this.featureGroupFense) && $simplemap.removeLayer(this.featureGroupFense);
              let result = await this.$api.queryFenseInfoAssetsV2({ fense_id_list: list });
              if (!result || result.status != 200) {
                this.$error(result.message || "查询出错");
                return;
              }
            this.featureGroupFense = new L.FeatureGroup().addTo($simplemap)
              result.data.page_data.forEach((fense) => {
                if (fense.fense_point_list && fense.fense_point_list.length) {
                  let layerList = generateNewVersionFenseLayer(fense, false);
                  layerList.forEach((item) => {
                    this.featureGroupFense.addLayer(item);
                    
                  });
                }
              });
              
            },
            async initData() {
                const alarmList = this.detailList.alarmList;
                let startTime = this.detailList.start_time;
                let endTime = this.detailList.end_time;
                let res = await this.$api.getCustomGPSRecord({
                    startTime: moment(startTime).valueOf(),
                    endTime: moment(endTime).valueOf(),
                    vehicleId: this.params.vehicleId,
                    value: this.detailList.event_param,
                });
                if (res.status === 200) {
                    alarmList.forEach(item => {
                        item.alarm_time = moment(item.alarm_time).valueOf();
                    })
                    if(res.data){
                        this.gpsList = res.data;
                        this.initMarker(alarmList, res.data);
                    }else {
                        this.clearMap()
                    }
                    
                } else {
                    this.$message({
                        type: 'error',
                        showClose: true,
                        message: this.$ct('messageInfo.1')
                    })
                }

            },
            clearMap(){
                let $simplemap = this.$refs['map']._map;
                $simplemap.hasLayer(this.featureGroup) && $simplemap.removeLayer(this.featureGroup);
                $simplemap.hasLayer(this.markerCluster) && $simplemap.removeLayer(this.markerCluster);
                $simplemap.hasLayer(this.markerStartEnd) && $simplemap.removeLayer(this.markerStartEnd);
                $simplemap.hasLayer(this.featureGroupFense) && $simplemap.removeLayer(this.featureGroupFense);


                
            },
            initMarker(alarmList, gpsList = []) {
                let $simplemap = this.$refs['map']._map;
                $simplemap.hasLayer(this.featureGroup) && $simplemap.removeLayer(this.featureGroup);
                $simplemap.hasLayer(this.markerCluster) && $simplemap.removeLayer(this.markerCluster);
                $simplemap.hasLayer(this.markerStartEnd) && $simplemap.removeLayer(this.markerStartEnd);

                // if (!gpsList.length) return;
                //有时候轨迹的第一个点的时间会晚于报警的第一个点时间，需要特殊处理 ↓
                //最后一个点则会晚于，也要处理
                if (alarmList.length > 0 && gpsList.length) {
                    // if (moment(gpsList[0].gps_time).valueOf() > alarmList[0].alarm_time) {
                    //     gpsList.unshift({
                    //         lat: alarmList[0].lat,
                    //         lng: alarmList[0].lng,
                    //         is_over_speed: false,
                    //     })
                    // }
                    // if (moment(gpsList[gpsList.length - 1].gps_time).valueOf() < alarmList[alarmList.length - 1]
                    //     .alarm_time) {
                    //     gpsList.push({
                    //         lat: alarmList[alarmList.length - 1].lat,
                    //         lng: alarmList[alarmList.length - 1].lng,
                    //         is_over_speed: false,
                    //     })
                    // }
                }
                // 特殊处理 ↑
                //轨迹
                let linePaths = gpsList.map(item => {
                    item.lng = +item.lng.toFixed(4);
                    item.lat = +item.lat.toFixed(4);
                    return {
                        lat: item.lat,
                        lng: item.lng,
                        overSpeed: item.is_over_speed
                    }
                });
                let flag;
                let stepPaths = linePaths.reduce((acc, cur) => {
                    if (cur.overSpeed !== flag) {
                        flag = cur.overSpeed;
                        acc[acc.length - 1] && acc[acc.length - 1].path.push([cur.lat, cur.lng])
                        acc.push({
                            overSpeed: flag,
                            path: []
                        });
                    }
                    acc[acc.length - 1].path.push([cur.lat, cur.lng])
                    return acc;
                }, []);
                let paths = [];
                let lineColor = '#3388FF'
                if(this.detailList.event_type==14||this.detailList.event_type==15){
                    lineColor = '#f5675d'
                }
                stepPaths.forEach(step => {
                    paths.push(L.polyline(step.path, {
                        stroke: true,
                        color: step.overSpeed ? '#f5675d' : lineColor,
                        weight: 4
                    }))
                })
                this.featureGroup = L.featureGroup(paths, {
                    snakingPause: 100
                }).addTo($simplemap);

                //报警点↓
                let markerArray = []
                //经纬度转换
                alarmList.forEach(item => {
                    if(!item.lng || !item.lat)return
                    item.lng = +item.lng.toFixed(4);
                    item.lat = +item.lat.toFixed(4);
                    let icon = getAlarmMapIcon(item.alarm_type)
                    let markers = L.marker([item.lat, item.lng], {
                        icon: icon,
                        zIndexOffset: 4000
                    });
                    markerArray.push(markers)
                });
                if(linePaths.length){
                  this.markerStartEnd = new L.FeatureGroup().addTo($simplemap)
                  let startMarker = L.marker(linePaths[0], {
                      icon: startIcon,
                      // zIndexOffset: 4000,
                      rotationAngle: 0
                  })
                  let endMarker = L.marker(linePaths[linePaths.length - 1], {
                      icon: endIcon,
                      // zIndexOffset: 4000,
                      rotationAngle: 0
                  })
                  this.markerStartEnd.addLayer(startMarker)
                  this.markerStartEnd.addLayer(endMarker)

                }

                // //开始点
                // linePaths.length && markerArray.push(L.marker(linePaths[0], {
                //     icon: startIcon,
                //     zIndexOffset: 4000,
                //     rotationAngle: 0
                // }))
                // //结束点
                // linePaths.length && markerArray.push(L.marker(linePaths[linePaths.length - 1], {
                //     icon: endIcon,
                //     zIndexOffset: 4000,
                //     rotationAngle: 0
                // }))
                  this.markerCluster = new L.MarkerClusterGroup({
                      showCoverageOnHover: true,
                      maxClusterRadius: 13,
                  }).addTo($simplemap);
                this.markerCluster.addLayers(markerArray);

                this.$nextTick(() => {
                    try{
                        if(paths.length){
                            $simplemap.fitBounds(this.featureGroup.getBounds(), {
                                paddingBottomRight: this.paddingBottomRight
                            })
                        }else {
                            if(!this.markerCluster)return
                            $simplemap.fitBounds(this.markerCluster.getBounds(), {
                            paddingBottomRight: this.paddingBottomRight
                        })
                        }
                    }catch(e){

                    }
                    
                    
                })
            },
            viewMarker(item) {
                switch (item) {
                    case 'start':
                        item = this.gpsList[0];
                        break;
                    case 'end':
                        item = this.gpsList[this.gpsList.length - 1];
                        break;
                    default:
                }
                this.$refs['map']._map.setView([item.lat, item.lng], 19)
            },
            linkToMonitor(){
                let needList = {
                    vehicleId: this.params.vehicleId,
                }
                this.$router.push({
                    path: '/home/<USER>',
                    query: needList
                })
            },
            linkToPlayBack() {
                let startTime = this.detailList.start_time;
                let endTime = this.detailList.end_time;
                if (this.detailList.start_time == this.detailList.end_time) {
                    let time = Date.parse(this.detailList.start_time);
                    startTime = this.TimeFormat(+time - 15 * 60 * 1000)
                    endTime = this.TimeFormat(+time + 1 * 60 * 1000)
                }
                let needList = {
                    vehicleId: this.params.vehicleId,
                    startTime: startTime,
                    endTime: (null == endTime) ? moment().format('YYYY-MM-DD HH:mm:ss') : endTime,
                    checkAllAlarm:1
                }
                this.$router.push({
                    path: '/home/<USER>',
                    query: needList
                })
            },
            async showCanRunLine() {
                if (this.canRun._layer) {
                    this.canRun._layer.clearLayers()
                    this.canRun._layer = null
                    return
                }

                if (!this.canRun.switch) return

                let $simplemap = this.$refs['map']._map;
                this.canRun._layer = new L.FeatureGroup().addTo($simplemap)
                let result = await this.$api.queryAccessRoutes({
                    vehicle_id: this.params.vehicleId,
                    start_time: this.detailList.start_time,
                    jurisdiction: this.detailList.fenseId
                })
                if (!result || result.status != 200) {
                    this.$warning(result.message || '查询失败')
                    return
                }
                if (!result.data.length) {
                    this.$warning('未查询围栏信息')
                    return
                }

                result.data.forEach(item => {
                    let lnglat = item.fense_point_list.map(it=>it.split(',').reverse())
                    let currentList = generatePonylineFenseLayer(lnglat, {
                        type: 2,
                        option: {
                            weight: 6,
                            color: '#7BD012',
                            opacity: 0.3
                        }
                    })
                    if (currentList.length) {
                        this.canRun._layer.addLayer(currentList[0])
                    }
                })
            },
            setMessageTemplate(val) {
                this.remark = (this.templateList.find(item => item.value === val)).template;
            },
            //对设备操作的时候调用
            async deviceCommit(type,deviceDealType = '', content = '') {
                let deal_desc = (this.remarkPrevious == null || this.remarkPrevious == '' ? '' : this.remarkPrevious + ';') + `DEAL_BY|DEAL_TIME|${deviceDealType}|${content}`
                try {
                    let arr = {
                        event_id_list: [this.params.eventId],
                        deal_type: type,
                        // 不用remark直接全部拼接到描述中
                        deal_desc,
                        // attachment: this.fileList
                    }

                    await this.$api.handleAlarmEventByEventIdV2(arr)
                    this.getDetail();
                    //暂时去掉处理过后的列表刷新
                    // this.$emit('changeDeal')
                    this.resultP.resolve({
                        dealType: type,
                        deal_desc: '',
                        isInterfered:true
                    })

                } finally {}
            },

            async followEvent(){
                this.$emit('lockRow',this.rowData,0)
            },
            // 点击处理
            async commit(dealType) {
                try {
                    this.loading = true;
                    let deal_desc = (!this.remarkPrevious ?'': this.remarkPrevious) + (!this.remark ? '' : `${this.remarkPrevious?';':''}DEAL_BY|DEAL_TIME|备注|${this.remark}`) + (!this.remarkAgain ?
                            '' : `${this.remarkPrevious || this.remark ?';':''}DEAL_BY|DEAL_TIME|备注|${this.remarkAgain}`)
                    let arr = {
                        event_id_list: [this.params.eventId],
                        deal_type: dealType,
                        // 不用remark直接全部拼接到描述中
                        // deal_desc: (this.remarkPrevious == null || this.remarkPrevious == '' ? '' : this.remarkPrevious) + (this.remark != null && this.remark.indexOf('|') == -1 && this.remark.indexOf(';') == -1 ? (this.remarkPrevious == null || this.remarkPrevious == '' ? '' : ';') + `DEAL_BY|DEAL_TIME|备注|${this.remark}` : '') + (this.remarkAgain == null || this.remarkAgain == '' ? '' : `;DEAL_BY|DEAL_TIME|备注|${this.remarkAgain}`),
                        // deal_desc,
                        // attachment: this.fileList
                    }
                    let result = await this.$api.handleAlarmEventByEventIdV2(arr)
                    if (result.status !== 200) {
                        this.$error(result.message)
                        return;
                    }
                    this.$success('处理成功！')
                    this.getDetail();


                    // this.detailList.deal_type = this.dealType;
                    if (this.autoClose) {
                        // this.show = false;
                        this.phone.show = false
                    }
                    //暂时去掉处理过后的列表刷新
                    // this.$emit('changeDeal')

                    // this.resultP.resolve({
                    //     dealType: dealType,
                    //     deal_desc: this.remark,
                    //     isInterfered:true
                    // })
                } finally {
                    this.loading = false;
                }
            },
            closeModal() {
                this.show = false;
                this.phone.show = false
                this.resultP.reject();
            },
            changeBlue(alarmId) {
                if (!alarmId) return
                const row = this.detailList.alarmList.find(alarm => alarm.id === alarmId)
                if (row) {
                    row.is_file = 2
                }
            },
            clear() {
                this.resultP.reject && this.resultP.reject();
                // this.resultP.resolve && this.resultP.resolve();
                this.remark = '';
                this.dealType = 0;
                this.detailList = {};
                this.stopVideo()
            },
            async stopVideo() {
                if (this.show && this.$refs['video']) {
                    this.$refs['video'].stop()
                }
            },
            setChn(chn) {
                this.chn = chn;
                this.$refs['video'].reload()
            },
            onRemainingTime(isStop, time) {
                this.remainingTime = isStop ? time : 0;
            },
            // 事件id 车辆id 自动播放实时视频 处理后是否自动关闭  fujian:处理模块中是否有附件，历史有，当日没有
            async showModal(eventId, vehicleId, autoPlay = false, autoClose = true, fujian = true,rowData) {
                this.rowData = rowData
                this.remarkAgain = null
                this.fujian = fujian
                this.autoPlay = autoPlay;
                this.autoClose = autoClose
                this.currentTTS = 0
                this.clear();
                this.loading = true

                let result = await this.$api.getChannelNoByVehicleId({
                        vehicle_id: vehicleId + ":V2"
                })
                this.chn = result.data.channelDms?result.data.channelDms:2
                this.channelNo = result.data.channel_count;
                this.channelNoList = []
                this.channelNoNameObj =  {}

                result.data.channel_valid_v2.forEach((item, index) => {
                        if (item) {
                                this.channelNoList.push(index + 1)
                        }
                })
                result.data.channel_valid_name && result.data.channel_valid_name.length && result.data.channel_valid_name.forEach((item, index) => {
										this.channelNoNameObj[item.no] = item.name
								})
                // let channelNo = (await this.$store.dispatch('mediaPlatform/getVehicleChannelNo', {
                //     id: vehicleId
                // })).data;
                // this.channelNo = channelNo;
                this.isVideoSupport = result.data.isVideoSupport == 1
                this.params.inputType = result.data.inputType
                // let channelNo = (await this.$store.dispatch('mediaPlatform/getVehicleChannelNo', {
                //     id: vehicleId
                // })).data;
                // this.channelNo = channelNo;
                if(!this.isVideoSupport){
                    this.params.terminalCode = '';
                }else {
                    try {
                        let vehicleInfo = await this.$store.dispatch('vehicle/getVehicleInfo', {
                            value: vehicleId
                        });
                        this.params.terminalCode = vehicleInfo && vehicleInfo.code ?  vehicleInfo.code : '';
                    } catch (e) {
                        this.params.terminalCode = '';
                    }
                }
                Object.assign(this.params, {
                    eventId,
                    vehicleId,
                })
                this.getDetail();
                this.show = true;
                await this.$nextTick();
                return new Promise((resolve, reject) => {
                    this.resultP.resolve = resolve;
                    this.resultP.reject = reject;
                })
            },
            async getSendMsgModelList(){
                let result = await this.$api.getEventRulerConfig({
                    version:'CLOUD_SMS'
                })
                if (!result || result.status != 200 || !result.data || !result.data.length) return
                this.sendMsgModelist = result.data
                let defaultMsg = this.sendMsgModelist.find(item=>item.sms_name == '默认消息')
                if(defaultMsg){
                    this.defaultMsg = defaultMsg.config_value
                }
            }
        },
        async mounted(){
            let userTypeList = (await this.$api.queryAlarmEventRuleConfiguration({
                userId: -1
            })).data.values;
            let typeList = await this.$store.dispatch('dictionary/getFormatListByCode', 'danger_event_type');
            typeList = typeList.filter(item => {
                return userTypeList.includes(item.value);
            })
            this.typeList = typeList
            this.vehicleLevelList = (await this.$api.getCommonListByKey({
                key:'vehicle_warn_color'
            })).data;
            document.onkeydown = (e)=>{
                if(!this.show)return
                if(e.keyCode == 39 || e.keyCode == 40){
                    this.changeRow(0)//下一条
                }
                if(e.keyCode == 37 || e.keyCode == 38){
                    this.changeRow(1)//上一条
                }
            }
        },
        async created() {
            const res = await this.$api.getCommonListByKey({
                key: "config_event_deal_type"
            })
            if (res.status !== 200) return this.$error('获取报警事件处理类型失败！')
            this.dealTypeMap = res.data
            // 文本下发TTS模板
            // this.getTTSModelList()
            this.getSendMsgModelList()
        },
        beforeDestroy(){
            if(this.$refs['popover']){
                this.$refs['popover'].doClose()
            }
        }
    }
</script>

<style scoped lang="scss">
    .alarm-detail-v2 {
        .risk-level {
                display: inline-block;
                margin-left: 10px;
                width: 80px;
                //padding: 3px 25px;
                border-radius: 5px;
                font-size: 12px;
                color: #fff;
                height: 23px;
                line-height: 23px;
                text-align: center;
        }
        .high-level {
            background-color: var(--color-danger);
        }
        .middle-level {
            background-color: var(--color-warning);
        }
        .low-level {
            background-color: var(--color-primary);
        }
        .event-change {
            display: inline-block;
            font-size: 13px;
            margin-left: 3px;
            transform: rotate(-90deg);

        }
        .left-wrap {
            height: 100%;
            // display: flex;
            flex-direction: column;
            overflow: auto;
            .alarm-title {
                width: 100% !important;
                >i {
                    display: inline-block;
                    width: 2px;
                    height: 12px;
                    margin: 0px 5px;
                    vertical-align: -1px;
                    background-color: var(--color-primary);
                }
                .hasHandle {
                    float: right;
                    color: #1a855a;
                    background-color: rgba(43, 173, 122,.2);
                    padding: 3px 10px;
                    border-radius: 5px;
                    vertical-align: -3px;
                    margin-right: 3px;
                    i {
                        font-style: normal;
                    }
                    // .gang {
                    //     display: inline-block;
                    //     width: 2px;
                    //     height: 15px;
                    //     margin: 0px 2px;
                    //     vertical-align: -2px;
                    //     background-color: #1cc07e;
                    // }
                }

                // margin-bottom: 5px;
            }
            .event-process {
                float: left;
                // width: 45%;
                height: 100%;
                border-right: solid 1px var(--background-color-lighter);
                .event-process-list {
                    width: 250px;
                    height: 100%;
                    float: left;

                    overflow: hidden;
                    border-right: solid 1px var(--background-color-lighter);
                }
                .alarm-attach {
                    float: left;
                    width: 130px;
                    height: 100%;
                    ul {
                        width: 100%;
                        height: 100%;
                        overflow-y: auto;
                        overflow-x: hidden;

                        li {
                            width: 100%;
                            height: 110px;
                            margin-bottom: 5px;
                            border-bottom: 1px solid var(--background-color-lighter);
                        }
                    }
                }
            }
            .event-detail {
                float: left;
                width: 461px;
                height: 100%;
                padding: 10px 0;
                position: relative;
                .alarm-info-phone {
                    padding: 10px;
                    overflow: hidden;
                    li {
                        float: left;
                        width: 50%;
                        margin-bottom: 10px;
                        span:first-child {
                            display: inline-block;
                            color: var(--color-text-secondary);
                            font-size: 12px;
                            min-width: 50px;
                            text-align: left;
                            text-align-last: justify;
                            text-align: justify;
                        }
                        span:nth-child(2) {
                            display: inline-block;
                            color: var(--color-text-regular);
                            font-size: 12px;
                            // text-align-last: justify;
                            // text-align: justify;
                            width: 58px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            margin: 0 3px 0 8px;
                            vertical-align: -3px;
                            // text-align: left;
                        }
                        span:last-child {
                            color: var(--color-text-regular);
                            font-size: 12px;
                            // margin-left: 6px;
                            i {
                                color: var(--color-primary);
                                margin-left: 3px;
                                cursor: pointer;
                            }
                            i:last-child {
                                color: var(--color-success);
                                margin-left: 5px;
                            }
                        }
                    }
                }
                .alarm-info-safe {
                    padding-left: 10px;
                    .button-item {
                        span {
                            display: inline-block;
                            padding: 3px 8px;
                            color: var(--color-text-secondary);
                            border: 1px solid var(--border-color-base);
                            border-radius: 5px;
                            margin-right: 3.8px;
                            margin-bottom: 5px;
                            max-width: 56.5px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            cursor: pointer;
                        }
                        .active {
                            background-color: rgba(42, 128, 224,.1);
                            color: var(--color-primary);
                            border: 1px solid var(--color-primary);
                        }

                    }

                }
                .commit-button {
                    text-align: right;
                    position: absolute;
                    bottom: 10px;
                    right: 1px;
                    .remain {
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: red;
                        position: absolute;
                        top: -5px;
                        right: -5px;
                    }
                }
            }
            .alarm-info-list {
                display: flex;
                flex-wrap: wrap;
                padding: 0 5px 10px 15px;
                border-bottom: solid 1px var(--background-color-lighter);
                flex-shrink: 0;

                .info-item {
                    padding: 5px 0;
                    width: 60%;

                    &.long {
                        width: 100%;
                    }

                    &.short {
                        width: 40%
                    }

                    span:first-child {
                        display: inline-block;
                        color: var(--color-text-regular);
                        font-size: 12px;
                        min-width: 60px;
                        text-align: right;
                    }

                    span:last-child {
                        color: var(--color-text-secondary);
                        font-size: 12px;
                        margin: 0px 5px;
                    }
                }
            }

            .alarm-card-list {
                width: 100%;
                height: 100%;
                overflow: auto;
                float: left;
                flex-grow: 1;
                padding: 10px;



                .card-item {
                    height: 60px;
                    width: 100%;
                    margin-top: 5px;
                    // padding: 10px 10px 0px 0px;
                    cursor: pointer;

                    &:last-child {
                        .tip-item:after {
                            display: none;
                        }
                    }

                    .tip-item {
                        height: 90px;
                        width: 18px;
                        margin: auto;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        transform: translateY(7px);

                        &:before {
                            content: ' ';
                            display: block;
                            width: 8px;
                            height: 8px;
                            border: 3px solid var(--color-primary);
                            border-radius: 50%;
                        }

                        &:after {
                            content: ' ';
                            display: block;
                            flex-grow: 1;
                            width: 3px;
                            background-color: var(--color-primary-o-10);
                        }
                    }

                    .card-main {
                        width: 100%;
                        float: right;
                        height: 58px;
                        border: solid 1px var(--background-color-lighter);
                        border-radius: 4px;
                        padding: 0px 10px;
                        // margin-top: -20px;

                        .top-line {
                            height: 50%;
                            width: 100%;
                            line-height: 35px;
                            display: flex;

                            span {
                                color: var(--color-text-regular);
                            }
                        }

                        .bottom-line {
                            height: 50%;
                            width: 100%;
                            line-height: 35px;
                            color: var(--color-text-secondary);
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
		                        display: flex;
		                        justify-content: space-between;
                                .alarm-time {
                                    width: 100%;
                                    text-align: right;
                                }
                               
                        }

                        li {
                            &:first-child {
		                            display: flex;
		                            align-items: center;
                                i {
                                    margin: 6px 0;
                                    float: left;
                                }
                            }

                            &:last-child {
                                float: right;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                i {
                                    float: right;
                                    color: var(--color-text-disabled);
                                    cursor: not-allowed;
                                    font-size: 16px;

                                    &.is_video {
                                        cursor: pointer;
                                        color: var(--color-text-primary);
                                    }

                                    &.watched {
                                        cursor: pointer;
                                        color: var(--color-primary);
                                    }
                                }
                            }

                            span {
                                margin-left: 10px;

                                &.alarm-name {
                                    margin-left: 8px;
                                    max-width: 188px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }

                            }
                        }
                    }

                    .card-corner {
                        position: relative;
                        left: 55px;
                        top: 3px;
                        height: 0px;
                        width: 0px;
                        border-top: 10px solid transparent;
                        border-right: 11px solid var(--background-color-lighter);
                        border-bottom: 11px solid transparent;

                        &:after {
                            content: '';
                            position: absolute;
                            top: -9px;
                            left: 1px;
                            border-top: 9px solid transparent;
                            border-right: 10px solid var(--background-color-modal);
                            border-bottom: 10px solid transparent;
                        }
                    }

                    .index-tip {
                        width: 50px;
                        height: 100%;
                        float: left;
                    }

                    &:last-child .tip-item {
                        padding-bottom: 15px;
                    }

                    &:hover {
                        .card-main {
                            background: var(--background-color-light);
                            transition: background-color .3s;
                        }

                        .card-corner:after {
                            border-right-color: var(--background-color-light);
                            transition: border-right-color .3s;
                        }
                    }
                }
            }

            .deal-box {
                border-top: solid 1px var(--background-color-lighter);
                flex-shrink: 0;
                // height: 152px;
                padding: 5px 10px;
                display: flex;
                flex-wrap: wrap;
                align-items: center;

                .top {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    i {
                        font-size: 20px;

                        &:hover {
                            cursor: pointer;
                        }
                    }
                }

                /deep/ .image-uploader .uploader {
                    .no-img {
                        padding-top: 0;

                        &>span {
                            display: none;
                        }

                    }

                    .clear-btn,
                    .preview-btn {
                        width: 12px;
                        height: 12px;
                        top: 2px;
                        right: 2px;
                        font-size: 12px;
                        line-height: 12px;

                        .pony-iconv2 {
                            font-size: 12px;

                        }
                    }

                    .preview-btn {
                        left: 2px;
                    }

                }
            }
        }


        .right-wrap {
            display: flex;
            flex-direction: column;
            height: 100%;

            .video-wrap {
                position: relative;
                height: 230px;

                .video-tip {
                    width: 140px;
                    // width: 100%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    padding-top: 5px;
                    padding-left: 5px;
                    display: flex;
                    z-index: 1;
                    align-items: center;

                    /deep/ .el-dropdown {
                        position: absolute;
                        top: 5px;
                        right: 0;
                    }

                    .video-tip-link {
                        line-height: 24px;
                        padding: 0 5px 0 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        margin-right: 5px;
                    }

                    .title {
                        width: 100%;
                        // display: flex;
                        justify-content: space-between;
                        padding: 0 0px 0 9px;

                        // width: 63px;
                        // width: 73px !important;
                        .remaining-time {
                            float: left;
                            width: 50px;
                        }
                    }

                }
            }

            .map-wrap {
                flex-grow: 1;
                position: relative;

                .map {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 100%;
                    width: 100%;
                    z-index: 0
                }
                .monitor-btn {
                    position: absolute;
                    top: 5px;
                    right: 40px;
                    width: 30px;
                    height: 30px;
                    z-index: 20;
                    border-radius: 6px;
                    line-height: 30px;
                    text-align: center;
                    background: var(--background-color-base);
                    cursor: pointer;
                    i {
                        font-size: 18px;
                        font-weight: 600;
                        color: #13c7e1;
                    }
                }
                .play-black-btn {
                    position: absolute;
                    top: 5px;
                    right: 8px;
                    width: 30px;
                    height: 30px;
                    z-index: 20;
                    border-radius: 6px;
                    background: var(--background-color-base);
                    cursor: pointer;

                    &:after {
                        content: ' ';
                        display: inline-block;
                        width: 30px;
                        height: 30px;
                        background: url("../../../../../../static/img/map/track_playback.png") no-repeat center;
                        cursor: pointer;
                    }
                }

                .show-can-run {
                    position: absolute;
                    top: 35px;
                    right: 5px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 30px;
                }
            }
        }

        .pdfViewBackground {
            position: relative;
            height: max-content;
            width: max-content;
            display: none;
        }

        .photo {
            .el-form {
                .el-form-item {
                    width: 95%;
                }

                .el-select,
                .el-input-number {
                    width: 100%;
                }

                .pony-iconv2 {
                    margin-left: 5px;
                }
            }
        }

        .text {
            .el-form {
                .el-form-item {
                    width: 95%;
                }

                .el-checkbox {
                    margin-right: 20px;
                }

                .el-select,
                .el-input-number {
                    width: 100%;
                }
            }
        }
    }
    .eventList {
        overflow: auto;
        li {
            width: calc(25% - 9px);
            float: left;
            background-color: var(--background-color-base);
            height: 30px;
            line-height: 30px;
            border-radius: 5px;
            padding: 0 12px;
            border: 1px solid var(--border-color-base);
            margin-bottom: 12px;
            margin-right: 12px;
            cursor: pointer;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            i {
                vertical-align: -5px;
            }
        }
        .active {
            background-color: rgba(42, 128, 224, .1);
            border: 1px solid var(--color-primary);
            span {
                color: var(--color-primary);
            }

        }
        .disabled {
            // background-color: rgba(218, 219, 224, .5);
            background-color: var(--border-color-disabled);
            cursor: no-drop;
            span {
                color: var(--color-text-disabled);
            }

            // span {
            //     color: var(--background-color-lighter);
            // }
        }
        li:nth-child(4n) {
            margin-right: 0px;
        }
    }
    .text-button{
        color: var(--color-primary);
        cursor: pointer;
        font-style: normal;
        margin-right: 4px;
        // margin-left: 10px;
    }
    .table {
        /deep/.el-table__body-wrapper {
            height: 100% !important;
        }
    }

    .speed {
		    width: 23px;
		    height: 23px;
		    display: inline-block;
		    line-height: 17px;
		    text-align: center;
		    background-color: #fff;
		    color: #0a1307;
		    border-radius: 50%;
		    border: 3px red solid;
		    font-weight: 800;
    }
</style>
