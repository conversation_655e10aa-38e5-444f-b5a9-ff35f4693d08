<template>
  <Layout :has-color="true" class="fenserelation" :contentLoading="table.loading">
    <template slot="query">
      <div class="query-item">
        <el-input placeholder="请输入围栏名称" v-model="queryList.name"> </el-input>
      </div>
      <div class="query-item">
        <span style="width: 100px">筛选规则:</span>

        <SelectTreeInput
          v-model="selectValue"
          type="fenceRule"
          :check-mode="true"
          :condition="treeCondition"
          placeholder="请选择规则"
          checkModeText="个"
          title="请选择规则"
          :showIcon="false"
          :extraKeys="['type']"
        ></SelectTreeInput>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="searchBndinList">查询</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="openFenceRule(null, 0)">新增</el-button>
      </div>
      <div class="query-item">
        <el-button
          type="primary"
          @click="handleBatchStart"
          :disabled="!selectedFenceList.length"
        >批量启用</el-button>
      </div>
      <div class="query-item">
        <el-button
          type="primary"
          @click="handleBatchStop"
          :disabled="!selectedFenceList.length"
        >批量暂停</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="handleBatchDelete" :disabled="!selectedFenceList.length">批量删除</el-button>
      </div>
      <div class="break-item"></div>
      <el-pagination
        small
        background
        :current-page.sync="table.page"
        :page-size="table.size"
        layout="prev, pager, next, total"
        :total="table.data.length"
      >
      </el-pagination>
    </template>
    <template slot="content">
      <el-table
        class="el-table--ellipsis el-table--radius"
        border
        stripe
        highlight-current-row
        size="mini"
        :data="formatFenceList"
        height="100%"
        style="width: 100%"
        ref="totalTable"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"></el-table-column>
        <el-table-column prop="address" label="操作" width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              :title="scope.row.status == -1 ? '启用' : '暂停'"
              size="small"
              @click="handleStopEvent(scope.row)"
            >
              <i :class="['pony-iconv2', scope.row.status == -1 ? 'pony-bofang' : 'pony-zanting']"></i>
            </el-button>
            <el-button type="text" title="修改" size="small" @click.native="openFenceRule(scope.row, 1)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>

            <el-button type="text" title="删除" size="small" @click="handleRemoveEvent(scope.row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="rule_name" label="规则名称" min-width="80"></el-table-column>
        <el-table-column prop="rule_type" label="规则类型" min-width="80"></el-table-column>
        <!-- <el-table-column prop="mixed" label="是否关联围栏" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.mixed ? "是" : "否" }}
          </template>
        </el-table-column> -->
        <el-table-column prop="fense_names" label="围栏名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fense_num" label="围栏绑定" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.fense_num + "个" }}
          </template>
        </el-table-column>
        <el-table-column prop="vehicle_num" label="车辆绑定" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.vehicle_num + "辆" }}
          </template>
        </el-table-column>
        <el-table-column prop="update_by" label="操作人" min-width="80"></el-table-column>
        <el-table-column prop="update_time" label="操作时间" min-width="120"></el-table-column>
      </el-table>
    </template>

    <PonyDialog
      v-model="modalRule.show"
      hasMask
      width="850"
      :title="modalRule.operate_type === 0 ? '新增规则配置' : '修改规则配置'"
      @confirm="handleFenceRuleChange"
      @close="closeDialog"
    >
      <el-row :gutter="24" style="width: 100%">
        <el-col :span="8">
          <div class="tree-container">
            <div class="tree-header">规则配置</div>
            <div class="tree-content" :class="{
              // 'no-events': modalRule.operate_type === 1,
              'scroll-only': modalRule.operate_type === 1  /* 添加新的操作类型 */
            }">
              <ElementTree
                @check="ruleTreeClickHandle"
                node-key="id"
                :showIcon="false"
                style="height: 100%"
                type="fenceRule"
                ref="ruleTree"
                :checkMode="true"
                :needLoading="true"
              >
              </ElementTree>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="tree-container">
            <div class="tree-header">围栏配置</div>
            <div class="tree-content">
              <ElementTree
                @check="selectFenceNodes"
                :checkMode="true"
                style="height: 100%"
                type="fenseTreeNew"
                ref="fenseTree"
                :needLoading="true"
              >
              </ElementTree>
            </div>
            <!-- <div class="relation-switch-container">
              <span>关联围栏</span>
              <el-switch v-model="modalRule.mixed"></el-switch>
            </div> -->
          </div>
        </el-col>
        <el-col :span="8">
          <div class="tree-container">
            <div class="tree-header">车辆配置</div>
            <div class="tree-content">

              <ElementTree
                style="height: 100%"
                type="vehicle"
                ref="vehicleTree"
                :checkMode="true"
                @check="selectVehicleNodes"
                @mounted="generatetreeObj"
                :needLoading="true"
              >
              </ElementTree>
            </div>
          </div>
        </el-col>
      </el-row>
    </PonyDialog>
  </Layout>
</template>

<script>
import PinyinMatch from "pinyin-match";
import SelectTreeInput from "@/components/common/SelectTreeInput";

export default {
  name: "FenseRelation",
  components: { SelectTreeInput },
  data() {
    return {
      queryList: {
        rule_id_list: [],
        version: 3,
        name: "",
      },
      modalRule: {
        show: false,
        operate_type: 0, // 0:新增 1:修改 -1:暂停/启用 -2:删除
        rule_id_list: [],
        fense_id_list: [],
        vehicle_id_list: [],
        mixed: false,
        bind_id: "",
      },
      table: {
        size: 30,
        page: 1,
        data: [],
        loading: false,
      },

      vehiclebasicNode: null,
      fence_id: "",
      selectValue: [],
      icleIdList: [],
      treeObj: null,
      selectedFenceList: [],
      vehicleTreeInitialized: false,
      vehicleTreeDataLoaded: false, // 标记车辆树数据是否已加载
      vehicleTreeLoading: false, // 表示车辆树是否正在加载
      expandedNodes: {}, // 用于缓存已展开的节点，避免重复展开
      treeScrollDebounceTimer: null, // 用于滚动防抖
      dialogOpenCount: 0, // 记录对话框打开次数，用于优化内存
    };
  },

  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },

    formatFenceList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  watch: {
    selectValue(val) {
      this.queryList.rule_id_list = val.filter((item) => item.type == 2).map((item) => item.value);
    },
  },
  async mounted() {
    await this.searchBndinList();

  },

  methods: {
    generatetreeObj(obj) {
      this.treeObj = obj;
      this.vehicleTreeInitialized = true;
      this.vehicleTreeDataLoaded = true;
    },

    // 滚动到选中节点的方法，使用平滑滚动效果
    async scrollToSelectedNode(treeRef, nodeId) {
      if (!treeRef || !nodeId) {
        return;
      }

      try {
        // 获取树组件实例
        const $tree = treeRef.$refs.tree;
        if (!$tree) {
          return;
        }

        // 设置当前键值，使节点成为当前节点
        $tree.setCurrentKey(nodeId);

        // 等待展开动画完成，减少等待时间
        await this.$utils.sleep(300);

        // 查找节点元素
        const el = treeRef.$el.querySelector(`.el-tree-node[data-key="${nodeId}"]`) ||
                  treeRef.$el.querySelector('.is-current');

        if (el) {
          // 获取树容器
          const treeContainer = treeRef.$el.querySelector('.tree-content');
          if (treeContainer) {
            // 计算滚动位置
            const containerRect = treeContainer.getBoundingClientRect();
            const elRect = el.getBoundingClientRect();
            const targetScrollTop = elRect.top + treeContainer.scrollTop - containerRect.top - (containerRect.height / 2) + (elRect.height / 2);

            // 平滑滚动到目标位置
            this.smoothScrollTo(treeContainer, treeContainer.scrollTop, targetScrollTop, 300);

            // 添加高亮效果
            setTimeout(() => {
              el.classList.add('highlight-node');
              setTimeout(() => {
                el.classList.remove('highlight-node');
              }, 1500);
            }, 300);
          } else {
            // 如果找不到容器，则使用原生方法
            el.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }
      } catch (err) {
        console.error('滚动到节点时出错:', err);
      }
    },

    // 平滑滚动方法
    smoothScrollTo(element, start, end, duration) {
      if (!element) return;

      const startTime = performance.now();

      // 使用requestAnimationFrame实现平滑滚动
      const animateScroll = (currentTime) => {
        const elapsedTime = currentTime - startTime;

        if (elapsedTime >= duration) {
          element.scrollTop = end;
          return;
        }

        // 使用easeInOutQuad缓动函数计算滚动位置
        const progress = elapsedTime / duration;
        const easeProgress = progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;

        element.scrollTop = start + (end - start) * easeProgress;
        requestAnimationFrame(animateScroll);
      };

      requestAnimationFrame(animateScroll);
    },

    ruleTreeClickHandle(current, { checkedNodes }) {
      if (this.modalRule.operate_type === 1) {
        this.$refs.ruleTree.$refs.tree.setCheckedKeys([this.modalRule.rule_id_list[0]]);
        return;
      }
      let list = checkedNodes.filter((item) => item.type == 2)
      if (!list.length) {
        this.$warning("请选择规则")
        return;
      }
      this.modalRule.rule_id_list = list.map((item) => item.id);
    },
    treeCondition(node) {
      if (node.type == 2) {
        return true;
      }
      return false;
    },
    handleSelectionChange(val) {
      this.selectedFenceList = val;
    },
    closeDialog() {
      this.modalRule.show = false;
      Object.assign(this.modalRule, {
        operate_type: 0,
        rule_id_list: [],
        fense_id_list: [],
        vehicle_id_list: [],
        mixed: false,
        bind_id: ""
      });

      // 清理树组件的状态，减少内存占用
      this.cleanupTrees();

      // 清除已展开节点的缓存
      this.expandedNodes = {};
    },

    // 清理树组件状态，减少内存占用
    cleanupTrees() {
      try {
        // 清理规则树
        if (this.$refs.ruleTree && this.$refs.ruleTree.$refs.tree) {
          this.$refs.ruleTree.$refs.tree.setCheckedKeys([]);
          this.$refs.ruleTree.$refs.tree.setCurrentKey(null);
        }

        // 清理围栏树
        if (this.$refs.fenseTree && this.$refs.fenseTree.$refs.tree) {
          this.$refs.fenseTree.$refs.tree.setCheckedKeys([]);
          this.$refs.fenseTree.$refs.tree.setCurrentKey(null);
        }

        // 清理车辆树
        if (this.$refs.vehicleTree && this.$refs.vehicleTree.$refs.tree) {
          this.$refs.vehicleTree.$refs.tree.setCheckedKeys([]);
          this.$refs.vehicleTree.$refs.tree.setCurrentKey(null);
        }

        // 强制进行垃圾回收
        setTimeout(() => {
          if (window.gc) {
            window.gc();
          }
        }, 100);
      } catch (err) {
        console.error('清理树组件状态时出错:', err);
      }
    },

    async openFenceRule(row, operate_type) {
      // 显示加载状态
      this.vehicleTreeLoading = true;
      this.modalRule.show = true;
      this.modalRule.operate_type = operate_type;
      Object.assign(this.modalRule, {
        rule_id_list: [],
        fense_id_list: [],
        vehicle_id_list: [],
        mixed: false,
        bind_id: "",
      });

      // 记录对话框打开次数
      this.dialogOpenCount++;

      // 每有5次打开对话框，强制清理内存
      if (this.dialogOpenCount % 5 === 0) {
        this.optimizeMemory();
      }

      // 先打开对话框，再异步加载树数据
      if (row) {
        this.modalRule.rule_id_list = [row.rule_id];
        this.modalRule.fense_id_list = row.fense_id_list;
        this.modalRule.vehicle_id_list = row.vehicle_id_list;
        this.modalRule.bind_id = row.bind_id;
        this.modalRule.mixed = row.mixed;

        // 使用Promise.all并行等待树初始化
        await this.$nextTick();
        const initPromises = [
          this.$refs.ruleTree.waitForInit,
          this.$refs.fenseTree.waitForInit
        ];
        await Promise.all(initPromises);

        // 规则树回显 - 只设置选中状态，不立即展开
        const ruleTree = this.$refs.ruleTree.$refs.tree;
        ruleTree.setCheckedKeys([row.rule_id]);
        ruleTree.setCurrentKey(row.rule_id);

        // 围栏树回显 - 只设置选中状态，不立即展开
        if (row.fense_id_list && row.fense_id_list.length) {
          const fenseTree = this.$refs.fenseTree.$refs.tree;
          fenseTree.setCheckedKeys(row.fense_id_list);
        }

        // 使用setTimeout延迟展开操作，避免阻塞UI
        setTimeout(() => {
          // 规则树展开 - 简化展开逻辑
          const ruleNode = ruleTree.getNode(row.rule_id);
          if (ruleNode && ruleNode.parent) {
            // 展开父节点
            ruleNode.parent.expand(null, true);

            // 延迟滚动到选中节点，等待展开完成
            setTimeout(() => {
              this.scrollToSelectedNode(this.$refs.ruleTree, row.rule_id);
            }, 150);
          }

          // 围栏树展开 - 只展开第一个节点的父节点，其他节点延迟展开
          if (row.fense_id_list && row.fense_id_list.length) {
            const fenseTree = this.$refs.fenseTree.$refs.tree;
            const firstFenceId = row.fense_id_list[0];
            const firstNode = fenseTree.getNode(firstFenceId);
            if (firstNode && firstNode.parent) {
              // 展开父节点
              firstNode.parent.expand(null, true);

              // 延迟滚动到选中节点，等待展开完成
              setTimeout(() => {
                this.scrollToSelectedNode(this.$refs.fenseTree, firstFenceId);
              }, 200);
            }

            // 延迟展开其他围栏节点 - 显著减少展开数量
            if (row.fense_id_list.length > 1) {
              setTimeout(() => {
                // 只展开第二个节点，大幅减少卡顿
                if (row.fense_id_list.length > 1) {
                  const fenceId = row.fense_id_list[1]; // 只展开第二个节点
                  const node = fenseTree.getNode(fenceId);
                  if (node && node.parent && !node.parent.expanded) {
                    node.parent.expand(null, true);
                  }
                }
              }, 800); // 显著增加延迟时间
            }
          }

          // 延迟加载车辆树
          setTimeout(async() => {
            try {
              await this.$refs.vehicleTree.waitForInit;
              // 车辆树回显 - 只设置选中状态
              if (row.vehicle_id_list && row.vehicle_id_list.length) {
                const vehicleTree = this.$refs.vehicleTree.$refs.tree;
                vehicleTree.setCheckedKeys(row.vehicle_id_list);

                // 只展开第一个节点的父节点，减少一次性展开的节点数量
                if (row.vehicle_id_list.length > 0) {
                  const firstVehicleId = row.vehicle_id_list[0];
                  const firstNode = vehicleTree.getNode(firstVehicleId);
                  if (firstNode && firstNode.parent) {
                    // 展开父节点
                    firstNode.parent.expand(null, true);

                    // 延迟滚动到选中节点，等待展开完成
                    setTimeout(() => {
                      this.scrollToSelectedNode(this.$refs.vehicleTree, firstVehicleId);
                    }, 250);
                  }

                  // 不再展开其他车辆节点，完全避免卡顿
                }
              }
            } finally {
              // 无论如何都要关闭加载状态
              this.vehicleTreeLoading = false;
            }
          }, 500); // 增加等待时间，确保前面的操作完成
        }, 100);
      } else {
        // 新增模式，不需要回显，直接关闭加载状态
        this.vehicleTreeLoading = false;
      }
    },
    async handleFenceRuleChange() {
      if (!this.modalRule.rule_id_list.length) {
        this.$message({
          message: "请选择规则!",
          showClose: true,
          type: "warning",
        });
        return;
      }

      if (!this.modalRule.vehicle_id_list.length) {
        this.$message({
          message: "请选择绑定车辆!",
          showClose: true,
          type: "warning",
        });
        return;
      }
      if (!this.modalRule.fense_id_list.length) {
        this.$message({
          message: "请选择绑定围栏!",
          showClose: true,
          type: "warning",
        });
        return
      }

      let params = {
        operate_type: this.modalRule.operate_type,
        vehicle_id_list: this.modalRule.vehicle_id_list,
        mixed: this.modalRule.mixed,
        rule_id_list: this.modalRule.rule_id_list,
        fense_id_list: this.modalRule.fense_id_list,
        bind_id: this.modalRule.operate_type === 1 ? this.modalRule.bind_id : "",
      };

      let result = await this.$api.operatefenserelatelite(params);
      if (!result || result.status != 200) {
        this.$message({
          message: result.message || "操作失败!",
          showClose: true,
          type: "warning",
        });
        return;
      }

      this.$success(result.message);
      this.searchBndinList();
      this.modalRule.show = false;
    },
    selectFenceNodes(current, { checkedNodes }) {
      this.modalRule.fense_id_list = checkedNodes.filter((item) => item.type == 2).map((item) => item.id);
    },
    async searchBndinList() {
      this.table.loading = true;
      let searchResult = await this.$api.queryfenserelatelite(this.queryList);
      if (!searchResult || searchResult.status != 200) {
        this.clearTableData();
        this.$message({
          showClose: true,
          message: searchResult.message || "查询出错!",
          type: "warning",
        });
        this.table.loading = false;
        return;
      }
      if (!searchResult.data.length) {
        this.clearTableData();
        this.$message({
          showClose: true,
          message: "未查询到数据!",
          type: "warning",
        });
        this.table.loading = false;

        return;
      }
      this.table.loading = false;

      this.table.data = searchResult.data;
      this.$nextTick(() => {
        this.$refs.totalTable?.doLayout();
      });
    },

    clearTableData() {
      this.table = { loading: false, data: [], page: 1, size: 30 };
    },

    selectVehicleNodes(current, { checkedNodes }) {
      this.modalRule.vehicle_id_list = checkedNodes.filter((item) => item.type == 4).map((item) => item.id);
    },

    handleRemoveEvent(row) {
      this.$confirm("是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            operate_type: -2,
            bind_id: row.bind_id,
          };
          let result = await this.$api.operatefenserelatelite(params);
          if (!result || result.status != 200) {
            this.$message({
              showClose: true,
              message: result.message || "删除失败!",
              type: "warning",
            });
            return;
          }
          this.$success(result.message);
          this.searchBndinList();
        })
        .catch((e) => {
          console.log("[ERROR :: FENCE REMOVE]", e);
        });
    },
    async searchFenceDetail(row) {
      let res = await this.$api.queryfenserelatelite({
        version: 2,
        fence_id: row.id,
      });
      this.fence_id = row.id;
      if (res.status == 200) {
        this.detailTable.data = res.data;
        this.$nextTick(() => {
          this.$refs.detailTable.doLayout();
        });
      }
    },

    handleStopEvent(row) {
      this.$confirm(`是否${row.status == -1 ? "启用" : "暂停"}该记录?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            operate_type: -1,
            bind_id: row.bind_id,
            stop: row.status == -1 ? false : true,
          };
          let result = await this.$api.operatefenserelatelite(params);
          if (!result || result.status != 200) {
            this.$message({
              showClose: true,
              message: result.message || "操作失败!",
              type: "warning",
            });
            return;
          }
          this.$success("操作成功");
          this.searchBndinList();
        })
        .catch((e) => {
          console.log("[ERROR :: FENCE STOP]", e);
        });
    },
    handleBatchStart() {
      if (!this.selectedFenceList.length) {
        this.$message({
          message: "请选择要操作的记录!",
          showClose: true,
          type: "warning",
        });
        return;
      }

      this.$confirm("确定要批量启用选中记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            operate_type: -1,
            bind_id_list: this.selectedFenceList.map((item) => item.bind_id),
            stop: false
          };
          let result = await this.$api.operatefenserelatelite(params);
          if (!result || result.status != 200) {
            this.$message({
              showClose: true,
              message: result.message || "批量启用失败!",
              type: "warning",
            });
            return;
          }
          this.$success("操作成功");
          this.searchBndinList();
        })
        .catch((e) => {
          console.log("[ERROR :: FENCE BATCH START]", e);
        });
    },

    handleBatchStop() {
      if (!this.selectedFenceList.length) {
        this.$message({
          message: "请选择要操作的记录!",
          showClose: true,
          type: "warning",
        });
        return;
      }

      this.$confirm("确定要批量暂停选中记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            operate_type: -1,
            bind_id_list: this.selectedFenceList.map((item) => item.bind_id),
            stop: true
          };
          let result = await this.$api.operatefenserelatelite(params);
          if (!result || result.status != 200) {
            this.$message({
              showClose: true,
              message: result.message || "批量暂停失败!",
              type: "warning",
            });
            return;
          }
          this.$success("操作成功");
          this.searchBndinList();
        })
        .catch((e) => {
          console.log("[ERROR :: FENCE BATCH STOP]", e);
        });
    },

    handleBatchDelete() {
      if (!this.selectedFenceList.length) {
        this.$message({
          message: "请选择要删除的记录!",
          showClose: true,
          type: "warning",
        });
        return;
      }

      this.$confirm("确定要批量删除选中记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            operate_type: -2,
            bind_id_list: this.selectedFenceList.map((item) => item.bind_id),
          };
          let result = await this.$api.operatefenserelatelite(params);
          if (!result || result.status != 200) {
            this.$message({
              showClose: true,
              message: result.message || "批量删除失败!",
              type: "warning",
            });
            return;
          }
          this.$success("操作成功");
          this.searchBndinList();
        })
        .catch((e) => {
          console.log("[ERROR :: FENCE BATCH REMOVE]", e);
        });
    },

    // 内存优化方法，减少内存占用和卡顿
    optimizeMemory() {
      try {
        // 清理树组件状态
        this.cleanupTrees();

        // 清理不必要的对象
        this.expandedNodes = {};

        // 强制进行垃圾回收
        setTimeout(() => {
          // 清理DOM缓存
          if (document.body.contains(this.$el)) {
            // 清理事件监听器
            const treeContainers = this.$el.querySelectorAll('.tree-content');
            treeContainers.forEach(container => {
              // 清理滚动事件
              const newContainer = container.cloneNode(true);
              if (container.parentNode) {
                container.parentNode.replaceChild(newContainer, container);
              }
            });
          }

          // 强制清理内存
          if (window.gc) {
            window.gc();
          }
        }, 100);
      } catch (err) {
        console.error('内存优化时出错:', err);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* 删除颜色变量定义 */
.tree-container {
  height: 460px;
  border: 1px solid var(--border-color-base);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 10px;
  font-size: 14px;
  border-bottom: 1px solid var(--border-color-base);
  background-color: var(--background-color-light);
  flex-shrink: 0;
}

.tree-content {
  flex: 1;
  overflow: auto;
  position: relative;

  /* 优化滚动性能 */
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

.tree-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  transition: opacity 0.3s ease;

  i {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--color-primary);
  }

  span {
    font-size: 14px;
    color: var(--color-primary);
  }
}

.no-events {
  pointer-events: none !important;
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

.scroll-only {
  pointer-events: none !important;
  overflow: auto !important;
  cursor: not-allowed !important;
}

.scroll-only * {
  pointer-events: none !important;
}

.scroll-only::-webkit-scrollbar-thumb {
  pointer-events: auto !important;
}

/* 选中节点的高亮效果 - 显著增强可见度 */
.highlight-node {
  animation: pulse-highlight 1.5s ease-in-out;
  position: relative;
  z-index: 10 !important; /* 提高z-index确保可见 */
  border: 2px solid var(--color-primary) !important;
  border-radius: 4px !important;
  background-color: rgba(64, 158, 255, 0.2) !important;
}

.highlight-node::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--color-primary);
  border-radius: 4px;
  animation: pulse-border 1.5s infinite;
  z-index: 9;
}

@keyframes pulse-highlight {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
    background-color: rgba(64, 158, 255, 0.3) !important;
  }
  50% {
    box-shadow: 0 0 0 8px rgba(64, 158, 255, 0.3);
    background-color: rgba(64, 158, 255, 0.4) !important;
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
    background-color: rgba(64, 158, 255, 0.2) !important;
  }
}

@keyframes pulse-border {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  70% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.relation-switch-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 15px;
  border-top: 1px solid var(--border-color-base);
  background-color: var(--background-color-light);
  flex-shrink: 0;
  span {
    margin-right: 10px;
  }
}

.switch-container {
  padding: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid var(--border-color-base);
  span {
    margin-right: 10px;
  }
}
</style>
