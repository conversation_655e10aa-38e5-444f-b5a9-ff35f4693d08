<template>
    <PonyDialog id="detailModal" :hasFooter="false" v-model="modal.show" width="1025" :is-fullscreen="true"
        :content-style="{ maxHeight: '550px', overflow: 'auto' }">
        <div class="detailContent" id="driverCard">
            <div slot="header" class="custom-header">
                <div class="custom-title-wrap">
                    <div class="custom-title-wrap">
                        <span class="title">{{ `${detailTitle}(${data.driverName})` }}</span>
                        <span class="info">
                            <span>
                                车牌号：{{ data.plateNo }}
                            </span>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            更新时间：{{ data.updateDate }}
                        </span>
                    </div>
                </div>
                <el-button @click="exportPdf" type="text" title="导出">
                    <i class="pony-iconv2 pony-pdf" style="margin-right: 0;vertical-align: middle;font-size: 20px;"></i>
                </el-button>
                <!-- <el-button @click="modal.show = false" circle>
                <i class="pony-iconv2 pony-guanbi" style="margin-right: 0;vertical-align: middle"></i>
            </el-button> -->
            </div>
            <div class="report-data-wrap">
                <div class="report-data-item">
                    <div style="width: 18%;flex-shrink: 0;display: flex;justify-content: center;align-items: center"
                        class="border border--r">
                        <TitleRandkingSK title="综合评分" style="width: 110px;height: 110px;" :reportData="totalDataObj">
                        </TitleRandkingSK>
                    </div>
                    <div class="block-list">
                        <TitleHintSK title="总里程" :num="Number(data.driveMile)" unit="km" boxColor="#8476Fc"
                            boxIcon="pony-licheng1">
                        </TitleHintSK>
                        <TitleHintSK title="总时长" :num="Number(data.driveTime)" unit="h" boxColor="#66c9ff"
                            boxIcon="pony-shijian2">
                        </TitleHintSK>
                        <TitleHintSK title="完成运单" :num="Number(data.num) ? Number(data.num) : '-'" unit="个"
                            boxColor="#f69063" boxIcon="pony-yundanliebiao1">
                        </TitleHintSK>
                        <TitleHintSK title="最高速度" :num="Number(data.maxSpeed)" unit="km/h" boxColor="#5b9ffe"
                            boxIcon="pony-zuigaosudu">
                        </TitleHintSK>
                        <TitleHintSK title="平均速度" :num="Number(data.avgSpeed)" unit="km/h" boxColor="#30cbc0"
                            boxIcon="pony-pingjunsudu">
                        </TitleHintSK>
                    </div>
                </div>
                <div class="detail">
                    <div class="driver">
                        <div class="title">司机信息</div>
                        <div class="content">
                            <div class="driver-info">
                                <div class="driver-name">
                                    <img :src="data.driverImage" width="100" class="driver-img" />
                                    <div class="driver-name-info">
                                        <div class="driver-name-info-font">
                                            {{ data.driverName }}
                                        </div>
                                        <div class="driver-name-info-age">
                                            年龄 {{ data.driverAge }}
                                        </div>
                                    </div>
                                </div>
                                <div class="driver-other">
                                    <div class="driver-info-item">
                                        <span class="label">工作年份</span>
                                        <span class="value">{{ data.workYear }}</span>
                                    </div>
                                    <div class="driver-info-item">
                                        <span class="label">归属车队</span>
                                        <span class="value">{{ data.dept }}</span>
                                    </div>
                                    <div class="driver-info-item">
                                        <span class="label">联系方式</span>
                                        <span class="value">{{ data.driverPhone }}</span>
                                    </div>
                                    <div class="driver-info-item">
                                        <span class="label">身份证号</span>
                                        <span class="value">{{ data.driverIdcardNo }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="score-echarts" ref="scoreEcharts">
                            </div>
                        </div>
                    </div>
                    <div class="alarm-score">
                        <div class="title">报警评分</div>
                        <div class="content">
                            <div class="score-item" v-for="(item) in alarmScoreList">
                                <TitleAndHint :key="item.code" :title="item.name" :count="100" :icon="Number(item.rate)"
                                    :reportData="Number(item.count)" :max="100" :score="Number(item.score)">
                                </TitleAndHint>
                            </div>
                        </div>
                    </div>
                    <div class="alarm-time">
                        <div class="title">在线时间折线图</div>
                        <div class="content">
                            <div class="alarm-time-echarts" ref="alarmEcharts"></div>

                        </div>
                    </div>
                    <div class="evaluate">
                        <div class="title">驾驶员评价/系统评语</div>

                        <div class="content">
                            <el-input v-model="data.tips" type="textarea" :row="5" disabled></el-input>
                        </div>
                    </div>
                    <div class="driver-echarts">
                        <div class="title">驾驶员对比趋势</div>
                        <div class="content">
                            <div class="driver-echarts" ref="driverEcharts"></div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </PonyDialog>
</template>

<script>
import TitleAndHint from './Title&HintAlarm'
import html2canvas from "html2canvas";
import jsPDF from "jspdf";


import TitleRandkingSK from '@/view/customReport/reportComponent/circle/Title&RankingSK'
import TitleHintSK from '@/view/customReport/reportComponent/circle/Title&HintSK'
export default {
    name: '',
    components: { TitleAndHint, TitleRandkingSK, TitleHintSK },
    data() {
        return {
            modal: {
                show: false,
            },
            data: {
            },
            detailTitle: '平衡记分卡综合评述',
            scoreEcharts: null,
            alarmEcharts: null,
            driverEcharts: null,
            alarmScoreList: [],
            totalDataObj: {
                rate: 0,
                value: 0
            },
        }
    },
    mounted() { },
    methods: {
        exportPdf() {
            let title = `${this.detailTitle}(${this.data.plateNo})${this.data.start}-${this.data.end}`
            let targetDom = document.querySelector('.detailContent');
            window.pageYoffset = 0;
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;
            html2canvas(targetDom, {
                allowTaint: true,
                scale: 2, // 提升画面质量，但是会增加文件大小
                height: targetDom.scrollHeight, //canvas高
                width: targetDom.scrollWidth, //canvas宽,
                background: "#fff"
            }).then((canvas) => {
                let leftHeight = canvas.height;
                let position = 0
                let a4Width = 595.28
                let a4Height = 841.89
                let a4HeightRef = Math.floor(canvas.width / a4Width * a4Height);
                let pageData = canvas.toDataURL('image/jpeg', 1.0)
                let pdf = new jsPDF('x', 'pt', 'a4')
                let index = 0,
                    canvas1 = document.createElement('canvas'),
                    height;
                pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen')

                function createImpl(canvas) {
                    if (leftHeight > 0) {
                        index++;
                        let checkCount = 0;
                        if (leftHeight > a4HeightRef) {
                            let i = position + a4HeightRef;
                            // for (i = position + a4HeightRef; i >= position; i--) {
                            //     let isWrite = true
                            //     for (let j = 0; j < canvas.width; j++) {
                            //         let c = canvas.getContext('2d').getImageData(j, i, 1, 1).data
                            //         if (c[0] != 0xff || c[1] != 0xff || c[2] != 0xff) {
                            //             isWrite = false
                            //             break
                            //         }
                            //     }
                            //     if (isWrite) {
                            //         checkCount++
                            //         if (checkCount >= 10) {
                            //             break
                            //         }
                            //     } else {
                            //         checkCount = 0
                            //     }
                            // }
                            height = Math.round(i - position) || Math.min(leftHeight, a4HeightRef);
                            if (height <= 0) {
                                height = a4HeightRef;
                            }
                        } else {
                            height = leftHeight;
                        }
                        canvas1.width = canvas.width;
                        canvas1.height = height;
                        let ctx = canvas1.getContext('2d');
                        ctx.drawImage(canvas, 0, position, canvas.width, height, 0, 0, canvas.width, height);
                        let pageHeight = Math.round(a4Width / canvas.width * height);
                        if (index != 1) {
                            pdf.addPage();
                        }
                        pdf.addImage(canvas1.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, a4Width, (a4Width / canvas1.width * height))
                        leftHeight -= height;
                        position += height
                        $('.pdfProgress').text(index + 1);
                        $('.pdfTotal').text(index + Math.ceil(leftHeight / a4HeightRef))
                        if (leftHeight > 0) {
                            setTimeout(createImpl, 10, canvas);
                        } else {
                            pdf.save(title + '.pdf')
                        }
                    }
                }

                //当内容未超过pdf一页显示的范围，无需分页
                if (leftHeight < a4HeightRef) {
                    pdf.addImage(pageData, 'JPEG', 0, 0, a4Width, a4Width / canvas.width * leftHeight);
                    pdf.save(title + '.pdf')
                } else {
                    setTimeout(createImpl, 10, canvas);
                }
            })
        },
        showModal(row) {
            this.modal.show = true;
            this.data = row
            this.totalDataObj.rate = Number(row.score) >= 0 ? Number((row.score / 100 * 100).toFixed(2)) : 0
            this.totalDataObj.value =row.score
            this.alarmScoreList = row.alarms.map(item => {
                item.rate = Number(item.score) >= 0 ? Number((item.score / item.scoreMax * 100).toFixed(0)) : 0
                return item
            })
            setTimeout(() => {
                this.initScortEcharts()
                this.initAlarmEcharts()
                this.initDriverEcharts()
            }, 100)
        },
        initScortEcharts() {
            this.scoreEcharts = this.$echarts.init(this.$refs.scoreEcharts);
            let indicator = []
            let data = []
            let valueObj = {
            }
            this.data.dimensions.forEach((item,index) => {
                let obj = {
                    name: item.name,
                    value: item.score,
                    max: item.scoreMax,
                }
                valueObj[index] = obj
                data.push(Number(item.score)>=0?item.score:0)
                indicator.push(obj)
            })
            let option = {
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '1%',
                    top: '10%',
                    containLabel: true
                },
                radar: {
                    // shape: 'circle',
                    trigger: 'item',
                    indicator: indicator.map((x, i) => {
                        Object.assign(x, {
                            axisLabel: {
                                show: !i,
                                formatter: function (value) {
                                    return `${value}分`;
                                }
                            },
                        })
                        return x
                    }),
                    axisName: {
                        fontSize: 14,
                    },
                    center: ['50%', '60%'],
                    radius: "50%"
                },
                tooltip: {
                    // 格式化提示内容
                    formatter: function (params) {
                        let str = ""
                        params.value.forEach((item,index) => {
                            str += `${valueObj[index].name} : ${valueObj[index].value}分<br/>`
                        })
                        return str
                    }
                },
                series: [
                    {
                        name: 'Budget vs spending',
                        type: 'radar',
                        data: [
                            {
                                value: data,
                                name: '',
                                areaStyle: {
                                    normal: {
                                        color: 'rgba(76, 134, 211,0.6)'
                                    }
                                },
                            },
                        ]
                    }
                ]
            };
            this.scoreEcharts.setOption(option);
        },
        initAlarmEcharts() {
            this.alarmEcharts = this.$echarts.init(this.$refs.alarmEcharts);
            let option = {
                // legend: {
                //     data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
                // },
                tooltip: {
                    // 格式化提示内容
                    formatter: function (params) {
                        return '在线次数 : ' + params.value
                    }
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '15%',
                    top: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23']
                },
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        name: '',
                        type: 'line',
                        stack: 'Total',
                        data: this.data.alarm24Hour
                    }
                ]
            };
            this.alarmEcharts.setOption(option);
        },
        initDriverEcharts() {
            this.driverEcharts = this.$echarts.init(this.$refs.driverEcharts);
            var yLabel = ['驾驶员', '车队', '公司'];
            var yData = [this.data.score, this.data.fleetScore, this.data.companyScore];
            yData = yData.map(item => {
                return Number(item)
            })
            var bgData = [100, 100, 100];
            let option = {
                grid: {
                    left: '1%',
                    right: '10%',
                    bottom: '1%',
                    top: '2%',
                    containLabel: true,
                },
                xAxis: {
                    show: false,
                    type: 'value',
                },
                tooltip: {
                    formatter: function (params) {
                        return params.name + '<br/>' +
                            "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgb(47,215,255)'></span>" +
                            '评分 : ' + params.value
                    }
                },
                yAxis: [
                    {
                        zlevel: 3,
                        type: 'category',
                        inverse: true,
                        axisTick: 'none',
                        axisLine: 'none',
                        show: true,
                        axisLabel: {
                            textStyle: {
                                color: '#000',
                                fontSize: '15',
                                padding: [0, 0, 0, 0],
                            },
                            formatter: function (value) {
                                return value;
                            },
                        },
                        data: yLabel,
                    },
                ],
                series: [
                    {
                        name: '评分',
                        type: 'bar',
                        zlevel: 3,
                        itemStyle: {
                            normal: {
                                barBorderRadius: [20, 20, 20, 20],
                                // barBorderRadius: 0,
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 1,
                                    y2: 0,
                                    colorStops: [
                                    {
                                            offset: 1,
                                            color: '#00BEFF', 
                                        },
                                        {
                                            offset: 0,
                                            color: 'rgba(84, 112, 198,0)', 
                                        },
                                    ],
                                    global: false, //  缺省为  false quanmei
                                },
                            },
                       
                        },
                        label: {
                            show: true,
                            position: 'right', // 位置
                            color: '#000',
                            fontSize: 14,
                            distance: 5 // 距离
                        },

                        barWidth: 15,
                        data: yData,
                    },

                    {
                        // 背景
                        type: 'bar',
                        barWidth: 15,
                        barGap: '-100%',
                        itemStyle: {
                            normal: {
                                color: '#fff',
                                barBorderRadius: [10, 10, 10, 10],
                            },
                        },
                        data: bgData,
                        z: 0,
                    },
                ],
            };
            this.driverEcharts.setOption(option);

        }
    },
};
</script>

<style scoped lang="scss">
.detailContent {
    width: 100%;
    height: 100%;

    .custom-header {
        margin: 0 -10px;
        width: calc(100% + 20px);
        height: 65px;
        display: flex;
        align-items: center;
        padding: 0 15px;
        justify-content: space-between;
        background: var(--background-color-base);

        .custom-title-wrap {
            align-self: stretch;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding-left: 33px;

            .title {
                margin-top: 15px;
                font-size: 18px;
                font-weight: 500;
                color: var(--color-text-regular);
                line-height: 1;
            }

            .info {
                margin-bottom: 8px;
                color: var(--color-text-secondary);
                line-height: 1;
            }
        }
    }

    .report-data-wrap {
        display: flex;
        flex-direction: column;
        padding-top: 15px;

        .report-data-item {
            display: flex;
            align-items: center;

            .total-score {
                display: flex;
                flex-direction: column;
                align-items: center;

                .score {
                    font-size: 50px;
                }
            }

            .block-list {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;
                padding: 0 0 0 10px;

                .block-item {
                    display: flex;
                    height: 100%;
                    align-items: center;
                    width: 20%;

                    .fl {
                        width: 50px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: space-around;
                        border-radius: 10px;


                        i {
                            font-size: 50px;
                            // color: #000;
                        }
                    }

                    .fr {
                        display: flex;
                        align-items: flex-start;
                        justify-content: center;
                        flex-direction: column;
                        margin-left: 10px;

                        .bottom {
                            .num {
                                font-size: 20px !important;
                            }

                            .unit {
                                font-size: 12px;
                                margin-left: 5px;
                            }
                        }
                    }
                }
            }
        }

        .detail {
            margin-top: 10px;
            background-color: var(--background-color-calendar-dayItem);
            padding: 10px 0;
            border-radius: 3px;
            border: 1px solid var(--border-color-base);

            .driver {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
                height: 285px;
                border: solid 1px var(--border-color-base);

                .title {
                    color: var(--color-primary);
                    width: 100%;
                    text-align: center;
                    line-height: 40px;
                    font-size: 16px;
                    font-weight: 600;
                    padding: 0 10px;
                    border-bottom: solid 1px var(--border-color-base);
                    background-color: var(--background-color-lighter);
                }

                .content {
                    padding: 10px;
                    padding-top: 28px;
                    display: flex;
                    overflow: auto;
                    width: 100%;
                    background-color: #f5f6f7;
                    justify-content: center;

                    .driver-info {
                        width: 45%;
                        display: flex;
                        flex-direction: column;
                        margin: 0 15px;
                        padding: 10px;
                        border-radius: 10px;
                        background-color: #fff;

                        .driver-name {
                            width: 100%;
                            height: 80px;
                            display: flex;
                            align-items: center;
                            border-bottom: solid 1px var(--border-color-base);
                            padding-bottom: 10px;

                            .driver-img {
                                height: 70px;
                                width: 70px;
                                border-radius: 50%;
                            }

                            .driver-name-info {
                                margin-left: 20px;

                                .driver-name-info-font {
                                    font-size: 20px;
                                }

                                .driver-name-info-age {
                                    font-size: 16px;
                                }
                            }
                        }

                        .driver-other {
                            width: 100%;
                            height: 90%;
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            justify-content: space-evenly;
                            font-size: 16px;
                        }

                        .driver-info-item {
                            .label {}

                            .value {}
                        }
                    }

                    .score-echarts {
                        width: 45%;
                        height: 200px;
                        margin: 0 15px;
                        border-radius: 10px;
                        background-color: #fff;
                    }
                }
            }

            .alarm-score {
                min-height: 290px;
                border: solid 1px var(--border-color-base);

                .title {
                    color: var(--color-primary);
                    text-align: center;
                    line-height: 40px;
                    font-size: 16px;
                    font-weight: 600;
                    padding: 0 10px;
                    border-bottom: solid 1px var(--border-color-base);
                    background-color: var(--background-color-lighter);
                }

                .content {
                    padding: 10px;
                    display: flex;
                    background-color: var(--background-color-base);
                    overflow: auto;
                    width: 100%;
                    flex-wrap: wrap;
                    height: 90%;

                    .score-item {
                        width: 16.6%;
                        height: 135px;
                        flex-wrap: wrap;

                    }
                }
            }

            .alarm-time {
                width: 100%;
                height: 235px;
                margin: 15px 0;
                border: solid 1px var(--border-color-base);

                .title {
                    color: var(--color-primary);
                    text-align: center;
                    line-height: 40px;
                    font-size: 16px;
                    font-weight: 600;
                    padding: 0 10px;
                    border-bottom: solid 1px var(--border-color-base);
                    background-color: var(--background-color-lighter);
                }

                .content {
                    padding: 10px;
                    padding-top: 28px;
                    display: flex;
                    background-color: var(--background-color-base);
                    overflow: auto;
                    width: 100%;

                    .alarm-time-echarts {
                        width: 100%;
                        height: 180px;
                    }
                }

            }

            .evaluate {
                margin: 15px 0;
                border: solid 1px var(--border-color-base);

                .title {
                    color: var(--color-primary);
                    text-align: center;
                    line-height: 40px;
                    font-size: 16px;
                    font-weight: 600;
                    padding: 0 10px;
                    border-bottom: solid 1px var(--border-color-base);
                    background-color: var(--background-color-lighter);
                }

                .content {
                    padding: 10px;
                    padding-top: 28px;
                    display: flex;
                    background-color: var(--background-color-base);
                    overflow: auto;
                    width: 100%;
                }

            }

            .driver-echarts {
                border: solid 1px var(--border-color-base);

                .title {
                    color: var(--color-primary);
                    text-align: center;
                    line-height: 40px;
                    font-size: 16px;
                    font-weight: 600;
                    padding: 0 10px;
                    border-bottom: solid 1px var(--border-color-base);
                    background-color: var(--background-color-lighter);
                    width: 100%;
                }

                .content {
                    padding: 10px;
                    padding-top: 28px;
                    display: flex;
                    background-color: var(--background-color-base);
                    overflow: auto;
                    flex-direction: column;
                    align-items: start;
                    width: 100%;

                    .driver-echarts {
                        width: 100%;
                        height: 130px;
                    }
                }
            }

        }
    }

}

/deep/.el-textarea__inner {
    min-height: 100px;
}

/deep/.el-form-item--mini.el-form-item {
    margin-bottom: 5px;
}
</style>