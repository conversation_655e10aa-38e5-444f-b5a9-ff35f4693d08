<template>
  <PonyDialog
    v-model="show"
    title="OCR识别"
    class="ocr-modal"
    :width="780"
    content-style="max-height:620px;overflow:auto;padding:20px"
    @close="closeDialog"
  >
    <el-form :model="form" :rules="rules" label-width="100px" size="mini" ref="ocrForm">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="企业名称" prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级机关" prop="parent_id">
            <SelectTreeInput
              v-model="deptSelectValue"
              :condition="departCondition"
              ref="deptInput"
              type="department"
              placeholder="请选择所属上级"
              title="请选择所属上级"
            >
            </SelectTreeInput>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="企业地址" prop="address">
            <el-input v-model="form.address"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="法人姓名" prop="master">
            <el-input v-model="form.master"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="phone">
            <el-input v-model="form.phone"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="营业执照" prop="trans_license_img_list[0]">
            <div class="upload-wrapper">
              <ImageUploader
                v-model="form.trans_license_img_list[0]"
                :maxSize="4"
                :absolute="1"
                @change="(url) => handleImageChange(url, 0)"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证正面" prop="trans_license_img_list[1]">
            <div class="upload-wrapper">
              <ImageUploader
                v-model="form.trans_license_img_list[1]"
                :maxSize="4"
                :absolute="1"
                @change="(url) => handleImageChange(url, 1)"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证反面" prop="trans_license_img_list[2]">
            <div class="upload-wrapper">
              <ImageUploader
                v-model="form.trans_license_img_list[2]"
                :maxSize="4"
                :absolute="1"
                @change="(url) => handleImageChange(url, 2)"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label=" ">
            <el-checkbox v-model="form.sync" v-if="hasPermission('company:company:sync')">平台信息同步</el-checkbox>
            <!-- <el-checkbox v-model="form.user" style="margin-left: 20px" v-if="hasPermission('company:company:user')">创建简易账号</el-checkbox> -->
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.sync">
          <el-form-item label="同步平台">
            <el-select v-model="form.sync_info" placeholder="请选择同步平台">
              <el-option v-for="item in vehicleSourceData" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="commit">确定</el-button>
      <el-button @click="closeDialog">取消</el-button>
    </template>
    <PonyDialog
    v-model="accountDialog.visible"
    title="账号信息"
    :width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    append-to-body
  >
    <div class="account-dialog">
      <div class="account-item">
        <span class="label">账号：</span>
        <span>{{accountDialog.username}}</span>
      </div>
      <div class="account-item">
        <span class="label">密码：</span>
        <span>{{accountDialog.password}}</span>
      </div>
    </div>
    <template slot="footer">
      <el-button type="text" @click="copyAccountInfo">复制</el-button>
      <el-button type="primary" @click="confirmAccount">确定</el-button>
    </template>
  </PonyDialog>
  </PonyDialog>

</template>

<script>
import ImageUploader from '@/components/common/ImageUploader'
import SelectTreeInput from '@/components/common/SelectTreeInput'
import { duration } from 'moment';
import { mapActions } from 'vuex'

export default {
  name: 'OCRDialog',
  components: {
    ImageUploader,
    SelectTreeInput
  },
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value.trim() == '' || value == null) {
        callback(new Error('请输入手机号码'));
      } else if (/^1(3[0-9]|5[0-3,5-9]|7[1-3,5-8]|8[0-9])\d{8}$/.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的手机号码'));
      }
    };
    return {
      show: false,
      activeTab: 'basic',
      deptSelectValue: null,
   
      form: {
        name: '',
        address: '',
        trans_license_img_list: ['','',''],
        master: '',
        phone: '',
        parent_id: '',
        type: 0,
        sync: false,
        user: false,
        sync_info: '',
        operate_type: 1
      },
      rules: {
        name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        parent_id: [{ required: true, message: '请选择上级机关', trigger: 'change' }],
        'trans_license_img_list[0]': [{ required: true, message: '请上传营业执照', trigger: ['blur', 'change'] }],
        'trans_license_img_list[1]': [{ required: true, message: '请上传身份证正面', trigger: ['blur', 'change'] }],
        'trans_license_img_list[2]': [{ required: true, message: '请上传身份证反面', trigger: ['blur', 'change'] }],
        master: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
        phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        address: [{ required: true, message: '请输入企业地址', trigger: 'blur' }],
      },
      disabled: false,
      vehicleSourceData: [],
      accountDialog: {
        visible: false,
        username: '',
        password: '123456'
      }
    }
  },
  watch: {
    deptSelectValue(newVal) {
      if (!newVal) return
      this.form.parent_id = newVal.value
    }
  },
  async created() {
    const res = await this.getFormatListByCode('vehicle_source_data')
    this.vehicleSourceData = res
  },
  methods: {
    ...mapActions('dictionary', ['getFormatListByCode']),

    departCondition(treeNode) {
        if (treeNode.type != this.form.type - 1 && this.form.type != 0) {
          this.$message({
            type: "warning",
            showClose: true,
            message: "请选择当前部门上一级"
          });
          return false;
        } else {
          return true;
        }
    },
    async showModal(row) {
      this.show = true
      Object.assign(this.form, row)
      this.form.sync_info = Number(this.form.sync_info)
      // this.form = {
      //   name: '',
      //   address: '',
      //   trans_license_img_list: ['','',''],
      //   master: '',
      //   phone: '',
      //   parent_id: row.id,
      //   type: row.type,
      //   sync: false,
      //   user: false,
      //   sync_info: '',
      //   operate_type: 0
      // }
      await this.$nextTick()
      this.deptSelectValue = await this.$refs["deptInput"].fillOtherProperty(row.parent_id);
      this.disabled = true
    },
    closeDialog() {
      this.show = false
      this.$refs.ocrForm.resetFields()
    },
    getBaseUrl() {
      // 使用window.location.origin获取当前页面的基础URL
      const baseUrl = window.location.origin 
      // const baseUrl = 'http://**************:8085'
      return baseUrl
    },
    async handleImageChange(url, index) {
      if(!url) return
      
      // 更新数组数据
      this.$set(this.form.trans_license_img_list, index, url)
      
      // 等待下一个tick,确保数据更新完成
      await this.$nextTick()
      
      // 验证对应字段
      this.$refs.ocrForm.validateField(`trans_license_img_list[${index}]`)
      
      // 根据index调用对应的OCR识别
      if(index === 0) {
        await this.handleOCRSuccess(url)
      } else if(index === 1) {
        await this.handleIDCardFrontSuccess(url)
      }
    },
    async handleOCRSuccess(url) {
      if(!url) return
      const baseUrl = this.getBaseUrl()
      const fullUrl = baseUrl + url
      let res = await this.$api.ocrRecognition({
        url: fullUrl,
        operateType: 2
      })
      if(res.status === 200&&res.data.companyName) {
        this.form.name = res.data.companyName
        this.form.address = res.data.businessAddress
      }else{
        this.$message.error('营业执照OCR识别失败')
      }
    },
    async handleIDCardFrontSuccess(url) {
      if(!url) return
      const baseUrl = this.getBaseUrl()
      const fullUrl = baseUrl + url
      let res = await this.$api.ocrRecognition({
        url: fullUrl,
        operateType: 0
      })
      if(res.status === 200&&res.data.face) {
        this.form.master = res.data.face.name
      }else{
        this.$message.error('身份证正面OCR识别失败')
      }
    },
    commit() {
      this.$refs.ocrForm.validate(async valid => {
        if(valid) {
          const res = await this.$api.operateCompany(this.form)
          if(res.status === 200) {
            this.$success('操作成功')
            if(this.form.user) {
              this.showAccountDialog()
            } else {
              this.$emit('refresh')
              this.closeDialog()
            }
          } else {
            this.$error(res.message)
          }
        }
      })
    },
    showAccountDialog() {
      this.accountDialog.username = this.form.name
      this.accountDialog.visible = true
    },
    async copyAccountInfo() {
      const text = `账号：${this.accountDialog.username}\n密码：${this.accountDialog.password}`
      try {
        // 创建临时文本区域
        const textarea = document.createElement('textarea')
        textarea.value = text
        textarea.setAttribute('readonly', '')
        textarea.style.position = 'absolute'
        textarea.style.left = '-9999px'
        document.body.appendChild(textarea)
        
        // 选择文本并复制
        textarea.select()
        document.execCommand('copy')
        
        // 移除临时元素
        document.body.removeChild(textarea)
        
        this.$message.success('复制成功')
      } catch(err) {
        console.log(err)
        this.$message.error('复制失败')
      }
    },
    confirmAccount() {
      this.accountDialog.visible = false
      this.$emit('refresh')
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.ocr-modal {
  /deep/ .el-form-item {
    margin-bottom: 18px;
    
    &__label {
      padding-right: 12px;
      color: #606266;
    }
  }

  .upload-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
    text-align: center;
  }

  /deep/ .el-input {
    width: 100%;
  }

  /deep/ .el-checkbox {
    color: #606266;
    font-weight: normal;
  }

  .upload-wrapper {
    width: 140px;
    height: 90px;
  }

  /deep/ .image-uploader {
    .uploader {
      .no-img {
        > i {
          font-size: 24px;
        }
        > span {
          padding-top: 4px;
          font-size: 12px;
        }
      }
    }
  }
}

.account-dialog {
  padding: 10px 20px;
  
  .tip {
    margin-bottom: 15px;
  }
  
  .account-item {
    display: flex;
    margin-bottom: 10px;
    
    .label {
      width: 45px;
    }
  }
}
</style> 