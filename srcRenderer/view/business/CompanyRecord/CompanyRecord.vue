<template>
  <Layout class="car-mgnt" :content-loading="carTable.loading" :has-color="true">
    <!-- <template slot="aside">
            <ElementTree type="area" ref="tree" @node-click="nodeClick"></ElementTree>
        </template> -->
    <template slot="query">
      <div class="query-item">
        <el-input v-model="name" style="width: 150px;margin-right: 10px" placeHolder="请输入关键字"></el-input>
        <el-button size="mini" type="primary" @click="getCarTableList(1)" :loading="carTable.loading">查询</el-button>
      </div>
      <div class="query-item">
        <el-popover ref="popoverRef" placement="bottom" width="200" trigger="click">
          <div style="display: flex;align-items: center;justify-content: center;">
            <el-radio-group v-model="radio1" size="mini">
              <el-radio v-model="radio1" label="0" border>平台</el-radio>
              <el-radio v-model="radio1" label="1" border>系统</el-radio>
              <el-radio v-model="radio1" label="2" border>企业</el-radio>
              <el-radio v-model="radio1" label="3" border>车队</el-radio>
            </el-radio-group>
          </div>
          <div style="display: flex;align-items: center;justify-content: right">
            <el-button type="text" @click="closePopover">取消</el-button>
            <el-button size="mini" type="primary" style="margin-right: 10px" @click="exportExcel">确定</el-button>
          </div>
          <el-button type="primary" slot="reference">导出</el-button>
        </el-popover>
      </div>
      <!-- <div class="query-item" >
                <el-button size="mini" type="primary" @click="exportTableList" :loading="exportLoading">导出</el-button>
            </div> -->
      <div class="query-item">
        <el-button size="mini" type="primary" @click="multipleImport">批量导入</el-button>
        <el-button size="mini" type="primary" @click="multipleModify">批量修改</el-button>
        <!-- <el-button size="mini" type="primary" @click="addCompany">新增</el-button> -->
      </div>
    </template>
    <template slot="content">
      <el-table class="el-table--radius " :empty-text="carTable.data.length ? '暂无数据' : ''" stripe :data="carTable.data"
        height="100%" border ref="carTable" highlight-current-row style="width: 100%" row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" @expand-change="expand"
        :expand-row-keys="expandList">
        >
        <!-- <el-table-column :label="$ct('label.index')" width="50">
                    <template slot-scope="scope">
                        <span>{{ (pager.current - 1) * pager.size + 1 + scope.$index }}</span>
                    </template>
                </el-table-column> -->
        <el-table-column prop="name" min-width="300" label="机构名称" show-overflow-tooltip align='left'>
          <template slot-scope="scope">
            <el-tag size="mini" :type="levelTypeMap[scope.row.type]">
              {{ scope.row.type_name }}
            </el-tag>
            <span>{{
              scope.row.name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="mini" title="新增" @click="addCompany(scope.row)" :disabled="scope.row.type == 3">
              <i class="pony-iconv2 pony-xinzeng"></i>
            </el-button>
            <el-button type="text" size="mini" title="修改" @click="editCompany(scope.row)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button type="text" size="mini" title="OCR" @click="showOCRDialog(scope.row)" v-if="hasPermission('company:ocr') && scope.row.type == 2 ">
              <i class="pony-iconv2 pony-tonghangzheng"></i>
            </el-button>
            <el-button type="text" size="mini" title="删除" @click="delItem(scope.row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="sort" min-width="40" label="排序" show-overflow-tooltip></el-table-column>
        <el-table-column prop="industry_type_list_view" min-width="100" label="行业类型"
          show-overflow-tooltip></el-table-column>
        <el-table-column prop="contact_name" min-width="100" label="联系人" show-overflow-tooltip></el-table-column>
        <el-table-column prop="contact_tel" min-width="100" label="联系电话" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area_name" min-width="100" label="所属区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dept_sign_view" min-width="100" label="企业类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="address" min-width="270" label="地址" show-overflow-tooltip></el-table-column>
        <!-- 操作列 -->
        <el-table-column prop="admin_name" min-width="60" label="管理员" show-overflow-tooltip></el-table-column>
        <el-table-column prop="admin_tel" min-width="80" label="管理员联系电话" show-overflow-tooltip></el-table-column>
      </el-table>
    </template>
    <!-- <template slot="footer">
            <el-pagination background small
                           layout="prev, pager, next, total" :pager-count="5"
                           :current-page.sync="pager.current"
                           :page-size="pager.size"
                           :total="pager.total">
            </el-pagination>
        </template> -->
    <AddCompany ref="addCompany" @refresh="getCarTableList()" @refreshId="refreshId"></AddCompany>
    <MultipleImport ref="multipleImport" @refresh="getCarTableList()"></MultipleImport>
    <MultipleModify ref="multipleModify" @refresh="getCarTableList()"></MultipleModify>
    <OCRDialog ref="ocrDialog" @refresh="getCarTableList()"></OCRDialog>
  </Layout>
</template>

<script>
import AddCompany from './modal/AddCompany'
import DictionarySelect from '../../../components/common/DictionarySelect'
import MultipleImport from './modal/MultipleImport'
import MultipleModify from './modal/MultipleModify'
import OCRDialog from './modal/OCRDialog'

import { createLogger } from 'vuex'

export default {
  name: "companyRecord",
  components: {
    AddCompany, MultipleImport, DictionarySelect, MultipleModify, OCRDialog
  },
  data() {
    return {
      currentNode: null,
      name: "",
      pager: {
        current: 1,
        total: 0,
        size: 30,
      },
      carTable: {
        data: [],
        loading: false,
      },
      exportLoading: false,
      levelLabel: ["平台", "系统", "企业", "车队", "第五级"],
      levelTypeMap: ["", "success", "info", "warning", "danger"],
      expandList: [],
      searchState: false,
      radio1: null,
      expandID: null,
      flatTreeList: [],
    }
  },
  computed: {
    pageStart() {
      return (this.pager.page - 1) * this.pager.size
    },
  },
  methods: {
    refreshId(id) {
      this.expandID = id;
      // if(this.expandID!==null){
      // this.$refs['carTable'].toggleRowExpansion(this.expandID,true)
      // }

    },
    closePopover() {
      this.$refs.popoverRef.doClose()
    },
    async exportExcel() {
      if (!this.radio1) {
        return this.$warning('请选择要导出的类型！')
      }
      let table = this.$utils.flatTree(this.carTable.data);
      let data = table.filter(item => item.type == this.radio1).map((item) => {
        return {
          name: item.name,
          parentName: item.parent_name,
          areaName: item.area_name,
          jurisdictionView: item.jurisdiction.toString(),
          contactName: item.contact_name,
          contactTel: item.contact_tel,
          adminName: item.admin_name,
          adminTel: item.admin_tel,
          email: item.email,
          fax: item.fax,
          address: item.address
        }
      })
      await this.$utils.excelExport("/ponysafety2/a/department/v2/export/dept", JSON.stringify(data), `企业备案-${this.levelLabel[this.radio1]}.xls`)
      this.closePopover()
    },
    search() {
      // if(this.query.value.trim().length === 0){
      //     return this.$error('请输入查询关键字!')
      // }
      this.getCarTableList()
    },
    expand() {
      this.$nextTick(() => {
        this.$refs['carTable'].doLayout()
      })
    },
    async getCarTableList(pageIndex = 1) {
      this.carTable.loading = true;
      let params = {};
      this.searchState = false;
      this.expandList = []
      this.flatTreeList = []
      if (this.name != '') {
        params = { name: this.name }
      }
      let res = await this.$api.queryCompany(params);
      this.carTable.loading = false;
      // if(res.status != 200) return this.$error(res.message)
      this.carTable.data = res.tree || [];
      if (!res.id_list.length) {
        this.expandList.push("0")
      } else {
        this.expandList = res.id_list
      }
      this.flatTreeList = this.$utils.flatTree(res.tree)
      let index = this.flatTreeList.findIndex(it => it.id == this.expandID)

      this.$nextTick(() => {
        this.$refs['carTable'].doLayout()
        if (index != -1) {
          this.$refs['carTable'].toggleRowExpansion(this.flatTreeList[index], true)
        }
      })

    },
    delItem(row) {
      this.$confirm('此操作将永久删除信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let result = await this.$api.operateCompany({ operate_type: -2, id: row.id })

        if (!result || result.status !== 200) {
          this.$error(result.message)
          return
        }
        this.$success('删除成功')
        this.getCarTableList()
      }).catch(err => {
      })
    },
    editCompany(row) {
      this.$refs['addCompany'].showModal(row, 1);
    },
    addCompany(row) {
      this.$refs['addCompany'].showModal(row, 0);
    },
    // driverBind(row) {
    //     this.$refs['driverBind'].showModal(row);
    // },
    multipleImport() {
      this.$refs['multipleImport'].showModal();
    },
    multipleModify() {
      this.$refs.multipleModify.showModal();
    },
    async exportTableList() {
      let searchList = {
        query_name: this.query.value,
        parent_area_id: this.query.areaId,
        page: this.pager.current,
        size: this.pager.size
      }
      this.exportLoading = true;
      await this.$utils.excelExport("/ponysafety2/a/department/exportDeptCustom", JSON.stringify(searchList),
        this.$ct('messageInfo.5'));
      this.exportLoading = false;
    },
    showOCRDialog(row) {
      this.$refs.ocrDialog.showModal(row);
    }
  },
  created() {
    this.getCarTableList();
  },
}
</script>

<style scoped lang="scss">
.el-radio {
  margin: 5px !important;
}

.el-radio-group {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}
</style>
