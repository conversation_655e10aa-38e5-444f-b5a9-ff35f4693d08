<template>
    <PonyDialog
     v-model="show"
     class="dealer-site-info"
     :has-mask="false"
     :insert-body="true"
     :title="mode === 'add' ? addModeTitle : modifyModeTitle"
     :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText"
     :width="600"
     content-style="min-height:150px;overflow:auto;padding:15px"
     @confirm="add"
     @close="clear"
    >
        <el-form
         :model="form"
         :rules="rules"
         label-width="80px"
         size="mini"
         ref="form"
         style="padding-top:10px"
        >
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item prop="areaType" label="类型:">
                        <el-select v-model="form.areaType" placeholder="请选择" @change="changeType">
                            <el-option
                             v-for="item in elementTypeList"
                             :key="item.areaType"
                             :label="item.name"
                             :value="item.areaType">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item prop="areaId" label="场地:">
                        <SelectTreeInput v-model="pointTerminal" ref="unPointTerminal" title="请选择收集点" type="unPointTerminal" @change="searchTerminalList"
                                         placeholder="请选择收集点" :condition="treeDriverCondition" v-if="form.areaType === 34"></SelectTreeInput>
                        <SelectTreeInput v-model="stopTerminal" ref="unStopTerminal" title="请选择停车场" type="unStopTerminal" @change="searchTerminalList"
                                         placeholder="请选择停车场" :condition="treeDriverCondition" v-else-if="form.areaType === 13"></SelectTreeInput>
                        <SelectTreeInput v-model="workTerminal" ref="unWorkTerminal" title="请选择工地" type="areaTerminalTree:11" @change="searchTerminalList"
                                         placeholder="请选择工地" :condition="treeDriverCondition" v-else-if="form.areaType === 11"></SelectTreeInput>
                        <SelectTreeInput v-model="xiaoTerminal" ref="unXiaoTerminal" title="请选择消纳场" type="areaTerminalTree:12" @change="searchTerminalList"
                                         placeholder="请选择消纳场" :condition="treeDriverCondition" v-else-if="form.areaType === 12"></SelectTreeInput>                 
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="remark" label="备注:">
                        <el-input type="textarea" placeholder="请输入备注" v-model="form.remark"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-divider content-position="left">
            设备列表
            <el-button type="text" class="pony-iconv2 pony-xinzeng" @click="addTerminal"></el-button>
        </el-divider>
        <div class="current">
          <span class="current-text">当前</span>
          <p class="current-info">
            <span class="code" :title="rowData ? rowData.channelId : ''">设备编码: {{rowData ? rowData.channelId : ''}}</span>
            <span :title="rowData ? rowData.channelName : ''">设备名称: {{rowData ? rowData.channelName : ''}}</span>
          </p>
          <span class="to-add" @click="toAdd">填充值列表</span>
        </div>
        <template v-for="(item,index) in terminal_list">
            <el-form :rules="terminalRules" :model="terminal_list[index]" label-width="80px"
                     ref="terminalForm">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="设备名称" prop="name">
                            <el-input v-model="terminal_list[index].name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备类型" prop="type">
                            <el-select v-model="terminal_list[index].type">
                                <el-option v-for="(value,key) in terminalTypeMap" :label="value.name" :value="key"
                                           :key="key"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="设备编号" prop="code">
                            <el-input v-model="terminal_list[index].code"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="出入口" prop="pos">
                            <DictionarySelect code="business_area_terminal_position"
                                              v-model="terminal_list[index].pos"></DictionarySelect>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <div class="delete-form-item">
                    <el-button type="text" @click="removeTerminal(index)"> 删除</el-button>
                </div>
                <div class="delete-form-item">
                    <el-button type='text'></el-button>
                </div>
            </el-form>
        </template>
    </PonyDialog>
</template>

<script>

import DictionarySelect from "@/components/common/DictionarySelect";
import SelectTreeInput from '@/components/common/SelectTreeInput'

export default {
    name: "addSpeedConfig",
    components: {
        DictionarySelect,
        SelectTreeInput
    },
  
    data() {
        return {
            show: false,
            mode: "add",
            loading: false,
            addModeTitle: "添加至平台",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改场地设备",
            modifyModeOkText: "确认修改",
            id: '',
            form: {
                areaType: null,
                remark: null,
                areaId: null,
                operateType:1
            },
            stopTerminal: null,
            pointTerminal: null,
            workTerminal:null,
            xiaoTerminal:null,
            options: [],
            rules: {
                areaType: [
                    {required: true, message: "请选择类型", trigger: "blur"}
                ],
                areaId: [
                    {required: true, message: "请选择场地", trigger: "change"}
                ],
            },
            terminalRules: {
                type: [{required: true, message: '请选择设备类型', trigger: 'change'}],
                code: [{required: true, message: '请输入设备编号', trigger: 'blur'}],
                name: [{required: true, message: '请输入设备名称', trigger: 'blur'}],
                pos: [{required: true, message: '请选择出入口', trigger: 'change'}],
            },
            terminal_list: [],
            terminalTypeMap: {},
            elementTypeList:[],
            renderDataType:5,
            rowData:null
        };
    },
    watch: {
        'pointTerminal': function (val) {
            if(!val) return
            this.form.areaId = val.value
        },
        'stopTerminal': function (val) {
            if(!val) return
            this.form.areaId = val.value

        },
        'workTerminal': function (val) {
            if(!val) return
            this.form.areaId = val.value

        },
        'xiaoTerminal': function (val) {
            if(!val) return
            this.form.areaId = val.value

        },
       
    },
    async mounted() {
      this.getTreeList()
        //非车载终端类型
        let terminalTypeList = await this.$api.getTerminalTypeList({type: 1, classify: 1});
        let temp = {};
        terminalTypeList.forEach(item => {
            temp[item.id] = item;
        })
        this.terminalTypeMap = temp;
    },
    methods: {
      async getTreeList() {
            const res = await this.$api.areaTerminalType({
                page: 'siteEquipmentMgt'
            })
            if(res.status === 200) {
                this.elementTypeList = res.data
                await this.treeChange(0)
            } else {
                return this.$warning('获取树类型失败！')
            }
        },
        async treeChange(val) {
            // this.$refs['tree'] && this.$refs['tree'].loading = true
            // this.treeType = this.elementTypeList[val].type
            this.renderDataType = this.elementTypeList[val].renderDataType
            // await this.$refs['tree'].initTree(this.treeType)
            // this.search.areaIdList = []
        },
        changeType(val) {
            this.pointTerminal = null
            this.stopTerminal = null
            this.workTerminal = null
            this.xiaoTerminal = null
            this.form.areaId = null
            this.renderDataType = (val == 11 || val == 12) ? 5 : 6
        },
        treeDriverCondition(treeNode) {
            if (treeNode.type !== this.renderDataType) {
                this.$warning(`请选择场地`)
                return false;
            }
            return true;
        },
        //设备添加
        addTerminal() {
            this.terminal_list.push({
                code: '',
                pos: 0,
                name: '',
                type: '',
            })
        },
        toAdd(){
          this.terminal_list.push({
                code: this.rowData.channelId,
                pos: 0,
                name: this.rowData.channelName,
                type: '',
            })
        },
        //删除设备
        removeTerminal(index) {
            this.terminal_list.splice(index, 1);
        },
        async add() {
            this.loading = true;
            try {
                await this.$refs['form'].validate()
                if (this.terminal_list.length) {
                    await Promise.all(this.$refs['terminalForm'].map(item => item.validate()))
                }
            } catch (e) {
                this.loading = false;
                // this.$warning('还有必填的项目未填写')
                return;
            }
            
            let param = this.form
            param = Object.assign(param,{terminalList: this.terminal_list})
            let res = await this.$api.areaTerminalBind(param);
            this.loading = false;
            if (res.status === 200) {
                this.$success("修改成功");

                // this.$success(this.mode === 'modify' ? "修改成功" : '新增成功');
                this.$emit("refresh");
                this.show = false;
            } else {
              this.$error("修改失败"+ res.message);
                throw new Error("修改失败" + res.message);
                // this.$error(this.mode === 'modify' ? "修改失败"+ res.message : '新增失败' + res.message);
                // throw new Error(this.mode === 'modify' ? "修改失败" + res.message: '新增失败' + res.message);
            }
        },
        async searchTerminalList(val) {
            if (!val || !val.value) {
                return
            }
            this.terminal_list = []
            this.loading = true;
            let res = await this.$api.areaTerminalBind({
                operateType: 21,
                areaId: val.value,
            });
            this.loading = false;
            if (!res || res.status !== 200 || !res.data || !res.data.length) {
                return
            }
            this.terminal_list.push(...res.data)
            // this.table.data = res.data
           
        },
        async showModal(rowData) {
            this.clear()
            this.show = true;
            if (rowData) {
                // this.mode = "modify";
                // const data = JSON.parse(rowData)
                this.rowData = rowData
                // this.form.areaType = data.areaType
                // this.form.remark = data.remark
                // this.form.areaId = data.areaId
                // this.terminal_list = data.terminalList
                // this.id = data.id
                // if(data.areaType === 34) {
                //     this.pointTerminal = {
                //         label: data.areaName,
                //         value: data.areaId
                //     };
                // } else if(data.areaType === 13){
                //     this.stopTerminal = {
                //         label: data.areaName,
                //         value: data.areaId
                //     }
                // }else if(data.areaType === 11){
                //     this.workTerminal = {
                //         label: data.areaName,
                //         value: data.areaId
                //     }
                // }else if(data.areaType === 12){
                //     this.xiaoTerminal = {
                //         label: data.areaName,
                //         value: data.areaId
                //     }
                // }
            } else {
                this.mode = "add";
            }
        },
        clear() {
            this.form = {
                areaType: this.elementTypeList[0].areaType,
                remark: null,
                areaId: null,
            }
            this.terminal_list = []
            this.pointTerminal = null
            this.stopTerminal = null
            this.workTerminal = null
            this.xiaoTerminal = null
            this.id = null
        }
    },
};
</script>

<style scoped lang="scss">
.dealer-site-info {
    .detail .el-form-item--mini.el-form-item {
        height: 28px;
    }
    
    /deep/ .el-tab-pane {
        padding: 10px;
    }
    
    /deep/ .select-tree-input {
        width: 100% !important;
    }
    
    /deep/ .el-divider {
        .el-divider__text {
            padding: 0 10px;
        }
        
        .el-button--mini.el-button--text {
            vertical-align: -1px;
        }
    }
    
    /deep/ .el-form-item {
        width: 100%;
    }
}
.current {
  background-color: rgba(42, 128, 224,.1);
  border-radius: 3px;
  margin: 15px 0;
  height: 30px;
  line-height: 30px;
  display: flex;
  font-size: 12px;
  .current-text {
    background-color: rgba(42, 128, 224,.15);
    border-radius: 3px 0 0 3px;
    color: var(--color-primary);
    width: 40px;
    text-align: center;

  }
  .current-info {
    width: calc(100% - 115px);
    display: flex;
    padding: 0 15px;
    justify-content: space-between;
    font-size: 14px;

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .code {
      margin-right: 20px;
    }
  }
  .to-add {
    color: var(--color-primary);
    width: 75px;
    padding-right: 15px;
    cursor: pointer;
  }
}
.el-input {
    width: 100%;
}

.el-select {
    width: 100%;
}

</style>
