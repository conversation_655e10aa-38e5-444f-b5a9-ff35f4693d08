<template>
  <Layout :content-loading="loading">
    <template slot="query">
      <div class="query-item">
        <span>上级设备编码: &nbsp;</span>
        <el-input placeholder="请输入上级设备编码" v-model="queryParams.deviceId" clearable> </el-input>
      </div>
      <div class="query-item">
        <span>上级设备名称: &nbsp;</span>
        <el-input placeholder="请输入上级设备名称" v-model="queryParams.deviceName" clearable> </el-input>
      </div>
      <div class="query-item">
        <span>设备IP: &nbsp;</span>
        <el-input placeholder="请输入设备IP" v-model="queryParams.deviceIp" clearable> </el-input>
      </div>
      <div class="query-item">
        <span>设备编号: &nbsp;</span>
        <el-input placeholder="请输入设备编号" v-model="queryParams.channelId" clearable> </el-input>
      </div>
      <div class="query-item">
        <span>设备名称: &nbsp;</span>
        <el-input placeholder="请输入设备名称" v-model="queryParams.channelName" clearable> </el-input>
      </div>
      <div class="query-item" >
        <span>设备在线状态: &nbsp;</span>
        <el-select v-model="queryParams.status" style="width: 150px;">
          <el-option label="全部" :value="null"></el-option>
          <el-option label="在线" :value="true"></el-option>
          <el-option label="离线" :value="false"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>是否录入平台: &nbsp;</span>
        <el-select v-model="queryParams.existed" style="width: 150px;">
          <el-option label="全部" :value="null"></el-option>
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="getRegisterDeviceList" :loading="queryLoading">查询</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="exportDataInfo()" :loading="exportLoading">导出</el-button>
      </div>

      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination
          small
          background
          :current-page.sync="table.page"
          :page-size="table.size"
          :total="table.data.length"
          layout="prev, pager, next, total"
        >
        </el-pagination>
      </div>
    </template>
    <template slot="content">
      <el-table
        class="el-table--radius"
        stripe
        :data="formatList"
        height="100%"
        border
        ref="table"
        highlight-current-row
        style="width: 100%"
      >
      <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"
                                 width="70"></el-table-column>
        <el-table-column prop="companyName" label="操作" min-width="60">
            <template slot-scope="{row}">
                <el-button type="text" title="录入设备" size="mini" @click="addTerminal(row)" :disabled="row.existed">
                    <i class="pony-iconv2 pony-xinzeng"></i>
                </el-button>
            </template>
        </el-table-column>
        <el-table-column
          :sortable="item.sortable"
          :prop="item.key"
          :label="item.name"
          :min-width="item.size"
          show-overflow-tooltip
          v-for="(item, index) in tableDataList"
          :key="index"
        >
        
          <template slot-scope="{ row }">
            <span v-if="item.type === 1">{{ item.key | dealTableSpecialData(row[item.key], that) }}</span>
            <span v-else>{{ row[item.key] || "-" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
  <addEquipment ref="addEquipment" @refresh="getRegisterDeviceList"></addEquipment>

  </Layout>
</template>

<script>
import moment from "moment";
import addEquipment from './components/addEquipment'

export default {
  name: "gB28181Register",
  components: {addEquipment},
  data() {
    return {
      that: this,
      // 查询列表参数
      queryParams: {
        deviceId: "",
        deviceName: "",
        deviceIp: "",
        channelId: "",
        channelName: "",
        channelIp: "",
        status:null,
        existed:null
      },

      loading: false,
      queryLoading: false,
      exportLoading: false,
      table: {
        data: [],
        page: 1,
        size: 30,
      },
      tableDataList: [
        // type: 0：正常展示; 1：需要变更展示; sortable 排序
        { sortable: false, name: "上级设备编码", key: "deviceId", size: "140", align: "center", type: 0 },
        { sortable: false, name: "上级设备名称", key: "deviceName", size: "140", align: "center", type: 0 },
        { sortable: false, name: "设备编号", key: "channelId", size: "140", align: "center", type: 0 },
        { sortable: false, name: "设备名称", key: "channelName", size: "140", align: "center", type: 0 },
        { sortable: false, name: "上级设备在线状态", key: "online", size: "140", align: "center", type: 1 },
        { sortable: true, name: "上级设备心跳时间", key: "keepaliveTime", size: "170", align: "center", type: 0 },
        { sortable: false, name: "设备在线状态", key: "status", size: "140", align: "center", type: 1 },
        { sortable: true, name: "设备心跳时间", key: "lastUpTime", size: "140", align: "center", type: 0 },
        { sortable: false, name: "录入平台", key: "existed", size: "140", align: "center", type: 1 },
        { sortable: false, name: "制造商", key: "manufacture", size: "140", align: "center", type: 0 },
        { sortable: false, name: "设备型号", key: "model", size: "140", align: "center", type: 0 },
        { sortable: false, name: "设备归属", key: "owner", size: "140", align: "center", type: 0 },
        { sortable: false, name: "行政区域", key: "civilCode", size: "140", align: "center", type: 0 },
        { sortable: false, name: "安装地址", key: "address", size: "140", align: "center", type: 0 },
        { sortable: false, name: "软件版本", key: "firmware", size: "140", align: "center", type: 0 },
        { sortable: false, name: "是否有子设备", key: "parental", size: "140", align: "center", type: 1 },
        { sortable: false, name: "父设备ID", key: "parentId", size: "140", align: "center", type: 0 },
        { sortable: false, name: "连接方式", key: "transport", size: "140", align: "center", type: 0 },
        { sortable: false, name: "设备IP", key: "deviceIp", size: "140", align: "center", type: 0 },
        { sortable: false, name: "摄像机类型", key: "ptzType", size: "140", align: "center", type: 1 },
        { sortable: true, name: "上次注册时间", key: "registerTime", size: "140", align: "center", type: 0 },
        { sortable: false, name: "注册有效期", key: "expires", size: "140", align: "center", type: 1 },
        { sortable: true, name: "创建时间", key: "createTime", size: "140", align: "center", type: 0 },
        { sortable: true, name: "修改时间", key: "updateTime", size: "140", align: "center", type: 0 },
      ],
    };
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },

    formatList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  filters: {
    dealTableSpecialData(key, val, that) {
      switch (key) {
        case "expires":
          return that.MillisecondDayFormat(val * 1000);
        case "online":
          return val ? "在线" : "离线";
        case "status":
          return val ? "在线" : "离线";
        case "parental":
          return val === 1 ? "是" : "否";
        case "existed":
          return val ? "是" : "否";
        case "ptzType":
          return val === 1 ? "球机" : val === 2 ? "半球" : val === 3 ? "固定枪机" : val === 4 ? "遥控枪机" : "-";
        default:
          return "-";
      }
    },
  },
  mounted() {
    this.getRegisterDeviceList();
  },

  methods: {
    addTerminal(row){
      this.$refs.addEquipment.showModal(row)
    },
    async getRegisterDeviceList() {
      this.queryLoading = true;
      let params = {
        operateType: 2,
        ...this.queryParams,
      };
      const registerDeviceList = await this.$api.getRegisterDevice(params);
      this.queryLoading = false;
      if (registerDeviceList.status !== 200) {
        this.$warning('获取 "注册设备列表" 失败');
        return;
      }
      this.table.data = registerDeviceList.data;
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    //导出表格
    async exportDataInfo() {
      if (this.table.data.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }
      let excelBody = this.table.data;
      let params = {};
      let paramsList = [];
      let sheetName = [];
      this.tableDataList.forEach((item) => sheetName.push(`${item.name}@${item.key + (item.type === 1 ? "View" : "")}@10000@000000`));
      excelBody.forEach((item) => {
        item.expiresView = this.MillisecondDayFormat(item.expires * 1000)
        item.onlineView = item.online ? "在线" : "离线";
        item.statusView = item.status ? "在线" : "离线";
        item.parentalView = item.parental === 1 ? "是" : "否";
        item.existedView = item.existed ? "是" : "否";
        item.ptzTypeView = item.ptzType ? "球机" : item.ptzType === 2 ? "半球" : item.ptzType === 3 ? "固定枪机" : item.ptzType === 4 ? "遥控枪机" : "-";
      })
      params = {
        sheetName: "28181联网注册设备",
        title: "28181联网注册设备",
        headers: sheetName,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.exportLoading = true;
      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "28181联网注册设备" + ".xlsx");
      this.exportLoading = false;
    },
  },
};
</script>
