<!--
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-09-19 14:39:20
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-09-27 14:44:27
 -->
<template>
    <div :class="['loginform', 'loginform--' + basicSetting.pattern]" :style="basicSetting.style">
        <div class="loginform__logo dfc" v-if="basicSetting.mainLogoTop"
             :style="basicSetting.mainLogoTopStyle ? basicSetting.mainLogoTopStyle : (basicSetting.mainLogoStyle ? basicSetting.mainLogoStyle : '')">
            <el-image :src="formatLogoTop" fit="fill"></el-image>
        </div>
        <div class="loginform__logo dfc" v-if="basicSetting.mainLogo"  
             :style="basicSetting.mainLogoStyle ? basicSetting.mainLogoStyle : ''">
            <el-image :src="formatLogo" fit="fill"></el-image>
        </div>
        <div class="loginform__content">
            <div class="loginform__form dfc">
                <div class="logingroup">
                    <i class="login-group-addon el-icon-user" v-if="basicSetting.loginIcon === false ? false: true"></i>
                    <p v-else>用户名</p>

                    <input class="form-control" v-model="form.user_name" :placeholder="$ct('nameMsg')"/>
                </div>
                <div class="logingroup">
                    <i class="login-group-addon el-icon-lock" v-if="basicSetting.loginIcon === false ? false: true"></i>
                    <p v-else>密码</p>

                    <input class="form-control" show-password v-model="form.pass_word" :placeholder="$ct('passMsg')"
                           :type="showPassWord" />
                    <el-button type="text" title="显示密码" @click="showPassWordStatus()" class="showIcon" v-if="company=='ywhj'">
                        <i class="pony-iconv2 pony-chakan"></i>
                    </el-button>
                </div>
            </div>
            <div class="loginform__tool" :style="textColor">
                <div v-if="basicSetting.rember" class="tool-btn" @click="rememberSwitch = !rememberSwitch">
                    <div :class="['login-checkBox', { active: rememberSwitch }]" :style="rememberSwitch ? basicSetting.remberColor ? {'background-color':basicSetting.remberColor} : backColor:''">
                        <div class="checkState"></div>
                    </div>
                    {{ $ct('rememberMsg') }}
                </div>
                <div v-if="basicSetting.forget" class="tool-btn">
                    {{ $ct('forgetMsg') }}
                </div>
            </div>
            <div class="loginform__btn">
                <div :style="`width: ${ basicSetting.loginBtnSize }`">
                    <button class="login-button" :style="backColor+basicSetting.loginBtnStyle" @click="loginSystem">{{ $ct('loginMsg') }}</button>
                </div>
            </div>
            <div class="loginform__extand">
                <ul slot="sponsor" class="ztc_custom">
                    <li v-for="(item, index) in basicSetting.extandLogo" :key="index">
                        <img :src="item | formatExtand"/>
                    </li>
                </ul>
            </div>
            <div class="errormsg">
                {{ errorStateMsg }}
                <!--<span>{{ errorStateMsg?$ct(`errorState[${ errorStateMsg }]`):'' }}</span>-->
            </div>
        </div>

        <!-- 忘记密码 -->
        <div class="loginform__content" style="display: none">

        </div>

    </div>
</template>

<script>
import {mapMutations,mapState} from 'vuex'
import {generateMixed} from '@/components/ztree/util/ZtreeMatics'
import Base64 from 'base-64';

export default {
    name: 'loginForm',
    components: {},
    data() {
        return {
            form: {
                user_name: '',
                pass_word: '',
            },
            rememberSwitch: false,
            forgetSwitch: false,
            errorStateMsg: '',
            showPassWord:'password',
            showstatus:false,
        };
    },

    props: {
        basicSetting: {
            type: Object,
            default: function () {
                return {}
            }
        }
    },

    watch: {
        'form.user_name'(newval) {
            this.errorStateMsg = ''
        },
        'form.pass_word'(newval) {
            this.errorStateMsg = ''
        },
        '$route.path':{
            handler(toPath,fromPath){
                // let company=toPath.split('/')[1]
                 this.showstatus=false
                this.showPassWord='password'
            },
            deep:true,
            immediate:true
        }
    },

    filters: {
        formatExtand(url) {
            return url ? `./static/imgNewVersion/userCustom/${url}.png` : ''
        },
    },

    computed: {
        ...mapState('main', ["company"]),

        formatLogoTop() {
            return './static/imgNewVersion/userCustom/' + this.basicSetting.mainLogoTop + '.png'
        },
        formatLogo() {
            return './static/imgNewVersion/userCustom/' + this.basicSetting.mainLogo + '.png'
        },
        textColor() {
            return `color: ${this.basicSetting.remberColor ? this.basicSetting.remberColor : this.basicSetting.mainColor}`
        },
        backColor() {
            return `background-color: ${this.basicSetting.mainColor};`
        },
    },

    mounted() {
        this.loadAccountInfo()
    },

    methods: {
        ...mapMutations('auth', ["setLoginState"]),
        ...mapMutations('main', ["toggleLoading"]),
        showPassWordStatus(){
                this.showstatus=!this.showstatus
                this.showPassWord=this.showstatus?"text":'password';
        },
        async loginSystem() {
            let accountInfo = this.form.user_name + "&" + this.form.pass_word;
            let newpass = this.changePassWordComplex()
            // let newpass = this.form.pass_word
            let result = await $.ajax({
                url: '/ponysafety2/login',
                type: 'post',
                data: JSON.stringify({user_name: this.form.user_name, pass_word: newpass}),
                headers: {
                    login_page: this.company,
                    'Content-Type':'application/json'
                },
            });
            // let result = await this.$api.login({user_name: this.form.user_name, pass_word: newpass})
            if (!result) {
                this.errorStateMsg = '系统错误'
                return
            }
            if (!result || result.RS != 1) {
                this.errorStateMsg = result.Reason
            } else {
                this.setLoginState(0);
                this.toggleLoading(true);
                this.$router.push("/home");
                if (this.rememberSwitch) {
                    this.setCookie("accountInfo", accountInfo, 1440 * 3);
                } else {
                    this.delCookie("accountInfo");
                }
            }
        },

        changePassWordComplex() {
            let prefix = generateMixed(7)
            let suffix = generateMixed(10)
            let complexpass = JSON.parse(JSON.stringify(this.form.pass_word))
            let splitlist = complexpass.split('')
            splitlist.splice(1, 0, prefix[1])
            splitlist.splice(5, 0, suffix[7])
            let passlenth = complexpass.length < 10 ? `0${complexpass.length}` : complexpass.length
            splitlist.push(passlenth)
            complexpass = splitlist.toString().replace(/,/g, '');
            return Base64.encode(`${prefix}dfbt${complexpass}sqdz${suffix}`)
        },

        setCookie(name, value, expiremMinutes) {
            let exdate = new Date();
            exdate.setTime(exdate.getTime() + expiremMinutes * 60 * 1000);
            if (pony.IS_WEB || process.env.NODE_ENV === 'development') {
                document.cookie = name + "=" + escape(value) + (expiremMinutes == null ? "" : ";expires=" + exdate.toGMTString());
            } else {
                const cookie = {
                    url: "http://www.ponyTech.cn",
                    name: name,
                    value: value,
                    expirationDate: exdate.getTime()
                };
                this.$electron.remote.session.fromPartition('persist:ponytech').cookies.set(cookie, (error) => {
                    if (error) console.error(error);
                });
            }
        },


        async getCookie(name) {
            if (pony.IS_WEB || process.env.NODE_ENV === 'development') {
                if (document.cookie.length > 0) {
                    let c_start = document.cookie.indexOf(name + "=");
                    if (c_start != -1) {
                        c_start = c_start + name.length + 1;
                        let c_end = document.cookie.indexOf(";", c_start);
                        if (c_end == -1) c_end = document.cookie.length;
                        return unescape(document.cookie.substring(c_start, c_end));
                    }
                }
                return "";
            } else {
                let result = await new Promise((resolve, reject) => {
                    this.$electron.remote.session.fromPartition('persist:ponytech').cookies.get({
                        url: "http://www.ponyTech.cn",
                        name,
                    }, function (error, cookies) {
                        if (cookies.length > 0) {
                            resolve(cookies[0].value)
                        } else {
                            resolve('')
                        }
                    });
                })
                return result;
            }
        },

        delCookie(name) {
            if (pony.IS_WEB) {
                let exp = new Date();
                exp.setTime(exp.getTime() - 1);
                let cval = this.getCookie(name);
                if (cval != null) {
                    document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
                }
            } else {
                this.$electron.remote.session.fromPartition('persist:ponytech').cookies.remove("http://www.ponyTech.cn", name, null)
            }
        },

        async loadAccountInfo() {
            let mySelf = this;
            let accountInfo = await mySelf.getCookie("accountInfo");
            if (Boolean(accountInfo) == false) {
                return false;
            } else {
                let userName = "";
                let passWord = "";
                let index = accountInfo.indexOf("&");
                userName = accountInfo.substring(0, index);
                passWord = accountInfo.substring(index + 1);
                mySelf.form.user_name = userName;
                mySelf.form.pass_word = passWord;
                mySelf.rememberSwitch = true;
            }
        },
    }
}

</script>

<style lang='scss' scoped>
.loginform {
    z-index: 2;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    &__logo {
        width: 100%;
        min-height: 80px;
        position: relative;
    }

    &__content {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-grow: 1;
    }

    &__form {
        width: 100%;
        flex-wrap: wrap;
        height: 35%;
        min-height: 140px;
    }

    &__tool {
        width: 100%;
        height: 35px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tool-btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: bold;
            font-size: 12px;
        }
    }

    &__btn {
        width: 100%;
        padding: 10px 0;
        display: flex;
        justify-content: center;

        .login-button {
            width: 100%;
            line-height: 35px;
            border-radius: 50px;
            color: #FFFFFF;
            border: 0;
            cursor: pointer;
            opacity: 0.85;
            outline: none;

            &:hover {
                opacity: 1;
            }
        }
    }

    &__extand {
        .ztc_custom {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: max-content;

            li {
                text-align: center;

                &:last-child {
                    border: 0;
                }
            }
        }
    }

}

.errormsg {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: red;
    position: absolute;
    bottom: -35px;
}

.login-checkBox {
    width: 14px;
    height: 14px;
    border: solid 1.5px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 12px;
    position: relative;

    .checkState {
        width: 5px;
        height: 8px;
        border-style: solid;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        position: absolute;
        top: 1px;
        left: 4px;
        display: none;
    }

    &.active {
        .checkState {
            display: block;
            color: #FFFFFF;
        }
    }
}


.logingroup {
    width: 100%;
    display: flex;
    align-items: center;

    .login-group-addon {
        text-align: center;
        padding: 0px 15px;
        font-size: 18px;
        font-weight: 500;
    }

    .form-control {
        height: 45px;
        width: 100%;
        border: 0;
        outline: none;
        line-height: 45px;
        background-color: transparent;

        &::-webkit-input-placeholder {
            color: #e5e5e5;
        }
    }
}

.loginform {
    &--black {
        color: #FFFFFF;
        background-color: rgba(49, 61, 79, 0.85);

        .logingroup {
            border-radius: 50px;
            background-color: rgba(255, 255, 255, 0.05);

            .form-control {
                color: #FFFFFF;

                &::-webkit-input-placeholder {
                    color: #e5e5e5;
                }
            }
        }
    }

    &--white {
        color: #97a2b6;
        background-color: #FFFFFF;

        .logingroup {
            background-color: transparent;
            border-bottom: solid 1px #e8eaed;

            .form-control {
                color: #97a2b6;

                &::-webkit-input-placeholder {
                    color: #97a2b6;
                }
            }
        }
    }

    &--nfhb {
        color: #546384;
        background-color: #eff6fe;

        .logingroup {
            background-color: transparent;
            border-bottom: solid 1px #546384;
            flex-direction: column;
            margin-top: 25px;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 35px;
                color: #546384 !important;
                font-size: 14px;
                margin-top: 8px;
                background-color: #eff6fe !important;
                // background-color: transparent !important;
                &::-webkit-input-placeholder {
                    color: #546384;
                }

            }
        }

        .loginform__tool {
            margin-top: 25px;
        }
    }

    &--picture {
        color: #97a2b6;
        background: url(~static/imgNewVersion/userCustom/ptzt_login_view.jpg);
        background-repeat: no-repeat;
        background-size: cover;

        .login-button {
            line-height: 45px;
        }

        .logingroup {
            background-color: transparent !important;
            border-bottom: solid 1px #2E3543;
            flex-direction: column;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 30px;
                color: #59B9EF;

                &::-webkit-input-placeholder {
                    color: #59B9EF;
                }
            }
        }

        .errormsg {
            bottom: -25px

        }
    }
    &--sgs {
        color: #8f9d90;
        background-color: #eff6fe;
        border-radius: 0px 14px 14px 0;
        // background: url(~static/imgNewVersion/userCustom/junk_login_view_right.png);
        background-size: 100% 100%;
        .login-button {
            line-height: 45px;
            font-size: 16px;
        }
        
        .logingroup {
           
            border-bottom: solid 1px #8f9d90;
            flex-direction: column;
            margin-bottom: 10px;
            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
              height: 40px;
                color: #5c6664;


                &::-webkit-input-placeholder {
                    color: #5c6664;

                }
            }
        }

        .errormsg {
            bottom: -25px

        }
    }
    &--ftcar {
      color: #8b95a5;
      background-color: rgba(255, 255, 255, 0.95);
       box-shadow: 0px 0px 16px 8px rgba(50, 102, 147,.2);
      .login-button {
            line-height: 45px;
            font-size: 16px;
        }
        .loginform__form {
          height: 55%;
        }
        .logingroup {
           
            border-bottom: solid 1px #97a2b6;
            flex-direction: column;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 30px;
                color: #5e6167;


                &::-webkit-input-placeholder {
                    color: #5e6167;

                }
            }
        }
    }
    &--junk {
        color: #8f9d90;
        background-color: #eff6fe;
        border-radius: 0px 14px 14px 0;
        // background: url(~static/imgNewVersion/userCustom/junk_login_view_right.png);
        background-size: 100% 100%;
        .login-button {
            line-height: 45px;
            font-size: 16px;
        }
        
        .logingroup {
           
            border-bottom: solid 1px #8f9d90;
            flex-direction: column;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 30px;
                color: #5c6664;


                &::-webkit-input-placeholder {
                    color: #5c6664;

                }
            }
            .showIcon{
                position:absolute;
                right: 5px;
                top: 100px;
            }
        }

        .errormsg {
            bottom: -25px

        }
    }
    &--jnfj {
        color: #5e6167;
        background-color: rgba(232, 234, 237,.9);
       
        .login-button {
            line-height: 45px;
            font-size: 16px;
        }
        
        .logingroup {
           
            border-bottom: solid 1px #97a2b6;
            flex-direction: column;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 30px;
                color: #5e6167;


                &::-webkit-input-placeholder {
                    color: #5e6167;

                }
            }
        }

        .errormsg {
            bottom: -25px

        }
    }
    &--huaian {
        color: #97a2b6;
        background: url(~static/imgNewVersion/userCustom/huaian_login.jpg);
        background-repeat: no-repeat;
        background-size: cover;

        .login-button {
            line-height: 45px;
            background-color: #0afafb;
        }

        .logingroup {
            width: 100%;
            display: flex;
            align-items: center;

            .login-group-addon {
                text-align: center;
                color: #0afafb !important;
                padding: 0px 15px;
                font-size: 18px;
                font-weight: 500;

                .el-icon-user {
                    color: #0afafb !important;

                }
            }


            border: solid 1px #0afafb;

            .form-control {
                height: 45px;
                width: 100%;
                border: 0;
                outline: none;
                line-height: 45px;
                color: #0afafb !important;
                background-color: transparent !important;

                &::-webkit-input-placeholder {
                    color: #0afafb !important;
                    // color: #0afafb;
                }
            }

        }

        .loginform__tool {
            color: #0afafb !important;
            padding: 0px 18px;
        }

        .loginform__btn {
            .login-button {
                background-color: #0afafb !important;
            }
        }

        .errormsg {
            bottom: -25px

        }
    }

    &--jinyu {
        color: #97a2b6;
        background-color: #FFFFFF;
        width: 370px!important;
        border-radius: 15px!important;
        box-shadow: 0 2px 12px 10px rgba(0, 0, 0, 0.1);

        .logingroup {
            background-color: transparent;
            border-bottom: solid 1px #e8eaed;
            flex-direction: column;
            margin-top: 15px;

            p {
                width: 100%;
                font-size: 12px;
            }

            .form-control {
                height: 35px;
                color: #546384 !important;
                font-size: 14px;
                margin-top: 8px;
                &::-webkit-input-placeholder {
                    color: #546384;
                }
            }
        }

        .loginform__tool {
            margin-top: 25px;
            color: rgb(15, 76, 129)!important;
        }

        .login-button {
            background-color: rgb(15, 76, 129)!important;
        }

        .errormsg {
            bottom: -25px
        }
    }
    &--qwts {
        left: 13%;
        top: 26%;
        .logingroup {
            background-color: transparent;
            flex-direction: column;
            margin-top: 15px;
            width: 100%;
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            p {
                width: 100%;
                font-size: 14px;
                color: #6b759c !important;
            }

            .form-control {
                border: solid 1px #3f4d74;
                border-radius: 10px;
                height: 55px;
                background-color: #262d4d !important;
                color: #9e9faa !important;
                font-size: 14px;
                padding-left: 10px !important;
                &::-webkit-input-placeholder {
                    color: #9e9faa;
                }
            }
        }

        .loginform__tool {
            color: #f24c4c !important;
            .tool-btn {
                display: flex;
                align-items: center;
                cursor: pointer;
                font-weight: bold;
                /* margin-top: 55px; */
                left: 50%;
                /* top: -8%; */
                bottom: 13%;
                position: absolute;
                font-size: 12px;
            }
        }
        .loginform__btn {
            width: 100%;
            padding: 10px 0;
            display: flex;
            justify-content: flex-start;
            .login-button {
                width: 50%;
                line-height: 45px;
                background-color: #f24c4c !important;
            }
        }
        // .login-button {
        //     background-color: #f24c4c!important;
        // }

        .errormsg {
            bottom: -25px;
        }
    }

}
</style>
