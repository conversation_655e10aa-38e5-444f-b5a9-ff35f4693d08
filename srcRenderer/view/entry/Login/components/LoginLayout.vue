<template>
    <div :class="['loginlayout', 
        'loginlayout--' + dataInfo.position,
        'loginlayout--' + dataInfo.pattern]">

        <el-image class="loginlayout__image" fit="fill" :src="formatLayoutBg"></el-image>
        

        <div class="loginlayout__content" v-if="dataInfo.viewPosition == 'centerview'">
            <slot></slot>
        </div>

        <div :class="['loginlayout__content', dataInfo.viewPosition]" v-else :style="dataInfo.shadow ? '':'box-shadow: none;'">
            <div class="view-left">
                
                <div :class="['view-personal',dataInfo.viewPositionBgc]" v-if="dataInfo.personalview">
                    <img :style="{ height: dataInfo.viewHeight }" :src="formatPersonView"/>
                </div>

                <el-carousel v-if="pictureView.length"
                    :height="dataInfo.viewHeight" 
                    style="width: 540px">
                    <el-carousel-item v-for="(item, index) in pictureView" :key="index">
                        <img :src="item.src">
                    </el-carousel-item>
                </el-carousel>

            </div>
            <div class="view-right">
                <slot></slot>
            </div>
        </div>
        <div class="loginlayout__bottomImg" v-if="dataInfo.name == 'sgs'">
          <p class="big">国际公认的检验、鉴定、测试和认证机构</p>
          <p>— Internationally recognized inspection, verification, testing and — certification body</p>
          <p>— Copyright © 2023 SGS</p>
        </div>
        <div class="loginlayout__copyRight" v-if="dataInfo.copyRight">
            copyright© {{ currentYear }} <a href='http://www.ponytech.cn' target="_Blank">Ponytech</a> Co. Ltd. All Rights Reserved.
        </div>
    </div>
</template>

<script>

export default {
    name: '',
    components: {  },
    data () {
        return {
            currentYear: new Date().getFullYear(),

            pictureList: [
                { index: 1, src: './static/imgNewVersion/userCustom/loginJob/login_bus.png' },
                { index: 2, src: './static/imgNewVersion/userCustom/loginJob/login_hy.png' },
                { index: 3, src: './static/imgNewVersion/userCustom/loginJob/login_ll.png' },
                { index: 4, src: './static/imgNewVersion/userCustom/loginJob/login_tc.png' },
                { index: 5, src: './static/imgNewVersion/userCustom/loginJob/login_wh.png' },
                { index: 6, src: './static/imgNewVersion/userCustom/loginJob/login_xc.png' },
                { index: 7, src: './static/imgNewVersion/userCustom/loginJob/login_net.png' },
            ]

            
        };
    },

    props: {
        dataInfo: {
            type: [Object],
            default: function() {
                return {}
            }
        }
    },

    computed: {

        pictureView() {
            return this.pictureList.filter(item => this.dataInfo.defaultViews.includes(item.index))
        },

        formatLayoutBg() {
            let special = Boolean(this.dataInfo.background.split(".").length > 1)
            let prefix = './static/imgNewVersion/userCustom/'
            return special?`${prefix}${ this.dataInfo.background }`:`${prefix}${ this.dataInfo.background }.jpg`
        },

        formatPersonView() {
            return `./static/imgNewVersion/userCustom/${ this.dataInfo.personalview }.png`
        },
    },

    mounted() {

    },

    methods: {

    }
}

</script>

<style lang='scss' scoped>
.loginlayout {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .view-personal {
        background-color: var(--color-white);
        img {
            display: block;
        }
    }
    .nobgc {
        background-color: transparent;
    }

    &__image {
        width: 100%;
        height: 100%;
        position: fixed;
        background-position: center center;
        background-size: cover;
        z-index: -1;
        top: 0;
        left: 0;
    }
    &__content {
        display: flex;
        color: #777777;

        &.viewonleft {
            flex-direction: row-reverse;
        }
        &.viewonright {
            flex-direction: row;
        }
    }
    &__bottomImg {
        width: 100%;
        height: auto;
        position: absolute;
        // background-position: center center;
        // background-size: cover;
        // z-index: -1;
        bottom: 5%;
        left: 0;
        text-align: center;
        color: #333333;
        font-size: 12px;
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, "microsoft yahei", arial, STHeiTi, sans-serif !important;
        .big {
          font-size: 18px;
        }
    }
    &__copyRight {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 20px;
        
        a {
            font-weight: bold;
            font-size: 14px;
            color: #ff6934;
        }
    }

    &--black {
        color: #e5e5e5;
        background: linear-gradient(0deg, rgba(30, 30, 30, 0.4), rgba(0, 193, 222, 0.33));
    }
    &--white {
        color: #777777;
        .loginlayout__content {
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        }
    }

    &--left {
        .loginlayout__content {
            position: absolute;
            left: 10%;
        }
    }
    &--right {
        .loginlayout__content {
            position: absolute;
            right: 10%;
        }
    }
    &--jhjy {
      .view-personal {
        background-color: transparent;
    }
    }
}
</style>
