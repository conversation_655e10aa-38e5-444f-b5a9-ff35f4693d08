<template>
  <div class="comprehensiceLeft">
    <div class="comprehensice">
      <div class="comprehensiceIcon">
        <!-- <img
          src="../../../../../../../static/imgNewVersion/demonstration/icon_PT.png"
        /> -->
        <div class="descTitle">
          <div class="icon"></div>
          <span>车辆总数</span>
        </div>
        <div class="descNum">{{ overview.numTotal }} </div>
        <div class="descLine"></div>
      </div>
      <div class="comprehensiceDesc">
        <div class="descItem">
          <div class="descTitle">
            <div class="icon"></div>
            <span>车辆在线数</span>
          </div>
          <div class="descNum">{{ overview.numOnline }}</div>
          <div class="descLine"></div>
        </div>
        <div class="descItem">
          <div class="descTitle">
            <div class="icon"></div>
            <span>报警总数</span>
          </div>
          <div class="descNum alarmColor">{{ overview.numAlarm }}</div>
          <div class="descLine"></div>
        </div>
        <div class="descItem">
          <div class="descTitle">
            <div class="icon"></div>
            <span>运单总量</span>
          </div>
          <div class="descNum">{{ overview.numBill }}</div>
          <div class="descLine"></div>
        </div>
        <div class="descItem">
          <div class="descTitle">
            <div class="icon"></div>
            <span>成本合计</span>
          </div>
          <div class="descNum">{{ overview.numCost }}</div>
          <div class="descLine"></div>
        </div>
      </div>
    </div>
    <div class="comprehensiceAlarm">
      <div ref="alarmDetail" class="alarmDetailList">
        <vue-seamless-scroll
          :data="alarmDetailList"
          :style="scrollStyle"
          :class-option="classOption"
        >
          <ul class="alarmInfo">
            <li
              v-for="(item, index) in alarmDetailList"
              :key="index"
              :style="bgColor(index)"
              @dblclick="toAlarmDetails(item)"
            >
              <div class="alarmName">
                <img :src="item.alarmUrl" class="alarmIcon" />
                
                <span>
                  {{
                  item.alarmName
                }}
                </span>

              </div>
              <span class="plateNo">{{ item.plateNo }}</span>
              <span class="alarmNum">{{ item.alarmTime }}</span>
            </li>
          </ul>
        </vue-seamless-scroll>
      </div>
    </div>
    <div class="comprehensiceWayBill">
      <div class="wayBill" ref="wayBillEcharts"></div>
    </div>
  </div>
</template>
<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { ALARM } from "@/service/wsService";
import moment from "moment";
export default {
  name: "ComprehensiceLeft",
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      alarmDetailList: [],
      scrollStyle: {},
      classOption: {
        step: 0.5,
        openWatch: true,
        limitMoveNum: 6,
      },
      subscription: [],
      alarmObj: {},
      vehicleIdObj: {},
      wayBillEcharts: null,
      overview: {
        numOnline: 0,
        numAlarm: 0,
        numBill: 0,
        numCost: 0,
        numTotal:0
      },
    };
  },
  async mounted() {
    let [adasRes, dmsRes] = await Promise.all([
      this.$api.getSysAlarmTypes({
        alarm_type: 2,
        type: 2,
      }),
      this.$api.getSysAlarmTypes({ alarm_type: 1, type: 2 }),
    ]);
    if (adasRes.RS == 1 && dmsRes.RS == 1) {
      let list = adasRes.alarm_type_list.concat(dmsRes.alarm_type_list);
      list.forEach((item) => {
        this.alarmObj[item.code] = item;
      });
    }
    let result = await this.$api.getCarInfoAndGpsAlarm();
    result.data.lastGps.forEach((item) => {
      this.vehicleIdObj[item.id] = item.plate;
    });
    let height = window.getComputedStyle(this.$refs.alarmDetail).height;
    this.scrollStyle = {
      height: height,
      overflow: "hidden",
    };
    this.subscription.push(
      ALARM.subscribe((msg) => {
        if (msg.alarmFlag == 1) {
          this.handleAlarmRefresh(msg);
        }
      })
    );
  },
  methods: {
    toAlarmDetails(parmas){
      let alarmList = [parmas.alarmType]
      let query ={
        vehicle_id : JSON.stringify([parmas.vehicleId]),
        start_time: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        end_time: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        alarmtype_list:JSON.stringify(alarmList)
      }
      this.$router.push({
                path: "/home/<USER>",
                query: query,
      });
    },
    handleAlarmRefresh(msg) {
      let obj = {
        alarmUrl: "",
        alarmName: "",
        plateNo: this.vehicleIdObj[msg.vehicleId],
        time: msg.beginTime,
      };
      let current = this.alarmObj[msg.alarmType];
      let className =
        current && current.className ? current.className : "OTHER";
      obj.alarmUrl = "./static/imgNewVersion/alarm/" + className + ".png";
      obj.alarmName = current && current.name ? current.name : "-";
      this.alarmDetailList.unshift(obj);
    },
    initData(data) {
      this.alarmDetailList = data.tableAlarm;
      this.overview = data.overview;
      this.alarmDetailList.forEach((item) => {
        let current = this.alarmObj[item.alarmType];
        let className =
          current && current.className ? current.className : "OTHER";
        item.alarmUrl = "./static/imgNewVersion/alarm/" + className + ".png";
        item.alarmName = current && current.name ? current.name : "-";
      });
      this.$echarts.init(this.$refs["wayBillEcharts"]).dispose();

      this.initWayBillEcharts(data);
    },
    initWayBillEcharts(data) {
      this.wayBillEcharts = this.$echarts.init(this.$refs["wayBillEcharts"]);
      if (data) {
        let xData = [];
        let numData = [];
        data.lineBill.forEach((item) => {
          xData.push(item.day);
          numData.push(item.num);
        });
        let options = {
          title: {
            show: true,
            text: "个",
            top: "0%",
            left: "8%",
            textStyle: {
              fontSize: 12,
              fontWeight: 400,
              //  fontFamily :'Courier New'
            },
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            show: true,
            right: "1%",
            itemWidth: 30, // 图例标记的图形宽度。
            //   itemGap: 20, // 图例每项之间的间隔。
            itemHeight: 10, //  图例标记的图形高度。
            textStyle: {
              color: "#fff",
              fontSize: 12,
              padding: [0, 8, 0, 8],
            },
          },
          grid: {
            containLabel: true,
            right: "5%",
            left: "5%",
            bottom: "0%",
            top: "16%",
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: xData,
            axisLine: {
              lineStyle: {
                color: "#16323d",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#083358",
              },
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#16323d",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#c7dfe9",
              fontSize: 12,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#083358",
              },
            },
          },
          color: ["#29bed1"],
          series: [
            {
              name: "运单数量",
              data: numData,
              type: "line",
              smooth: true,
              symbol: "circle",
              showSymbol: true,
              z: 100,
              lineStyle: {
                normal: {
                  width: 3,
                  color: "#29bed1", // 线条颜色
                },
              },
              itemStyle: {
                normal: {
                  color: "#fff",
                  borderColor: "#11f7ac",
                  borderWidth: 2,
                },
              },
              symbolSize: 5,
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#186985", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "#0e4b68",
                    },
                    {
                      offset: 0.9,
                      color: "#003152", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
          ],
        };
        this.wayBillEcharts.setOption(options);
      }
    },
    bgColor(index) {
      if (index % 2 == 0) {
        return {
          backgroundColor: "#1a354d",
        };
      }
    },
  },
  beforeDestroy() {
    this.subscription.forEach((item) => {
      item.unsubscribe();
    });
  },
};
</script>
<style lang="scss" scoped demonstration>
.comprehensiceLeft {
  width: 100%;
  height: 100%;
  background: url("../../../../../../../static/imgNewVersion/demonstration/comprehensive_left_PT.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .comprehensice {
    width: 94%;
    height: 25%;
    margin-top: 12%;
    display: flex;
    align-items: center;
    padding: 4% 6% 0 0%;
    .comprehensiceIcon {
      width: 40%;
      height: 100%;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 0 4%;
      .descTitle {
        display: flex;
        align-items: center;
        .icon {
          width: 20px;
          height: 20px;
          background-color: #fff;
          border: 1px solid #00e7ea;
          transform: rotate(45deg);
          margin-right: 20px;
        }
      }
      .descNum {
        background-image: linear-gradient(
          to left,
          rgba(255, 0, 0, 0),
          rgba(50, 125, 171, 1)
        );
        text-align: center;
        line-height: 100px;
        font-size: 60px;
        font-weight: bold;
        margin-top: 15px;
        width: 100%;
        color: #00e7ea;
        letter-spacing: 8px;
      }
      .descLine {
        background-image: linear-gradient(
          to left,
          rgba(32, 90, 133, 1) 10%,
          rgba(7, 194, 236, 1) 50%,
          rgba(32, 91, 132, 1)
        );
        margin-top: 15px;
        height: 10px;
        width: 100%;
      }
    }
    .comprehensiceDesc {
      width: 60%;
      height: 100%;

      display: flex;
      flex-wrap: wrap;
      align-items: center;
      align-content: center;
      .descItem {
        width: 50%;
        height: 40%;
        display: flex;
        padding: 0 8px;
        flex-direction: column;
        .descTitle {
          display: flex;
          align-items: center;
          .icon {
            width: 20px;
            height: 20px;
            background-color: #fff;
            border: 1px solid #00e7ea;
            transform: rotate(45deg);
            margin-right: 20px;
          }
        }
        .descNum {
          background-image: linear-gradient(
            to left,
            rgba(255, 0, 0, 0),
            rgba(50, 125, 171, 1)
          );
          text-align: center;
          line-height: 40px;
          font-size: 35px;
          font-weight: bold;
          margin-top: 5px;
          width: 100%;
          color: #00e7ea;
        }
        .alarmColor {
          color: #d82e7e;
        }
        .descLine {
          background-image: linear-gradient(
            to left,
            rgba(32, 90, 133, 1) 10%,
            rgba(7, 194, 236, 1) 50%,
            rgba(32, 91, 132, 1)
          );
          margin-top: 10px;
          height: 10px;
        }
      }
    }
  }
  .comprehensiceAlarm {
    width: 94%;
    height: 25%;
    margin-top: 20%;
    .alarmDetailList {
      width: 100%;
      height: 100%;
      .alarmInfo {
        width: 100%;
        height: 100%;
        padding: 6%;
        li {
          display: flex;
          align-items: center;
          justify-content: space-around;
          line-height: 80px;
          padding: 0 2%;
          cursor: pointer;
          span{
              overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            }
          .alarmIcon {
            width: 50px;
            height: 50px;
            margin-right: 10px;
          }
          .plateNo {
            width: 20%;
          }
          .alarmName {
            width: 40%;
            display: flex;
            align-items: center;
            padding-right: 5px;
          
            span{
              overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            }
          }
          .alarmNum {
            width: 40%;
          }
        }
      }
    }
  }
  .comprehensiceWayBill {
    width: 94%;
    height: 25%;
    margin-top: 15%;
    .wayBill {
      width: 100%;
      height: 100%;
      padding: 4% 0;
    }
  }
}
</style>
