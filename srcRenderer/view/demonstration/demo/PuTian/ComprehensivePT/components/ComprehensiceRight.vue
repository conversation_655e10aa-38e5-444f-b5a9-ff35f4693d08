<template>
  <div class="comprehensiceRight">
    <div class="vehicleRating">
      <ul class="vehicleRatingList">
        <li v-for="(item, index) in vehicleRatingList" :key="index">
          <div :class="['item',`bg${index}`]">
            <span :class="`status${index}`">{{ `TOP${index + 1}` }}</span>
            <span :class="['name',`status${index}`]" :title="item.plateNo">{{ item.plateNo }}</span>
            <span class="text">{{ `${item.score}分` }}</span>
          </div>
        </li>
      </ul>
    </div>
    <div class="vehicleFault">
      <div class="vehicleFaultEcharts" ref="vehicleFaultEcharts"></div>
    </div>
    <div class="driverAction">
      <div class="driverActionEcharts" ref="driverActionEcharts"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: "ComprehensiceRight",
  data() {
    return {
      vehicleFaultEcharts: null,
      driverActionEcharts: null,
      vehicleRatingList: [
        
      ],
    };
  },
  async mounted() {
  },
  methods: {
    initData(data){
      this.vehicleRatingList = data.tableScore
      this.$echarts.init(this.$refs["vehicleFaultEcharts"]).dispose()
      this.$echarts.init(this.$refs["driverActionEcharts"]).dispose()
      this.initVehicleFaultEcharts(data)
      this.initDriverActionEcharts(data)
    },
    initVehicleFaultEcharts(data){
      this.vehicleFaultEcharts = this.$echarts.init(this.$refs["vehicleFaultEcharts"]);
      if(data){
        let xData = []
        let numData = []
          data.lineFault.forEach(item=>{
          xData.push(item.day)
          numData.push(item.num)
        })
      let options = {
        title: {
                    show: true,
                    text: '个',
                    top: '0%',
                    left:'8%',
                    textStyle: {
                        fontSize: 12,
                        fontWeight: 400
                        //  fontFamily :'Courier New'
                    }

                },
        tooltip: {
          trigger: "axis",
          
        },
        legend: {
          show: true,
          right: "1%",
          itemWidth: 30, // 图例标记的图形宽度。
          //   itemGap: 20, // 图例每项之间的间隔。
          itemHeight: 10, //  图例标记的图形高度。
          textStyle: {
            color: "#fff",
            fontSize: 12,
            padding: [0, 8, 0, 8],
          },
        },
        grid: {
          containLabel: true,
          right: "5%",
          left: "5%",
          bottom: "0%",
          top: "16%",
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xData,
          axisLine: {
            lineStyle: {
              color: "#16323d",
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "#083358",
            },
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
            lineStyle: {
              color: "#16323d",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#c7dfe9",
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#083358",
            },
          },
        },
        color: ["#29bed1"],
        series: [
          {
            name: "原车故障数量",
            data: numData,
            type: "line",
            smooth: true,
            symbol: "circle",
            showSymbol: true,
            z: 100,
            lineStyle: {
                normal: {
                    width: 3,
                    color: '#f82b88', // 线条颜色
                },
            },
            itemStyle: {
              normal: {
                color: "#fff",
                borderColor: "#f82b88",
                borderWidth: 2,
              },
              
            },
            symbolSize: 5,
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#f82b88", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#3e2450",
                  },
                  {
                    offset: 0.9,
                    color: "#003152", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      };
      this.vehicleFaultEcharts.setOption(options);
      }
    },
    initDriverActionEcharts(data){
      this.driverActionEcharts = this.$echarts.init(this.$refs["driverActionEcharts"])
            if (data) {
              let total = 0
        let Data = data.pieDriving.map(item => {
            total += item.value
            return {
                value: item.value,
                name: item.label,
            }
        })
                let option = {
                    color: ['#00ffff', '#8fceff', '#e6307b', '#00baff', '#12B796'],
                    tooltip: {
                        show: true,
                        trigger: 'item',
                        formatter: function (parm) {
                            return `${parm.name}` + '<br/>' + `${parm.value}`
                        },
                        textStyle: {
                            color: "#fff"
                        },
                        backgroundColor: '#083157'
                    },
                    series: [{
                        type: 'pie',
                        radius: ['60%', '50%'],
                        center: ['50%', '50%'],
                        clockWise: false,
                        data: Data,
                        startAngle: 90,
                        itemStyle: {
                            normal: {
                                borderColor: '#052237',
                                borderWidth: 0
                            }
                        },
                        labelLine: {
                            normal: {
                                length: 30,
                                length2: 35,
                                // lineStyle: {
                                //     color: '#24DCF7'
                                // }
                            }
                        },
                        label: {
                            textStyle: {
                                align: 'left'
                            },
                            formatter: '{a|{d}%}\n {b|{b}: }{c|{c}}',
                            padding: [0, 0, 0, 0],
                            rich: {
                                a: {
                                    fontSize: 12,
                                    padding: [0, 10, 6, 4],
                                    color: '#fff'
                                },
                                b: {
                                    fontSize: 12,
                                    color: '#fff'
                                },
                                c: {
                                    fontSize: 14,
                                    color: '#00cace'
                                }
                            }
                        }
                    },
                    {
                        // 短刻度节点线
                        type: 'gauge',
                        startAngle: 270, // 270
                        endAngle: -89.9999,
                        center: ['50%', '50%'],
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false
                        },
                        radius: '100%',
                        splitNumber: '60', // 42
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: [
                                    [1, '#1755ff'] // 01ADF8 1755ff
                                ],
                                width: 10
                            }
                        },
                        splitLine: {
                            length: 8,
                            lineStyle: {
                                width: 2,
                                color: '#0177ba',
                                distance: 10,
                            }
                        },
                        detail: {
                            show: false
                        },
                    },
                    {
                        // 长刻度节点线
                        type: 'gauge',
                        startAngle: 270, // 270
                        endAngle: -89.9999,
                        center: ['50%', '50%'],
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false
                        },
                        radius: '100%',
                        splitNumber: '12',
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: [
                                    [1, '#0099e8'] // 01ADF8 1755ff
                                ],
                                width: 10
                            }
                        },
                        splitLine: {
                            length: 10,
                            lineStyle: {
                                width: 2,
                                color: '#00ADF9',
                                distance: 10,
                            }
                        },
                        detail: {
                            show: false
                        },
                    },
                    {
                        name: '告警总数',
                        type: 'pie',
                        radius: ['0%', '0%'],
                        itemStyle: {
                            color: 'transparent'
                        },
                        label: {
                            position: 'inside',
                            formatter: `{data|{c}}\n{serie|{a}}`,
                            rich: {
                                data: {
                                    fontWeight: '600',
                                    fontSize: 20,
                                    color: `#da2d75`,
                                    lineHeight: 40,
                                    textBorderColor: `transparent`,
                                    textBorderWidth: 0
                                },
                                serie: {
                                    fontSize: 13,
                                    color: `#fff`,
                                    textBorderColor: `transparent`,
                                    textBorderWidth: 0
                                }
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [total]
                    }

                    ]
                };
                this.driverActionEcharts.setOption(option)
            }
    }
  },
};
</script>
<style lang="scss" scoped demonstration>
.comprehensiceRight {
  width: 100%;
  height: 100%;
  background: url("../../../../../../../static/imgNewVersion/demonstration/comprehensive__right_PT.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .vehicleRating {
    width: 94%;
    height: 25%;
    margin-top: 14%;
    .vehicleRatingList {
      list-style: none;
      padding: 2% 4%;
      li {
        display: flex;
        /* align-content: center; */
        justify-content: center;
        color: #73e2f4;
        font-size: 27px;
        line-height: 55px;
        padding:10px 0;

        // font-size:30px;
        .item {
          width: 100%;
          display: flex;
          align-items: center;
          text-align: center;
          justify-content: space-evenly;
          border: 1px solid rgba(12, 50, 88,1);
          background-color: rgba(12, 45, 70,0.1);
          span{
            text-align: center;
            width: 30%;
            font-weight: 500;

          }

          .text {
            color: #63d0e8;
            text-align: center;
          }
          .name {
            color: #fff;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            width: 40%;
          }
          .status0 {

            color: #30f8f8;
          }
          .status2 {

            color: #d1d76f;
          }
          .status1 {

            color: #f89d63;
          }
          .status0 {

            color: #f75e61;
          }
        }
        .bg0{
          background-color: rgba(247, 94, 97,0.1);
          border: 1px solid rgba(247, 94, 97,0.2);
          border-radius: 5px;
          
        }
        .bg1{
          background-color: rgba(248, 157, 99,0.1);
          border: 1px solid rgba(248, 157, 99,0.2);
          border-radius: 5px;
        }
        .bg2{
          background-color: rgba(209, 215, 111,0.1);
          border: 1px solid rgba(209, 215, 111,0.2);
          border-radius: 5px;
        }
      }
    }
  }
  .vehicleFault {
    width: 94%;
    height: 25%;
    margin-top: 20%;

    .vehicleFaultEcharts {
      width: 100%;
      height: 100%;
      padding: 4% 0;
    }
  }
  .driverAction {
    width: 94%;
    height: 24%;
    margin-top: 15%;

    .driverActionEcharts {
      width: 100%;
      height: 100%;
      padding: 4% 0;
    }
  }
}
</style>
