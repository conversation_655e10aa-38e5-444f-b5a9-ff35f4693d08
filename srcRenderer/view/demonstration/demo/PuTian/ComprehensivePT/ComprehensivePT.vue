<template>
  <div class="ComprehensivePT">
    <div class="title">
      <ComprehensiceTitle v-if="changeLayer"></ComprehensiceTitle>
    </div>
    <div class="ComprehensivePTContent">
      <div class="ComprehensivePTLeft">
        <ComprehensiceLeft
          ref="ComprehensivePTLeft"
          v-if="changeLayer"
        ></ComprehensiceLeft>
      </div>
      <div class="ComprehensivePTrMap">
        <MapLayer ref="MapLayer" ></MapLayer>
      </div>
      <div class="ComprehensivePTRight">
        <ComprehensiceRight
          ref="ComprehensivePTRight"
          v-if="changeLayer"
        ></ComprehensiceRight>
      </div>
    </div>
  </div>
</template>
<script>
import ComprehensiceLeft from "./components/ComprehensiceLeft.vue";
import ComprehensiceRight from "./components/ComprehensiceRight.vue";
import ComprehensiceTitle from "./components/ComprehensiceTitle.vue";
import MapLayer from "../components/mapLayerPT.vue";
export default {
  name: "comprehensivePT",
  components: {
    ComprehensiceLeft,
    ComprehensiceRight,
    ComprehensiceTitle,
    MapLayer,
  },
  data() {
    return {
      totalData: {},
      timer: null,
      changeLayer: true,
    };
  },
  async mounted() {
    this.initData();
    window.addEventListener("resize", this.reflash);

    this.timer = setInterval(this.initData, 60 * 1000);
  },
  methods: {
    reflash() {
      this.changeLayer = false;
      setTimeout(() => {
        this.changeLayer = true;
        this.initData();

      }, 100);
    },
    async initData() {
      let result = await this.$api.complexPT({ key: "" });
      if (result.status == 200) {
        this.initComprehensivePTLeftData(result.data);
        this.initComprehensivePTRightData(result.data);
        this.$refs.MapLayer.handleMapPoint(result.data.mapPoint);
      }
    },
    initComprehensivePTLeftData(data) {
      this.$refs.ComprehensivePTLeft.initData(data);
    },
    initComprehensivePTRightData(data) {
      this.$refs.ComprehensivePTRight.initData(data);
    },
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
};
</script>
<style lang="scss" scoped demonstration>
.ComprehensivePT {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #06142c;
  .title {
    width: 100%;
    height: 8%;
  }
  .ComprehensivePTContent {
    width: 100%;
    height: 92%;
    padding: 1% 0;
    position: relative;
    .ComprehensivePTLeft {
      height: 90%;
      width: 22%;
      margin: 0 1%;
      padding: 0 20px;
      z-index: 99;
      position: absolute;
    }
    .ComprehensivePTrMap {
      height: 97%;
      width: 100%;
      z-index: 1;
      position: absolute;
    }
    .ComprehensivePTRight {
      height: 90%;
      width: 22%;
      margin: 0 1%;
      padding: 0 20px;
      z-index: 99;
      position: absolute;
      right: 0;
    }
  }
}
</style>
