<!--
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-08-14 09:43:55
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-29 15:21:05
 -->
<template>
    <div class="mapLayer">
        <OnlineMap ref="onlineMap" @mapReady="generateMapObj"></OnlineMap>
        <vehiclePupop ref="vehiclePupop" :vehiclePupopInfo="vehiclePupopInfo" v-show="showPupop" @closePupop="closePupop" :loading="vehicleLoading"></vehiclePupop>
    </div>
</template>

<script>
import { GPS, TerminalLink, StatsInfo } from "@/service/wsService";
import OnlineMap from "./GaodeOnlineMap";
import { wgs84togcj02, bd09togcj02 } from "@/view/monitor/util/mapUtil";
import {
    getVehicleState,
    checkCarRunState,
    getMapBasicIconRequire,
    getVehicleMapIcon,
    checkCarOffLine,
    getVehicleOnlineMapIcon,
    getOnlineMapBasicIconRequire,
    getFenseLayerByCustomOnline,
    transSecondToHMSCN,
} from "@/view/monitor/util/monitorUtil.js";
import vehiclePupop from "./vehiclePupop";

import { initWsVehicleBasicData } from "@/view/monitor/page/Monitor/util/monitor.js";
export default {
    name: "onlineMap",
    components: { OnlineMap, vehiclePupop },
    data() {
        return {
            _map: null,
            vehicleInfo: {},
            vehicleJudgeObj: {},
            markerList: [],
            onlineList: [],
            cluster: null,

            source: {
                subscriptList: [],
                interVaList: [],
                gpsList: [],
                terminalLinkList: [],
            },
            vehiclePupopInfo: {},
            infoWindow: null,
            lineLaryer: null,
            totalVehicleList: [],
            onLineVehicleList: [],
            markerLayer: null,
            sinotransList: [],
            vehiclePupopInfo: {},
            showPupop: false,
            infoWindow: null,
            vehicleLineLayer: null,
            startPoint: null, //记录画线的起点
            vehicleLoading: false,
            showStatus: false, //天气的显示
            currentVehicleId: null, //当前弹窗的车辆ID
            queryList: {
                //查询前半段轨迹
                // startTime: moment('2021-02-25 14:00:00').valueOf(),
                // endTime: moment('2021-02-25 16:08:00').valueOf(),
                // vehicleId: 24312,

                startTime: moment().startOf("day").valueOf(),
                endTime: moment().endOf("day").valueOf(),
                vehicleId: 0,

                adasList: [],
                distractList: [],
                gpsList: [],
                operationList: [],
                getAll: true,
                getGps: true,
                getStop: true,
                getCan: false,
                getFuel: true,
                getAlarm: false,
                getEvent: false,
                getLock: true,
                getFenselO: true,
                getTemp: true,
                getLongTime: true,
                // statsBAreaIO:true
            },
        };
    },
    props: {
        //长途：2，短途：1
        sinotrans: {
            type: Number,
            default: 0,
        },
        //长途运单车辆
        vehicleList: {
            type: Array,
            default: function () {
                return [];
            },
        },
    },
    computed: {},
    watch: {
        vehicleList: function (val) {
            if (this.sinotrans == 2) {
                this.setLineFence();
            }
        },
    },
    async mounted() {
        await this.$refs["onlineMap"].waitForInit;
        await this.getVehicleJudge();
        await this.generateVehiclePoint();
        this.source.subscriptList.push(
            GPS.subscribe((msg) => this.source.gpsList.push(msg)),
            StatsInfo.subscribe((msg) => {
                this.handleVehicleMileState(msg);
            })
        );

        this.source.interVaList.push(
            setInterval(this.handleGpsRefresh, 1000),
            setInterval(this.handleOnlineRefresh, 30 * 1000),
            setInterval(this.handleLineRefresh,20 * 60 * 1000),
        );
    },
    methods: {
        async getVehicleJudge() {
            let result = await this.$api.getCommonListByKey({
                key: "vehicle_online_judge",
            });
            this.vehicleJudgeObj = result.data;
        },

        generateMapObj(bmap) {
            this._map = bmap;
        },
        async generateVehiclePoint() {
            this._vehicleMarkerBind = {};
            let result = await this.$api.getCarInfoAndGpsAlarm();
            this.markerList = [];
            this.totalVehicleList = [];
            this.onLineVehicleList = [];
            this.sinotransList = [];
            result.data.lastGps.forEach((item) => {
                item.judgeRule = this.vehicleJudgeObj[item.id];
                this.vehicleInfo[item.id] = item;
                if (!item.lat || !item.lng) return;
                if (item.sinotrans_usage !== this.sinotrans) return;
                //有长途运单的车辆才显示到地图上
                if (this.sinotrans == 2 && this.vehicleList.length > 0) {
                    let vehicleIndex = this.vehicleList.findIndex(
                        (it) => it.vehicleId == item.id
                    );
                    if (vehicleIndex == -1) return;
                }
                this.totalVehicleList.push(item.id);
                this.sinotransList.push(item.id);

                // item.acc = (item.terminalStatus & 0x1) === 1
                let state = getVehicleState(item);
                let gcj02 = wgs84togcj02(item.lng, item.lat);
                let icon = new AMap.Icon({
                    size: new AMap.Size(26, 26), // 图标尺寸
                    image: getVehicleOnlineMapIcon(state), // Icon的图像
                });
                let marker = new AMap.Marker({
                    position: gcj02,
                    offset: new AMap.Pixel(-6, -6),
                    icon: icon,
                    extData: { id: item.id },
                });
                marker.on("click", (e) => {
                    this.openVehiclePupop(marker);
                });
                if (!checkCarOffLine(state)) {
                    let rote = 0;
                    if (checkCarRunState(state)) {
                        rote = item.dire;
                    }
                    marker.setAngle(rote);
                    this.markerList.push(marker);
                    this.onLineVehicleList.push(item.id);
                    // this._tsrFeature.addLayer(marker)
                }
                this._vehicleMarkerBind[item.id] = marker;
            });
            this.markerLayer = new AMap.MarkerClusterer(this._map, this.markerList, {
                gridSize: 80, // 聚合网格像素大小,
            });
            this._map.setFitView(this.markerList);
            if (this.sinotrans == 2) {
                this.setLineFence();
            }
            this.$emit("getList", this.sinotransList);
            this.emitVehicleOnlineNum();
        },
        async setLineFence() {
            let fenseList = [];
            if (this.lineLaryer != null) {
                this.lineLaryer.clearOverlays();
                this.lineLaryer = null;
            }
            if (this.vehicleList.length) {
                this.vehicleList.forEach((item) => {
                    let path = [];
                    let polyline = null;
                    if (item.fensePointList.length) {
                        item.fensePointList.forEach((it) => {
                            let gcj02 = wgs84togcj02(it.lng, it.lat);
                            path.push(gcj02);
                        });
                        polyline = new AMap.Polyline({
                            path: path,
                            strokeColor: "#00f9fa",
                            strokeOpacity: 1,
                            strokeWeight: 4,
                            // 折线样式还支持 'dashed'
                            strokeStyle: "solid",
                            // strokeStyle是dashed时有效
                            lineJoin: "round",
                            lineCap: "round",
                            zIndex: 50,
                        });
                        fenseList.push(polyline);
                    }
                });
            }
            await this.$refs["onlineMap"].waitForInit;

            this.lineLaryer = new AMap.OverlayGroup(fenseList);
            this._map.add(this.lineLaryer);
        },
        handleGpsRefresh() {
            if (!this.source.gpsList.length) return;
            let handleList = this.source.gpsList.slice(0, 600);
            handleList.sort((a, b) => +a.gpsTime - +b.gpsTime);
            handleList.forEach((item) => {
                let sinotransIndex = this.sinotransList.findIndex(
                    (it) => it == item.id
                );
                if (sinotransIndex == -1) return;
                if (this.sinotrans == 2 && this.vehicleList.length > 0) {
                    let vehicleIndex = this.vehicleList.findIndex(
                        (it) => it.vehicleId == item.id
                    );
                    if (vehicleIndex == -1) return;
                }
                let marker = this._vehicleMarkerBind[item.id];
                let vehicle = this.vehicleInfo[item.id];
                if (marker && item.latlng[0]) {
                    let gcj02 = wgs84togcj02(item.latlng[1], item.latlng[0]);
                    marker.setPosition(gcj02);
                }

                if (this.vehicleJudgeObj[item.id] == 2) return;
                let state = getVehicleState(item);
                let icon = new AMap.Icon({
                    size: new AMap.Size(26, 26), // 图标尺寸
                    image: getVehicleOnlineMapIcon(state), // Icon的图像
                });
                let rotationAngle = 0;
                if (checkCarRunState(state)) {
                    rotationAngle = item.dire;
                }
                if (marker) {
                    marker.setIcon(icon);
                    marker.setAngle(rotationAngle);
                }
                vehicle.latlng = item.latlng;
                vehicle.gpsSpeed = item.gpsSpeed;
                let markerInd = this.onLineVehicleList.findIndex((it) => it == item.id);
                if (checkCarOffLine(state)) {
                    if (markerInd != -1) {
                        this.markerLayer.removeMarker(marker);
                        this.markerList.splice(markerInd, 1);
                        this.onLineVehicleList.splice(markerInd, 1);
                        if (item.id == this.vehiclePupopInfo.id && this.showPupop) {
                            this.closePupop();
                        }
                    }
                } else {
                    if (marker && item.latlng[0] && item.latlng[1]) {
                        let gcj02 = wgs84togcj02(item.latlng[1], item.latlng[0]);
                        marker.setPosition(gcj02);
                    }
                    if (markerInd == -1) {
                        this.markerLayer.addMarker(marker);
                        this.markerList.push(marker);
                        this.onLineVehicleList.push(item.id);
                    }
                    if (item.id == this.vehiclePupopInfo.id && this.showPupop == true) {
                        this.vehicleInfoRrfresh(marker);
                        this.infoWindow.open(this._map, marker.getPosition());
                    }
                }
            });
            this.emitVehicleOnlineNum();
            this.source.gpsList = this.source.gpsList.slice(
                handleList.length,
                this.source.gpsList.length
            );
        },
        async handleOnlineRefresh() {
            let result = await this.$api.getVehicleslastonlinetime();
            if (!result || result.status != 200 || !result.data.length) return;
            result.data.forEach((item) => {
                let sinotransIndex = this.sinotransList.findIndex(
                    (it) => it == item.vehicle_id
                );
                if (sinotransIndex == -1) return;
                if (this.sinotrans == 2 && this.vehicleList.length > 0) {
                    let vehicleIndex = this.vehicleList.findIndex(
                        (it) => it.vehicleId == item.vehicle_id
                    );
                    if (vehicleIndex == -1) return;
                }
                let marker = this._vehicleMarkerBind[item.vehicle_id];
                let vehicle = this.vehicleInfo[item.vehicle_id];
                if (!vehicle) return;
                if (!vehicle.acc) {
                    Object.assign(vehicle, { last_time_online: item.last_time_online });
                }
                let state = getVehicleState(vehicle);
                let icon = new AMap.Icon({
                    size: new AMap.Size(26, 26), // 图标尺寸
                    image: getVehicleOnlineMapIcon(state), // Icon的图像
                });
                let rotationAngle = 0;
                if (checkCarRunState(state)) {
                    rotationAngle = vehicle.dire;
                }
                if (marker) {
                    marker.setIcon(icon);
                    marker.setAngle(rotationAngle);
                }
                let markerInd = this.onLineVehicleList.findIndex(
                    (it) => it == item.vehicle_id
                );
                if (checkCarOffLine(state)) {
                    if (markerInd != -1) {
                        this.markerLayer.removeMarker(marker);
                        this.markerList.splice(markerInd, 1);
                        this.onLineVehicleList.splice(markerInd, 1);
                        if (item.vehicle_id == this.vehiclePupopInfo.id && this.showPupop) {
                            this.closePupop();
                        }
                    }
                } else {
                    if (marker) {
                        if (vehicle.latlng) {
                            let gcj02 = wgs84togcj02(vehicle.latlng[1], vehicle.latlng[0]);
                            marker.setPosition(gcj02);
                            // marker.setPosition([vehicle.latlng[1],vehicle.latlng[0]])
                        } else {
                            let gcj02 = wgs84togcj02(vehicle.lng, vehicle.lat);
                            marker.setPosition(gcj02);
                        }
                    }
                    if (markerInd == -1 && marker) {
                        this.markerLayer.addMarker(marker);
                        this.markerList.push(marker);
                        this.onLineVehicleList.push(item.vehicle_id);
                    }
                    if (
                        item.vehicle_id == this.vehiclePupopInfo.id &&
                        this.showPupop == true
                    ) {
                        this.vehicleInfoRrfresh(marker);
                        this.infoWindow.open(this._map, marker.getPosition());
                    }
                }
            });
            this.emitVehicleOnlineNum();
        },
        //车辆弹窗一直打开就二十分钟更新一次显示的线路
        handleLineRefresh() {
            if (this.showPupop && this.currentVehicleId != null) {
                this.showVehicleLine(this.currentVehicleId);
            } else {
                return;
            }
        },
        //打开地图弹窗
        openVehiclePupop(marker) {
            this.showPupop = true;
            if(this.sinotrans ==2) this.vehicleLoading = true;
            if (this.vehicleLineLayer != null) {
                this.vehicleLineLayer.clearOverlays();
                this.vehicleLineLayer = null;
            }
            this.$emit("getWeather", {
                    showStatus: false,
                    city: "",
                });
            this.infoWindow = new AMap.InfoWindow({
                isCustom: true,
                content: this.$refs.vehiclePupop.$el,
                offset: new AMap.Pixel(0, 0),
            });
            
            this.currentVehicleId = marker.De.extData.id;
            this.vehicleInfoRrfresh(marker);
            this.showVehicleLine(marker.De.extData.id);
            this.infoWindow.open(this._map, marker.getPosition());
            this.$emit("getVehicleId", marker.De.extData.id);
        },

        //点击车辆图标显示车辆规划路线
        async showVehicleLine(id) {
            if (this.vehicleLineLayer != null) {
                this.vehicleLineLayer.clearOverlays();
                this.vehicleLineLayer = null;
            }
            let index = this.vehicleList.findIndex((it) => it.vehicleId == id);
            if (index == -1) return;
            let startPoint = "";
            let endPoint = "";
            this.startPoint = null;
            let vehicle = this.vehicleList[index];
            let vehiclePosition = this.vehicleInfo[id];
            if (vehiclePosition.latlng) {
                startPoint = `${vehiclePosition.latlng[1]},${vehiclePosition.latlng[0]}`;
                // this.endPoint = [vehiclePosition.latlng[1],vehiclePosition.latlng[0]]
            } else {
                startPoint = `${vehiclePosition.lng},${vehiclePosition.lat}`;
            }
            this.queryList.startTime = vehicle.startTimeDB;
            this.queryList.endTime = moment()
                .endOf("days")
                .valueOf();
            this.queryList.vehicleId = id;
            endPoint = `${vehicle.receiveLng},${vehicle.receiveLat}`;
            let [planLineRes, playBackRes] = await Promise.all([
                this.$api.getLineBillRoute({
                    startPoint: startPoint,
                    endPoint: endPoint,
                }),
                this.$api.getGpsRecords(this.queryList),
            ]);
            let lineList = planLineRes.data.polyline.split(";");
            let path = [];
            lineList.forEach((item, index) => {
                let latlng = item.split(",");
                let gcj02 = bd09togcj02(latlng[0], latlng[1]);
                if (index == 0) {
                    this.startPoint = gcj02;
                }
                path.push(gcj02);
            });
            let playBackPath = [];
            playBackRes.data.gps.forEach((item) => {
                let arr = [];
                arr = [item.lng,item.lat];
                playBackPath.push(arr);
            });
            let polyline = new AMap.Polyline({
                path: path,
                strokeColor: "#00f9fa",
                strokeOpacity: 1,
                strokeWeight: 4,
                // 折线样式还支持 'dashed'
                strokeStyle: "solid",
                // strokeStyle是dashed时有效
                lineJoin: "round",
                lineCap: "round",
                zIndex: 50,
            });
            let playBackLine = new AMap.Polyline({
                path: playBackPath,
                strokeColor: "#00f9fa",
                strokeOpacity: 1,
                strokeWeight: 4,
                // 折线样式还支持 'dashed'
                strokeStyle: "solid",
                // strokeStyle是dashed时有效
                lineJoin: "round",
                lineCap: "round",
                zIndex: 50,
            });
            this.vehicleLineLayer = new AMap.OverlayGroup([polyline, playBackLine]);
            this._map.add(this.vehicleLineLayer);
            this.vehicleLoading = false;
        },
        //关闭弹窗
        closePupop() {
            this.showPupop = false;
            this.$emit("getVehicleId", null);
            if (this.vehicleLineLayer != null) {
                this.vehicleLineLayer.clearOverlays();
                this.vehicleLineLayer = null;
            }
            this.infoWindow.close();
            this.infoWindow = null;
            this.currentVehicleId = null;
            this.vehicleLoading = false,
            this.$emit("getWeather", {
                    showStatus: false,
                    city: "",
                });
        },

        //弹窗数据更新
        vehicleInfoRrfresh(marker) {
            let info = JSON.parse(
                JSON.stringify(this.vehicleInfo[marker.De.extData.id])
            );
            let path = [];
            this.showStatus = true;
            path.push(this.endPoint);
            if(this.sinotrans==2){
                let index = this.vehicleList.findIndex(
                (it) => it.vehicleId == marker.De.extData.id
            );
            let vehicle = this.vehicleList[index];
            let city = vehicle.receiveAddress.split("-");
            this.$emit("getWeather", {
                showStatus: this.showStatus,
                city: city[1],
            });
            }
            
            // let gcj02= marker.getPosition()
            // let point = [gcj02.lng,gcj02.lat]
            // path.push(point)
            // let polyline = new AMap.Polyline({
            //     path: path,
            //     strokeColor: "#00f9fa",
            //     strokeOpacity: 1,
            //     strokeWeight: 4,
            //     // 折线样式还支持 'dashed'
            //     strokeStyle: "solid",
            //     // strokeStyle是dashed时有效
            //     lineJoin: 'round',
            //     lineCap: 'round',
            //     zIndex: 50,
            // })
            // if(this.vehicleLineLayer!=null){
            // this.vehicleLineLayer.addOverlay(polyline)

            // }
            // this.endPoint = gcj02
            // initWsVehicleBasicData(info)
            this.vehiclePupopInfo = info;
            this.vehiclePupopInfo.display_time = transSecondToHMSCN(
                info.display_time
            );
        },
        emitVehicleOnlineNum() {
            this.$emit("change", this.onLineVehicleList.length);
        },
        handleVehicleMileState(msgList) {
            if (!msgList || !msgList.b.length) return;
            msgList.b.forEach((item) => {
                let vehicle = this.vehicleInfo[item.a];
                if (!vehicle) return;
                let stopType = item.c;
                let stopTime = item.d;
                let display_time = item.f;
                Object.assign(vehicle, {
                    stop_time: stopTime,
                    stop_type: stopType,
                    display_time: display_time,
                });
            });
        },
    },

    beforeDestroy() {
        this.source.subscriptList.forEach((item) => item.unsubscribe());
        this.source.subscriptList = [];
        this.source.interVaList.forEach((item) => clearInterval(item));
        this.source.interVaList = [];
    },
};
</script>

<style lang="scss" scoped demonstration>
.mapLayer {
    width: 100%;
    height: 100%;
    position: relative;

    &--tool {
        position: absolute;
        top: 28px;
        left: 866px;
        z-index: 10;
        display: flex;

        .card {
            height: 100px;
            width: 100px;
            background-color: rgba(40, 58, 97, 0.5);
            border: 2px solid #4e83cd;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 28px;
            &:first-child {
                margin-left: 0;
            }
            &.active {
                background-color: rgb(40, 58, 97);
            }
        }
    }
}
/deep/.amap-marker-label {
    position: relative;
    top: 0;
    right: 0;
    background-color: #24313e;
    border: 0;
}
* {
    @mixin position($index) {
        background-image: url("../../../../../../static/imgNewVersion/monitor/zhatuche.png");
        background-repeat: no-repeat;
        display: inline-block;
        image-rendering: -webkit-optimize-contrast;
        height: 52px;
        width: 52px;
        vertical-align: middle;
        background-position: (($index - 1) * -52px) 0;
        background-size: cover;
    }
    .soil {
        @include position(3);
    }
    .limit {
        @include position(2);
    }
    .xnc {
        @include position(5);
    }
}
</style>
