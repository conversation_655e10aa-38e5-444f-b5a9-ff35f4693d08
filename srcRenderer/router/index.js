import Vue from "vue";
import Router from "vue-router";
import store from "../store";
import { generateMixed } from "@/components/ztree/util/ZtreeMatics";
import Base64 from "base-64";

// 去掉路由重定向的那个的报错信息
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};

Vue.use(Router);

// 禁止使用动态引入
// function resolve(path) {
//     return () => import('@/view/' + path);
// }

let router = new Router({
  routes: [
    {
      path: "/:company",
      name: "getCompany",
      // beforeEnter:(to,form,next)=>{
      //     //alert('非登陆状态，不能访问此页面！');
      //     //next('/login');
      //     //判断是否登录  store.gettes.isLogin === false
      //     if(to.path == '/auto'){
      //         console.log(to.path);
      //         // next();
      //     }else{
      //         alert("还没有登录，请先登录！");
      //         next('/login');
      //     }
      // }
    },
    {
      path: "/:company/login",
      name: "login",
      component: () => import("@/view/entry/Login/Login"),
    },
    {
      path: "/:company/home",
      name: "home",
      component: () => import("@/view/entry/Home/Home"),
      redirect: "/:company/home/<USER>",
      meta: {
        permissions: [
          { label: "多语言修改", symbol: "home:i18nFileBuilder" },
          { label: "主题切换", symbol: "home:toggleTheme" },
          {
            label: "上级平台链路状态",
            symbol: "home:809SuperiorLinkStateIcon",
          },
        ],
      },
      children: [
        {
          path: "guide",
          component: () => import("@/view/entry/Guide/Guide"),
          name: "guide",
          meta: { title: "首页" },
        },
        {
          path: "dangerEventMonitor",
          component: () => import("@/view/monitor/page/DangerEventMonitor/DangerEventMonitor"),
          name: "dangerEventMonitor",
          meta: { title: "报警事件监控" },
        },
        {
          path: "riskMonitor",
          component: () => import("@/view/monitor/page/RiskMonitor/RiskMonitor"),
          name: "riskMonitor",
          meta: { title: "实时风险监控台" },
        },
        {
          path: "riskReport",
          component: () => import("@/view/monitor/page/RiskReport/RiskReport"),
          name: "riskReport",
          meta: { title: "风险监控台坐席报表" },
        },
        {
          path: "temperatureAlarmMonitor",
          component: () => import("@/view/monitor/page/TemperatureAlarmMonitor/TemperatureAlarmMonitor"),
          name: "temperatureAlarmMonitor",
          meta: { title: "温度报警监控" },
        },
        {
          path: "roleUserMgt",
          component: () => import("@/view/system/RoleUserMgt"),
          name: "roleUserMgt",
          meta: {
            title: "用户管理",
            permissions: [
              { label: "列表查询", symbol: "user:query" },
              { label: "新增", symbol: "user:add" },
              { label: "修改", symbol: "user:update" },
              { label: "删除", symbol: "user:delete" },
              { label: "报警配置", symbol: "rule:alarmset" },
              { label: "报警事件配置", symbol: "rule:alarmEventConfig" },
            ],
          },
        },
        {
          path: "roleTemplate",
          component: () => import("@/view/system/RoleTemplate"),
          name: "roleTemplate",
          meta: { title: "角色模板" },
        },
        {
          path: "checkFneseLose",
          component: () => import("@/view/business/CheckFenseLose"),
          name: "checkFenseLose",
          meta: { title: "缺失围栏查询" },
        },
        {
          path: "vehicleAccident",
          component: () => import("@/view/business/VehicleAccident"),
          name: "vehicleAccident",
          meta: { title: "事故录入" },
        },
        {
          path: "workOrder",
          component: () => import("@/view/business/WorkOrder"),
          name: "WorkOrder",
          meta: { title: "工单管理" },
        },
        {
          path: "workOrderInspection",
          component: () => import("@/view/business/WorkOrderInspection/WorkOrderInspection"),
          name: "WorkOrderInspection",
          meta: { title: "工单巡检" },
        },
        {
          path: "exposureTable",
          component: () => import("@/view/business/ExposureTable/ExposureTable"),
          name: "exposureTable",
          meta: { title: "曝光台" },
        },
        {
          path: "temperatureMonitor",
          component: () => import("@/view/business/TemperatureMonitor/TemperatureMonitor"),
          name: "temperatureMonitor",
          meta: { title: "实时监控" },
        },
        {
          path: "installWorkOrder",
          component: () => import("@/view/business/InstallWorkOrder/InstallWorkOrder"),
          name: "installWorkOrder",
          meta: { title: "安装工单" },
        },
        {
          path: "repairWorkOrder",
          component: () => import("@/view/business/RepairWorkOrder/RepairWorkOrder"),
          name: "repairWorkOrder",
          meta: { title: "维修申报" },
        },
        {
          path: "temperatureAlarmQuery",
          component: () => import("@/view/business/TemperatureAlarmQuery/TemperatureAlarmQuery"),
          name: "temperatureAlarmQuery",
          meta: { title: "报警查询" },
        },
        {
          path: "vehicleAlarm",
          component: () => import("@/view/business/VehicleAlarm"),
          name: "vehicleAlarm",
          meta: { title: "车辆报警" },
        },
        {
          path: "enterpriseAlarm",
          component: () => import("@/view/business/EnterpriseAlarm"),
          name: "enterpriseAlarm",
          meta: { title: "企业报警" },
        },
        {
          path: "erpList",
          component: () => import("@/view/report/ErpList/ErpList"),
          name: "erpList",
          meta: { title: "ERP统计" },
        },
        {
          path: "gpsMonitor",
          component: () => import("@/view/report/GpsMonitor/GpsMonitor"),
          name: "gpsMonitor",
          meta: { title: "GPS监控记录" },
        },
        {
          path: "safetyReport",
          component: () => import("@/view/report/SafetyReport/SafetyReport"),
          name: "safetyReport",
          meta: { title: "安全报表" },
        },
        {
          path: "checkReport",
          component: () => import("@/view/report/CheckReport/CheckReport"),
          name: "checkReport",
          meta: { title: "车辆检查报告" },
        },
        {
          path: "carDataSearch",
          component: () => import("@/view/report/CarDataSearch"),
          name: "carDataSearch",
          meta: { title: "车辆数据明细" },
        },
        {
          path: "coolingConditionMonitoring",
          component: () => import("@/view/report/CoolingConditionMonitoring/CoolingConditionMonitoring"),
          name: "coolingConditionMonitoring",
          meta: { title: "冷机状态监控" },
        },
        {
          path: "carFuelConsumption",
          component: () => import("@/view/report/CarFuelConsumption"),
          name: "carFuelConsumption",
          meta: { title: "车辆油耗查询" },
        },
        {
          path: "carFuelAnalysis",
          component: () => import("@/view/report/CarFuel/CarFuelAnalysisList"),
          name: "carFuelAnalysis",
          meta: { title: "油耗分析" },
        },
        {
          path: "carFuelCalendar",
          component: () => import("@/view/report/CarFuel/CarFuelCalendar"),
          name: "carFuelCalendar",
          meta: { title: "车辆油耗日历" },
        },
        {
          path: "temperatureQuery",
          component: () => import("@/view/business/TemperatureQuery"),
          name: "temperatureQuery",
          meta: { title: "温度查询" },
        },
        {
          path: "driverCheck",
          component: () => import("@/view/business/DriverCheck/DriverCheck"),
          name: "DriverCheck",
          meta: { title: "出车检查" },
        },
        {
          path: "vehicleCleaning",
          component: () => import("@/view/business/VehicleCleaning"),
          name: "VehicleCleaning",
          meta: { title: "洗车查询" },
        },
        {
          path: "companyRecord",
          component: () => import("@/view/business/CompanyRecord/CompanyRecord"),
          name: "companyRecord",
          meta: { title: "企业备案" },
        },
        {
          path: "muckMgt",
          component: () => import("@/view/business/MuckMgt/MuckMgt"),
          name: "muckMgt",
          meta: { title: "渣土处置证管理" },
        },
        {
          path: "licenseMgt",
          component: () => import("@/view/system/LicenseMgt"),
          name: "LicenseMgt",
          meta: { title: "证书管理" },
        },
        {
          path: "keyCarbinetMgt",
          component: () => import("@/view/system/KeyCarbinetMgt/KeyCarbinetMgt"),
          name: "keyCarbinetMgt",
          meta: { title: "钥匙柜管理" },
        },
        {
          path: "menuMgt",
          component: () => import("@/view/system/MenuTreeMgt/MenuTreeMgt"),
          name: "menuTreeMgt",
          meta: { title: "菜单管理" },
        },
        {
          path: "groupMgt",
          component: () => import("@/view/system/DepartmentTreeMgt"),
          name: "departmentTreeMgt",
          meta: { title: "部门管理" },
        },
        {
          path: "downloadExport",
          component: () => import("@/view/system/DownloadExport"),
          name: "downloadExport",
          meta: { title: "我的下载" },
        },
        {
          path: "areaMgt",
          component: () => import("@/view/system/AreaTreeMgt"),
          name: "areaTreeMgt",
          meta: { title: "行政区划管理" },
        },
        {
          path: "radioBroadCast",
          component: () => import("@/view/system/RadioBroadCast"),
          name: "radioBroadCast",
          meta: { title: "定时语音广播" },
        },
        {
          path: "loginMonitor",
          component: () => import("@/view/system/LoginMonitor"),
          name: "LoginMonitor",
          meta: { title: "账号在线监控" },
        },
        {
          path: "video1078",
          component: () => import("@/view/videoPlay/Video1078"),
          name: "video1078",
          meta: { title: "1078视频" },
        },
        {
          path: "video/playback",
          component: () => import("@/view/videoPlay/Playback1078"),
          name: "playback1078",
          meta: { title: "录像回放" },
        },
        {
          path: "video/playbackStandard",
          component: () => import("@/view/videoPlayStandard/PlaybackStandard"),
          name: "playbackStandard",
          meta: { title: "录像回放(部标)" },
        },
        {
          path: "videoFileDown",
          component: () => import("@/view/videoPlayStandard/VideoFileDown"),
          name: "videoFileDown",
          meta: { title: "录像下载" },
        },
        {
          path: "video/PlayOnlineStandard",
          component: () => import("@/view/videoPlayStandard/PlayOnlineStandard"),
          name: "PlayOnlineStandard",
          meta: { title: "实时视频" },
        },
        {
          path: "video/realtime",
          component: () => import("@/view/videoPlay/RealTime1078"),
          name: "realTime1078",
          meta: { title: "实时视频" },
        },
        {
          path: "video/realtimeIfrname",
          component: () => import("@/view/videoPlay/RealTime1078Ifrname"),
          name: "realTime1078Ifrname",
          meta: { title: "实时视频" },
        },
        {
          path: "video/pollingVideo",
          component: () => import("@/view/videoPlay/PollingVideo"),
          name: "pollingVideo",
          meta: { title: "视频轮询" },
        },
        {
          path: "video/realtimeV2",
          component: () => import("@/view/videoPlay/RealTime1078V2"),
          name: "realTime1078V2",
          meta: { title: "实时视频" },
        },
        {
          path: "theme",
          component: () => import("@/view/tool/ThemeTester"),
          name: "themeTester",
          meta: { title: "主题测试" },
        },
        {
          path: "monitor",
          component: () => import("@/view/monitor/page/Monitor/Monitor"),
          name: "monitor",
          meta: { title: "实时监控" },
        },
        // {
        //     path: 'maptest', component: () => import('@/view/monitor/page/mapViewTest'), name: 'mapViewTest',
        //     meta: {title: '测试页面',}
        // },
        {
          path: "FenseMonitor",
          component: () => import("@/view/monitor/page/FenseMonitor/FenseMonitor"),
          name: "FenseMonitor",
          meta: { title: "辖区监控" },
        },
        {
          path: "FenseMonitorV2",
          component: () => import("@/view/monitor/page/FenseMonitor/FenseMonitorV2"),
          name: "FenseMonitorV2",
          meta: { title: "辖区监控" },
        },
        // {
        //     path: 'Leaflet3dcharts', component: () => import('@/view/monitor/page/LeafletEchart/Leaflet3dcharts'), name: 'Leaflet3dcharts',
        //     meta: {title: '测试echart在leaflet上',}
        // },
        {
          path: "suspectedhgdl",
          component: () => import("@/view/monitor/page/SuspectedHGDL"),
          name: "SuspectedHGDL",
          meta: { title: "黑工地消纳场分析(大车)" },
        },
        {
          path: "suspectedhgds",
          component: () => import("@/view/monitor/page/SuspectedHGDS"),
          name: "SuspectedHGDS",
          meta: { title: "黑工地消纳场分析(小车)" },
        },
        {
          path: "alarmMap",
          component: () => import("@/view/monitor/page/AlarmMap/AlarmMap"),
          name: "alarmMap",
          meta: { title: "风险地图" },
        },
        {
          path: "driverActionMap",
          component: () => import("@/view/monitor/page/DriverActionMap/DriverActionMap"),
          name: "driverActionMap",
          meta: { title: "风险地图" },
        },
        {
          path: "liftingMap",
          component: () => import("@/view/monitor/page/LiftingMap/LiftingMap"),
          name: "liftingMap",
          meta: { title: "举升地图" },
        },
        {
          path: "workingMap",
          component: () => import("@/view/monitor/page/WorkingMap"),
          name: "workingMap",
          meta: { title: "作业地图" },
        },
        {
          path: "stationsMap",
          component: () => import("@/view/monitor/page/StationMap/StationsMap"),
          name: "StationsMap",
          meta: { title: "站点危险地图" },
        },
        {
          path: "operateMapV2",
          component: () => import("@/view/monitor/page/OperateMap/OperateMapV2"),
          name: "operateMap",
          meta: { title: "运营地图" },
        },
        {
          path: "limitSpeed",
          component: () => import("@/view/monitor/page/RoadLimitSpeed"),
          name: "roadLimitSpeed",
          meta: { title: "限速标识地图" },
        },
        {
          path: "timePosition",
          component: () => import("@/view/monitor/page/VehicleTimePosition"),
          name: "VehicleTimePosition",
          meta: { title: "定时定位车辆查询" },
        },
        {
          path: "alarmVideo",
          component: () => import("@/view/monitor/page/AlarmVideo"),
          name: "alarmVideo",
          meta: { title: "报警视频" },
        },
        {
          path: "lockMap",
          component: () => import("@/view/monitor/page/LockMap/LockMap"),
          name: "lockMap",
          meta: { title: "锁定跟踪" },
        },
        {
          path: "playback",
          component: () => import("@/view/monitor/page/PlayBackG7/PlayBackG7"),
          name: "playBack",
          meta: { title: "轨迹回放" },
        },
        {
          path: "vehicleTracks",
          component: () => import("@/view/monitor/page/VehicleTracks/VehicleTracks"),
          name: "vehicleTracks",
          meta: { title: "多车轨迹" },
        },
        {
          path: "playbacktemp",
          component: () => import("@/view/monitor/page/TermianlTempBack/TermianlTempBack"),
          name: "TermianlTempBack",
          meta: { title: "温度回放" },
        },
        {
          path: "fenseCollect",
          component: () => import("@/view/monitor/page/FenseCollect/FenseMgnt"),
          name: "fenseMgnt",
          meta: { title: "围栏采集" },
        },
        {
          path: "fenseProject",
          component: () => import("@/view/monitor/page/FenseCollect/FenseProject"),
          name: "fenseProject",
          meta: { title: "工程项目" },
        },
        {
          path: "fenseLineCollect",
          component: () => import("@/view/monitor/page/FenseCollect/FenseLineMgnt"),
          name: "fenseLineMgnt",
          meta: { title: "围栏采集(线路)" },
        },
        {
          path: "FenseBusMgnt",
          component: () => import("@/view/monitor/page/FenseCollect/FenseBusMgnt"),
          name: "FenseBusMgnt",
          meta: { title: "公交线路采集" },
        },
        {
          path: "fenseGroup",
          component: () => import("@/view/monitor/page/FenseCollect/FenseGroupMgnt"),
          name: "fenseGroupMgnt",
          meta: { title: "围栏分组" },
        },
        {
          path: "fenseRule",
          component: () => import("@/view/monitor/page/FenseCollect/FenseRule"),
          name: "fenseRule",
          meta: { title: "围栏规则" },
        },
        {
          path: "fenseRelation",
          component: () => import("@/view/monitor/page/FenseCollect/FenseRelation"),
          name: "FenseRelation",
          meta: { title: "围栏关联" },
        },
        {
          path: "alarmSetting",
          component: () => import("@/view/system/AlarmSetting/AlarmSetting"),
          name: "alarmSetting",
          meta: { title: "报警管理" },
        },
        {
          path: "vehicleCommand",
          component: () => import("@/view/standard/VehicleCommand/VehicleCommand"),
          name: "vehicleCommand",
          meta: { title: "指令下发" },
        },
        {
          path: "vehicleCommandList",
          component: () => import("@/view/standard/VehicleCommand/VehicleCommandList"),
          name: "VehicleCommandList",
          meta: { title: "终端属性" },
        },
        {
          path: "1078command",
          component: () => import("@/view/standard/1078command/1078command"),
          name: "Command1078",
          meta: { title: "1078指令下发" },
        },
        {
          path: "serviceset809",
          component: () => import("@/view/standard/ServiceSetting809/ServiceSetting809"),
          name: "ServiceSetting809",
          meta: { title: "809协议上报" },
        },
        {
          path: "riskDimension",
          component: () => import("@/view/report/RiskDimension"),
          name: "riskDimension",
          meta: { title: "风险维度报表" },
        },
        {
          path: "vehicleTravel",
          component: () => import("@/view/report/VehicleTravel/VehicleTravel"),
          name: "vehicleTravel",
          meta: { title: "车辆行程报表" },
        },
        {
          path: "riskFactorReport",
          component: () => import("@/view/report/RiskFactorReport"),
          name: "riskFactorReport",
          meta: { title: "风险系数报表" },
        },
        {
          path: "alarmDetailSearch",
          component: () => import("@/view/report/AlarmDetailSearch"),
          name: "alarmDetailSearch",
          meta: { title: "报警明细查询" },
        },
        {
          path: "alarmDetailSearchSK",
          component: () => import("@/view/report/AlarmDetailSearchSK"),
          name: "alarmDetailSearchSK",
          meta: { title: "赛科报警分析" },
        },
        {
          path: "siteCapacityAlarm",
          component: () => import("@/view/report/SiteCapacityAlarm/SiteCapacityAlarm"),
          name: "siteCapacityAlarm",
          meta: { title: "场地容量报警" },
        },
        {
          path: "vehicleAlarmStatistics",
          component: () => import("@/view/report/VehicleAlarmStatistics"),
          name: "vehicleAlarmStatistics",
          meta: { title: "车辆报警统计" },
        },
        {
          path: "businessViolationStatistics",
          component: () => import("@/view/report/BusinessViolationStatistics"),
          name: "businessViolationStatistics",
          meta: { title: "企业违规统计" },
        },
        {
          path: "ParkingLotSearch",
          component: () => import("@/view/report/ParkingLotSearch"),
          name: "ParkingLotSearch",
          meta: { title: "停车场进出查询" },
        },
        {
          path: "scoreMonitor",
          component: () => import("@/view/report/ScoreMonitor/scoreMonitor"),
          name: "scoreMonitor",
          meta: { title: "评分监控报表" },
        },
        {
          path: "scoreMonitorSK",
          component: () => import("@/view/report/ScoreMonitorSK/ScoreMonitorSK"),
          name: "scoreMonitorSK",
          meta: { title: "赛科评分报表" },
        },
        {
          path: "scoreMonitorPT",
          component: () => import("@/view/report/ScoreMonitorPT/scoreMonitorPT"),
          name: "scoreMonitorPT",
          meta: { title: " 莆田评分报表" },
        },
        {
          path: "squareReport",
          component: () => import("@/view/report/SquareReport/SquareReport"),
          name: "squareReport",
          meta: { title: " 方量报表（月）" },
        },
        {
          path: "reportList",
          component: () => import("@/view/customReport/ReportList"),
          name: "reportList",
          meta: { title: "个性化报表" },
        },
        {
          path: "reportPreview/:reportKey",
          component: () => import("@/view/customReport/Preview/KeepAlivePreview"),
          name: "keepAlivePreview",
          meta: {
            title: require("@/view/customReport/staticReport/staticReportMap").reportPageNameMap, //含参路径 个性化标签名方案
            titleKey: "reportKey", //和路径上的参数名保持一致
            icon: "report",
            noHint: true,
          },
        },
        {
          path: "terminalError",
          component: () => import("@/view/report/TerminalErrorSearch"),
          name: "TerminalErrorSearch",
          meta: { title: "车辆终端故障" },
        },
        {
          path: "vehicleTrack",
          component: () => import("@/view/report/VehicleRunTrack"),
          name: "vehicleRunTrack",
          meta: { title: "车辆行驶轨迹" },
        },
        {
          path: "ftVehicleRunTrack",
          component: () => import("@/view/report/FtVehicleRunTrack"),
          name: "ftVehicleRunTrack",
          meta: { title: "车辆轨迹" },
        },
        {
          path: "alarmHandlingStatistics",
          component: () => import("@/view/report/AlarmHandlingStatistics"),
          name: "alarmHandlingStatistics",
          meta: { title: "报警处理统计" },
        },
        {
          path: "refidreport",
          component: () => import("@/view/report/RefidReport"),
          name: "RefidReport",
          meta: { title: "RFID记录查询" },
        },
        {
          path: "vehicleTrip",
          component: () => import("@/view/report/VehicleTrip"),
          name: "vehicleTrip",
          meta: { title: "车辆行程统计" },
        },
        {
          path: "vehicleTrackSpeed",
          component: () => import("@/view/report/VehicleTrackSpeed"),
          name: "vehicleTrackSpeed",
          meta: { title: "轨迹超速统计" },
        },
        {
          path: "workingCondition",
          component: () => import("@/view/report/WorkingCondition/WorkingCondition"),
          name: "workingCondition",
          meta: { title: "车辆作业时长" },
        },
        {
          path: "workingTable",
          component: () => import("@/view/report/WorkingCondition/workingTableList"),
          name: "workingTableList",
          meta: { title: "作业时长" },
        },

        {
          path: "workingHours",
          component: () => import("@/view/report/WorkingCondition/WorkingHours"),
          name: "workingHours",
          meta: { title: "工作时长" },
        },
        {
          path: "scoreCompare",
          component: () => import("@/view/report/compare/ScoreCompare"),
          name: "scoreCompare",
          meta: { title: "对比报表" },
        },
        {
          path: "ledMgnt",
          component: () => import("@/view/custom/zhatuche/LedMgnt"),
          name: "ledMgnt",
          meta: { title: "车辆LED设置" },
        },
        {
          path: "blacklist",
          component: () => import("@/view/custom/zhatuche/BlackList"),
          name: "blacklist",
          meta: { title: "黑名单管理" },
        },
        {
          path: "soilReport",
          component: () => import("@/view/custom/zhatuche/SoilReport"),
          name: "soilReport",
          meta: { title: "挖土点统计" },
        },
        {
          path: "crossReport",
          component: () => import("@/view/custom/zhatuche/CrossReport"),
          name: "crossReport",
          meta: { title: "路口超速统计" },
        },
        {
          path: "limitReport",
          component: () => import("@/view/custom/zhatuche/LimitReport"),
          name: "limitReport",
          meta: { title: "限行区违规统计" },
        },
        {
          path: "xiaquReport",
          component: () => import("@/view/custom/zhatuche/XiaQuReport"),
          name: "xiaquReport",
          meta: { title: "辖区违规统计" },
        },
        {
          path: "circleLimitReport",
          component: () => import("@/view/custom/zhatuche/CircleLimitReport"),
          name: "circleLimitReport",
          meta: { title: "限速圈违规统计" },
        },
        {
          path: "passCheck",
          component: () => import("@/view/custom/zhatuche/PassCheck"),
          name: "passCheck",
          meta: { title: "通行证管理" },
        },
        {
          path: "roadBillMgt",
          component: () => import("@/view/custom/cangnan/RoadBillMgt/RoadBillMgt"),
          name: "roadBillMgt",
          meta: { title: "路单管理" },
        },
        {
          path: "greaseCollectionWorkMgt",
          component: () => import("@/view/custom/cangnan/GreaseCollectionWorkMgt/GreaseCollectionWorkMgt"),
          name: "greaseCollectionWorkMgt",
          meta: { title: "油脂清运作业" },
        },
        {
          path: "taskMgt",
          component: () => import("@/view/custom/cangnan/TaskMgt/TaskMgt"),
          name: "taskMgt",
          meta: { title: "任务管理" },
        },
        {
          path: "wastePointMap",
          component: () => import("@/view/custom/cangnan/WastePointMap/WastePointMap"),
          name: "wastePointMap",
          meta: { title: "垃圾点位热力图" },
        },
        {
          path: "collectDetail",
          component: () => import("@/view/custom/cangnan/CollectDetail"),
          name: "collectDetail",
          meta: { title: "收运明细" },
        },
        {
          path: "detainCarMgt",
          component: () => import("@/view/custom/cangnan/DetainCarMgt/DetainCarMgt"),
          name: "detainCarMgt",
          meta: { title: "扣车管理" },
        },
        {
          path: "garbageLocation",
          component: () => import("@/view/custom/cangnan/GarbageLocation/GarbageLocation"),
          name: "garbageLocation",
          meta: { title: "垃圾点位报表" },
        },

        {
          path: "passCheckJD",
          component: () => import("@/view/system/PassCheck/PassCheckJD"),
          name: "passCheckJD",
          meta: { title: "通行证审批(建德)" },
        },
        {
          path: "passCheckWY",
          component: () => import("@/view/system/PassCheck/PassCheckWY"),
          name: "passCheckWY",
          meta: { title: "通行证审批(武义)" },
        },
        {
          path: "passCheckJHnew",
          component: () => import("@/view/system/PassCheck/PassCheckJHNew"),
          name: "passCheckJHnew",
          meta: { title: "通行证审批(金华)" },
        },
        {
          //这个页面没有用
          path: "dregshandle",
          component: () => import("@/view/system/DregsHandle/DregsHandle"),
          name: "DregsHandle",
          meta: { title: "渣土处置证管理" },
        },
        {
          path: "passScoreSearch",
          component: () => import("@/view/system/PassCheck/PassScoreSearch"),
          name: "PassScoreSearch",
          meta: { title: "预申请数据查询" },
        },
        {
          path: "tablewidth",
          component: () => import("@/view/tool/TableWidthPicker"),
          name: "tablewidth",
          meta: { title: "表格宽度设置" },
        },
        {
          path: "generateTool",
          component: () => import("@/view/tool/MapLayerGenerateTool"),
          name: "generateTool",
          meta: { title: "生成工具" },
        },
        {
          path: "vehicleControl",
          component: () => import("@/view/custom/zhatuche/VehicleControl"),
          name: "VehicleControl",
          meta: { title: "车辆管控" },
        },
        {
          path: "TemperatureControl",
          component: () => import("@/view/custom/futian/TempControl/TempControl"),
          name: "TempControl",
          meta: { title: "温度管控" },
        },
        {
          path: "EnergizingRecording",
          component: () => import("@/view/custom/futian/FuelEvents/EnergizingRecording/EnergizingRecording"),
          name: "OilAnomaly",
          meta: { title: "加能记录" },
        },
        {
          path: "DrivingBehaviorStatistics",
          component: () => import("@/view/custom/guming/DrivingBehaviorStatistics/DrivingBehaviorStatistics"),
          name: "DrivingBehaviorStatistics",
          meta: { title: "驾驶行为分析" },
        },
        {
          path: "fuelAbnormal",
          component: () => import("@/view/custom/futian/FuelEvents/FuelAbnormal/FuelAbnormal"),
          name: "fuelAbnormal",
          meta: { title: "油量异常" },
        },
        {
          path: "faultParticulars",
          component: () => import("@/view/custom/futian/FaultDetails/FaultParticulars"),
          name: "faultParticulars",
          meta: { title: "故障明细查询" },
        },
        {
          path: "vehicleSubscribe",
          component: () => import("@/view/custom/futian/VehicleSubscribe/VehicleSubscribe"),
          name: "vehicleSubscribe",
          meta: { title: "福田车辆订阅" },
        },
        {
          path: "deviceAbnormalCheck",
          component: () => import("@/view/custom/vcare/DeviceAbnormalCheck/DeviceAbnormalCheck"),
          name: "deviceAbnormalCheck",
          meta: { title: "设备异常检查" },
        },
        {
          path: "carWayBill",
          component: () => import("@/view/custom/putian/CarWayBill/CarWayBillMgt"),
          name: "carWayBillMgt",
          meta: { title: "商品车订单" },
        },
        {
          path: "carWayBillAssignV2",
          component: () => import("@/view/custom/putian/CarWayBillAssignV2/CarWayBillMgtAssignV2"),
          name: "carWayBillMgtAssignV2",
          meta: { title: "运单分配" },
        },
        {
          path: "carWayBillAssignMgt",
          component: () => import("@/view/custom/putian/CarWayBillAssign/CarWayBillAssignMgt"),
          name: "carWayBillAssignMgt",
          meta: { title: "运单分配" },
        },
        {
          path: "costAudit",
          component: () => import("@/view/custom/putian/CarWayBillAssign/CostAudit"),
          name: "costAudit",
          meta: { title: "成本审核" },
        },
        {
          path: "photoCheck",
          component: () => import("@/view/custom/vcare/PhotoCheck/PhotoCheck"),
          name: "photoCheck",
          meta: { title: "相机检查" },
        },
        {
          path: "eventHandlingStatistics",
          component: () => import("@/view/custom/vcare/EventHandlingStatistics/EventHandlingStatistics"),
          name: "eventHandlingStatistics",
          meta: { title: "事件处理统计" },
        },
        {
          path: "longWayBill",
          component: () => import("@/view/custom/zhongwaiyun/LongWayBill/LongWayBill"),
          name: "longWayBill",
          meta: { title: "长途运单管理" },
        },
        {
          path: "monitorZX",
          component: () => import("@/view/custom/zhongxing/MonitorZX/MonitorZX"),
          name: "monitorZX",
          meta: { title: "实时监控" },
        },
        {
          path: "playbackZX",
          component: () => import("@/view/custom/zhongxing/PlaybackZX/PlaybackZX"),
          name: "playbackZX",
          meta: { title: "轨迹回放" },
        },
        {
          path: "logisticsElecLockControl",
          component: () => import("@/view/custom/zhongwaiyun/ElecLockControl/LogisticsElecLockControl"),
          name: "logisticsElecLockControl",
          meta: { title: "物流电子锁管控" },
        },
        {
          path: "logisticsLockStatusQuery",
          component: () => import("@/view/custom/zhongwaiyun/LockStatusQuery/LockStatusQuery"),
          name: "logisticsLockStatusQuery",
          meta: { title: "物流电子锁状态查询" },
        },
        {
          path: "temperatureDetails",
          component: () => import("@/view/monitor/page/TemperatureDetails"),
          name: "TemperatureDetails",
          meta: { title: "温度明细" },
        },
        {
          path: "humidityDetails",
          component: () => import("@/view/monitor/page/HumidityDetials/HumidityDetials.vue"),
          name: "humidityDetails",
          meta: { title: "湿度明细" },
        },
        {
          path: "TurnOverpeedConfig",
          component: () => import("@/view/custom/zhatuche/TurnOverspeedConfig"),
          name: "TurnOverspeedConfig",
          meta: { title: "左右转弯超速配置" },
        },
        {
          path: "inandoutrecord",
          component: () => import("@/view/business/InAndOutRecord"),
          name: "InAndOutRecord",
          meta: { title: "运输趟次统计" },
        },
        {
          path: "shortStopRecord",
          component: () => import("@/view/business/ShortStopRecord"),
          name: "shortStopRecord",
          meta: { title: "短倒趟次统计" },
        },
        {
          path: "passengerCarRecord",
          component: () => import("@/view/business/PassengerCarRecord"),
          name: "passengerCarRecord",
          meta: { title: "客车趟次统计" },
        },

        {
          path: "alarmMsgSend",
          component: () => import("@/view/business/AlarmMsgSend"),
          name: "alarmMsgSend",
          meta: { title: "报警下发信息配置" },
        },
        {
          path: "downloadLog",
          component: () => import("@/view/log/VideoDownload"),
          name: "videoDownload",
          meta: { title: "录像下载日志" },
        },
        {
          path: "systemOperateLog",
          component: () => import("@/view/log/SystemOperateLog"),
          name: "SystemOperateLog",
          meta: { title: "用户日志" },
        },
        {
          path: "carMgt",
          component: () => import("@/view/system/CarMgt/CarMgt"),
          name: "carMgt",
          meta: {
            title: "车辆管理",
            permissions: [
              { label: "删除车辆", symbol: "car:del" },
              { label: "报备", symbol: "car:report" },
              { label: "修改sim卡号", symbol: "car:editSim" },
              { label: "修改车牌号", symbol: "car:editPlateNo" },
              { label: "修改终端", symbol: "car:editTerminal" },
            ],
          },
        },
        {
          path: "vehicleGroupMgt",
          component: () => import("@/view/system/VehicleGroupMgt/VehicleGroupMgt"),
          name: "vehicleGroupMgt",
          meta: { title: "车辆分组管理" },
        },
        {
          path: "rulerConfig",
          component: () => import("@/view/system/ThemeRuler/ThemeRuler"),
          name: "themeRuler",
          meta: {
            title: "规则配置",
            permissions: [
              { label: "停车规则配置", symbol: "themeRuler:stop" },
              { label: "周期配置", symbol: "themeRuler:time" },
              { label: "速度配置", symbol: "themeRuler:speed" },
              { label: "行业报警配置", symbol: "themeRuler:job" },
              { label: "行业报警事件配置", symbol: "themeRuler:jobEvent" },
              { label: "视频平台地址配置", symbol: "themeRuler:screen" },
              { label: "危险事件配置", symbol: "themeRuler:dangerEvent" },
              { label: "报警事件规则配置", symbol: "themeRuler:alarmEvent" },
              { label: "TTS模板", symbol: "themeRuler:ttsMould" },
              { label: "油量异常故障模板", symbol: "themeRuler:fuelMould" },

              { label: "区域级别配置", symbol: "themeRuler:areaPermission" },
              {
                label: "超温报警来源配置",
                symbol: "themeRuler:overtemperaturesource",
              },
              { label: "1078规则配置", symbol: "themeRuler:1078rules" },
              { label: "808规则配置", symbol: "themeRuler:808rules" },
            ],
          },
        },
        {
          path: "fenseSpeedSet",
          component: () => import("@/view/custom/zhatuche/FenseSpeedSet"),
          name: "fenseSpeedSet",
          meta: { title: "车辆速度配置" },
        },
        {
          path: "fenseSpeedSetting",
          component: () => import("@/view/custom/zhatuche/FenseSpeedSetting"),
          name: "FenseSpeedSetting",
          meta: { title: "车辆限速配置" },
        },
        {
          path: "passApproval",
          component: () => import("@/view/custom/zhatuche/PassApproval/PassApproval"),
          name: "PassApproval",
          meta: { title: "通行证审批" },
        },
        {
          path: "securityCodeQuery",
          component: () => import("@/view/business/SecurityCodeQuery"),
          name: "SecurityCodeQuery",
          meta: { title: "企业安全码查询" },
        },
        {
          path: "vehicleOfflineDetail",
          component: () => import("@/view/business/VehicleOfflineDetail/VehicleOfflineDetail"),
          name: "vehicleOfflineDetail",
          meta: { title: "车辆离线明细" },
        },
        {
          path: "vehiclePosition",
          component: () => import("@/view/report/VehiclePosition"),
          name: "vehiclePosition",
          meta: { title: "车辆起始点统计" },
        },
        {
          path: "switchDoorReport",
          component: () => import("@/view/report/SwitchDoorReport"),
          name: "switchDoorReport",
          meta: { title: "开关门报表" },
        },
        {
          path: "temperatureReport",
          component: () => import("@/view/report/TemperatureReport"),
          name: "temperatureReport",
          meta: { title: "温度报警报表" },
        },
        {
          path: "vehicleRunStop",
          component: () => import("@/view/report/VehicleRunStop"),
          name: "vehicleRunStop",
          meta: { title: "车辆行车停车" },
        },
        {
          path: "monitorLog",
          component: () => import("@/view/report/MonitorLog"),
          name: "monitorLog",
          meta: { title: "监控日志" },
        },
        {
          path: "driverMgt",
          component: () => import("@/view/system/DriverMgt/DriverMgt"),
          name: "driverMgt",
          meta: {
            title: "司机管理",
            permissions: [
              { label: "删除车辆", symbol: "driver:del" },
              { label: "修改司机名称", symbol: "driver:editName" },
              { label: "修改身份证号", symbol: "driver:editId" },
            ],
          },
        },
        {
          path: "simData",
          component: () => import("@/view/system/SimData"),
          name: "simData",
          meta: { title: "流量卡信息" },
        },
        {
          path: "simMgnt",
          component: () => import("@/view/system/SimMgnt/SimMgnt"),
          name: "simMgnt",
          meta: { title: "物联网卡管理" },
        },
        {
          path: "simGroupData",
          component: () => import("@/view/system/SimGroupData"),
          name: "simGroupData",
          meta: { title: "流量池信息" },
        },
        {
          path: "flowUseQuery",
          component: () => import("@/view/system/flowUseQuery"),
          name: "flowUseQuery",
          meta: { title: "流量使用查询" },
        },
        {
          path: "drivingDetails",
          component: () => import("@/view/system/DrivingDetails"),
          name: "drivingDetails",
          meta: { title: "出车详情" },
        },
        {
          path: "dispatchingManagement",
          component: () => import("@/view/system/DispatchingManagement"),
          name: "dispatchingManagement",
          meta: { title: "调度管理" },
        },
        {
          path: "vehicleMileageStats",
          component: () => import("@/view/report/VehicleMileageStats"),
          name: "vehicleMileageStats",
          meta: { title: "车辆里程统计" },
        },
        {
          path: "clzclStatsVehicle",
          component: () => import("@/view/report/ClzclStatsVehicle"),
          name: "clzclStatsVehicle",
          meta: { title: "清障车辆综合报表" },
        },
        {
          path: "clzclStatsFleet",
          component: () => import("@/view/report/ClzclStatsFleet"),
          name: "clzclStatsFleet",
          meta: { title: "清障车队综合报表" },
        },
        {
          path: "terminalTypeMgt",
          component: () => import("@/view/system/TerminalTypeMgt"),
          name: "terminalTypeMgt",
          meta: {
            title: "终端类型管理",
            permissions: [
              { label: "终端类型修改", symbol: "terminaltype:update" },
              { label: "终端类型删除", symbol: "terminaltype:delete" },
              { label: "终端类型新增", symbol: "terminaltype:add" },
              { label: "终端类型查询", symbol: "terminaltype:query" },
            ],
          },
        },
        {
          path: "terminalMgt",
          component: () => import("@/view/system/TerminalMgt"),
          name: "terminalMgt",
          meta: { title: "终端管理" },
        },
        {
          path: "terminalStatus",
          component: () => import("@/view/system/TerminalStatus"),
          name: "terminalStatus",
          meta: { title: "终端状态" },
        },
        {
          path: "carRepair",
          component: () => import("@/view/system/CarRepair"),
          name: "carRepair",
          meta: {
            title: "维修保养",
            permissions: [
              { label: "修改", symbol: "repair:update" },
              { label: "删除", symbol: "repair:delete" },
              { label: "新增", symbol: "repair:add" },
            ],
          },
        },
        {
          path: "i18nFileBuilder",
          component: () => import("@/view/tool/I18nFileBuilder"),
          name: "i18nFileBuilder",
          meta: { title: "多语言文件对比" },
        },
        {
          path: "HikFaceCommand",
          component: () => import("@/view/standard/HikFaceCommand/HikFaceCommand"),
          name: "hikFaceCommand",
          meta: { title: "海康FaceID配置" },
        },
        {
          path: "dahFaceCommand",
          component: () => import("@/view/standard/DahFaceCommand/DahFaceCommand"),
          name: "dahFaceCommand",
          meta: { title: "大华FaceID配置" },
        },
        {
          path: "HikFaceError",
          component: () => import("@/view/standard/HikFaceCommand/HikFaceError"),
          name: "HikFaceError",
          meta: { title: "人脸建模异常查询" },
        },
        {
          path: "terminalCommand",
          component: () => import("@/view/standard/TerminalCommand/TerminalCommand"),
          name: "TerminalCommand",
          meta: { title: "终端参数设置" },
        },
        {
          path: "ZTCStats",
          component: () => import("@/view/report/ZTCStats"),
          name: "ZTCStats",
          meta: { title: "渣土车综合报表" },
        },
        {
          path: "workSiteMgnt",
          component: () => import("@/view/business/WorkSiteMgt/WorkSiteMgnt"),
          name: "workSiteMgt",
          meta: { title: "工地审批备案" },
        },
        {
          path: "placeMgt",
          component: () => import("@/view/business/PlaceMgt/PlaceMgt"),
          name: "PlaceMgt",
          meta: { title: "场站备案" },
        },
        {
          path: "jurisdictionMgt",
          component: () => import("@/view/system/JurisdictionMgt/JurisdictionMgnt"),
          name: "jurisdictionMgt",
          meta: { title: "辖区管理" },
        },
        {
          path: "restrictedZoneMgt",
          component: () => import("@/view/system/JurisdictionMgt/RestrictedZoneMgnt"),
          name: "restrictedZoneMgt",
          meta: { title: "限行区管理" },
        },
        {
          path: "xiaoNaMgt",
          component: () => import("@/view/business/WorkSiteMgt/XiaoNaMgt"),
          name: "xiaoNaMgt",
          meta: { title: "消纳场审批备案" },
        },
        {
          path: "workSiteApprov",
          component: () => import("@/view/business/Approv/WorkSiteApprov"),
          name: "workSiteApprov",
          meta: { title: "工地审批" },
        },
        {
          path: "xiaoNaApprov",
          component: () => import("@/view/business/Approv/XiaoNaApprov"),
          name: "XiaoNaApprov",
          meta: { title: "消纳场审批" },
        },
        {
          path: "spoilCapture",
          component: () => import("@/view/business/SpoilCapture/SpoilCapture"),
          name: "spoilCapture",
          meta: { title: "渣土作业抓拍" },
        },
        {
          path: "TransportationApprov",
          component: () => import("@/view/business/Approv/TransportationApprov"),
          name: "transportationApprov",
          meta: { title: "运输许可证审批备案" },
        },
        {
          path: "video/workSite",
          component: () => import("@/view/videoPlay/WorkSiteVideo"),
          name: "workSiteVideo",
          meta: { title: "场地视频回放" },
        },
        {
          path: "video/worklive",
          component: () => import("@/view/videoPlay/WorkSite1078"),
          name: "workSite1078",
          meta: { title: "场地实时视频" },
        },
        {
          path: "deviceMgt",
          component: () => import("@/view/business/DeviceMgt/DeviceMgt"),
          name: "deviceMgt",
          meta: { title: "设备管理" },
        },
        {
          path: "caseMgt",
          component: () => import("@/view/business/CaseMgt/CaseMgt"),
          name: "caseMgt",
          meta: { title: "案件管理" },
        },
        {
          path: "wyCaseMgt",
          component: () => import("@/view/business/WyCaseMgt/WyCaseMgt"),
          name: "wyCaseMgt",
          meta: { title: "案件管理" },
        },
        {
          path: "caseMgtPT",
          component: () => import("@/view/business/CaseMgtPT/CaseMgtPT"),
          name: "caseMgtPT",
          meta: { title: "案件管理" },
        },
        {
          path: "workSitePassingStatics",
          component: () => import("@/view/business/WorkSitePassingStatics"),
          name: "workSitePass",
          meta: { title: "场地过车统计" },
        },
        {
          path: "passingRecord",
          component: () => import("@/view/business/PassingRecord"),
          name: "PassingRecord",
          meta: { title: "过车记录" },
        },
        {
          path: "passingRecordKK",
          component: () => import("@/view/business/PassingRecordKK"),
          name: "PassingRecordKK",
          meta: { title: "卡口过车记录" },
        },
        {
          path: "fenceCommand",
          component: () => import("@/view/standard/FenceCommand/FenceCommand"),
          name: "fenceCommand",
          meta: { title: "围栏指令下发" },
        },
        {
          path: "fenceCommandB",
          component: () => import("@/view/standard/FenceCommand/ForBStandard"),
          name: "fenceCommandB",
          meta: { title: "(部)围栏指令下发" },
        },
        {
          path: "superiorExchange",
          component: () => import("@/view/standard/SuperiorExchange/SuperiorExchange"),
          name: "superiorExchange",
          meta: { title: "上级平台交换" },
        },
        {
          path: "gB28181Up",
          component: () => import("@/view/standard/GB28181Up/GB28181Up"),
          name: "gB28181Up",
          meta: { title: "28181上级平台配置" },
        },

        {
          path: "gB28181Register",
          component: () => import("@/view/standard/GB28181Register/GB28181Register"),
          name: "gB28181Register",
          meta: { title: "28181联网注册设备" },
        },
        {
          path: "contractMgt",
          component: () => import("@/view/business/ContractMgtNoLevelLimit"),
          name: "ContractMgtNoLevelLimit",
          meta: { title: "合同管理" },
        },
        {
          path: "itemBankManagement",
          component: () => import("@/view/business/ItemBankManagement"),
          name: "ItemBankManagement",
          meta: { title: "题库管理" },
        },
        {
          path: "testPaper",
          component: () => import("@/view/business/TestPaper"),
          name: "TestPaper",
          meta: { title: "试卷列表" },
        },
        {
          path: "scoreStatistics",
          component: () => import("@/view/business/ScoreStatistics/ScoreStatistics"),
          name: "ScoreStatistics",
          meta: { title: "分数统计" },
        },
        {
          path: "cloudCrossTraffic",
          component: () => import("@/view/report/CloudCrossTraffic"),
          name: "CloudCrossTraffic",
          meta: { title: "云卡口" },
        },
        {
          path: "attendanceDetails",
          component: () => import("../view/business/AttendanceDetails"),
          name: "AttendanceDetails",
          meta: { title: "考勤明细" },
        },
        {
          path: "attendanceDetailsDH",
          component: () => import("../view/business/AttendanceDetailsDH"),
          name: "AttendanceDetailsDH",
          meta: { title: "考勤明细(大华)" },
        },
        {
          path: "operatingReport",
          component: () => import("@/view/report/OperatingReport"),
          name: "operatingReport",
          meta: { title: "工地/消纳场作业报表" },
        },
        {
          path: "equipmentDamage",
          component: () => import("../view/equipmentdamage/EquipmentDamage"),
          name: "EquipmentDamage",
          meta: { title: "人为设备损坏" },
        },
        {
          path: "videoReportConfig",
          component: () => import("@/view/system/VideoReportConfig"),
          name: "videoReportConfig",
          meta: { title: "视频上报配置" },
        },
        {
          path: "videoPhotoQuery",
          component: () => import("@/view/system/VideoPhotoQuery"),
          name: "videoPhotoQuery",
          meta: { title: "实时抓图" },
        },
        {
          path: "propagandaArticles",
          component: () => import("@/view/system/PropagandaArticles"),
          name: "PropagandaArticles",
          meta: { title: "宣传文章" },
        },
        {
          path: "eventCount",
          component: () => import("@/view/system/EventCount"),
          name: "eventCount",
          meta: { title: "事件数量统计" },
        },
        {
          path: "seniorDispatchOld",
          component: () => import("@/view/business/SeniorDispatch/SeniorDispatch"),
          name: "seniorDispatchOld",
          meta: { title: "高级调度" },
        },
        {
          path: "seniorDispatch",
          component: () => import("@/view/business/SeniorDispatchNew/SeniorDispatchNew"),
          name: "seniorDispatch",
          meta: { title: "高级调度" },
        },
        {
          path: "seniorDispatch2",
          component: () => import("@/view/business/SeniorDispatchNew/SeniorDispatchNew2"),
          name: "seniorDispatch2",
          meta: { title: "高级调度" },
        },
        {
          path: "waybillMonitor",
          component: () => import("@/view/custom/maotai/WaybillMonitor/WaybillMonitor"),
          name: "waybillMonitor",
          meta: { title: "运单监控" },
        },
        {
          path: "exhibitGoodsMonitor",
          component: () => import("@/view/custom/maotai/ExhibitGoodsMonitor/ExhibitGoodsMonitor"),
          name: "exhibitGoodsMonitor",
          meta: { title: "上货监控" },
        },
        {
          path: "openCompartment",
          component: () => import("@/view/custom/maotai/OpenCompartment/OpenCompartment"),
          name: "openCompartment",
          meta: { title: "开厢监控" },
        },
        {
          path: "DealerMgt",
          component: () => import("@/view/custom/maotai/DealerMgt/DealerMgt"),
          name: "DealerMgt",
          meta: { title: "收货人管理" },
        },
        {
          path: "sanitationScheduling",
          component: () => import("@/view/custom/others/SanitationScheduling/SanitationScheduling"),
          name: "sanitationScheduling",
          meta: { title: "环卫调度" },
        },
        {
          path: "elecLockControl",
          component: () => import("@/view/custom/maotai/ElecLockControl/ElecLockControl"),
          name: "elecLockControl",
          meta: { title: "电子锁管控" },
        },
        {
          path: "WaybillMgt",
          component: () => import("@/view/custom/maotai/WaybillMgt/WaybillMgt"),
          name: "WaybillMgt",
          meta: { title: "运单管理" },
        },
        {
          path: "WaybillMgtPT",
          component: () => import("@/view/custom/maotai/WaybillMgtPT/WaybillMgtPT"),
          name: "waybillMgtPT",
          meta: { title: "运单管理(莆田)" },
        },
        {
          path: "LockedQuery",
          component: () => import("@/view/custom/maotai/LockedQuery/LockedQuery"),
          name: "LockedQuery",
          meta: { title: "开锁记录查询" },
        },
        {
          path: "LockStatusQuery",
          component: () => import("@/view/custom/maotai/LockStatusQuery/LockStatusQuery"),
          name: "LockStatusQuery",
          meta: { title: "电子锁状态记录查询" },
        },
        {
          path: "switchDoorQuery",
          component: () => import("@/view/custom/maotai/SwitchDoorQuery/SwitchDoorQuery"),
          name: "switchDoorQuery",
          meta: { title: "开关门记录查询" },
        },
        {
          path: "temperatureRangeMgt",
          component: () => import("@/view/business/GmColdChain/TemperatureRangeMgt/TemperatureRangeMgt"),
          name: "temperatureRangeMgt",
          meta: { title: "温区管理" },
        },
        {
          path: "TemperatureLine",
          component: () => import("@/view/business/GmColdChain/TemperatureLine"),
          name: "TemperatureLine",
          meta: { title: "温区温度曲线" },
        },
        {
          path: "waybillDetails",
          component: () => import("@/view/custom/maotai/WaybillDetails/WaybillDetails"),
          name: "waybillDetails",
          meta: { title: "运单详情" },
        },
        {
          path: "onlineRate",
          component: () => import("@/view/custom/yixing/onlineRate/onlineRate"),
          name: "onlineRate",
          meta: { title: "上线率统计" },
        },
        {
          path: "drumStateSimulation",
          component: () => import("@/view/business/drumStateSimulation/drumStateSimulation"),
          name: "drumStateSimulation",
          meta: { title: "滚筒状态模拟" },
        },
        // 大屏跳转 V2(本tab页子菜单打开)
        {
          path: "zhatucheAuto",
          component: () => import("@/view/demonstration/demo/Jiande/zhatuche"),
          name: "zhatucheAuto",
          meta: { title: "金华渣土车", icon: "monitor" },
        },
        {
          path: "carExpirationTime",
          component: () => import("@/view/system/CarExpirationTime/CarExpirationTime"),
          name: "carExpirationTime",
          meta: { title: "车辆到期管理" },
        },
        {
          path: "expenseReportQuery",
          component: () => import("@/view/business/ExpenseReportQuery/ExpenseReportQuery"),
          name: "expenseReportQuery",
          meta: { title: "费用上报查询" },
        },

        // 部标2.0迁移过来
        {
          path: "electronicWayBill",
          component: () => import("@/view/ministerialStandard/ElectronicWayBill/ElectronicWayBill"),
          name: "electronicWayBill",
          meta: { title: "电子运单" },
        },
        {
          path: "mediaEvent",
          component: () => import("@/view/ministerialStandard/MediaEvent/MediaEvent"),
          name: "mediaEvent",
          meta: { title: "多媒体事件上报" },
        },
        {
          path: "telCommand",
          component: () => import("@/view/ministerialStandard/TelCommand/TelCommand"),
          name: "telCommand",
          meta: { title: "电话本设置" },
        },
        {
          path: "driverInfo",
          component: () => import("@/view/ministerialStandard/DriverInfo/DriverInfo.vue"),
          name: "driverInfo",
          meta: { title: "驾驶员身份识别" },
        },
        {
          path: "dataTransfer",
          component: () => import("@/view/ministerialStandard/DataTransfer/DataTransfer.vue"),
          name: "dataTransfer",
          meta: { title: "透传查看" },
        },
        {
          path: "compressData",
          component: () => import("@/view/ministerialStandard/CompressData/CompressData.vue"),
          name: "compressData",
          meta: { title: "压缩数据上报" },
        },
        {
          path: "mediaCommand",
          component: () => import("@/view/ministerialStandard/MediaCommand/MediaCommand.vue"),
          name: "mediaCommand",
          meta: { title: "多媒体数据上报" },
        },
        {
          path: "drivingRecord",
          component: () => import("@/view/ministerialStandard/DrivingRecord/DrivingRecord.vue"),
          name: "drivingRecord",
          meta: { title: "行驶记录管理" },
        },
        {
          path: "mileageReports",
          component: () => import("@/view/ministerialStandard/mileageReports/mileageReports"),
          name: "mileageReports",
          meta: { title: "里程查询" },
        },
        {
          path: "equipmentOnlineStatusMgt",
          component: () => import("@/view/ministerialStandard/EquipmentOnlineStatusMgt/EquipmentOnlineStatusMgt"),
          name: "equipmentOnlineStatusMgt",
          meta: { title: "设备在线状态管理" },
        },
        {
          path: "upAndDownLine",
          component: () => import("@/view/ministerialStandard/upAndDownLine/upAndDownLine"),
          name: "upAndDownLine",
          meta: { title: "上下线统计" },
        },
        {
          path: "canData",
          component: () => import("@/view/ministerialStandard/canData/canData"),
          name: "canData",
          meta: { title: "can数据统计" },
        },
        {
          path: "passengerQuery",
          component: () => import("@/view/ministerialStandard/passengerQuery/passengerQuery"),
          name: "passengerQuery",
          meta: { title: "乘客流量统计" },
        },
        {
          path: "trajectoryIntegrity",
          component: () => import("@/view/ministerialStandard/trajectoryIntegrity/trajectoryIntegrity"),
          name: "trajectoryIntegrity",
          meta: { title: "轨迹完整率" },
        },
        {
          path: "deviceLogMgt",
          component: () => import("@/view/ministerialStandard/deviceLogMgt/deviceLogMgt"),
          name: "deviceLogMgt",
          meta: { title: "设备日志管理" },
        },
        {
          path: "flowStatistics",
          component: () => import("@/view/ministerialStandard/flowStatistics/flowStatistics"),
          name: "flowStatistics",
          meta: { title: "流量统计" },
        },
        {
          path: "carInformation",
          component: () => import("@/view/report/CarInformation"),
          name: "carInformation",
          meta: { title: "车辆综合信息查询" },
        },
        {
          path: "videoFile",
          component: () => import("@/view/ministerialStandard/VideoFile/VideoFile"),
          name: "videoFile",
          meta: { title: "录像文件" },
        },
        {
          path: "unhandledAlarm",
          component: () => import("@/view/ministerialStandard/unhandledAlarm/UnHandledAlarm"),
          name: "unhandledAlarm",
          meta: { title: "未处理报警" },
        },
        {
          path: "fatigueDrivie",
          component: () => import("@/view/system/Fatiguedrivie/Fatiguedrivie"),
          name: "fatigueDrivie",
          meta: { title: "疲劳驾驶查询" },
        },
        {
          path: "turnRightNoStop",
          component: () => import("@/view/custom/zhatuche/TurnRightNoStop/TurnRightNoStop"),
          name: "turnRightNoStop",
          meta: { title: "路口右转未停车" },
        },
        {
          path: "upDownLine",
          component: () => import("@/view/custom/junweida/upDownLine/upDownLine"),
          name: "upDownLine",
          meta: { title: "上下线统计分析" },
        },
        {
          path: "historyAddress",
          component: () => import("@/view/custom/junweida/historyAddress/HistoryAddress"),
          name: "historyAddress",
          meta: { title: "历史位置" },
        },
        {
          path: "trafficStatistics",
          component: () => import("@/view/custom/junweida/trafficStatistics/trafficStatistics"),
          name: "trafficStatistics",
          meta: { title: "行车统计分析" },
        },
        {
          path: "parkingStatistics",
          component: () => import("@/view/custom/junweida/parkingStatistics/parkingStatistics"),
          name: "parkingStatistics",
          meta: { title: "停车统计分析" },
        },
        {
          path: "alarmStatisticalAnalysis",
          component: () => import("@/view/custom/junweida/alarmStatisticalAnalysis/alarmStatisticalAnalysis"),
          name: "alarmStatisticalAnalysis",
          meta: { title: "报警统计分析" },
        },
        {
          path: "videoStatistics",
          component: () => import("@/view/custom/junweida/videoStatistics/videoStatistics"),
          name: "videoStatistics",
          meta: { title: "视频统计" },
        },
        {
          path: "alarmGroupManagement",
          component: () => import("@/view/custom/junweida/alarmGroupManagement/alarmGroupManagement"),
          name: "alarmGroupManagement",
          meta: { title: "报警分组管理" },
        },
        {
          path: "scoreManage",
          component: () => import("@/view/demonstration/demo/Ptzt/scoreManage"),
          name: "scoreManage",
          meta: { title: "莆田通知公告" },
        },

        {
          path: "turnLink",
          component: () => import("@/view/system/TurnLink"),
          name: "turnLink",
          meta: { title: "运行监控管理" },
          // beforeEnter (to,from,next) {
          //   window.open('http://49.4.51.11:8908/zabbix/zabbix.php?action=dashboard.view')
          //   next(false)
          // }
        },
        {
          path: "flowCard",
          component: () => import("@/view/business/FlowCard/flowCard"),
          name: "flowCard",
          meta: { title: "流量卡查询" },
        },
        {
          path: "clearWaybillMgt",
          component: () => import("@/view/business/ClearWaybillMgt/ClearWaybillMgt"),
          name: "clearWaybillMgt",
          meta: { title: "清运运单" },
        },
        {
          path: "clearWaybillMgtApproval",
          component: () => import("@/view/business/ClearWaybillMgtApproval/ClearWaybillMgtApproval"),
          name: "clearWaybillMgtApproval",
          meta: { title: "清运运单(审批)" },
        },
        {
          path: "scoreManageHa",
          component: () => import("@/view/business/ClearWaybillMgt/ScoreManage"),
          name: "scoreManageHa",
          meta: { title: "分数管理" },
        },
        {
          path: "blackWokersit",
          component: () => import("@/view/business/BlackWokersit/blackWokersit"),
          name: "blackWokersit",
          meta: { title: "黑工地消纳场处理报表" },
        },
        {
          path: "validityCertificate",
          component: () => import("@/view/business/ValidityCertificate/ValidityCertificate"),
          name: "validityCertificate",
          meta: { title: "证件时效" },
        },
        {
          path: "collectionRouteMgt",
          component: () => import("@/view/custom/cangnan/collectionRouteMgt/CollectionRouteMgt"),
          name: "collectionRouteMgt",
          meta: { title: "清运线路管理" },
        },
        {
          path: "clearDetail",
          component: () => import("@/view/custom/cangnan/ClearDetail/ClearDetail"),
          name: "clearDetail",
          meta: { title: "清运明细" },
        },
        {
          path: "pointClearDetail",
          component: () => import("@/view/custom/cangnan/PointClearDetail/PointClearDetailHT"),
          name: "pointClearDetail",
          meta: { title: "点位清运明细" },
        },
        {
          path: "clearanceContractMgt",
          component: () => import("@/view/custom/cangnan/ClearanceContractMgt/ClearanceContractMgt"),
          name: "clearanceContractMgt",
          meta: { title: "清运合同管理" },
        },
        {
          path: "clearanceStatistics",
          component: () => import("@/view/custom/cangnan/ClearanceStatistics/ClearanceStatistics"),
          name: "clearanceStatistics",
          meta: { title: "清运统计" },
        },
        {
          path: "collectionPointMgt",
          component: () => import("@/view/custom/cangnan/collectionPointMgt/CollectionPointMgt"),
          name: "collectionPointMgt",
          meta: { title: "收集点申请审批" },
        },
        {
          path: "collectionPointMgtHT",
          component: () => import("@/view/custom/cangnan/CollectionPointMgtHT/CollectionPointMgtHT"),
          name: "collectionPointMgtHT",
          meta: { title: "收集点申请" },
        },
        {
          path: "greaseCollectionPointMgt",
          component: () => import("@/view/custom/cangnan/GreaseCollectionPointMgt/GreaseCollectionPointMgt"),
          name: "greaseCollectionPointMgt",
          meta: { title: " 油脂清运点位" },
        },
        {
          path: "collectionAreaMgt",
          component: () => import("@/view/custom/cangnan/CollectionAreaMgt/CollectionAreaMgt"),
          name: "collectionAreaMgt",
          meta: { title: "街镇管理" },
        },
        {
          path: "carAlarmHanding",
          component: () => import("@/view/custom/cangnan/CarAlarmHanding/CarAlarmHanding"),
          name: "carAlarmHanding",
          meta: { title: "扣车报警处置" },
        },
        {
          path: "timeReport",
          component: () => import("@/view/custom/sgs/TimeReport/TimeReport"),
          name: "timeReport",
          meta: { title: "工时报表" },
        },
        {
          path: "malfunctionDetails",
          component: () => import("@/view/custom/sgs/MalfunctionDetails/MalfunctionDetails"),
          name: "malfunctionDetails",
          meta: { title: "运单送货轨迹明细" },
        },

        {
          path: "summaryTableExport",
          component: () => import("@/view/custom/sgs/SummaryTableExport/SummaryTableExport"),
          name: "summaryTableExport",
          meta: { title: "汇总表导出" },
        },
        {
          path: "drowsyDrivingEnquiry",
          component: () => import("@/view/custom/sgs/DrowsyDrivingEnquiry/DrowsyDrivingEnquiry"),
          name: "drowsyDrivingEnquiry",
          meta: { title: "疲劳驾驶" },
        },
        {
          path: "drowsyDrivingEnquiryYD",
          component: () => import("@/view/custom/sgs/DrowsyDrivingEnquiryYD/DrowsyDrivingEnquiryYD"),
          name: "drowsyDrivingEnquiryYD",
          meta: { title: "疲劳驾驶(英迪)" },
        },
        {
          path: "videoDownloadView",
          component: () => import("@/view/custom/sgs/VideoDownloadView/VideoDownloadView"),
          name: "videoDownloadView",
          meta: { title: "自动录像查看器" },
        },
        {
          path: "cutVideoQuery",
          component: () => import("@/view/custom/sgs/CutVideoQuery/CutVideoQuery"),
          name: "cutVideoQuery",
          meta: { title: "手动录像查看" },
        },
        {
          path: "waybillLMS",
          component: () => import("@/view/custom/sgs/WayBillLMS/WayBillLMS"),
          name: "WayBillLMS",
          meta: { title: "LMS运单" },
        },
        {
          path: "videoDownloadConfig",
          component: () => import("@/view/custom/sgs/VideoDownloadConfig/VideoDownloadConfig"),
          name: "VideoDownloadConfig",
          meta: { title: "录像下载配置" },
        },
        {
          path: "videoDownloadPlan",
          component: () => import("@/view/custom/sgs/VideoDownloadPlan/VideoDownloadPlan"),
          name: "VideoDownloadPlan",
          meta: { title: "视频预约下载" },
        },
        {
          path: "videoStatisticsQuery",
          component: () => import("@/view/custom/sgs/VideoStatisticsQuery/VideoStatisticsQuery"),
          name: "VideoStatisticsQuery",
          meta: { title: "自动录像明细查询" },
        },
        {
          path: "cumulativeDriving",
          component: () => import("@/view/custom/sgs/CumulativeDriving/CumulativeDriving"),
          name: "CumulativeDriving",
          meta: { title: "累计驾驶" },
        },
        {
          path: "loadingInformationQuery",
          component: () => import("@/view/custom/sgs/LoadingInformationQuery/LoadingInformationQuery"),
          name: "LoadingInformationQuery",
          meta: { title: "装卸货信息查询" },
        },
        {
          path: "stopAbnormal",
          component: () => import("@/view/custom/sgs/StopAbnormal/StopAbnormal"),
          name: "stopAbnormal",
          meta: { title: "异常停车点分析" },
        },
        {
          path: "videoAnalysisList",
          component: () => import("@/view/custom/sgs/VideoAnalysisList/VideoAnalysisList"),
          name: "videoAnalysisList",
          meta: { title: "视频分析汇总表" },
        },
        {
          path: "wayBillMgtJH",
          component: () => import("@/view/custom/jianhua/wayBillMgtJH/wayBillMgtJH"),
          name: "wayBillMgtJH",
          meta: { title: "运单管理" },
        },
        {
          path: "siteEquipmentMgt",
          component: () => import("@/view/system/SiteEquipmentMgt/SiteEquipmentMgt"),
          name: "siteEquipmentMgt",
          meta: { title: "场地设备管理" },
        },
        {
          path: "dryForecastTable",
          component: () => import("@/view/business/DryForecastTable"),
          name: "DryForecastTable",
          meta: { title: "干预报表" },
        },
        {
          path: "vehicleEconomicReport",
          component: () => import("@/view/custom/futian/FuelEvents/VehicleEconomicReport/VehicleEconomicReport"),
          name: "vehicleEconomicReport",
          meta: { title: "车辆经济报表" },
        },
        {
          path: "mileageEnergyReport",
          component: () =>
            import("@/view/custom/futian/FuelEvents/MileageEnergyConsumptionReport/MileageEnergyConsumptionReport"),
          name: "mileageEnergyReport",
          meta: { title: "里程能耗报表" },
        },
        {
          path: "faultCodeStatistics",
          component: () => import("@/view/custom/futian/FuelEvents/FaultCodeStatistics/FaultCodeStatistics"),
          name: "faultCodeStatistics",
          meta: { title: "车队故障汇总" },
        },
        {
          path: "tirePressureDetails",
          component: () => import("@/view/business/TirePressureDetails/TirePressureDetails"),
          name: "tirePressureDetails",
          meta: { title: "胎压明细查询" },
        },
        {
          path: "standardMileage",
          component: () => import("@/view/custom/putian/StandardMileage/StandardMileage"),
          name: "standardMileage",
          meta: { title: "标准里程报表" },
        },
        {
          path: "vehicleMaintenance",
          component: () => import("@/view/custom/putian/VehicleMaintenance/VehicleMaintenance"),
          name: "vehicleMaintenance",
          meta: { title: "车型维护报表" },
        },
        {
          path: "standardPrice",
          component: () => import("@/view/custom/putian/StandardPrice/StandardPrice"),
          name: "standardPrice",
          meta: { title: "标准单价报表" },
        },
        {
          path: "priceArea",
          component: () => import("@/view/custom/putian/PriceArea/PriceArea"),
          name: "priceArea",
          meta: { title: "特殊线路" },
        },
        {
          path: "revenueSettlement",
          component: () => import("@/view/custom/putian/RevenueSettlement/RevenueSettlement"),
          name: "revenueSettlement",
          meta: { title: "收入结算" },
        },
        {
          path: "industryControl",
          component: () => import("@/view/business/IndustryControl"),
          name: "industryControl",
          meta: { title: "行业限速管控" },
        },
        {
          path: "transportationVehicleStatistics",
          component: () => import("@/view/custom/zhatuche/TransportationVehicleStatistics/TransportationVehicleStatistics"),
          name: "transportationVehicleStatistics",
          meta: { title: "商砼单车统计" },
        },
        {
          path: "feedbackStatistics",
          component: () => import("@/view/custom/cangnan/FeedbackStatistics/FeedbackStatistics"),
          name: "feedbackStatistics",
          meta: { title: "海通固废反馈查询" },
        },
        {
          path: "externalOrders",
          component: () => import("@/view/custom/putian/ExternalOrders/ExternalOrders"),
          name: "externalOrders",
          meta: { title: "外部订单录入" },
        },
        {
          path: "vehicleFuelConsumption",
          component: () => import("@/view/custom/jianhua/VehicleFuelConsumption/VehicleFuelConsumption"),
          name: "vehicleFuelConsumption",
          meta: { title: "车辆空重载油耗查询" },
        },
        {
          path: "overspeedExport",
          component: () => import("@/view/custom/jianhua/OverspeedExport/OverspeedExport"),
          name: "overspeedExport",
          meta: { title: "超速报表导出" },
        },
        {
          path: "baseFuelConsumption",
          component: () => import("@/view/custom/jianhua/BaseFuelConsumption/BaseFuelConsumption"),
          name: "baseFuelConsumption",
          meta: { title: "基地空重载油耗查询" },
        },
        {
          path: "balancedScorecard",
          component: () => import("@/view/business/BalancedScorecard/BalancedScorecard"),
          name: "balancedScorecard",
          meta: { title: "平衡记分卡" },
        },
        {
          path: "alarmAiSearch",
          component: () => import("@/view/business/AlarmAiSearch/AlarmAiSearch"),
          name: "alarmAiSearch",
          meta: { title: "报警智能分析查询" },
        },
        {
          path: "orderListJH",
          component: () => import("@/view/custom/jianhua/OrderListJH/OrderListJH"),
          name: "orderListJH",
          meta: { title: "订单列表" },
        },
        {
          path: "orderEntryJH",
          component: () => import("@/view/custom/jianhua/orderEntryJH/orderEntryJH"),
          name: "orderEntryJH",
          meta: { title: "订单录入" },
        },
        {
          path: "clientMgt",
          component: () => import("@/view/custom/jianhua/ClientMgt/ClientMgt"),
          name: "clientMgt",
          meta: { title: "客户管理" },
        },
        {
          path: "contactMgt",
          component: () => import("@/view/custom/jianhua/ContactMgt/ContactMgt"),
          name: "contactMgt",
          meta: { title: "档案管理" },
        },
        {
          path: "projectMgt",
          component: () => import("@/view/custom/jianhua/ProjectMgt/ProjectMgt"),
          name: "projectMgt",
          meta: { title: "项目管理" },
        },
        {
          path: "waybillEntryJH",
          component: () => import("@/view/custom/jianhua/WaybillEntryJH/WaybillEntryJH"),
          name: "waybillEntryJH",
          meta: { title: "运单录入" },
        },
        {
          path: "entryOfWaybill",
          component: () => import("@/view/custom/jianhua/EntryOfWaybill/EntryOfWaybill"),
          name: "entryOfWaybill",
          meta: { title: "运单录入" },
        },
        {
          path: "waybillEntryPlan",
          component: () => import("@/view/custom/jianhua/WaybillEntryPlan/WaybillEntryPlan"),
          name: "waybillEntryPlan",
          meta: { title: "运输计划" },
        },
        {
          path: "waybillEntryJHDetail",
          component: () => import("@/view/custom/jianhua/WaybillEntryJH/WaybillEntryJHDetail"),
          name: "waybillEntryJHDetail",
          meta: { title: "运单详情" },
        },
        {
          path: "waybillListJH",
          component: () => import("@/view/custom/jianhua/WaybillListJH/WaybillListJH"),
          name: "waybillListJH",
          meta: { title: "运单列表" },
        },
        {
          path: "waybillList",
          component: () => import("@/view/custom/jianhua/WaybillList/WaybillList"),
          name: "waybillList",
          meta: { title: "运单列表" },
        },
        {
          path: "quickOrderingJH",
          component: () => import("@/view/custom/jianhua/QuickOrderingJH/QuickOrderingJH"),
          name: "quickOrderingJH",
          meta: { title: "快捷下单" },
        },
        {
          path: "keyReturnRecord",
          component: () => import("@/view/business/KeyReturnRecord/KeyReturnRecord"),
          name: "keyReturnRecord",
          meta: { title: "钥匙柜领用记录" },
        },
        {
          path: "holePositionRecord",
          component: () => import("@/view/business/HolePositionRecord/HolePositionRecord"),
          name: "holePositionRecord",
          meta: { title: "设备孔位信息上报" },
        },
        {
          path: "receiptMgt",
          component: () => import("@/view/custom/jianhua/ReceiptMgt/ReceiptMgt"),
          name: "receiptMgt",
          meta: { title: "回单管理" },
        },
        {
          path: "waybillEntryList",
          component: () => import("@/view/custom/jianhua/WaybillEntryList/WaybillEntryList"),
          name: "waybillEntryList",
          meta: { title: "多装多卸" },
        },
        {
          path: "waybillEntryListDetail",
          component: () => import("@/view/custom/jianhua/WaybillEntryList/WaybillEntryListDetail"),
          name: "waybillEntryListDetail",
          meta: { title: "多装多卸运单详情" },
        },
        {
          path: "transportPlanMgt",
          component: () => import("@/view/custom/jianhua/TransportPlanMgt/TransportPlanMgt"),
          name: "transportPlanMgt",
          meta: { title: "运输计划列表" },
        },
        {
          path: "abnormalOilMgt",
          component: () => import("@/view/custom/jianhua/AbnormalOilMgt/AbnormalOilMgt"),
          name: "abnormalOilMgt",
          meta: { title: "油量异常统计" },
        },
        {
          path: "trailerMgt",
          component: () => import("@/view/business/TrailerMgt/TrailerMgt"),
          name: "trailerMgt",
          meta: { title: "挂车备案" },
        },
        {
          path: "vehicleDetailsList",
          component: () => import("@/view/business/VehicleDetailsList/VehicleDetailsList"),
          name: "vehicleDetailsList",
          meta: { title: "车辆明细列表" },
        },
        {
          path: "vehicleRecords",
          component: () => import("@/view/business/VehicleRecords/VehicleRecords"),
          name: "vehicleRecords",
          meta: { title: "用车记录" },
        },
        {
          path: "vehicleCoverageList",
          component: () => import("@/view/custom/sgs/VehicleCoverageList/VehicleCoverageList"),
          name: "vehicleCoverageList",
          meta: { title: "普货车辆覆盖清单" },
        },
        {
          path: "highRiskSpeedLimit",
          component: () => import("@/view/custom/sgs/HighRiskSpeedLimit/HighRiskSpeedLimit"),
          name: "highRiskSpeedLimit",
          meta: { title: "高风险点超速明细" },
        },
        {
          path: "environmentalSanitationTrip",
          component: () => import("@/view/business/EnvironmentalSanitationTrip/EnvironmentalSanitationTrip"),
          name: "environmentalSanitationTrip",
          meta: { title: "环卫经过次数报表" },
        },
        {
          path: "intersectionViolationStatistics",
          component: () => import("@/view/business/IntersectionViolationStatistics/IntersectionViolationStatistics"),
          name: "intersectionViolationStatistics",
          meta: { title: "路口违规统计" },
        },
        {
          path: "facialRecognition",
          component: () => import("@/view/business/FacialRecognitionMgt/FacialRecognition"),
          name: "facialRecognition",
          meta: { title: "调用记录明细" },
        },

        {
          path: "*",
          component: () => import("@/view/errorPage/NotFound"),
          meta: { title: "404" },
        },
      ],
    },
    {
      path: "/:company/customReport/editor",
      component: () => import("@/view/customReport/EditorV2/Editor.vue"),
      name: "reportEditorV2",
      meta: { title: "自定义报表", icon: "report" },
    },
    {
      path: "/:company/customReport/preview",
      component: () => import("@/view/customReport/Preview/PreviewPage.vue"),
      name: "report",
      meta: { title: "自定义报表预览", icon: "report" },
    },

    // {
    //     path: '/:company/datavscreen',
    //     name: '大屏展示',
    //     component: () => import('@/view/dataview/page/Home'),
    //     redirect: 'datavdemo',
    //     children: [
    //         {
    //             path: 'datavdemo',
    //             component: () => import('@/view/dataview/page/DataViewDemo/index'),
    //             name: 'DataViewDemo',
    //             meta: {title: 'DataV测试使用'}
    //         }
    //     ]
    // },

    {
      path: "/:company/demonstration",
      name: "大屏展示",
      component: () => import("@/view/demonstration/DemoHome"),
      redirect: "jiande",
      children: [
        {
          path: "jiande",
          component: () => import("@/view/demonstration/demo/Jiande/Jiande"),
          name: "jiande",
          meta: { title: "建德大屏展示", icon: "monitor" },
        },
        {
          path: "shangtong",
          component: () => import("@/view/demonstration/demo/Shangtong/Shangtong"),
          name: "shangtong",
          meta: { title: "商砼大屏", icon: "monitor" },
        },
        {
          path: "longgang",
          component: () => import("@/view/demonstration/demo/LongGang/longGang"),
          name: "longgang",
          meta: { title: "温州龙港数据座舱", icon: "monitor" },
        },
        {
          path: "xiangtan",
          component: () => import("@/view/demonstration/demo/XiangTan/XiangTan"),
          name: "xiangtan",
          meta: { title: "湘潭市渣土车监管数据舱", icon: "monitor" },
        },
        {
          path: "zwyShortDistance",
          component: () => import("@/view/demonstration/demo/zwy/zwyShort"),
          name: "zwyShortDistance",
          meta: { title: "中外运短途", icon: "monitor" },
        },
        {
          path: "zwyLongDistance",
          component: () => import("@/view/demonstration/demo/zwy/zwyLong"),
          name: "zwyLongDistance",
          meta: { title: "中外运长途", icon: "monitor" },
        },
        {
          path: "obd",
          component: () => import("@/view/demonstration/demo/obd/obdJH"),
          name: "obd",
          meta: { title: "金华OBD", icon: "monitor" },
        },
        {
          path: "mineCar",
          component: () => import("@/view/demonstration/demo/MineCar/MineCar"),
          name: "mineCar",
          meta: { title: "经纬煤矿车辆安全驾驶舱", icon: "monitor" },
        },
        {
          path: "comprehensivePT",
          component: () => import("@/view/demonstration/demo/PuTian/ComprehensivePT/ComprehensivePT"),
          name: "comprehensivePT",
          meta: { title: "普田物流综合驾驶舱", icon: "monitor" },
        },
        {
          path: "wayBillPT",
          component: () => import("@/view/demonstration/demo/PuTian/WayBillPT/WayBillPT"),
          name: "wayBillPT",
          meta: { title: "普田物流运单驾驶舱", icon: "monitor" },
        },
        // 大屏跳转 V1(新tab页打开)
        {
          path: "zhatuche",
          component: () => import("@/view/demonstration/demo/Jiande/zhatuche"),
          name: "zhatuche",
          meta: { title: "金华渣土车", icon: "monitor" },
        },
        // 大屏跳转 V3(本tab页打开)
        {
          path: "zhatucheCurrent",
          component: () => import("@/view/demonstration/demo/Jiande/zhatuche"),
          name: "zhatucheCurrent",
          meta: { title: "金华渣土车", icon: "monitor" },
        },
        {
          path: "dataMonitor",
          component: () => import("@/view/demonstration/demo/Jinhuazt/Monitor"),
          name: "dataMonitor",
          meta: { title: "监控看板", icon: "monitor" },
        },
        {
          path: "Jinhuazt",
          component: () => import("@/view/demonstration/demo/Jinhuazt/Jinhuazt"),
          name: "jinhuazt",
          meta: { title: "金华渣土车", icon: "monitor" },
        },
        {
          path: "kygdxnc",
          component: () => import("@/view/demonstration/demo/Jinhuazt/Kygdxnc"),
          name: "Kygdxnc",
          meta: { title: "可疑工地消纳场分析", icon: "monitor" },
        },
      ],
    },

    {
      path: "/:company/open",
      name: "open",
      component: () => import("@/view/entry/Open/Open"),
      children: [
        {
          path: "scoreMonitorPTOpen",
          component: () => import("@/view/demonstration/demo/Ptzt/scoreMonitorPT"),
          name: "scoreMonitorPTOpen",
          meta: { title: "莆田评分报表" },
        },
        {
          path: "riskSharing",
          component: () => import("@/view/demonstration/demo/RiskSharing/RiskSharing"),
          name: "riskSharing",
          meta: { title: "车辆实时风险" },
        },
        {
          path: "workSiteMgt",
          component: () => import("@/view/business/WorkSiteMgt/WorkSiteMgtOpen"),
          name: "workSiteMgtOpen",
          meta: { title: "工地信息" },
        },
        {
          path: "xiaoNaMgt",
          component: () => import("@/view/business/WorkSiteMgt/XiaoNaMgtOpen"),
          name: "xiaoNaMgtOpen",
          meta: { title: "消纳场信息" },
        },
        {
          path: "/:company/chat",
          component: () => import("@/view/entry/Home/components/ChatDialog"),
          name: "chat",
          meta: { title: "小驹助手" },
        },
        {
          path: "app",
          component: () => import("@/view/open/App"),
          name: "app",
          meta: { title: "跳转" },
        },
      ],
    },

    {
      path: "/:company/performanceTest",
      component: () => import("@/view/tool/PerformanceTest.vue"),
      name: "performanceTest",
      meta: { title: "组件性能测试", icon: "report" },
    },
    {
      path: "/:company/*",
      component: () => import("@/view/errorPage/NotFound"),
    },
    {
      path: "/404",
      component: () => import("@/view/errorPage/NotFound"),
    },
  
  ],
});

const companyList = [
  "pony",
  "ai",
  "clzcl",
  "wdczn",
  "zglt",
  "matics",
  "jhztc",
  "hfjt",
  "cgo8",
  "jiande",
  "zljt",
  "dwr",
  "hipony",
  "jhpolice",
  "macao",
  "truck",
  "locoway",
  "ningd",
  "yx",
  "nanp",
  "foton",
  "transport",
  "ptzt",
  "coldchain",
  "safety",
  "yw",
  "wys",
  "sws",
  "tzcl",
  "huaian",
  "xiapu",
  "jinyu",
  "nfhb",
  "zhouning",
  "cangnanzfj",
  "qwts",
  "sgs",
  "safetylz",
  "jnfj",
  "jhztn",
  "loop",
  "junk",
  "shyh",
  "htgf",
  "xyjc",
  "sgs-cspc",
  "ywhj",
  "ftcar",
  "chache",
  "jhjy"
];
//不能成为company的单词
const companyBlackList = ["home", "login", "customReport", "demonstration", "open"];
//新窗口打开的链接
const _blankLinks = ["customReport", "demonstration", "open"];
const changePassWordComplex = (pass_word) => {
  let prefix = generateMixed(7);
  let suffix = generateMixed(10);
  let complexpass = JSON.parse(JSON.stringify(pass_word));
  let splitlist = complexpass.split("");
  splitlist.splice(1, 0, prefix[1]);
  splitlist.splice(5, 0, suffix[7]);
  let passlenth = complexpass.length < 10 ? `0${complexpass.length}` : complexpass.length;
  splitlist.push(passlenth);
  complexpass = splitlist.toString().replace(/,/g, "");
  return Base64.encode(`${prefix}dfbt${complexpass}sqdz${suffix}`);
};
const loginSystem = async (user_name, pass_word, compony) => {
  let newpass = changePassWordComplex(pass_word);
  // let newpass = this.form.pass_word
  let result = await $.ajax({
    url: "/ponysafety2/login",
    type: "post",
    data: JSON.stringify({ user_name, pass_word: newpass }),
    headers: {
      login_page: compony,
      "Content-Type": "application/json",
    },
  });
  // let result = await Vue.prototype.$api.login({
  //   user_name,
  //   pass_word: newpass,
  // });

  if (!result || result.RS != 1) {
    return null;
  } else {
    return result;
  }
};
const akVerification = async (ak) => {
  let loginInfoRes = await Vue.prototype.$api.autoLogin({
    sign: ak,
  });
  if (!loginInfoRes || loginInfoRes.status != 200) {
    return null;
  } else {
    return loginInfoRes;
  }
};

router.beforeEach(async (to, from, next) => {
  let aliveList = store.state.main.aliveView.map((item) => item.name);
  if (aliveList.includes(from.name)) {
    //这个获取方式是因为一个页面会有很多个表格的滚动条，需要分开处理，在表格的全局minix混入Table.js获取的时候也是querySelectorAll方法，根据下标存取的，格式如下
    // {
    //   pageName:{
    //     index:{
    //       scrollTop:0,
    //       scrollLeft:0
    //     }
    //   }
    // }
    let divList = document.querySelectorAll(".el-table__body-wrapper");
    if (divList.length) {
      let scrollObj = {};
      divList.forEach((item, index) => {
        scrollObj[index] = {
          scrollTop: item.scrollTop,
          scrollLeft: item.scrollLeft,
        };
      });
      let oldScrollObj = { ...store.state.main.scrollObj };
      oldScrollObj[from.name] = scrollObj;
      store.commit("main/setScrollObj", oldScrollObj);
    }
  }
  if (new RegExp("/open/").test(to.path)) {
    next();
    return;
  }
  /*
   * 2019-10-30 16:13:27 yezy
   * 当router.push('/home')时会报一个 Uncaught (in promise) undefined
   * router.push('/pony/home')时则不会
   * 经查看vue-router的源码发现，并不是错误，不影响功能
   * next() => next('/xxxx')都能复现
   * */
  //中间有-的需要重命名一下
  let changeNameList = {
    "sgs-cspc": "sgs_cspc",
  };

  const paramCompany = to.params.company;
  const paraString = changeNameList[to.params.company] ? changeNameList[to.params.company] : to.params.company;
  if (!paramCompany) {
    if (to.path === "/") {
      next(`/${store.state.main.company || "pony"}/home`);
    } else {
      next();
    }
    return;
  }

  // 例如: /ai  /ai/home(/xxxx)
  if (companyList.includes(paramCompany)) {
    // 例如 /ai
    if (to.path === `/${paramCompany}`) {
      if (store.state.auth.loginState === -1) {
        next(`/${paramCompany}/login`);
      } else {
        next(`/${paramCompany}/home`);
      }
    }
    // 包含了具体路径 例如 /ai/home /ai/login
    else {
      //auto路由加正确的ak参数可以自动登录
      if (new RegExp(`/${paramCompany}/auto`).test(to.path) && (to.query.ak || to.query.token)) {
        let ak = to.query.ak || to.query.token;
        let loginInfoRes = await akVerification(ak);
        if (!loginInfoRes) {
          next("/404");
          return;
        }
        let result = loginSystem(loginInfoRes.data.user_name, loginInfoRes.data.pass_word, paramCompany);

        if (!result) {
          next("/404");
          return;
        } else {
          store.commit("auth/setLoginState", 0);
          store.commit("main/setCompany", paramCompany);
          if (to.query.url) {
            // next(`/${paramCompany}/home/<USER>
            next(`/${paramCompany}/${to.query.url}`);
          } else {
            next(`/${paramCompany}/home`);
          }
          return;
        }
      }
      if (new RegExp(`/${paramCompany}/login`).test(to.fullPath)) {
        //进入登录页的时候 设置一下vuex里的company
        //整个页面的应用的使用过程中 company字段都应取决于登录时设置的值
        store.commit("main/setCompany", paramCompany);
        next();
        return;
      }
      if (store.state.auth.loginState === -1) {
        next(`/${paramCompany}/login`);
      } else {
        //基本上所有跳转都要经过这里
        //拦截需要新建窗口打开的路由
        const reg = new RegExp(`^\/${paramCompany}\/(\\w+)(?:\/\\w+)?`),
          targetSubRoute = to.path.match(reg)[1];
        if (_blankLinks.includes(targetSubRoute)) {
          const subRoute = from.path.match(reg)?.[1];
          //已经是新窗口就不用打开了

          if (
            !subRoute ||
            subRoute === targetSubRoute ||
            // 金华城管局只要跳转到一个全屏界面
            -1 != to.path.indexOf("zhatucheCurrent") ||
            (window.PONY.companyOptions[paraString].layout.loginSuccess &&
              -1 != to.path.indexOf(window.PONY.companyOptions[paraString].layout.loginSuccess) &&
              store.state.main.homeOpen)
          ) {
            next();
          } else {
            const { href } = router.resolve(to);
            if (
              window.PONY.companyOptions[paraString].layout.loginSuccess &&
              -1 != to.path.indexOf(window.PONY.companyOptions[paraString].layout.loginSuccess)
            ) {
              window.open(href + "?noBack=true", "_blank");
            } else {
              window.open(href, "_blank");
            }
            next(false);
          }
        } else {
          next();
        }
      }
    }
  }
  // 例如: /home  /home/<USER>
  else if (companyBlackList.includes(paramCompany)) {
    next(`/${store.state.main.company || "pony"}${to.fullPath}`);
  }
  //错误路径
  else {
    next("/404");
  }
});
export default router;
