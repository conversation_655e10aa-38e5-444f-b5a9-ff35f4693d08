/*
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-10-08 17:25:47
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-31 16:16:48
 */
import moment from "moment";
import {decodEdrData} from "../../axios/api";
import {errorDesc, errorStats,} from "../../view/monitor/page/Monitor/util/ftMonitorHandle";
// import {alarmMessage1, filterData,} from "../../view/monitor/page/Monitor/util/ftDetail";

/**
 *
 * @param {GSPSOCKETTRANSER} message
 */

const vehicleWeiTypeBind = {
  0: "无特定位置",
  1: "圆形区域",
  2: "矩形区域",
  3: "多边形区域",
  4: "路段/路线",
};

// const rollerStatusValue = {
//   0: '未知',
//   1: '正转',
//   2: '反转',
//   3: '停转'
// }

const transOverspeed = (msg) => {
  let list = msg.split("|");
  let result = "无";
  if (list.length) {
    result = vehicleWeiTypeBind[list[0]] || "未知区域" + "超速";
  }
  return result;
};

const transformweilan = (msg) => {
  let list = msg.split("|");
  let result = "无";
  if (list.length) {
    let prefix = list[2] ? "出 " : "进入 ";
    let suffix = vehicleWeiTypeBind[list[0]] || "未知区域";
    result = prefix + suffix;
  }
  return result;
};

const transweilanTime = (msg) => {
  let list = msg.split("|");
  let result = "无";
  if (list.length) {
    result = list[2] ? "过长" : "不足";
  }
  return result;
};
const NumberOf1 = (n)=>{
    // write code here
    //-123的二进制表示为-1111011，123的为1111011，因此首先要得到负数二进制的补码表示
    //其后面部分的补码0000101 = 122的正码1111011按位取反，
    //这个正码加上前面的0即是再全部按位取反即得-123的补码表示
    if(n < 0){
        n = -n;
        n = n-1;
        var str = (Array(32).join("0")+n.toString(2)).slice(-32);
        str = exchange(str);
    }else{
        var str = (Array(32).join("0")+n.toString(2)).slice(-32);
    }

    return str;
}
// //计算1的个数
// function cal(str){
//     var sum = 0;
//     for(var i = 0; i < str.length; i++){
//         if(str[i] == 1){
//             sum ++;
//         }
//     }
//     return sum;
// }
//如果是负数，0变1，1变0
const exchange = (str)=>{
    var arr = str.split('');
    for(var i = 0; i < arr.length; i++){
        if(arr[i] == 0){
            arr[i] = 1;
        }else {
            arr[i] = 0;
        }
    }
    str = arr.join("");
    return str;
}
const transLateTowSupport = (num) => {
  num = Number(num)
  let list = [];
  let detail = NumberOf1(num).split("").reverse();
  if (detail.length) {
    detail.forEach((item, index) => {
      if (+item) {
        list.push(index + 1);
      }
    });
  }
  return list;
};

const transLateMediaError = (num, type) => {
  let media = transLateTowSupport(num);
  if (!media.length) return "无";
  let word;
  switch (type) {
    case 1:
      word = "通道视频信号丢失";
      break;
    case 2:
      word = "通道视频信号遮挡";
      break;
    case 3:
      word = "通道主存储器发送故障";
      media = media.filter((item) => item <= 12);
      break;
    case 4:
      word = "通道灾备存储器发送故障";
      media = media
        .filter((item) => item > 12)
        .map((result) => {
          return result - 12;
        });
      break;
  }
  if (!media.length) {
    if(type == 3 || type == 4){
      return '正常'
    }else {
      return "无"
    }
  };
  let content = media.toString() + word;
  return content;
};
//视频报警的存储器报警区分
const transLateMediaErrorStroStatus = (num, type) => {
  let media = transLateTowSupport(num);
  if (!media.length) return 0;
  switch (type) {
    case 3:
      media = media.filter((item) => item <= 12);
      break;
    case 4:
      media = media
        .filter((item) => item > 12)
        .map((result) => {
          return result - 12;
        });
      break;
  }
  if (!media.length) {
    if (type == 3 || type == 4) {
      return 0;
    }
  }
  return 1;
};
/**
 * 判断 number 的第 position 位是不是 1
 * @param {number} number - 要检查的数字
 * @param {number} position - 要检查的位位置（从 0 开始）
 * @returns {boolean} - 如果第 position 位是 1，则返回 true，否则返回 false
 */
const isBitSet = (number, position)=> {
  // 生成一个掩码，只有第 position 位是 1，其余位都是 0
  let mask = 1 << position;

  // 按位与操作，如果结果不为 0，则第 position 位是 1
  return (number & mask) !== 0;
}
const transformGpsMsg = (message) => {
    let additionalR = {},
    additionalG = {},
    videoAddObj = {},
    erpObj = {},
    aebObj = {}
    //所有的附加信息转成key：value形式
    let extObj = {};
    if (message.ext) {
        message.ext.forEach((item) => {
            extObj[item.a] = item.b;
        });
    }
    let extObjArr = Object.keys(extObj)

    let additionalRArr = ['T1','T3','T4','T5','T6']
    if(additionalRArr.find(it=>extObjArr.includes(it))){
        additionalR = {
            stand_oilNumber: extObj['T1'] || "0",
            stand_exporEventId: extObj['T3'] || "无",
            stand_overspeedState: extObj['T4'] ? transOverspeed(extObj['T4']) : "无",
            stand_inandoutarea: extObj['T5'] ? transformweilan(extObj['T5']) : "无",
            stand_inandoutline: extObj['T6'] ? transweilanTime(extObj['T6']) : "无",
        };
    }


    //erp的数据是一个JsonObject
    if (extObj['S0']) {
        let erpDetail = extObj['S0'];
        erpObj = {
        orderNo: erpDetail.a,
        orderState: erpDetail.b,
        orderMile: erpDetail.c,
        waitTime: erpDetail.d,
        dischargingTime: erpDetail.e,
        };
    }
    if (extObj['T15']) {
      let aebNum = extObj['T15'];
      aebObj = {
        canbox_message_status: isBitSet(aebNum,25) ? '连接' : '未连接',
        engine_ecu_status: isBitSet(aebNum,26) ? '连接' : '未连接',
        engine_work_status: isBitSet(aebNum,27) ? '连接' : '未连接',
        aeb_host_status: isBitSet(aebNum,28) ? '连接' : '未连接',
      };
  }
  // gps.e为终端状态列表(已修改)
  if (message.gps.e) {
    let gdetail = message.gps.e;
    additionalG = {
      acc: gdetail.includes(0),
      stand_lockState: gdetail.includes(1) ? "已定位" : "未定位",
      stand_doorState: gdetail.includes(12) ? "加锁" : "解锁",
      stand_dianState: gdetail.includes(11) ? "异常" : "正常",
      stand_oilState: gdetail.includes(10) ? "异常" : "正常",
      stand_operateState: gdetail.includes(4) ? "停运" : "运营",
    };
  }

  if (extObj['T7'] && extObj['T7'].length) {
    let result = extObj['T7'];
    result.forEach(item=>{
      switch (item){
        case 261:
            videoAddObj.media_lostSignal = transLateMediaError(extObj["T8"], 1);
            videoAddObj.media_261 = 1;
            break;
        case 262:
            videoAddObj.media_fadeSignal = transLateMediaError(extObj["T9"], 2);
            videoAddObj.media_262 = 1;

            break;
        case 263:
            videoAddObj.media_mainStroStatus = transLateMediaError(extObj['T10'], 3);
            videoAddObj.media_auxStroStatus = transLateMediaError(extObj['T10'], 4);
            videoAddObj.media_main_263 = transLateMediaErrorStroStatus(
              extObj["T10"],3);
            videoAddObj.media_aux_263 = transLateMediaErrorStroStatus(
              extObj["T10"],
              4
            );;

            break;
        case 264:
            videoAddObj.media_otherStroStatus =
              result.indexOf(264) !== -1 ? "异常" : "无";
            videoAddObj.media_264 = 1;
              break;
        case 265:
            videoAddObj.media_265 = 1
            break;
        case 266:
            videoAddObj.media_266 = 1
            break;
        case 267:
            videoAddObj.media_numBeyond =
              result.indexOf(267) !== -1 ? "异常" : "无";
            videoAddObj.media_267 = 1;
              break;
      }
    })
    // if (result.includes(261) && extObj['T8']) {
    //   videoAddObj.media_lostSignal = transLateMediaError(extObj['T8'], 1);
    // }else {
    //   videoAddObj.media_lostSignal = '无'
    // }
    // if (result.includes(262) && extObj['T9']) {
    //   videoAddObj.media_fadeSignal = transLateMediaError(extObj['T9'], 2);
    // }else {
    //   videoAddObj.media_fadeSignal = '无'
    // }
    // if (result.includes(263) && extObj['T10']) {
    //   videoAddObj.media_mainStroStatus = transLateMediaError(extObj['T10'], 3);
    //   videoAddObj.media_auxStroStatus = transLateMediaError(extObj['T10'], 4);
    // }else {
    //   videoAddObj.media_mainStroStatus = '正常'
    //   videoAddObj.media_auxStroStatus = '正常'
    // }
    // videoAddObj.media_otherStroStatus =
    //   result.indexOf(264) !== -1 ? "异常" : "无";

    // videoAddObj.media_numBeyond = result.indexOf(267) !== -1 ? "异常" : "无";
  } else {
    videoAddObj = {
      media_lostSignal: "无",
      media_fadeSignal: "无",
      media_mainStroStatus: "正常",
      media_auxStroStatus: "正常",
      media_otherStroStatus: "无",
      media_numBeyond: "无",
      media_261: 0,
      media_262: 0,
      media_main_263: 0,
      media_aux_263: 0,
      media_264: 0,
      media_265: 0,
      media_266: 0,
      media_267: 0,
    };
  }


  let vehicleBasic = {
    id: message.a,
    terminalNo: message.b,
    gpsTime: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    latlng: [+message.gps.a[0].toFixed(6), +message.gps.a[1].toFixed(6)],
    gpsSpeed: message.gps.b,
    pulseSpeed: extObj["T2"] || 0,
    dire: message.gps.c,
    height: message.gps.d,
    terminalStatus: message.gps.e,
    gpsAlarm: message.gps.f,
    mile: extObj["S1"] || 0,
    location: message.gps.g,
    gpsSingle: extObj["T13"] || 0,
    gsmSingle: extObj["T12"] || 0,
    extand_detail: extObj,
    engine_mile:extObj["T56"] || 0,
    curvature:extObj["T20"] || '-',
    coldchain_rota_speed: extObj["T58"] || 0,
    coldchain_oil_consum: extObj["T57"] || 0,
    rollerState: extObj["T59"] ? extObj["T59"] : null,
    fuelEconomy: extObj["T66"] || 0,
    coldchain_job_status: extObj["T65"] ? extObj["T65"] : extObj["T65"] === 0 ? 0 : '-',

    dischargingNumber: extObj["T59"]?.split("|")[2] || null,
    M600_doorStatus: extObj["T60"] ? extObj["T60"] : null,
    M600_compressorStatus: extObj["T61"] ? extObj["T61"] : null,
    M600_carriageStatus: extObj["T62"] ? extObj["T62"] : null,

    mini_analog: extObj["T25"] || "-", // 模拟量
    wash_water_level:extObj["T25"]?extObj['T25'][0] :'-', //水位百分比
    mini_io_status: extObj["T24"] || "-", // IO状态
    mini_temp: extObj["T23"] || "-", // 车厢温度
    mini_tire_pressure: extObj["T22"] || "-", // 胎压
    gsmSinglep: extObj["T12"] || 0, // 网络信号强度
    ext_status: extObj["T11"] || "-", // 车辆信号状态
    signal_status: extObj["T11"] || [], // 车辆信号状态
    tireStatus: extObj["T22"] || "-", // 胎压
    terminalStatusArray: message.gps.e,
  };
  return Object.assign(
    additionalR,
    additionalG,
    videoAddObj,
    vehicleBasic,
    erpObj,
    aebObj
  );
};

const transForVehicleOnline = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    // time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    time: moment(message.c).valueOf(),

  };
};
const transformObdInfo = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    terminalNo: message.b,
    obdTime: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    gsix_protocol: message.d,
    gsix_mil: message.mil,
    gsix_support: message.f,
    gsix_ready: message.g,
    gsix_vin: message.h,
    gsix_scn: message.i,
    gsix_cvn: message.j,
    gsix_errors: message.k,
  };
};
const transformErpOrderChange = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminal_no: message.b,
    order_id: message.c,
  };
};
// const transformCanDataUpload = (message) => {
//   let resultObj = {};
//   let firstNew = [];
//   let obj = {};
//   message.d.forEach((item) => {
//     if (obj[item.b]) {
//       obj[item.b].push(item);
//     } else {
//       obj[item.b] = [item];
//     }
//   });

//   let ObjArr = Object.values(obj).map((item) => {
//     return item.sort((val1, val2) => {
//       return Number(val2["a"]) - Number(val1["a"]);
//     });
//   });
//   firstNew = ObjArr.map((item) => {
//     return item[0];
//   });

//   firstNew.forEach((item) => {
//     if(!item || !item.c || !item.c.length || !Array.isArray(item.c) )return
//     item.c.forEach((val) => {
//       if(!val)return
//       let splitArr = val.split("=");
//       resultObj[alarmMessage1[Number(splitArr[0])]] = splitArr[1];
//     });
//   });
//   let objFinally = filterData(resultObj);
//   return Object.assign(
//     {
//       cmd: message.cmd,
//       vehicleId: message.a,
//       // terminal_no: message.b,
//       canUploadTime: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
//     },
//     objFinally
//   );
// };
// adas和dms 报警统一
const transformAdasMsg = (message) => {
  let media = {}
  if (message.t) {
    media = {
      filename: message.t.a || null,
      filetype: message.t.b || null,
      filezie: message.t.c || null,
      url: message.t.d || null,
    }
  }
  let alarm = {}
  if (message.u) {
    alarm = {
      miles: message.u.a || null,
      frontDistance: message.u.b || null,
      frontRelativeSpeed: message.u.c || null,
      limitSpeed: message.u.d || null,
      duration: message.u.e || null,
      alarmSerial: message.u.f || null,
      alarmLevel: message.u.g || null,
      minAngularSpeed: message.u.h1 || null,
      maxAngularSpeed: message.u.h2 || null,
      minSpeed: message.u.h3 || null,
      maxSpeed: message.u.h4 || null,
      minRPM: message.u.h5 || null,
      maxRPM: message.u.h6 || null,
      decelerations: message.u.h7 || null,
      rolloverSpeed: message.u.h7 || null,
      rolloverAngle: message.u.h9 || null,
      failureCode: message.u.h10 || null,
      lostSignal: message.u.i1 || null,
      fadeSignal: message.u.i2 || null,
      storageStatus: message.u.i3 || null,
      abnormalBehavior: message.u.i4 || null,
      deviceTemp: message.u.j1 || null,
      onStatus: message.u.j2,
      compressorStatus: message.u.j3 ,
      doorStatus: message.u.j4 ,
      alarmChannel: message.u.j5 || null,
      alarmTempSetting: message.u.j6 || null,
      alarmTemp: message.u.j7 || null,
      avgTemp: message.u.j8 || null,
      extremeTemp: message.u.j9 || null,
      areaType: message.u.j10 || null,
      loadingStatus: message.u.j11|| null,
      axlePos: message.u.k1 || null,
      tyrePos: message.u.k2 || null,
      tyrePressure: message.u.k3 || null,
      tyreTemp: message.u.k4 || null,
      power: message.u.k5 || null,
      jurisdiction_name:message.u.l2 || null,
      acc33Param:message.u.m1||'',
      fct56Param:message.u.m2||'',
    }
  }

    return {
      cmd: message.cmd,
      vehicleId: message.a,
      terminalNo: message.b,
      alarmId: message.c,
      alarmType: message.d,
      dire: message.e || null,
      height: message.f || null,
      terminalStatus: message.g || null,
      beginTime: message.h || null,
      beginSpeed: message.i || 0,
      beginLatlng: message.j || null,
      beginLocation: message.k || null,
      beginRoadname: message.l || null,
      beginRoadlevel: message.m || null,
      endTime: message.n || null,
      endSpeed: message.o || null,
      endLatlng: message.p || null,
      endLocation: message.q || null,
      endRoadname: message.r || null,
      endRoadlevel: message.s || null,
      mediaList: media,
      alarmParam: alarm,
      alarmOrigin: message.v,
      alarmFlag: message.w || 0,
    };
};

// const transformDmsAlarm = (message) => {
//   return {
//     cmd: message.cmd,
//     alarmId: message.c,
//     vehicleId: message.a,
//     terminalNo: message.b,
//     alarmTime: moment(message.d, "YYYYMMDDHHmmss").valueOf(),
//     alarmType: message.e,
//     latlng: [+message.gps.b.toFixed(6), +message.gps.a.toFixed(6)],
//     gpsSpeed: message.gps.c,
//     pulseSpeed: message.gps.d,
//     location: message.gps.o,
//     hasMedia: !!message.f && !!message.f.length,
//     isVideo: 0,
//   };
// };

// 2022.04.24 与AlARM合并了
const transformGpsAlarmMsg = (message) => {
    //所有的附加信息转成key：value形式
    // let extObj = {};
    // if (message.ext) {
    //     message.ext.forEach((item) => {
    //         extObj[item.a] = item.b;
    //     });
    //    }
    let gcj02LatLng
    if(!message.h){
        gcj02LatLng = null
    }else {
        gcj02LatLng = message.h.a
    }
    let detail = ''

    if(message.f == 261 || message.f == 262 || message.f == 266 || message.f == 263){
      if(!message.g)return
      let messageG = JSON.parse(message.g)
      switch (message.f) {
        case 261:
          if(!messageG.lostSignal)return
          detail = transLateMediaError(messageG.lostSignal,1)
          break;
        case 262:
          if(!messageG.fadeSignal)return
          detail = transLateMediaError(messageG.fadeSignal,2)
            break;
        case 263:
          if(!messageG.storageStatus)return

          detail = (transLateMediaError(messageG.storageStatus,3) == '无'?'':transLateMediaError(messageG.storageStatus,3)) + (transLateMediaError(messageG.storageStatus,4) == '无'?'':transLateMediaError(messageG.storageStatus,4))
            break;
        case 266:
          if(!messageG.abnormalBehavior)return
          let list = transLateTowSupport(messageG.abnormalBehavior.split('|')[0])


          if(!list.length){
            detail = ''
          }else{
            if(list.includes(3)){
              detail = '抽烟'
            }
            if(list.includes(2)){
              if(detail){
                detail += ',打电话'
              }else {
                detail = '打电话'
              }
            }
            if(list.includes(1)){
              if(detail){
                detail += ',疲劳'
              }else {
                detail = '疲劳'
              }

            }
            if(messageG.abnormalBehavior.split('|')[1]){
              if(detail){
                detail += ',疲劳程度:' + messageG.abnormalBehavior.split('|')[1]

              }else {
                detail += '疲劳程度:' + messageG.abnormalBehavior.split('|')[1]
              }
            }
          }
            break;

        default:
          break;
      }
    }

  return {
    cmd: message.cmd,
    alarmId: message.c,
    vehicleId: message.a,
    terminalNo: message.b,
    alarmTime: moment(message.d, "YYYYMMDDHHmmss").valueOf(),
    alarmAddTime: message.e,
    alarmType: message.f,
    alarmParam: message.g,
    latlng: gcj02LatLng?[+gcj02LatLng[1].toFixed(6), +gcj02LatLng[0].toFixed(6)]:null,
    gpsSpeed: message.h?.b || 0,
    pulseSpeed: 0,
    location: message.h?.g || '',
    hasMedia: false,
    alarmDetail:detail
  };
};

const transForMessagePush = (message) => {
  return {
    cmd: message.cmd,
    msg_id: message.b,
    msg_desc: message.t,
    msg_content: message.c,
    msg_level: +message.d,
    msg_state: 1,
    msg_time: moment(message.e, "YYYYMMDDHHmmss").valueOf(),
    file_list: message.file_list || [],
  };
};
const transformDownloadMsgEndInform = (message) => {
  return {
    cmd: message.cmd,
    vehicleNo: message.a,
    vehicleColor:message.b,
    code:message.c,
    id: message.d,
    filePath: message.i,
  };
};
const transformTaskResponse = (message) => {
  return {
    cmd: message.cmd,
    vehicle_id: message.a,
    plateNo:message.plateNo,
    terminal_no: message.c.b,
    message: message.c.e,
    task_id: message.b,
    cmd_id: message.c.a,
    task_cmd: message.c.cmd,
    task_time: message.c.c,
    task_state: message.c.d,
    result: message.c.f,
  };
};
const transformSgsPictureStateShare = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    id:message.b,
    status: message.c,
    list: message.d ? JSON.parse(message.d) : null,
  };
};

const transformMotorInfo = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    terminalNo: message.b,
    motorTime: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    motorSpeed: message.d,
    gsix_pressure: message.e,
    gsix_engine: message.f,
    gsix_engine_speed: message.h,
    gsix_rub: message.g,
    gsix_oil: message.h,
    gsix_src_upper: message.j,
    gsix_src_lower: message.k,
    gsix_rest: message.l,
    gsix_gas: message.m,
    gsix_src_temp1: message.n,
    gsix_src_temp2: message.o,
    gsix_dpf: message.p,
    gsix_cool_temp: message.q,
    gsix_oil_rate: message.r,
  };
};

/**
 * @description 上述命名与功能页面结合较高 有自主名字 以下统一命名
 */

const transformMediaResult = (message) => {
  return {
    cmd: message.cmd,
    mediaId: message.b,
    vehicleId: message.a,
    code: message.c,
    message: message.d,
    mediaUrl: message.e,
  };
};

const transformMediaLineUp = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    mediaId: message.b,
    position: message.c,
  };
};
const transformBackgroundTaskSuccess = (message) => {
  return {
    cmd: message.cmd,
    id: message.b,
    duration: message.c,
    size: message.d,
    link: message.e,
    status:message.f,
    filename:message.g,
    page:message.h,
    param:message.i,
    createTime:message.j
  };
};
const transformAlarmEvent = (message) => {
  const start = moment(message.c, "YYYYMMDDHHmmss");
  const end = moment(message.d, "YYYYMMDDHHmmss");
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    eventId: message.b,
    startTime: start.format("YYYY-MM-DD HH:mm:ss"),
    endTime: end.format("YYYY-MM-DD HH:mm:ss"),
    runTime: end.diff(start, "minutes"),
    eventType: message.e,
    mileage: message.g.j,
    startAddRess: message.g.o,
    endAddRess: message.h.o,
    hasMedia: !!message.f,
    alarmlist: message.i.split(","),
  };
};
const transformTerminalLink = (message) => {
  const time = moment(message.c, "YYYYMMDDHHmmss")
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: time.format("YYYY-MM-DD HH:mm:ss"),
    times:moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    status: message.d,
    statusDes: message.d == 0 ? "上线" : "下线",
  };
};

const transformDangerEvent = (message) => {
  const start = message.c && moment(message.c, "YYYYMMDDHHmmss");
  const end = message.d && moment(message.d, "YYYYMMDDHHmmss");
  return {
    cmd: message.cmd,
    vehicleId: message.a, //  车辆id
    eventId: message.b, //  事件id
    startTime: start && start.format("YYYY-MM-DD HH:mm:ss"), //  报警开始时间
    endTime: end && end.format("YYYY-MM-DD HH:mm:ss"), //  报警结束时间
    runTime: end && end.diff(start, "minutes"), //  行驶时长
    eventType: message.e, //  报警类型   -----   疲劳驾驶
    startAddress: message.f, //  开始位置
    endAddress: message.g, //  结束位置
    dealType: message.h,
    fenseId: message.i,
    fenseName: message.j,
    eventParams: message.k,
    driverName: message.l,
    riskLevel: message.m,
    dealDesc: message.n,
    riskValue: message.q,
    level: message.w | null
  };
};

const transformDangerEventProcessed = (msg) => {
  return {
    cmd: msg.cmd,
    eventId: msg.b,
    vehicleId: msg.a,
    dealType: msg.c,
    dealDesc: msg.d,
    dealUser: msg.f,
    attachment: msg.e,
    role:msg.role
  };
};

const transformDangerEventEnd = (msg) => {
  return {
    cmd: msg.cmd,
    eventId: msg.b,
    endTime: msg.d,
    endLocation: msg.g,
    fenseId: msg.i,
    fenseName: msg.j,
    eventParams: msg.k,
  };
};

const transformTrailState = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    gpsTime: moment(message.b).format("YYYY-MM-DD HH:mm:ss"),
    trailState: message.c, // 1 开始装车 2 装车行驶 3 开始卸车 4 卸车完成
  };
};

const transformStationState = (msg) => {
  return {
    cmd: msg.cmd,
    vehicleId: msg.a,
    lineId: msg.b,
    stationName: msg.c,
    state: msg.d, //  0 路上 1 出战 2 进站
    stationDistance: msg.e,
    distance: msg.f,
  };
};

const transformMediaCommand = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    mediaId: message.c,
    mediaType: message.d,
    mediaCodec: message.e,
    eventCode: message.f,
    channelId: message.g,
    url: message.h,
    mediaTime: message.i,
    gps: message.gps,
  };
};

const transformAddBaseMSG = (message) => {
  return {
    // 链路id
    id: message.a,
    // 车牌号
    plateNo: message.b,
  };
};

const transformTerminalInfo = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    terminalType: message.c,
    producerID: message.d,
    terminalModel: message.e,
    terminalID: message.f,
    ICCID: message.g,
    hardwareVer: message.h,
    firmwareVer: message.i,
    gnss: message.j,
    gsm: message.k,
    message,
  };
};

const transformDataDriverInfo = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    status: message.d,
    result: message.e,
    driverName: message.f,
    certId: message.g,
    organization: message.h,
    expirationDate: message.i,
    idNumber: message.j,
  };
};

const transformAskAnswer = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    answerID: message.d,
  };
};
// (已删除)
// const transformRecordRespond = (message) => {
//   return {
//     cmd: message.cmd,
//     vehicleId: message.a,
//     terminalNo: message.b,
//     cmdID: message.c,
//     result: message.d,
//     mediaList: message.e,
//   };
// };

/**
 *
 */
const transformCANData = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    data: message.d,
    name: "CAN总线数据上传",
  };
};

const transformCompressData = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment().valueOf(),
    data: message.c, //  base64编码
    name: "数据压缩包上报",
  };
};

const transformEventReport = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment().valueOf(),
    data: message.c, //  事件ID
    name: "事件报告",
  };
};

const transformSMSDemand = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment().valueOf(),
    type: message.c,
    code: message.d,
    data: message.d ? "点播" : "取消点播", //  结果
    name: "信息点播/取消",
  };
};

// 二进制数据解析
const transFormSecondNumber = async (result) => {
  let data = {
    src_type: "DataTransfer",
    src_data: result,
  };
  let detail = await decodEdrData(data);
  if (!detail || detail.status != 200) {
    return "[ERROR::] 数据解析失败";
  }
  return detail.data;
};

// 需要解析
const transformDataTransfer = async (message) => {
  let data = await transFormSecondNumber(message.d);
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment().valueOf(),
    type: message.c,
    result: message.d,
    data: data,
    name: "数据上行透传",
  };
};

// 需要解析
const transformDataWayBill = async (message) => {
  let data = await transFormSecondNumber(message.c);
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: moment().valueOf(),
    result: message.c,
    data: data,
    name: "电子运单上报",
  };
};

const transformMediaEvent = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    mediaId: message.c,
    mediaType: message.d, //  0、图片 1、音频 2、视频
    mediaCode: message.e, //  0、JPEG 1、TIF 2、MP3 3、WAV 4、WMV
    eventCode: message.f, //  0、平台下发 1、定时动作
    channelId: message.g,
    time: moment().valueOf(),
    name: "多媒体事件信息上报",
  };
};

const ReportUserDesc = ["链路正常", "链路未连接", "链路未登录"];
const transformReportUserOnOff = (message) => {
  return {
    cmd: message.cmd,
    linkId: message.a,
    parentId: message.b,
    state: message.c,
    data: `主${ReportUserDesc[message.c[0]]}, 次${
      ReportUserDesc[Math.abs(message.c[1])]
    }`,
    time: moment().valueOf(),
    reason: message.d,
    name: "用户在线离线通知",
  };
};

const WarnMsgDesc = {
  1: "车载终端",
  2: "企业监控平台",
  3: "政府监控平台",
  9: "其他",
};
const transformWarmMsgUrgeTodoReq = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    warnSrc: message.b,
    warmType: message.c,
    time: message.d,
    supervisionId: message.e,
    supervisionEndtime: message.f,
    supervisionLevel: message.byte,
    supervisor: message.h,
    supervisorTel: message.i,
    supervisorEmail: message.j,
    data: WarnMsgDesc[message.b] + "督办",
    name: "报警督办",
  };
};

const transformGPSTimeLocation = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    taskId: message.b,
    result: message.c,
    desc: message.d,
    progress: message.f,
    lastVehicle: message.g,
  };
};

const transformTempUpload = (message) => {
  // if(message.m == 1){
  //   return {
  //     dataType:message.m,
  //   }
  // }
  const gcj02LatLng = [message.f,message.e]

  let templist = message.k.map((item) => {
    let tempdesc = errorStats.includes(item.c)
      ? errorDesc[item.c]
      : item.c / 10;

    return {
      pass: item.a,
      state: item.b,
      temp: tempdesc,
      alarmTemp: item.c,
      minTemp: item.d,
      maxTemp: item.e,
    };
  });
// // 排序
//   var compare = function(prop) {
//     return function(obj1, obj2) {
//       var val1 = obj1[prop];
//       var val2 = obj2[prop];
//       if (val1 < val2) {
//         return -1;
//       } else if (val1 > val2) {
//         return 1;
//       } else {
//         return 0;
//       }
//     };
//   };
//   templist.sort(compare('pass'));
  let tempdesc = templist
    .map((item) => `通道${item.pass}: ${item.temp}`)
    .toString();
  let listObj = {}
  if(message.m == 1){
    listObj.ftt_humiditylist = templist
  }else {
    listObj.ftt_templist = templist
  }
  return Object.assign({
    id: message.a,
    terminalNo: message.b,
    dataType:message.m,
    // ftt_zaiz: '-',
    ftt_time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    ftt_temp: message.d, //  设备自身温度
    ftt_latlng: [+gcj02LatLng[1].toFixed(6), +gcj02LatLng[0].toFixed(6)],
    ftt_speed: message.g,
    ftt_onState: message.h,
    ftt_ysj: message.i,
    ftt_doorstate: message.j,
    ftt_tempdesc: tempdesc,
  },listObj);
};
const transformTempEvent = (message) => {
  let zaizDesc = ["空载", "未满载", "重载"];
  let zaiz = null;
  if (message.c == 0x5a) {
    zaiz == zaizDesc[message.j];
  }
  return {
    id: message.a,
    terminalNo: message.b,
    ftt_zaiz: zaiz,
    ftt_time: moment(message.d, "YYYYMMDDHHmmss").valueOf(),
  };
};
const transformPlatMsgInfoReq = (message) => {
    let deptList = ['下级平台所属单一平台','当前连接的下级平台','下级平台所属单一业户','下级平台所属所有业户','下级平台所属所有平台','下级平台所属所有平台和业户',
                    '下级平台所属所有政府监管平台（含监控端）','下级平台所属所有企业监控平台','下级平台所属所有经营性企业监控平台','下级平台所属所有非经营性企业监控平台',]
  return {
    cmd: message.cmd,
    objectType: deptList[message.a],
    objId: message.b,
    infoId: message.c,
    infoContent: message.d,
    time: moment().format('YYYY-MM-DD HH:mm:ss'),
  };
};

const transformEquipmentTempUpload = (message) => {
  let tempList = message.h.map((item) => {
    return {
      channel: item.i,
      temp: item.c,
      humidity: item.d,
      tempStatus: item.f,
      humidityStatus: item.g,
    };
  });
  return {
    cmd: message.cmd,
    equipmentCode: message.b,
    timeView: moment(message.c, "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm:ss"),
    power: message.f,
    powerStatus: message.g,
    tempList,
  };
};
const transformOverTempAlarmProcessed = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    id: message.b,
    dealType: message.c,
    dealDesc: message.d,
    dealUser: message.e,
  };
};

const transformForeignInOut = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    terminalNo: message.b,
    juris_time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    juris_type: message.d,
    plateNo: message.e,
    juris_id: message.f,
    juris_name: message.g,
    belong_juris_id: message.h,
    belong_juris_name: message.i,
    gps: transformGpsMsg(message),
  };
};
const transformFotonVehicleSubscribe = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    time: moment(message.b, "YYYYMMDDHHmmss").valueOf(),
    statusCall: message.c,
    statusResult: message.d,
    data: message.e,
  };
};

// 茅台电子锁状态上报
const transformLockStatus = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    terminalNo: message.b,
    time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    statusView: message.d == 0 ? "开" : "关",
    lock: message.e == 0 ? "解封" : "施封",
    //电量单位（毫伏）
    power: message.f,
    //防拆开关状态
    antiBroken: message.g == 0 ? "正常" : "被拆触发",
    reason: message.h,
    password: message.j,

    subLocks: message.i == 0 ? "关" : message.i == 1 ? "开" : "无法获取状态",
    batteryPercent:cumputePercent(message.f),
    chargeState:message.l==0?'未充电':message.l==1?'充电中':'充满电',
    motionState:message.m==0?'运动状态':'静止状态',
    // gps: transformGpsMsg(message)
  };
};
const cumputePercent = (nVol)=>{
    nVol = (nVol / 1000).toFixed(2);

    if ( nVol <= 3 )
    {
        return 0;
    }

    else if ( nVol >= 4.2 )
    {
        return 100;
    }

    var  nBase   = 0;
    var  nMax    = 0;
    var  nMin    = 0;
    var  nMult   = 10;

    if ( nVol < 4.2 && nVol >= 4.06 )
    {
        nBase   = 90;
        nMax    = 4.2;
        nMin    = 4.06;
    }
    else if ( nVol < 4.06 && nVol >= 3.98 )
    {
        nBase   = 80;
        nMax    = 4.06;
        nMin    = 3.98;
    }
    else if ( nVol < 3.98 && nVol >= 3.92 )
    {
        nBase   = 70;
        nMax    = 3.98;
        nMin    = 3.92;
    }
    else if ( nVol < 3.92 && nVol >= 3.87 )
    {
        nBase   = 60;
        nMax    = 3.92;
        nMin    = 3.87;
    }
    else if ( nVol < 3.87 && nVol >= 3.82 )
    {
        nBase   = 50;
        nMax    = 3.87;
        nMin    = 3.82;
    }
    else if ( nVol < 3.82 && nVol >= 3.79 )
    {
        nBase   = 40;
        nMax    = 3.82;
        nMin    = 3.79;
    }
    else if ( nVol < 3.79 && nVol >= 3.77 )
    {
        nBase   = 30;
        nMax    = 3.79;
        nMin    = 3.77;
    }
    else if ( nVol < 3.77 && nVol >= 3.74 )
    {
        nBase   = 20;
        nMax    = 3.77;
        nMin    = 3.74;
    }
    else if ( nVol < 3.74 && nVol >= 3.68 )
    {
        nBase   = 10;
        nMax    = 3.74;
        nMin    = 3.68;
    }
    else if ( nVol < 3.68 && nVol >= 3.45 )
    {
        nBase   = 5;
        nMax    = 3.68;
        nMin    = 3.45;
        nMult   = 5;
    }
    else if ( nVol < 3.45 && nVol >= 3 )
    {
        nBase   = 0;
        nMax    = 3.45;
        nMin    = 3;
        nMult   = 5;
    }

    var  nResult = nBase + nMult * ( nVol - nMin ) / ( nMax - nMin );
    return Math.round(nResult) +'%';
}
// 茅台电子锁随机密码上报
const transformLockPassword = (message) => {
  return {
    cmd: message.cmd,
    id: message.a,
    terminalNo: message.b,
    time: moment(message.c, "YYYYMMDDHHmmss").valueOf(),
    password: message.d,
    gps: transformGpsMsg(message),
  };
};
// 茅台申请开锁流程
const transformBillLockProcess = (message) => {
  return {
    cmd: message.cmd,
    key: message.key,
    id: message.a,
    judge: message.judge,
    plateNo: message.a1 || "",
    driverName: message.a2 || "",
    driverPhone: message.a3 || "",
    location: message.a4 || "",
    billId: message.b || "",
    orderNo: message.c || "",
    dealId: message.d || "",
    dealName: message.d1 || "",
    approvalTime: message.e || "",
    approvalName: message.e1 || "",
    approvalRemark: message.e2 || "",
    attachment: message.e3 || [],
    approvalPhone: message.e4 || "",
    unlockTime: message.g,
    unlockName: message.g1,
    unlockRemark: message.g2,
  };
};
// 终端新视频属性查询
const transformMediaProperty = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    audioCodec: message.c,
    ac: message.d,
    sample_rate: message.e,
    bit: message.f,
    aframeLen: message.g,
    audioOutput: message.h,
    videoCodec: message.i,
    audioChannel: message.j,
    videoChannel: message.k,
  };
};
//绑定温度传感器ID
const transformBindTempSensorID = (message) => {
  return {
    cmd: message.cmd,
    channelNo: message.a,
    command: message.b,
    result: message.c,
    sensorID: message.d,
  };
};
// 报警事件处理完成通知
 const transformAlarmDealProcessed = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    alarmId: message.b,
    dealTime: message.c,
    dealType: message.d,
    dealDesc: message.e,
    dealUser: message.f,
    alarmCode: message.g,
    alarmDate: moment(message.h, "YYYYMMDDHHmmss").valueOf(),
  };
};
const transformGpsAlarmDeal = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    alarmId: message.b,
    dealTime: message.c,
    dealType: message.d,
    dealDesc: message.e,
    dealUserId: message.f,
  };
}
const transformGpsAlarmDealt = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    alarmId: message.b,
    dealTime: message.c,
    dealType: message.d,
    dealDesc: message.e,
    dealUserId: message.f,
    alarmType:message.g
  };
}

const transformGNSS = (message) => {
  return {
    cmd: message.cmd,
    vehicleId: message.a,
    terminalNo: message.b,
    time: message.c,
    bdsList: transformGNSSMessage(message.d) || [],
    gpsList: transformGNSSMessage(message.e) || [],
    glonassList: transformGNSSMessage(message.f) || [],
    galileoList: transformGNSSMessage(message.g) || [],
    gps: transformGpsMsg(message)
  }
}

const transformGNSSMessage = (message) => {
  return message.map((item) => {
    return {
      satelliteNo: item.a,
      elevation: item.b,
      azimuth: item.c,
      ratio: item.d
    }
  })
}
// 区域围栏上报信息
const transformAreaList = (message) => {
    return {
      cmd:message.cmd,
      vehicleId: message.a,
      terminalNo: message.b,
      type: message.c, //区域或者线路类型
      areaList : message.d
    }
}

// 特殊报警通知
const transformSpecialAlarm = (message) => {
  return {
    vehicleId: message.a,
    terminalNo: message.b,
    alarmId: message.c,
    alarmType: message.d,
    alarmTime: moment(message.e, "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm:ss"),
    liveUrls: transformList(message.f),
    gps: transformGpsMsg(message)
  }
}
// 特殊报警通知
const transformWasteCollectMsg = (message) => {
  return {
    vehicleId: message.a,
    plateNo: message.a1,
    time: moment(message.b, "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm:ss"),
    hourTime:moment(message.b, "YYYYMMDDHHmmss").format("HH:mm"),
    type: message.c,
    lineId:message.e,
    areaId:message.d2,
    pointId:message.f
  }
}

const transformList = (message) => {
  let arr = Object.getOwnPropertyNames(message)
  message = arr.map((i) => Number(i))
  return message
}
const transformJianhuaBillAndLoad= (message)=>{
  return {
    vehicleId: message.a,
    isload: message.c,
    isbill: message.b,
    time: moment(message.e, "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm:ss"),
  }
}
export {
  transformGpsMsg,
  transformAdasMsg,
  transformGpsAlarmMsg,
  transForMessagePush,
  transformMediaResult,
  transformMediaLineUp,
  transformAlarmEvent,
  transformDangerEvent,
  transformTrailState,
  transformStationState,
  transformMediaCommand,
  transformAddBaseMSG,
  transformTaskResponse,
  transformDownloadMsgEndInform,
  transformObdInfo,
  transForVehicleOnline,
  transformMotorInfo,
  transformTerminalInfo,
  transformDataDriverInfo,
  transformAskAnswer,
  transformCANData,
  transformCompressData,
  transformEventReport,
  transformSMSDemand,
  transformDataTransfer,
  transformDataWayBill,
  transformMediaEvent,
  transformReportUserOnOff,
  transformWarmMsgUrgeTodoReq,
  transformDangerEventProcessed,
  transformDangerEventEnd,
  transformGPSTimeLocation,
  transformTempUpload,
  transformTempEvent,
  transformEquipmentTempUpload,
  transformOverTempAlarmProcessed,
  transformForeignInOut,
  transformErpOrderChange,
  // transformCanDataUpload,
  transformLockStatus,
  transformLockPassword,
  transformBillLockProcess,
  transformPlatMsgInfoReq,
  transformTerminalLink,
  transformMediaProperty,
  transformBindTempSensorID,
  transformAlarmDealProcessed,
  transformGpsAlarmDeal,
  transformGpsAlarmDealt,
  transformGNSS,
  transformAreaList,
  transformSpecialAlarm,
  transformFotonVehicleSubscribe,
  transformWasteCollectMsg,
  transformSgsPictureStateShare,
  transformBackgroundTaskSuccess,
  transformJianhuaBillAndLoad
};
