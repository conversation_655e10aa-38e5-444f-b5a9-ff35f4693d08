<template>
		<div class="checkbox-container">
				<div class="query-wrap">
						<div class="query-select">
								<div class="item" :class="alarmType === 'all' ? 'active' : ''" @click="changeAlarm('all')" title="全部报警">
										<i class="pony-iconv2 pony-quanbu"></i>
								</div>
								<div class="item" :class="alarmType === 'category' ? 'active' : ''" @click="changeAlarm('category')" title="分组报警">
										<i class="pony-iconv2 pony-fenzu"></i>
								</div>
						</div>
						<el-select v-model="query.value" class="filter-value" remote
						           :remote-method="filterChange" filterable clearable
						           @clear="filterClear"
						>
								<el-option v-for="(item,index) in query.selectorList" :key="item.id"
								           :label="item.name" :value="item.type"></el-option>
						</el-select>
						<div class="manage" v-if="alarmType !== 'all'" @click="alarmGroup" title="前往分组管理">
								<i class="pony-iconv2 pony-fenzuguanli"></i>
						</div>
				</div>
				<div class="alarm-tree" ref="treeWrap">
						<el-tree v-show="alarmType === 'all'"
						         :data="alarmTreeData"
						         style="margin: 10px"
						         ref="allTree"
						         :show-checkbox="checkMode"
						         :check-on-click-node="true"
						         node-key="type"
						         :default-checked-keys="defaultChecked"
						         :default-expanded-keys="expandedKeys"
						         @check="onCheck"
								>
								<div  slot-scope="{ node, data }" class="custom-tree-node">
									<div class="node-wrap">
										<span class="node-title">
											<span :class="data.className"></span>
											{{data.name}}
										</span>
										<el-tag type="danger" size="mini" v-if="!!alarmCount[data.type]" v-show="showAlarmCount">{{alarmCount[data.type]}}</el-tag>
									</div>
								</div>
						</el-tree>
						<el-tree v-show="alarmType === 'category'"
						         :data="groupTreeData"
						         style="margin: 10px"
						         ref="groupTree"
						         :show-checkbox="checkMode"
						         :check-on-click-node="true"
						         node-key="id"
						         :default-checked-keys="defaultCheckedCategory"
						         :default-expanded-keys="['0']"
						         @check="onCheckGroup"
						>
						<div slot-scope="{ node, data }" class="custom-tree-node">
							<div class="node-wrap" v-if="data.type === -2">
								<span class="node-title"> 
									<span :class="data.iconSkin"></span>
									{{data.name}}
								</span>
								<el-tag type="danger" size="mini" v-if="!!alarmCountGroup[data.id]" v-show="showAlarmCount">{{alarmCountGroup[data.id]}}</el-tag>
							</div>
							<div class="node-wrap" v-else>
								<span class="node-title">
									<span :class="data.iconSkin"></span>
									{{data.name}}
								</span>
								<el-tag type="danger" size="mini" v-if="!!alarmCountGroup[data.type]" v-show="showAlarmCount">{{alarmCountGroup[data.type]}}</el-tag>
							</div>
						</div>
						</el-tree>
				</div>
		</div>
</template>


<script>
/**
 * @Author: jinzh
 * @Email: <EMAIL>
 * @Date: 2018-11-06 17:19:30
 * @LastEditors: yezy
 * @LastEditTime: 2020-07-09 14:30:49
 * @Description:
 */
import {mapState} from 'vuex'
import pinyinMatch from "pinyin-match";

export default {
		name: "asideAlarm",
		props: {
				count: {
						type: Object,
						default: () => ({})
				},
				checkedAll: {
						type: Boolean,
						default: true
				},
				// ['gps', 'dms', 'adas', 'vdss', 'vadas']
				filter: {
						type: Array
				},
				// 类型 all全部报警  category 分组报警
				type: {
						type: String,
						default: 'all'
				},
				checkMode: {
						type: Boolean,
						default: true
				},
				defaultCheckedList: {
					type: Array,
						default: () => ([])
				},
        // 是否在节点后面显示该节点的报警数量
        showAlarmCount: {
          type: Boolean,
          default: false
        }
		},
		data() {
				return {
						query: {
								value: '',
								selectorList: []
						},
						alarmFilterData: {},
						alarmTreeData: [],
						defaultChecked: [],
						defaultCheckedCategory:[],
						expandedKeys: [0],
						treeFlatArray: [],
						alarmType: 'all',
						groupTreeData: [],
						groupFlatArray: [],
				};
		},
		computed: {
				...mapState('dictionary', {alarmData: 'alarmFilter'}),
				alarmCount() {
						let temp = {
								...this.count,
						};
						Object.keys(this.alarmFilterData).forEach(alarmType => {
								temp[alarmType] = this.alarmFilterData[alarmType].reduce((pre, cur) => {
										return pre + (this.count[cur] || 0);
								}, 0)
						})
						return temp;
				},
				alarmCountGroup() {
						let temp = {
								...this.count,
						};
						Object.keys(this.alarmFilterData).forEach(alarmType => {
								temp[alarmType] = this.alarmFilterData[alarmType].reduce((pre, cur) => {
										return pre + (this.count[cur] || 0);
								}, 0)
						})
						let arr = this.groupFlatArray.filter(item => item.type === -2)
						arr.forEach(item => {
								temp[item.id] = 0
								item.children.forEach(alarm => {
										temp[item.id] += (this.count[alarm.type] || 0 )
								})
						})
						return temp;
				}
		},
		watch: {
			defaultCheckedList:{
				handler(val){
					if(!this.checkedAll && val.length){
						this.defaultChecked = val;
						let checkedIds = []
						let arr = this.groupFlatArray.filter(item => item.type == -2)
						arr.forEach(item => {
								item.children.forEach(alarm => {
									if(val.includes(String(alarm.type))){
										checkedIds.push(alarm.id)
									}
								})
						})
						this.defaultCheckedCategory = checkedIds
					}
				},
				deep:true
			},
				alarmData: {
						immediate: true,
						handler: 'init'
				},
				'query.value': async function (value, oldV) {
						if (!value) return;
						let $tree
						let node
						await this.$nextTick()
						if (this.alarmType === 'all') {
								$tree = this.$refs['allTree'];
								node = $tree.getNode(value);
								node.expand(null, true);
								$tree.setCurrentKey(value);
								node.setChecked(true, true);
								this.onCheck()
						} else {
								$tree = this.$refs['groupTree'];
								let checkAlarmNode = this.groupFlatArray.filter(item => value === item.type)
								checkAlarmNode.forEach(item => {
										node = $tree.getNode(item);
										node.expand(null, true);
										$tree.setCurrentKey(item.id);
										node.setChecked(true, true);
										this.onCheckGroup()
								})
						}

						//手动聚焦到节点
						await this.$utils.sleep(600) //要等el-tree展开动画完了以后scrollto才能生效
						const $node = document.querySelector('div[role=treeitem].el-tree-node.is-current');
						const wrapRect = this.$refs['treeWrap'].getBoundingClientRect()
						const nodeRect = $node.getBoundingClientRect();
						this.$refs['treeWrap'].scrollTo({
								top: (nodeRect.top + this.$refs['treeWrap'].scrollTop) - wrapRect.top,
						});
				},
		},
		mounted() {
				this.alarmType = this.type
				this.getAlarmGroupTree()
		},
		methods: {
				// 跳转报警分组管理
				alarmGroup() {
						this.$router.push({
								path: '/home/<USER>'
						})
				},
				// 获取报警分组树
				async getAlarmGroupTree() {
						this.groupTreeData = [await this.$api.getUserAlarmGroupTree()]
						this.groupFlatArray = this.$utils.flatTree(this.groupTreeData)
				},
				// 改变树类型
				changeAlarm(val) {
						this.$nextTick(() => {})
						this.alarmType = val
						this.query.selectorList = []
						this.query.value = ''
				},
				filterChange(value) {
						this.query.selectorList = []
						if (this.alarmType === 'all' && value) {
								this.query.selectorList = this.treeFlatArray.filter(data => {
										return pinyinMatch.match(data.name, value)
								})
						} else if (this.alarmType === 'category' && value) {
								this.query.selectorList = this.groupFlatArray.filter(data => {
										return pinyinMatch.match(data.name, value)
								})
						} else {
								this.query.selectorList = [];
						}
				},
				filterClear() {
						this.query.selectorList = [];
				},
        // 用于清除选中
        clearChecked() {
          this.defaultChecked = []
          this.defaultCheckedCategory = []
          this.$refs.allTree.setCheckedKeys([])
          this.$refs.groupTree.setCheckedKeys([])
          this.$emit("change", [], [])
          this.$emit("changeAll", [], []);
        },
        setCheckedKeys(alarmLastTimeQueryData) {
          console.log('setCheckedKeys', alarmLastTimeQueryData);
          // 用于设置选中
          // 怎么知道是分组的选中，还是全部的选中
          this.alarmType = alarmLastTimeQueryData.alarmType
          if (this.alarmType === 'all') {
            this.defaultChecked = alarmLastTimeQueryData.checkedAlarmNodes
            this.$refs.allTree.setCheckedKeys(this.defaultChecked)
            this.onCheck()
          } else {
            this.defaultCheckedCategory = alarmLastTimeQueryData.checkedAlarmNodes
            this.$refs.groupTree.setCheckedKeys(this.defaultCheckedCategory)
            this.onCheckGroup()
          }
        },
				// 点击全部报警树
				onCheck(e) {
						let checkedTypes = this.$refs.allTree.getCheckedKeys(true);
						let checkAlarm = this.treeFlatArray.filter(item => checkedTypes.includes(item.type))
						this.$emit("change", checkedTypes, checkAlarm,e);
						let checkedIdsGroup = this.$refs.groupTree.getCheckedKeys(true);
						let checkAlarmGroup = this.groupFlatArray.filter(item => checkedIdsGroup.includes(item.id)).map(item=>item.type).filter(item=>!checkedTypes.includes(item))
						this.$emit("changeAll", checkedTypes.concat(checkAlarmGroup));
				},
				// 点击报警分组树
				onCheckGroup(e) {
						let checkedIds = this.$refs.groupTree.getCheckedKeys(true);
						// checkedIds.splice(checkedIds.indexOf('0'), 1)
						let checkAlarm = this.groupFlatArray.filter(item => checkedIds.includes(item.id))
						// 去重
						let obj = {};
						checkAlarm = checkAlarm.reduce(function(item, next) {
								obj[next.type] ? '' : obj[next.type] = true && item.push(next)
								return item
						}, [])
						let checkedTypes = checkAlarm.map(item => Number(item.type))
						this.$emit("change", checkedTypes, checkAlarm,e)
						let checkedTypesAll = this.$refs.allTree.getCheckedKeys(true).filter(item=>!checkedTypes.includes(item));
						this.$emit("changeAll", checkedTypes.concat(checkedTypesAll), checkAlarm);
				},
				init() {
						if (!this.alarmData) return []
						let data = JSON.parse(JSON.stringify(this.alarmData));

						this.filter.forEach(item => delete data[item]);
						this.alarmFilterData = data;
						const defaultChecked = [];
						let treeData = Object.entries(data).filter(([alarmType, alarms]) => alarms.length > 0).map(([alarmType, alarms]) => {
								if (this.checkedAll) {
										defaultChecked.push(alarmType)
								}
								return {
										type: alarmType,
										name: this.$store.state.dictionary.alarmTypeName[alarmType],
										className: this.$store.state.dictionary.alarmClassName[alarmType],
										children: alarms.map(alarm => {
												if (this.checkedAll) {
														defaultChecked.push(alarm)
												}
												return {
														type: alarm,
														name: this.TypeForName(alarm),
														className: this.TypeForClass(alarm)
												}
										})
								}
						})
						this.treeFlatArray = this.$utils.flatTree(treeData);
						this.defaultChecked = defaultChecked;
						this.alarmTreeData = [
								{
										type: 0,
										name: '报警选择',
										children: [
												...treeData
										]
								}
						];
						this.$nextTick(() => {
								this.onCheck();
						})
				},
				renderContent(h, {node, data, store}) {
						return <div class="node-wrap">
								<span class="node-title">
								{data.name}</span>
								{!!this.alarmCount[data.type] &&
								<el-tag type="danger" size="mini">{this.alarmCount[data.type]}</el-tag>}
						</div>
				},
				renderContentGroup(h, {node, data, store}) {
						if (data.type === -2) {
								return <div class="node-wrap">
										<span class="node-title"> {data.name}</span>
										{!!this.alarmCountGroup[data.id] &&
										<el-tag type="danger" size="mini">{this.alarmCountGroup[data.id]}</el-tag>}
								</div>
						} else {
								return <div class="node-wrap">
										<span class="node-title"> {data.name}</span>
										{!!this.alarmCountGroup[data.type] &&
										<el-tag type="danger" size="mini">{this.alarmCountGroup[data.type]}</el-tag>}
								</div>
						}
				}
		},
};
</script>

<style lang="scss" scoped>
.checkbox-container {
		height: 100%;
		
		/deep/ .node-wrap {
				flex-grow: 1;
				display: flex;
				justify-content: space-between;
				overflow: hidden;

				.node-title {
						display: block;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						font-size: 12px;
				}

				/deep/ .el-tag {
						flex-shrink: 0;
				}
		}

		.query-wrap {
				padding: 10px 10px 0 10px;
				display: flex;
				position: relative;

				.query-select {
						width: 60px;
						display: flex;
						align-items: center;
						justify-content: center;
						height: 28px;
						line-height: 28px;
						background-color: var(--background-color-base);
						border-radius: 4px;
						border: 1px solid var(--border-color-base);
						color: var(--color-calendar-text);
						transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
						border-top-right-radius: 0;
						border-bottom-right-radius: 0;
						padding: 3px 5px;

						.item {
								width: 50%;
								height: 100%;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 3px;
								cursor: pointer;

								&:hover {
										background-color: rgba(40, 128, 226, 0.4);
								}
						}

						.active {
								color: var(--color-calendar-base);
								background-color: rgba(40, 128, 226, 0.4);
						}
				}

				.manage {
						position: absolute;
						right: 18px;
						top: 13px;
						padding: 2px;
						border-radius: 2px;

						&:hover {
								background-color: rgba(40, 128, 226, 0.4);
								cursor: pointer;
						}
				}

				.filter-value {
						width: 100%;
				}
		}

		.alarm-tree {
				height: calc(100% - 38px);
				overflow: auto;
				.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
		}
}
</style>
