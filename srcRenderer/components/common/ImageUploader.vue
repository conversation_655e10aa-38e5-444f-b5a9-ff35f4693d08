<template>
    <div class="image-uploader" :style="{cursor:disabled?'not-allowed':'pointer'}">
        <el-upload :style="{pointerEvents:disabled?'none':'all'}"
                   ref="uploader"
                   class="uploader"
                   action="/ponysafety2/a/fileupload/common"
                   :show-file-list="false"
                   :on-success="handleSuccess"
                   :before-upload="beforeUpload"
                   :on-progress="handleProgress"
                   :on-error="handleError"
                   :disabled="loading"
                   :data="extraData"
                   :accept="accept"
        >
            <div class="no-img" v-if="disabled">
                <i class="pony-iconv2 pony-cuowu"></i>
                <span>{{whyDisable}}</span>
            </div>
            <div class="no-img" v-else-if="!url">
                <i class="el-icon-loading" v-if="loading"></i>
                <i class="pony-iconv2 pony-jia" v-if="isImage"></i>
                <i class="pony-iconv2 pony-shipin11" v-if="isVideo"></i>
                <span v-if="error" style="color:var(--color-danger)">{{tip}}</span>
                <span v-else>{{(loading && tip) ||  placeholder || defaultPlaceholder}}</span>
            </div>
            <template v-else-if="isImage || isVideo">
                <img :src="url || bloburl" v-if="isImage">
                <video :src="url || bloburl" v-else ref="video" style="width:100%;height:100%"></video>
                <div class="clear-btn">
                    <i class="pony-iconv2 pony-guanbi" @click.stop="clear"></i>
                </div>
                <div class="preview-btn">
                    <i class="pony-iconv2 pony-chakan" @click.stop="preview = true"></i>
                </div>
            </template>
            <template v-else>
                <div class="other-img">
                    <div class="clear-btn">
                        <i class="pony-iconv2 pony-guanbi" @click.stop="clear"></i>
                    </div>
                    <div class="preview-btn">
                        <i class="pony-iconv2 pony-chakan" @click.stop="download(url)"></i>
                    </div>
                    <img src="../../../static/imgNewVersion/customReport/other_fileType.png">
                    <p :title="fileName">{{fileName}}</p>
                </div>
            </template>
        </el-upload>
        <div class="video-view" v-if="isVideo && preview">
          <div class="mask"></div>
          <i class="pony-iconv2 pony-guanbi close" @click.stop="preview = false"></i>
          <video ref="video" autoplay>
            <source :src="url || bloburl" type="video/mp4" />
          </video>
        </div>
        <ElImageViewer v-if="isImage && preview" :on-close="()=>{preview = false}" :url-list="[bloburl || url]"></ElImageViewer>
    </div>
</template>

<script>
    import ElImageViewer from "element-ui/packages/image/src/image-viewer";

    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019/12/25 15:50
     * @LastEditors: yezy
     * @LastEditTime: 2020-05-21 16:29:51
     * @Description:
     */
    export default {
        name: "imageUploader",
        components: {ElImageViewer},
        model: {
            prop: 'value',
            event: 'change',
        },
        props: {
            value: String,
            placeholder: String,
            uploadKey: String,
            accept: {
                type: String,
                default: 'image/png,image/jpeg',
            },
            maxSize: {
                type: Number,
                default: 4
            },
            videoType:{
                typetype: Boolean,
                default: false,
            },
            //传入的是否为完整path
            fullPath: {
                type: Boolean,
                default: false,
            },

            //
            disabled: {
                type: Boolean,
                default: false,
            },
            whyDisable: {
                type: String,
                default: function () {
                    return this.$ct('label.disabled');
                }
            }
        },
        data() {
            return {
                url: '',
                loading: false,
                hover: false,
                fileList: null,
                tip: null,
                error: false,
                bloburl: '',
                extraData: {
                    key: 'common',
                },
                preview: false,
                imageList:['jpg','png','jpeg',],
                isImage:true,
                isVideo:false,
                isOther:false,
                fileName:''
            }
        },
        computed: {
            defaultPlaceholder: function () {
                return this.$ct('expression.0', [this.maxSize])
            },
        },
        watch: {
            value: {
                immediate: true,
                handler: function (val) {
                    
                    if (val) {
                        this.getFileType(val)
                        if (val.startsWith('http') || /^\/ponysafety2/.test(val) || /^\/ponywechat/.test(val)) {
                            this.url = val;
                        } else {
                            this.url = `/ponysafety2${val}`
                        }
                    } else {
                        this.url = ''
                    }
                }
            },
            uploadKey: function (val) {
                this.extraData.key = val;
            },
        },
        methods: {
          // 下载
            download(url) {
                let link = document.createElement("a"); //创建a标签
                link.style.display = "none"; //使其隐藏
                link.href = url; //赋予文件下载地址
                link.setAttribute("download", this.fileName); //设置下载属性 以及文件名
                document.body.appendChild(link); //a标签插至页面中
                link.click(); //强制触发a标签事件
            },
            getFileType(url){
                this.isImage = this.videoType ? false : true
                this.isVideo = this.videoType ? true : false
                this.isOther = false
                if(!url)return
                let urlList = url.split('.')
                let fileType = urlList[urlList.length - 1]
                if(this.imageList.includes(fileType))return
                if(this.videoType || fileType == 'mp4'){
                    this.isVideo = true
                    this.isImage = false
                }else {
                    this.isOther = false
                    this.isImage = false
                    let nameListArr = url.split('/')
                    this.fileName = nameListArr[nameListArr.length - 1]
                }
            },
            handleSuccess(res, file) {
                this.bloburl = URL.createObjectURL(file.raw);
                this.loading = false;
                this.fileList = null;
                if (res.rs === 1) {
                    this.getFileType(res.url)
                    let url = res.url;
                    if (this.fullPath && url.startsWith('http')) {
                        this.url = url;
                    } else {
                        this.url = '/ponysafety2' + url;
                    }
                    this.$emit('change', this.url);
                } else {
                    this.error = true;
                    this.tip = this.$ct('messageInfo.0');
                }
            },
            handleError(err) {
                this.$error(this.$ct('messageInfo.1'))
            },
            beforeUpload(file) {
                if (file.size >= this.maxSize * 1024 * 1024) {
                    this.error = true;
                    this.tip = this.$ct('messageInfo.2');
                    this.$message({type:'error',message:`请选择${this.maxSize}M以内的文件`})
                    return false;
                }
                this.loading = true;
                this.error = false;
                return true;
            },
            handleProgress(event, file, fileList) {
                this.tip = this.$ct('expression.1', [event.percent.toFixed(1)])
                this.fileList = fileList;
            },
            handleClick() {
                if (this.loading && this.hover && this.fileList) {
                    this.$refs['uploader'].abort(this.fileList[0]);
                    this.loading = false;
                }
            },
            clear() {
                this.url = '';
                this.blobUrl = '';
                this.isImage = this.videoType ? false : true
                this.isVideo = this.videoType ? true : false
                this.isOther = false
                this.$emit('change', '');
            }
        },
        mounted() {
            this.blobUrl = '';
            this.extraData.key = this.uploadKey
            this.isImage = this.videoType ? false : true
            this.isVideo = this.videoType ? true : false
        }
    }
</script>
<style lang="scss">
    .el-form-item.is-error .image-uploader {
        $color: var(--color-danger);

        .uploader {
            border-color: $color;

            .no-img {
                color: $color;

                > i {
                    color: $color;
                }
            }
        }
    }

    .el-form-item.is-success .image-uploader {
        $color: var(--color-success);

        .uploader {
            border: 1px dashed $color;

            .no-img {
                color: $color;

                > i {
                    color: $color;
                }
            }
        }
    }
</style>
<style scoped lang="scss">
    .image-uploader {
        position: relative;
        width: 100%;
        height: 100%;
        .video-view {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
            z-index: 999;

          .mask {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,.5);
          } 
          video {
            position: absolute;
            width: 800px;
            height: 800px;
            top: 50%;
            margin-top: -400px;
            left: 50%;
            margin-left: -400px;
          }
          .close {
            position: absolute;
            right: 20px;
            top: 20px;
            color: #fff;
            font-size: 20px;
          }         
        }
        .uploader {
            height: 100%;
            width: 100%;
            background: var(--background-color-base);
            border: 1px dashed var(--border-color-base);
            border-radius: 4px;
            overflow: hidden;

            /deep/ .el-upload {
                height: 100%;
                width: 100%;
            }

            .no-img {
                height: 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: var(--color-text-secondary);
                font-size: 12px;
                padding-top: 10px;

                > i {
                    font-size: 40px;
                    color: var(--border-color-base);
                }

                > span {
                    padding-top: 10px;
                    white-space: pre;
                    line-height: 20px;
                }
            }

            .clear-btn {
                position: absolute;
                top: 10px;
                right: 10px;
                width: 20px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                border-radius: 10px;
                background: var(--background-color-base);
                box-shadow: var(--box-shadow-base);
            }

            .preview-btn {
                position: absolute;
                top: 10px;
                left: 10px;
                width: 20px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                border-radius: 10px;
                background: var(--background-color-base);
                box-shadow: var(--box-shadow-base);
            }

            img {
                height: calc(100% + 2px);
                width: calc(100% + 2px);
                object-fit: cover;
                margin: -1px;
            }
            .other-img {
                width: 100%;
                height: 100%;
                padding: 10px;
                text-align: center;
                img {
                    width: 60px;
                    height: 60px;
                    object-fit: contain;
                }
                p {
                    font-size: 12px;
                    line-height: 1;
                }
            }
        }
    }
</style>
