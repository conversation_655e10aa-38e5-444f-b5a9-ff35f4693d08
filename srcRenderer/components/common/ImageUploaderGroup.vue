<template>
    <div class="image-uploader-group flex flex-wrap">
        <ImageUploader v-for="(item,index) in listLength" :disabled="disabled" :whyDisable="whyDisable"
                       v-model="list[index]"
                       :maxSize="maxSize"
                       :key="uniKey(index)" :fullPath="true"
                       :uploadKey="uploadKey" :style="imageStyle"
                       :placeholder="placeholder"
                       :accept="accept"
                       :videoType="videoType"
                       @change="successUpload"
                       >
        </ImageUploader>
    </div>
</template>

<script>
    import ImageUploader from "@/components/common/ImageUploader";

    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2020/5/21 14:05
     * @LastEditors: yezy
     * @LastEditTime: 2020/5/21 14:05
     * @Description:
     */
    export default {
        name: "imageUploaderGroup",
        model: {
            prop: 'value',
            event: 'change',
        },
        components: {ImageUploader},
        props: {
            placeholder:String,
            //资源路径分类标识
            uploadKey: {
                type: String,
                default: 'common',
            },
            accept: {
                type: String,
                default: 'image/png,image/jpeg',
            },
            videoType:{
                typetype: Boolean,
                default: false,
            },
            imgSize: {
                type: Array,
                default: function () {
                    return [200, 120]
                }
            },
            maxSize: {
                type: Number,
                default: 4
                
            },
            value: {
                type: Array,
                required: true,
            },
            min: {
                type: Number,
                default: 1,
            },
            max: {
                type: Number,
                default: Infinity,
            },
            //
            disabled: {
                type: Boolean,
                default: false,
            },
            whyDisable: {
                type: String,
                default: function () {
                    return this.$ct('label.disabled');
                }
            }
        },
        data() {
            return {
                list: [],
            }
        },
        watch: {
            list: {
                handler: function (val, oldV) {
                    this.$emit('change', val);
                },
                deep: true,
            },
            value: {
                handler: function (val) {
                    let flag = val.length === this.list.length;
                    if (flag) {
                        for (let i = 0; i < val.length; i++) {
                            flag = val[i] === this.list[i];
                            if (!flag) break;
                        }
                    }
                    if (!flag) {
                        this.list = val;
                    }
                },
                immediate: true,
            }
        },
        computed: {
            listLength: function () {
                if (this.list.length) {
                    this.list = this.list?.filter(item => !!item) || [];
                }
                return Math.min(this.max, Math.max(this.list.length + 1, this.min));
            },
            imageStyle: function () {
                return {
                    height: this.imgSize[1] + 'px',
                    width: this.imgSize[0] + 'px',
                    margin: '5px 5px 5px 0'
                }
            },
        },
        methods: {
          successUpload(url){
            this.$emit('successUpload',url)
          },
            uniKey(index) {
                return this.list[index] + Date.now();
            }
        }
    }
</script>

<style scoped lang="scss">
    .image-uploader-group {
    }
</style>
