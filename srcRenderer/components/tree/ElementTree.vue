<template>
  <div class="element-tree" v-loading="loading && needLoading">
    <div class="query-wrap"
      :style="((type == 'vehicle' || type == 'vehicleWithChannel') && shezhiShow) || paddingLeft ? 'padding:10px 40px 0 10px' : 'padding:10px 10px 0'">
      <el-button plain style="padding:5px;margin-right:5px" v-if="refreshList.includes(type)" @click="getNewData" title="刷新树数据">
        <i class="pony-iconv2 pony-shuaxin"></i>
      </el-button>
      <el-select v-model="query.elementType" class="filter-type" v-if="elementType" @change="elementTypeChange">
        <el-option v-for="(item, index) in elementTypeList" :key="index" :label="item.name" :value="item.value"> </el-option>
      </el-select>
      <el-select v-model="query.fence" class="filter-type" v-else-if="type=='areaFenceTree'">
        <el-option v-for="(item, index) in fenseTypeList" :key="index" :label="item.name" :value="Number(item.value)">
        </el-option>
        </el-select>
      <el-select v-model="query.type" class="filter-type" v-else>
        <el-option v-for="(item, index) in filterTypeList" :key="index" :label="filterLabelList[index]" :value="index">
        </el-option>
      </el-select>
      <el-select
        v-model="query.value"
        class="filter-value"
        remote
        :placeholder="'请选择' + placeholderText"
        :remote-method="filterChange"
        filterable
        :clearable="!isIconNoShow"
        @clear="filterClear"
        @focus="focusSearch"
        @visible-change="visibleChange"
      >
        <el-option
          v-for="(item, index) in selectorList"
          :key="item.id"
          :label="isIconNo ? item[isIconNoType] : item[filterTypeList[query.type].type]"
          :value="item[nodeKeyId]"
        ></el-option>
      </el-select>
      <!-- 用编号搜索的按钮  场地树用 -->
      <span class="icon-no" v-if="isIconNoShow" @click="isIconNoChange">
          <i :class="['pony-iconv2 pony-bianhao',isIconNo ? 'active' : '']"></i>
        </span>
      <el-popover
        placement="right"
        v-if="(type == 'vehicle' || type == 'vehicleWithChannel') && shezhiShow"
        trigger="hover"
        @hide="closePopover"
      >
        <div class="show-config">
          <div class="tree-tab" style="overflow: hidden">
            <span>分类显示</span>
            <i
              style="float: right; color: var(--color-primary); cursor: pointer"
              @click="toVehicleGroupMgt"
              v-show="userTreeType == 1"
              >前往分组管理>></i
            >
            <el-radio-group v-model="userTreeType" class="radioList" @change="changeTreetype">
              <el-radio :label="0">车队车辆</el-radio>
              <el-radio :label="1">分组车辆</el-radio>
            </el-radio-group>
          </div>
          <div class="tree-tab" v-if="isVehicleState" style="overflow: hidden">
            <span style="margin-top: 8px">车辆过滤</span>
            <el-radio-group v-model="treeState" class="radioList">
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">在线</el-radio>
              <el-radio :label="2">离线</el-radio>
            </el-radio-group>
          </div>
          <div class="tree-tab" v-if="isVehicleState" style="overflow: hidden">
            <span style="margin-top: 8px">企业过滤</span>
            <el-radio-group v-model="componyState" class="radioList">
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">有车辆</el-radio>
            </el-radio-group>
          </div>

          <div class="tree-tab" style="overflow: hidden">
            <span style="margin-top: 8px">车辆显示</span>
            <el-checkbox-group v-model="checkList1" class="show-checklist" @change="changeShowConfig" v-if="isVehicleState">
              <el-checkbox :label="item.value" v-for="item in monitorState ? checkListDes2 : checkListDes1" :key="item.value"
                >{{ item.name }}
              </el-checkbox>
            </el-checkbox-group>

            <el-checkbox-group v-model="checkList" class="show-checklist" @change="changeShowConfig" v-else>
              <el-checkbox :label="item.value" v-for="item in checkListDes" :key="item.value">
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
            <el-checkbox
              v-model="showTwoOrFourG"
              v-if="type == 'vehicle' || type == 'vehicleWithChannel'"
              style="width: 50%; margin-right: 0; float: left; line-height: 28px"
            >
              2G/4G
            </el-checkbox>
            <el-checkbox
              v-model="showCan"
              v-if="type == 'vehicle' || type == 'vehicleWithChannel'"
              style="width: 50%; margin-right: 0; float: left; line-height: 28px"
            >
              原车接入
            </el-checkbox>
          </div>
          <div class="tree-tab" v-if="videoShow" style="overflow: hidden">
            <span style="margin-top: 8px">通道显示</span>
            <div
              @click="showChannels"
              style="cursor: pointer; display: flex; align-items: center; margin-top: 8px; width: 220px"
              :title="channelDes"
            >
              <el-input v-model="channelDes" placeHolder="默认全部通道">
                <i
                  slot="suffix"
                  class="pony-iconv2 pony-xia button-top"
                  v-show="showChannelStatus == false && !channelList.length"
                ></i>
                <i
                  slot="suffix"
                  class="pony-iconv2 pony-shang button-top"
                  v-show="showChannelStatus == true && !channelList.length"
                ></i>
                <i
                  slot="suffix"
                  class="pony-iconv2 pony-guanbi button-top"
                  v-show="channelList.length"
                  @click="clearChannels"
                ></i>
              </el-input>
            </div>
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
              v-if="showChannelStatus"
              >全选
            </el-checkbox>
            <el-checkbox-group
              v-model="channelList"
              @change="handleCheckedCitiesChange"
              v-if="showChannelStatus"
              style="height: 200px; width: 100%; overflow-y: auto"
            >
              <el-checkbox v-for="item in 16" :label="item" :key="item" style="width: 80%">{{ `逻辑通道${item}` }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div class="shezhi" v-if="(type == 'vehicle' || type == 'vehicleWithChannel') && shezhiShow" slot="reference">
          <i class="pony-iconv2 pony-shezhi"></i>
        </div>
      </el-popover>
    </div>
    <div class="tree-wrap" ref="treeWrap" :style="{ height: 'calc(100% - 38px)' }">
      <slot name="follow"></slot>
      <slot name="check"></slot>
      <!-- :render-content="renderContentFun" -->
      <el-tree
        :data="treeData"
        ref="tree"
        @check="treeClick"
        :show-checkbox="checkMode"
        v-on="$listeners"
        :node-key="nodeKeyId"
        :default-checked-keys="defaultCheckedList"
        @node-click="nodeClick"
        :props="treeProps"
        :filter-node-method="filterMethod"
        :check-strictly="checkStrictly"
        v-bind="treeAttrs"
      >
        <div class="element-tree-node" slot-scope="{ node, data }">
          <i :class="['tree-icon', data.iconSkin, (data.iconSkin == 'terminal' && data.online === false) ? 'terminal_off':'']" v-if="showIcon && data.iconSkin"></i>
          <span class="element-tree-node__label" v-if="useAbleData.includes(type)" :title="suspensionText(node)"
            :style="suspensionStyle(node)">
            <i class="pony-iconv2 pony-jia"
              v-if="renderContent && (data.type == renderDataType || data.type == (renderDataType + 1))"
              style="margin:0 2px" @click="renderContentClick({ node, data, type: 'jia' })"></i>

            {{ `${node.label}${node.data.ableData ? `（${node.data.ableData.total}）` : ""}` }}
          </span>
          <span class="element-tree-node__label" v-else :title="suspensionText(node)" :style="suspensionStyle(node)">
            <i
              class="pony-iconv2 pony-jian"
              v-if="renderContent && jian && data.type == renderDataType"
              style="margin-right: 2px"
              @click="renderContentClick({ node, data, type: 'jian' })"
            ></i>
            <i
              class="pony-iconv2 pony-jia"
              v-if="renderContent && (data.type == renderDataType || data.type == renderDataType + 1)"
              style="margin: 0 2px"
              @click="renderContentClick({ node, data, type: 'jia' })"
            ></i>
            {{
              `${node.label}${node.data.onlineData ? `（${node.data.onlineData.online} / ${node.data.onlineData.total}）` : ""}`
            }}
            <i v-if="node.data.expired" style="color: #e78423; font-style: normal">[已到期]</i>
            {{ labelConcat(node) }}
            <i
              :class="['pony-iconv2 pony-shipin11', 'is-video-support']"
              v-if="
                (type == 'vehicle' || type == 'vehicleWithChannel') && data.type == 4 && showTwoOrFourG && data.isVideoSupport
              "
            ></i>
            <i
              :class="['pony-iconv2 pony-shujujieru', 'is-video-support']"
              v-if="(type == 'vehicle' || type == 'vehicleWithChannel') && data.type == 4 && showCan && data.can"
            ></i>
            <i
              :class="['pony-iconv2 pony-xuni', 'is-video-support']"
              v-if="(type == 'vehicle' || type == 'vehicleWithChannel') && data.type == 4 && data.virtualDevice"
            ></i>
            <i class="pony-iconv2 pony-xiehuo bill-color" v-show="node.data.moutai_bill_unload"></i>
          </span>
        </div>
      </el-tree>
    </div>
    <!-- <div class="control-wrap text" v-if="onlineFilter">
            {{$ct('onlyOnline')}}&nbsp;<el-switch v-model="onlyOnline"></el-switch>
        </div> -->
  </div>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/3/30 10:53
 * @LastEditors: yezy
 * @LastEditTime: 2020-05-26 14:56:55
 * @Description: element tree组件
 * 因为原来的ztree 不怎么支持自定义节点结构，故使用el-tree实现
 * 注意区分node 和 data,在el-tree中 data是原始数据，node是节点数据，node.data = data，
 * node中包含各种方法，包括parent的引用也在node中
 *
 * 会将el-tree的所有事件暴露出去
 * 已知特殊点：
 * 1.在 state=true的情况下 data会附带一个extraInfo属性，包含许多车辆信息，例如通道数等等
 * 2.在type = vehicleWithChannel 并且开启在线过滤时 filterNodeMethod 和 treeFlatArray 都需要特殊处理，详见相关代码
 *
 * 已知缺点：
 * 1.在数据量过大的时候 vue-devtool会炸
 *
 * 已知坑:
 * element getCheckedNodes 返回的是data列表 那为什么不叫getCheckData？
 *
 * 2020-05-26 15:20:54
 * + svn:25499版本 确认本组件无内存泄漏问题
 * 2020-05-26 19:43:50
 * + 解决把状态维护提到外部，共同引用，而不是每个实例维护一个 大幅优化cpu占用 6000活跃车在10%一下 复数实例不影响cpu占用
 * + svn:25507版本 确认本组件无内存泄漏问题
 */
import { getTreeFilterSetting, filterLabelList } from "@/components/ztree/util/ZtreeMatics";
// import { transSecondToHMSCN } from "@/view/monitor/util/monitorUtil";
import pinyinMatch from "pinyin-match";
import vehicleState from "./vehicleState";
import { mapState, mapMutations } from "vuex";
import { transSecondToHMSCN } from "@/view/monitor/util/monitorUtil";

export default {
  name: "elementTree",
  props: {
    //输入框默认placeholder
    placeholderText: {
      type: String,
      default: "",
    },
    //是否设置车辆到期禁选
    expiredDisabled: {
      type: Boolean,
      default: true,
    },
    shezhiShow: {
      type: Boolean,
      default: true,
    },
    //是否需要给设置按钮留空隙
    paddingLeft: {
      type: Boolean,
      default: false,
    },
    // 多选状态下  父子节点是否互斥的标志
    mutex: {
      type: Boolean,
      default: false,
    },
    defaultChecked: {
      type: Boolean,
      default: false,
    },
    //树类型
    type: {
      type: String,
      default: "vehicle",
    },
    //是否多选
    checkMode: {
      type: Boolean,
      default: false,
    },
    // 父子节点不关联
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    //自定义节点渲染函数 会覆盖掉slot的内容
    //注意 渲染函数不会 添加 data-v-xxxxx 不会受到scoped样式影响 需要添加全局样式
    renderContent: {
      type: Boolean,
      default: false,
    },
    renderDataType: {
      type: Number,
      default: 4,
    },
    jian: {
      type: Boolean,
      default: true,
    },
    //是否显示车辆在线状态 type = vehicle or vehicleWithChannel 时有效
    state: {
      type: Boolean,
      default: false,
    },
    //显示在线车辆统计 需要state = true
    onlineCountTip: {
      type: Boolean,
      default: false,
    },
    //显示下方在线筛选按钮 需要 state = true
    // onlineFilter: {
    //     type: Boolean,
    //     default: false,
    // },
    //在数据中作为label的键名
    labelKey: {
      type: String,
      default: "name",
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    elementType: {
      type: Boolean,
      default: false,
    },
    //左侧下拉列表,是树的类型(elementType为true时)
    elementTypeList: {
      type: Array,
    },
    nodeKeyId: {
      type: String,
      default: "id",
    },
    treeStateShow: {
      type: Number,
      default: 2,
    },
    monitorState: {
      type: Boolean,
      default: false,
    },
    needLoading: {
      type: Boolean,
      default: true,
    },
    //是否需要点击节点就选中节点(必须是多选树)
    checkOnClickNode: {
      type: Boolean,
      default: true,
    },
    //实时监控pro的通道选择
    videoShow: {
      type: Boolean,
      default: false,
    },
    //如果需要isIconNO这个功能,指定点亮图标后需要查询的字段
    isIconNoType: {
      type: String,
      default: 'hrefName',
    },
  },
  data() {
    return {
      clickFlag: true,
      treeState: 0,
      componyState: 0,
      showTwoOrFourG: true,
      showCan: false,
      showWaybill: false,
      showLoad: false,
      userTreeType: 0,
      timer: null,
      isIconNo:false,//这个是场地类型的有一个查编号的图标,点亮了就是搜编号,而不是搜名字
      isIconNoList: ['unPointTerminal', 'unStopTerminal','areaTerminalTree:11','areaTerminalTree:12'],
      refreshList: ['xiaoNaVideo', 'workSiteVideo','stopTerminalWorksite','pointTerminalWorksite','fenseTreeNew','areaFenceTree'],
      workSiteFilterList: ['workSitePass', 'xiaoNaPass'],
      workSiteOnlineFilterList: ['xiaoNaVideo', 'workSiteVideo','stopTerminalWorksite','pointTerminalWorksite'],
      //下面这个数组的item是树的type,均为给树添加ableData的类型
      useAbleData: ['workSitePass', 'xiaoNaPass', 'changzhan', 'xianxingqu', 'xiaquMgt', 'fenseTreeNew', 'xianxingquCross', 'xiaquMgtCross', 
      'cloudCrossTraffic', 'transportPointsFenseTree',"areaFenceTree","areaRoadFenceTree"],
      checkListDes: [
        {
          name: "车牌颜色",
          value: 1,
        },
        // {
        //     name:'别名',
        //     value:2
        // },
        {
          name: "品牌-车型",
          value: 3,
        },
        {
          name: "司机姓名",
          value: 4,
        },
      ],
      checkListDes1: [
        {
          name: "车牌颜色",
          value: 1,
        },
        // {
        //     name:'别名',
        //     value:2
        // },
        {
          name: "品牌-车型",
          value: 3,
        },
        {
          name: "司机姓名",
          value: 4,
        },
        {
          name: "ACC",
          value: 5,
        },
      ],
      checkListDes2: [
        {
          name: "车牌颜色",
          value: 1,
        },
        // {
        //     name: '别名',
        //     value: 2
        // },
        {
          name: "品牌-车型",
          value: 3,
        },
        {
          name: "司机姓名",
          value: 4,
        },
        {
          name: "ACC",
          value: 5,
        },
        // {
        //     name: '滚筒状态',
        //     value: 6
        // },
        {
          name: "所在区域",
          value: 7,
        },
        {
          name: "速度",
          value: 8,
        },
        {
          name: "当日里程",
          value: 9,
        },
        {
          name: "行停时长",
          value: 10,
        },
        {
          name: "显示运单",
          value: 11,
        },
        {
          name: "载重",
          value: 12,
        },
      ],
      checkList: [], //显示设置（车辆数上的设置）
      checkList1: [],
      tempCheckList: [],
      filterLabelList: this.$ct("filterType"),
      query: {
        type: 0,
        value: "",
        elementType: 0,
        fence:0
      },
      loading: false,
      treeData: [],
      methodName: "",
      treeDataById: {},
      treeFlatArray: [],
      selectorList: [],
      defaultCheckedList: [], //停车场进出查询树的id列表，为了实现默认全部选中
      onlyOnline: false,

      dblClickSign: undefined,

      //用于判断树是否初始化完成
      waitForInit: null,
      _promiseResolve: null,
      subscription: [],
      GPSList: [],
      stopTypeDes: ["行驶中", "停车", "熄火", "离线"],
      channelList: [],
      isIndeterminate: false,
      checkAll: false,
      channelDes: "",
      showChannelStatus: false,
      //记录滚动条位置
      scrollRecordObj: {
        scrollTop: 0,
        scrollLeft: 0,
      },
      fenseTypeList: [],
    };
  },
  computed: {
    ...mapState("auth", ["userInfo"]),
    ...mapState("switch", ["treeShowConfig"]),
    ...mapState("ztreeData", ["treeFlag"]),
    ...mapState("ztreeData", ["changeTreeList"]),
    isVehicleState() {
      return (this.type == "vehicle" || this.type == "vehicleWithChannel") && this.expiredDisabled;
    },
    isIconNoShow() {
      return this.workSiteOnlineFilterList.includes(this.type);
    },
    treeAttrs: function () {
      return Object.assign(
        {
          "highlight-current": true,
          "node-key": "id",
          "expand-on-click-node": false,
          "check-on-click-node": this.checkMode && this.checkOnClickNode && this.clickFlag,
        },
        this.$attrs
      );
    },
    treeProps: function () {
      return {
        label: this.labelKey,
        children: "children",
        isLeaf: function () {
          return false;
        },
      };
    },
    filterTypeList: function () {
      const extraFilter = getTreeFilterSetting[this.methodName];
      const list = [{ type: this.labelKey }];
      if (extraFilter) {
        list.push(...extraFilter);
      }
      return list;
    },
    filterMethod: function () {
      if (this.type == "workSite" || this.type == "xiaoNa") {
        return this.filterNodeMethodWork;
      } else if (this.workSiteFilterList.includes(this.type)) {
        return this.filterNodeMethodVideo
      } else if (this.type == "areaFenceTree"||this.type == "areaRoadFenceTree" ) {
        return this.filterNodeMethodAreaFence;
      }
      else if(this.workSiteOnlineFilterList.includes(this.type)) {
        return this.filterNodeMethodOnline
      }else {
        return this.filterNodeMethod
      }
    },
  },
  watch: {
    async treeFlag(val) {
      if (val && (this.type == "vehicle" || this.type == "vehicleWithChannel")) {
        this.treeVehicleChange(this.type);
        this.changeTreeFlag({ type: false });
      }
    },
    treeState: {
      handler: function (val) {
        this.$nextTick(() => {
          this.filterTree();
          this.$emit("change", val);
        });
      },
      // immediate: true,
    },
    componyState: {
      handler: function (val) {
        this.$nextTick(() => {
          this.filterTree();
          // this.$emit('change', val)
        });
      },
      // immediate: true,
    },
    treeStateShow: {
      handler: function () {
        this.$nextTick(() => {
          this.filterTree();
        });
      },
      // immediate: true,
    },
    "query.value": async function (value, oldV) {
    
      if (!value) return;
      const $tree = this.$refs["tree"];
      let node = $tree.getNode(value);
      if (this.type == "vehicleWithChannel" && this.videoShow) {
        this.$emit("node-expand", node.data.type == 4 ? node.parent.data : node.data);
      }
      $tree.setCurrentKey(value);
      node.expand(null, true);
      if ((this.type == "vehicle" || this.type == "vehicleWithChannel") && node.data.type == 4) {
        let localList = JSON.parse(localStorage.getItem(this.userInfo.id + "recordList")) || [];
        if (localList.includes(node.data.id)) {
          let index = localList.findIndex((it) => it == node.data.id);
          localList.splice(index, 1);
        }
        if (localList.length >= 10) {
          localList.pop();
        }
        localList.unshift(node.data.id);
        localStorage.setItem(this.userInfo.id + "recordList", JSON.stringify(localList));
      }

      if (this.checkMode) {
        if (!(!this.hasPermission("expiredNoDisabled:admin") && this.expiredDisabled && node.data && node.data.expired)) {
          node.setChecked(true, true);
          this.$emit("check", node.data, {
            checkedNodes: $tree.getCheckedNodes(),
            checkedKeys: $tree.getCheckedKeys(),
            halfCheckedNodes: $tree.getHalfCheckedNodes(),
            halfCheckedKeys: $tree.getHalfCheckedKeys(),
          });
        } else {
          this.$warning("车辆到期冻结");
        }
      } else {
        if (!(!this.hasPermission("expiredNoDisabled:admin") && this.expiredDisabled && node.data && node.data.expired)) {
          node.setChecked(true, true);
          this.$emit("node-click", node.data, {
            ...node,
          });
        } else {
          this.$warning("车辆到期冻结");
        }
        // this.$emit('node-click', node.data, node, null)
      }

      //手动聚焦到节点
      await this.$utils.sleep(600); //要等el-tree展开动画完了以后scrollto才能生效
      const $nodeList = document.querySelectorAll("div[role=treeitem].el-tree-node.is-current");
      if (!$nodeList.length) return;
      const $node = $nodeList[$nodeList.length - 1];
      const wrapRect = this.$refs["treeWrap"].getBoundingClientRect(),
        nodeRect = $node.getBoundingClientRect();
      this.$refs["treeWrap"].scrollTo({
        top: nodeRect.top + this.$refs["treeWrap"].scrollTop - wrapRect.top,
      });
    },
    "query.fence":{
      handler: function () {
        this.$nextTick(() => {
          //区域围栏树的围栏筛选目前只有重载树才能正确展示节点后围栏数量
          this.initTree(this.type);
          this.$emit('filterFence',this.query.fence)
        });
      },
    },
    treeShowConfig(val) {
      this.checkList = val;
      this.checkList1 = val;
      // this.toShowVehicleConfig(false)
    },
  },
  methods: {
    isIconNoChange(){
      this.isIconNo = !this.isIconNo
      this.query.value = ''
      this.selectorList = []
    },
    //区域围栏树的围栏类型筛选
    computedFenceTree(treeFlatArray, nodeType, forList) {
      let fleet = treeFlatArray.filter((node) => {
        return node.type === nodeType;
      });
      fleet.forEach((item) => {
         item.ableData = {
          total:item.children.filter(it=>this.query.fence == it.fence_type ).length,
        };
      });
      //企业，代理商，平台 级别
      forList.forEach((type) => {
        let group = treeFlatArray.filter((node) => {
          return node.type == type ;
        });
        group.forEach((item)=>{
          // if(list.length > 0){
          //   item.childNodes = list;
          // }else{
          //   item.childNodes = [];
          // }
         item.ableData=item.children.reduce(
            function (acc, cur) {
              acc.total += cur.ableData ? cur.ableData.total : 0;
              return acc;
            },
            { total: 0 }
          );
        })
        });

    },
    //围栏类型
    async getFenseTypeList() {
      let result = await this.$api.getSysDictByCode({ code: "fense_type" });
      if (!result || !result.length) return;

      this.fenseTypeList = result;
      this.query.fence = Number(this.fenseTypeList[0].value)
    },
    //筛选区域围栏树
    filterNodeMethodAreaFence(value, data, node) {
      let onlineFlag = true;
      if (this.query.fence!= null && this.type=='areaFenceTree') {

        onlineFlag = this.query.fence == data.fence_type ? true : false;
      }else{
        if (data.ableData) {
        onlineFlag = data.ableData.total != 0;
      }
      }
      return onlineFlag;
    },
    closePopover() {
      this.showChannelStatus = false;
    },
    showChannels() {
      this.showChannelStatus = !this.showChannelStatus;
    },
    clearChannels() {
      this.showChannelStatus = true;
      this.channelDes = "";
      this.checkAll = false;
      this.channelList = [];
      this.isIndeterminate = true;
      this.$emit("channelStatus", this.channelList);

      this.filterTree();
    },
    handleCheckAllChange(val) {
      let arr = [];
      let channelDes = "";
      for (let i = 1; i <= 16; i++) {
        arr.push(i);
        channelDes += `逻辑通道${i},`;
      }
      this.channelDes = val ? channelDes.substring(-1, channelDes.length - 1) : "";
      this.channelList = val ? arr : [];
      this.isIndeterminate = false;
      this.$emit("channelStatus", this.channelList);
      this.filterTree();
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === 16;
      this.channelDes = "";
      value.forEach((item) => {
        this.channelDes += `逻辑通道${item},`;
      });
      this.channelDes = this.channelDes.substring(-1, this.channelDes.length - 1);

      this.isIndeterminate = checkedCount > 0 && checkedCount < 16;
      this.$emit("channelStatus", this.channelList);
      this.filterTree();
    },

    ...mapMutations("ztreeData", ["changeTreeFlag"]),
    elementTypeChange(val) {
      this.query.value = "";
      this.$emit("change", val);
    },
    changeTreetype(val) {
      if (val == 0) {
        sessionStorage.setItem("treeTypeSelect", JSON.stringify(false));
        this.initTree(this.type);
      } else {
        sessionStorage.setItem("treeTypeSelect", JSON.stringify(true));
        this.initTree("userVehicle");
      }
    },
    // 通道树,点击加减号触发事件
    renderContentClick(params) {
      this.$emit("renderContentClick", params);
    },
    //车辆到期禁用样式
    suspensionStyle(node) {
      if (node.data.expired && !this.hasPermission('expiredNoDisabled:admin')) {
        return {
          cursor: "not-allowed",
          // pointer-events:"none"
        };
      }
    },

    //树上鼠标悬浮展示的文字
    suspensionText(node) {
      if (!this.useAbleData.includes(this.type)) {
        if (node.data.onlineData) {
          return `${node.label}（${node.data.onlineData.online} / ${node.data.onlineData.total}）`;
        } else {
          // 如果是车辆树,需要判断是否需要显示已冻结,并且车辆树需要显示左上角设置勾选的字段
          if (this.isVehicleState) {
            if (node.data.expired) {
              return "车辆已到期冻结";
            } else {
              if (node.data.type == 4) {
                return `${node.label}${this.labelConcat(node)}-${this.timeTransfor(node.data)}`;
              } else {
                return `${node.label}`;
              }
            }
          } else if(node.data.iconSkin == 'terminal' && node.data.onlineTime){
            return `${node.label}(${node.data.onlineTime})`
          }else {
            return `${node.label}`
          }
        }
      } else {
        return `${node.label}${node.data.ableData ? `（${node.data.ableData.total}）` : ""}`;
      }
    },
    //书上显示的最近在线时间的过滤
    timeTransfor(data) {
      if (data.gpsTime) {
        return this.TimeFormat(data.gpsTime);
      } else {
        return "从未上线";
      }
    },
    removeNode(data, iconSkin = "vehicle_0_0_0") {
      let $tree = this.$refs["tree"];
      if (!$tree.getNode(data.id)) return;
      let state = iconSkin.split("_")[1] != 0;
      $tree.remove($tree.getNode(data.id));
      let list = [data.parent_id, null, null, null, null];
      list.reduce((res, data) => {
        if (!res) return;
        let parentNode = $tree.getNode(res);
        // if(data){
        //     parentNode = $tree.getNode(data)
        //     parentNode.data.onlineData.total--
        //     if(state){
        //         parentNode.data.onlineData.online--
        //     }
        // }else {
        if (parentNode && parentNode.data && parentNode.data.onlineData) {
          parentNode.data.onlineData.total--;
        }
        if (state) {
          parentNode.data.onlineData.online--;
        }
        // }
        if (parentNode) {
          return parentNode.data.parent_id;
        } else {
          return;
        }
      });
    },
    addNode(data, iconSkin = "vehicle_0_0_0") {
      let $tree = this.$refs["tree"];
      if ($tree.getNode(data.id)) return;
      data.iconSkin = iconSkin;
      $tree.append(data, data.parent_id);
      let list = [data.parent_id, null, null, null, null];
      list.reduce((res, data) => {
        if (!res) return;
        let parentNode = $tree.getNode(res);
        if (parentNode && parentNode.data && parentNode.onlineData) {
          parentNode.data.onlineData.total++;
        }
        if (parentNode) {
          return parentNode.data.parent_id;
        } else {
          return;
        }
      });
    },
    treeVehicleChange() {
      let $tree = this.$refs["tree"];
      if (!this.changeTreeList || !this.changeTreeList.length) return;
      let changeList = JSON.parse(JSON.stringify(this.changeTreeList)).sort((a, b) => a.type - b.type);
      changeList.forEach((item) => {
        switch (item["o"]) {
          case 0: //新增节点
            //新增节点之后需要把在线数量和总数都计算一下
            if ($tree.getNode(item.id)) return;
            if (item.type == 4) {
              this.addNode(item);
            } else {
              item.onlineData = {
                total: 0,
                online: 0,
              };
              $tree.append(item, item.parent_id);
              let list = [item.parent_id];
              for (let i = 0; i < item.type - 1; i++) {
                list.push(null);
              }
              list.reduce((res, data) => {
                let parentNode;
                if (data) {
                  parentNode = $tree.getNode(data);
                  parentNode.data.onlineData.total++;
                } else {
                  parentNode = $tree.getNode(res);
                  parentNode.data.onlineData.total++;
                }
                return parentNode.data.parent_id;
              });
            }
            break;
          case 1:
            if (!$tree.getNode(item.id)) return;
            if (item.type == 4) {
              let iconSkin = $tree.getNode(item.id).data.iconSkin;
              item.iconSkin = iconSkin;
              $tree.remove($tree.getNode(item.id));
              $tree.append(item, item.parent_id);
            } else {
              let child = $tree.getNode(item.id).data.children;
              $tree.remove($tree.getNode(item.id));
              item.children = child;
              item.onlineData = {
                online: child.filter((it) => it.iconSkin.split("_")[1] != 0),
                total: child.length,
              };
              $tree.append(item, item.parent_id);
            }
            break;
          case -2:
            //删除节点之后需要把在线数量和总数都计算一下
            if (!$tree.getNode(item.id)) return;
            if (item.type == 4) {
              this.removeNode(item, $tree.getNode(item.id).data.iconSkin);
            } else {
              let child = $tree.getNode(item.id).data.children;
              let stateChild = child.filter((it) => it.iconSkin.split("_")[1] != 0);
              $tree.remove($tree.getNode(item.id));
              let list = [item.parent_id];
              for (let i = 0; i < item.type; i++) {
                list.push(null);
              }
              list.reduce((res, data) => {
                let parentNode;
                if (data) {
                  parentNode = $tree.getNode(data);
                  parentNode.data.onlineData.total -= child.length;
                  parentNode.data.onlineData.online -= stateChild.length;
                } else {
                  parentNode = $tree.getNode(res);
                  parentNode.data.onlineData.total -= child.length;
                  parentNode.data.onlineData.online -= stateChild.length;
                }
                return parentNode.data.parent_id;
              });
            }

            break;
        }
      });
    },
    toVehicleGroupMgt() {
      this.$router.push({
        path: "/home/<USER>",
      });
      this.$refs["poppver"].doClose();
    },
    initShowConfig() {
      this.tempCheckList = JSON.parse(localStorage.getItem(this.userInfo.id + "showConfig"))
        ? JSON.parse(localStorage.getItem(this.userInfo.id + "showConfig"))
        : [];
      if (this.isVehicleState) {
        this.checkList1 = this.tempCheckList;
      } else {
        this.checkList = this.tempCheckList.filter((item) => {
          if (item <= 5) return item;
        });
      }
    },
    labelConcat(node) {
      this.tempCheckList = this.isVehicleState ? this.checkList1 : this.checkList;
      let finallyStr = "";
      if (node.data.type == 4 && this.tempCheckList.length && this.isVehicleState) {
        this.tempCheckList.forEach((item) => {
          switch (item) {
            case 1:
              if (finallyStr != "") {
                finallyStr =
                  finallyStr + ` ${node.data.plateColor && node.data.plateColor != "-" ? " " + node.data.plateColor : ""}`;
              } else {
                finallyStr = finallyStr + `${node.data.plateColor && node.data.plateColor != "-" ? node.data.plateColor : ""}`;
              }
              break;
            case 2:
              if (finallyStr != "") {
                finallyStr = finallyStr + `${node.data.alias && node.data.alias != "-" ? " " + node.data.alias : ""}`;
              } else {
                finallyStr = finallyStr + `${node.data.alias && node.data.alias != "-" ? node.data.alias : ""}`;
              }
              break;
            case 3:
              if (finallyStr != "") {
                finallyStr = finallyStr + `${node.data.brand && node.data.brand != "-" ? " " + node.data.brand : ""}`;
              } else {
                finallyStr = finallyStr + `${node.data.brand && node.data.brand != "-" ? node.data.brand : ""}`;
              }
              break;
            case 4:
              if (finallyStr != "") {
                finallyStr =
                  finallyStr +
                  `${node.data.driverName && node.data.driverName != "-" ? "司机:" + " " + node.data.driverName : ""}`;
              } else {
                finallyStr =
                  finallyStr + `${node.data.driverName && node.data.driverName != "-" ? "司机:" + node.data.driverName : ""}`;
              }
              break;

            case 5:
              if (!this.isVehicleState) {
                break;
              } else {
                finallyStr = finallyStr + ` ${node.data.acc && node.data.acc != "-" ? "ACC:" + node.data.acc : ""}`;
              }
              break;
            case 6:
              if (!this.isVehicleState) {
                break;
              } else {
                let rollerState = "";
                rollerState = node.data.rollerState;

                if (finallyStr != "") {
                  finallyStr = finallyStr + `${rollerState && rollerState != "-" ? " " + rollerState : ""}`;
                } else {
                  finallyStr = finallyStr + `${rollerState && rollerState != "-" ? rollerState : ""}`;
                }
                break;
              }
            case 7:
              if (!this.isVehicleState) {
                break;
              } else {
                let newLocation = "";
                let location = node.data.location;
                if (location !== "-" && location != null) {
                  let start = location.indexOf("市") + 1;
                  let end = location.indexOf("县") !== -1 ? location.indexOf("县") + 1 : location.indexOf("区") + 1;
                  newLocation = location.substring(start, end);
                } else {
                  newLocation = "-";
                }

                if (finallyStr != "") {
                  finallyStr = finallyStr + `${newLocation && newLocation != "-" ? " " + newLocation : ""}`;
                } else {
                  finallyStr = finallyStr + `${newLocation && newLocation != "-" ? newLocation : ""}`;
                }
                break;
              }
            case 8:
              if (!this.isVehicleState) {
                break;
              } else {
                //这是车辆时行驶状态  直接显示速度  其他状态再判断stopType(优先判断是否为行驶状态)
                if (node.data.iconSkin.split("_")[1] && node.data.iconSkin.split("_")[1] == 2) {
                  let speed = (node.data.gpsSpeed ? node.data.gpsSpeed : 0) + "km/h";
                  if (finallyStr != "") {
                    finallyStr = finallyStr + `${speed && speed != "-" ? " " + speed : ""}`;
                  } else {
                    finallyStr = finallyStr + `${speed && speed != "-" ? speed : ""}`;
                  }
                  break;
                }

                if (node.data.stopType != "-" && node.data.stopType != 0) {
                  let speed = `${this.stopTypeDes[node.data.stopType]}${transSecondToHMSCN(node.data.stopTime)}`;
                  if (finallyStr != "") {
                    finallyStr = finallyStr + `${speed && speed != "-" ? " " + speed : ""}`;
                  } else {
                    finallyStr = finallyStr + `${speed && speed != "-" ? speed : ""}`;
                  }
                  break;
                } else {
                  let speed = (node.data.gpsSpeed ? node.data.gpsSpeed : 0) + "km/h";
                  if (finallyStr != "") {
                    finallyStr = finallyStr + `${speed && speed != "-" ? " " + speed : ""}`;
                  } else {
                    finallyStr = finallyStr + `${speed && speed != "-" ? speed : ""}`;
                  }
                  break;
                }
              }
            case 9:
              if (!this.isVehicleState) {
                break;
              } else {
                let dayMile = (node.data.mile - node.data.dayStartMile) / 1000 + "km";
                if (finallyStr != "") {
                  finallyStr = finallyStr + `${dayMile && dayMile != "-" ? " " + dayMile : ""}`;
                } else {
                  finallyStr = finallyStr + `${dayMile && dayMile != "-" ? dayMile : ""}`;
                }
                break;
              }
            case 10:
              if (!this.isVehicleState) {
                break;
              } else {
                //行停时长
                let driveAndStop = "";
                if (node.data.stopType != "-") {
                  if (node.data.stopType == 0 && node.data.driveTimeNow !== "-") {
                    driveAndStop = `行${transSecondToHMSCN(node.data.driveTimeNow)}`;
                  }
                  if (node.data.stopType > 0 && node.data.displayTime !== "-") {
                    driveAndStop = `停${transSecondToHMSCN(node.data.displayTime)}`;
                  }
                  if (finallyStr != "") {
                    finallyStr = finallyStr + `${driveAndStop && driveAndStop != "-" ? " " + driveAndStop : ""}`;
                  } else {
                    finallyStr = finallyStr + `${driveAndStop && driveAndStop != "-" ? driveAndStop : ""}`;
                  }
                  break;
                }
              }
            case 11:
              //运单状态
              if (!this.isVehicleState) {
                break;
              } else {
                let waybill = node.data.jianhua56_isbill != null ? (node.data.jianhua56_isbill ? "有单" : "无单") : "未知";
                if (finallyStr != "") {
                  finallyStr = finallyStr + `${waybill && waybill != "-" ? " " + waybill : ""}`;
                } else {
                  finallyStr = finallyStr + `${waybill && waybill != "-" ? waybill : ""}`;
                }
                break;
              }
            case 12:
              //运单载重
              if (!this.isVehicleState) {
                break;
              } else {
                let load = node.data.jianhua56_isload != null ? (node.data.jianhua56_isload ? "重载" : "空载") : "未知";
                if (finallyStr != "") {
                  finallyStr = finallyStr + `${load && load != "-" ? " " + load : ""}`;
                } else {
                  finallyStr = finallyStr + `${load && load != "-" ? load : ""}`;
                }
                break;
              }
          }
        });
      }
      // finallyStr ? `${node.label}[${finallyStr}]` : node.label
      return finallyStr ? `[${finallyStr}]` : "";
    },
    //树的显示配置更改
    changeShowConfig(val) {
      //增加 里程和行停时长互斥
      if (val.includes(8) && val.indexOf(10) < val.indexOf(8)) {
        val = val.filter((item) => item != 10);
      }
      if (val.includes(10)) {
        val = val.filter((item) => item != 8);
      }
      localStorage.setItem(this.userInfo.id + "showConfig", JSON.stringify(val));
      this.$store.commit("switch/changeTreeShowConfig", val);
    },
    // 父子级节点互斥功能   author：changli
    treeClick(e) {
      if (!this.mutex) return;
      // 父级type==4  子级type==5
      let current = this.$refs.tree.getCheckedNodes();
      // 深拷贝避免两数组互相影响  解决控制台打印数组有两个元素， length只有1的问题
      let tempArr = JSON.parse(JSON.stringify(current));
      let currentData = []; // 排序后的数组，最新勾选的节点在最前面
      let flag = current.find((val) => val.id == e.id);
      if (flag) {
        currentData.unshift(flag);
      } else {
        let delIndex = currentData.findIndex((val) => val.id == flag.id);
        if (delIndex != -1) {
          currentData.splice(delIndex, 1);
        }
      }
      if (!currentData.length) return;
      currentData.forEach((item) => {
        if (item.type == 5) {
          // 将父级从tempArr中剔除
          let currentIndex1 = tempArr.findIndex((val) => val.id == item.parent_id);
          if (currentIndex1 != -1) {
            tempArr.splice(currentIndex1, 1);
          }
        }
        if (item.type == 4) {
          // 将子级从tempArr中剔除
          if (item.children && item.children.length) {
            item.children.forEach((val) => {
              let currentIndex = tempArr.findIndex((val1) => val1.id == val.id);
              if (currentIndex != -1) {
                tempArr.splice(currentIndex, 1);
              }
            });
          }
        }
      });
      this.$refs.tree.setCheckedNodes(tempArr);
    },
    nodeClick(data, node, $node) {
      if (!this.hasPermission("expiredNoDisabled:admin") && this.expiredDisabled && data.type == 4 && data.expired) {
        this.clickFlag = false;
        this.$warning("车辆到期冻结");
        //这里改为true,围栏防止下一次点击之前还是false,就不会选中了
        this.clickFlag = true;
        return;
      }
      if (!this.clickFlag) {
        this.clickFlag = true;
      }

      if (this.dblClickSign) {
        clearTimeout(this.dblClickSign);
        this.dblClickSign = undefined;
        this.$emit("node-dbl-click", data, node, $node);
      } else {
        this.dblClickSign = setTimeout(() => {
          this.dblClickSign = undefined;
        }, 300);
      }
    },
    visibleChange(val) {
      if (val) {
        this.focusSearch();
      }
    },
    focusSearch() {
      if ((this.type != "vehicle" && this.type != "vehicleWithChannel") || this.query.value) {
        this.selectorList = []
        return
      };
      let localList = JSON.parse(localStorage.getItem(this.userInfo.id + "recordList"));
      if (!localList || !localList.length) return;
      this.selectorList = localList
        .map((item) => {
          return this.treeFlatArray.find((it) => it.id == item);
        })
        .filter((item) => item);
    },
    filterChange(value) {
      if (value) {
        const condition = this.filterTypeList[this.query.type];
        let searchStr = this.isIconNo ? this.isIconNoType : condition.type;
        this.selectorList = this.treeFlatArray.filter((data) => {
          return (!("kind" in condition) || data.type === condition.kind) && pinyinMatch.match(data[searchStr], value);
        });
      } else {
        this.selectorList = [];
      }
    },
    filterClear() {
      this.selectorList = [];
    },
    filterTree() {
      this.$refs["tree"].filter();
    },
    filterNodeMethod(value, data, node) {
      let onlineFlag = true;
      let onlineFlagCom = true;
      if (this.workSiteFilterList.includes(this.type)) {
        onlineFlag = this.filterNodeMethodVideo(value, data, node);
      } else if (this.treeState) {
        if (this.treeState == 1) {
          if (data.onlineData) {
            //隐藏在线车辆为0的车队 企业 代理商 平台
            onlineFlag = data.onlineData.online != 0;
          } else {
            //隐藏离线的车辆
            onlineFlag = data.iconSkin.split("_")[1] != 0;
          }
        } else {
          if (data.onlineData) {
            //隐藏在线车辆为0的车队 企业 代理商 平台
            onlineFlag = data.onlineData.online == data.onlineData.total;
          } else {
            //隐藏离线的车辆
            onlineFlag = data.iconSkin.split("_")[1] == 0;
          }
        }
      } else {
        if (this.videoShow && data.type == 5 && this.channelList.length) {
          onlineFlag = this.channelList.includes(data.chn);
        }
      }
      if (this.componyState) {
        // onlineFlagCom = !(data.onlineData && data.type == 3 && data.onlineData.total == 0)
        onlineFlagCom = !(data.onlineData && data.onlineData.total == 0);
      }
      return onlineFlag && onlineFlagCom;
    },

    filterNodeMethodVideo(value, data, node) {
      let onlineFlag = true;
      // let iconName = this.type.includes('xiaoNa') ? 'absorption' :'worksite'
      if (data.ableData) {
        //隐藏无工地上级
        onlineFlag = data.ableData.total != 0;
      }
      return onlineFlag;
    },
    filterNodeMethodOnline(value, data, node) {
      let onlineFlag = true;
        if (data.onlineData) {
          //隐藏无工地上级
          onlineFlag = data.onlineData.total != 0;
        }
      return onlineFlag;
    },
    filterNodeMethodWork(value, data, node) {
      let onlineFlag = true;
      if (this.workSiteFilterList.includes(this.type)) {
        onlineFlag = this.filterNodeMethodVideo(value, data, node);
      } else if (this.treeStateShow != 2) {
        if (this.treeStateShow) {
          if (data.onlineData) {
            //隐藏无效工地的上级
            onlineFlag = data.onlineData.online != 0;
          } else {
            //隐藏无效工地
            onlineFlag = !data.iconSkin.includes("black");
          }
        } else {
          if (data.onlineData) {
            //隐藏有效工地的上级
            onlineFlag = data.onlineData.online != data.onlineData.total;
          } else {
            //隐藏有效工地
            onlineFlag = data.iconSkin.includes("black");
          }
        }
      }
      return onlineFlag;
    },

    /**
     *特殊处理原因： el-tree  filterNodeMethod即使return false 也不能在子节点显示的情况下隐藏父节点
     *
     * */
    filterNodeWidthChannelMethod(value, data, node) {
      let onlineFlag = true;
      if (this.treeState) {
        if (this.treeState == 1) {
          if (data.onlineData) {
            //隐藏在线车辆为0的车队 企业 代理商 平台
            onlineFlag = data.onlineData.online != 0;
          } else if (data.type == 4) {
            //隐藏离线的车辆
            onlineFlag = data.iconSkin.split("_")[1] != 0;
            //给子节点（通道)添加_parentShow属性
            data.children.forEach((chn) => {
              chn._parentShow = onlineFlag;
            });
          } else {
            onlineFlag = data._parentShow;
          }
        } else {
          if (data.onlineData) {
            //隐藏在线车辆为0的车队 企业 代理商 平台
            onlineFlag = data.onlineData.online == data.onlineData.total;
          } else if (data.type == 4) {
            //隐藏离线的车辆
            onlineFlag = data.iconSkin.split("_")[1] == 0;
            data.children.forEach((chn) => {
              chn._parentShow = onlineFlag;
            });
          } else {
            onlineFlag = data._parentShow;
          }
        }
      } else {
        if (this.videoShow && data.type == 5 && this.channelList.length) {
          onlineFlag = this.channelList.includes(data.chn);
        }
      }
      return onlineFlag;
    },
    onlineChange(data) {
      if (!this.treeState) return;
      let node = this.$refs["tree"].getNode(data);
      //获取相关联的node
      let nodes = [node];
      while ((node = node.parent)) {
        nodes.push(node);
      }
      if (this.treeState == 1) {
        //显示相关的隐藏节点
        nodes.forEach((node) => (node.visible = true));
      } else {
        //隐藏相关的已显示节点
        nodes.forEach((node) => {
          if (node.data.onlineData && node.data.onlineData.online === 0) {
            node.visible = false;
          }
          if (node.data.iconSkin && node.data.iconSkin.split("_")[1] == 0) {
            node.visible = false;
          }
        });
      }
    },
    offlineChange(data) {
      if (!this.treeState) return;
      let node = this.$refs["tree"].getNode(data);
      //获取相关联的node
      let nodes = [node];
      while ((node = node.parent)) {
        nodes.push(node);
      }
      if (this.treeState == 1) {
        //隐藏相关的已显示节点
        nodes.forEach((node) => {
          if (node.data.onlineData && node.data.onlineData.online === 0) {
            node.visible = false;
          }
          if (node.data.iconSkin && node.data.iconSkin.split("_")[1] == 0) {
            node.visible = false;
          }
        });
      } else {
        //显示相关的隐藏节点
        nodes.forEach((node) => (node.visible = true));
      }
    },

    changeNodeCheckState(data, state) {
      if (data._disabled === undefined) {
        data._disabled = data.disabled === undefined ? false : data.disabled;
      }
      data.disabled = state;
      // 如果有子级
      if (data.children && data.children != undefined) {
        this.setNodeDisableState(data.children, state);
      }
    },

    setNodeDisableState(data, state) {
      return data.map((current) => {
        return this.changeNodeCheckState(current, state);
      });
    },
    computedTree(treeFlatArray, nodeType, forList,showKey = 'ableData',checkKey) {
      let fleet = treeFlatArray.filter((node) => {
        return node.type === nodeType;
      })
      fleet.forEach(item => {
        if(showKey == 'ableData'){
          item[showKey] = {
            total: item.children ? item.children.length : 0,
          }
        }else {
          item[showKey] = {
            total: item.children ? item.children.length : 0,
            online:item.children ? item.children.filter(child=>child[checkKey]).length : 0
          }
        }
      });
      //企业，代理商，平台 级别
      forList.forEach((type) => {
        let group = treeFlatArray.filter((node) => {
          return node.type === type;
        });
        group.forEach(item => {
          if(showKey == 'ableData'){
            item.ableData = item.children ? item.children.reduce(function (acc, cur) {
              acc.total += cur.ableData ? cur.ableData.total : 0;
              return acc;
            }, { total: 0 }) : {total: 0}
          }else {
            item.onlineData = item.children ? item.children.reduce(function (acc, cur) {
              acc.online += cur.onlineData.online;
              acc.total += cur.onlineData.total;
              return acc;
            }, { online: 0, total: 0 }) : { online: 0, total: 0 }
          }
          
        })
      })
    },
    computedChild(treeFlatArray, type) {
      let gdTotalNumber = treeFlatArray.filter((item) => item.type == 4).length || 0;
      if (type == "project" || type == "projectOnly") {
        let list = [];
        let ableNumber = treeFlatArray.filter((node) => node.type == 4 && node.remark == 0).length || 0;
        let unableList = treeFlatArray.filter((node) => node.type == 4 && node.remark == 1);

        let fleet = treeFlatArray.filter((node) => {
          return node.type === 3;
        });
        fleet.forEach((item) => {
          item.onlineData = {
            online: item.children && item.children.length ? item.children.filter((child) => child.remark == 0).length : 0,
            total: item.children && item.children.length ? item.children.length : 0,
          };
        });
        //企业，代理商，平台 级别
        [2, 1, 0, -1].forEach((type) => {
          let group = treeFlatArray.filter((node) => {
            return node.type === type;
          });
          group.forEach((item) => {
            item.onlineData = item.children.reduce(
              function (acc, cur) {
                acc.online += cur.onlineData.online;
                acc.total += cur.onlineData.total;
                return acc;
              },
              { online: 0, total: 0 }
            );
          });
        });

        unableList.forEach((item) => {
          item.iconSkin = `${item.iconSkin} black`;
          list.push(item.id);
        });
        return {
          gdTotalNumber,
          ableNumber,
          unableNumber: unableList.length || 0,
          list,
        }
      } else if (type == 'xiaoNaVideo' || type == 'workSiteVideo' ) {
        this.computedTree(treeFlatArray, 5, [15, 4, 3, 2, 1],'onlineData','online')
      }
      else if (type == 'workSitePass' || type == 'xiaoNaPass') {
        this.computedTree(treeFlatArray, 5, [15, 4, 3, 2, 1])
      } else if(type == 'pointTerminalWorksite' || type == 'stopTerminalWorksite'){
        this.computedTree(treeFlatArray, 6, [1, 0],'onlineData','online')

      }else if (type == 'changzhan' || type == 'cloudCrossTraffic') {
        this.computedTree(treeFlatArray, 4, [3, 2, 1])
        return {
          gdTotalNumber: treeFlatArray.filter(item => item.type == 5).length || 0,
        }
      } else if (type == 'xianxingqu' || type == 'xiaquMgt' || type == 'xianxingquCross' || type == 'xiaquMgtCross') {
        this.computedTree(treeFlatArray, 3, [2, 1])
        return {}
      } else if (type == "areaFenceTree" ) {
        this.computedFenceTree(treeFlatArray, 4, [3, 2, 1]);
        return {
          totalNumber: treeFlatArray.filter((item) => item.type == 5).length || 0,
        };
      }else if(type == "areaRoadFenceTree"){
        this.computedTree(treeFlatArray, 4, [3,2, 1]);
      } else if (type == 'fenseTreeNew' || type == 'transportPointsFenseTree') {
        this.computedTree(treeFlatArray, 1, [0])

        // let fleet = treeFlatArray.filter(node => {
        //     return node.type === 1;
        // })

        // fleet.forEach(item => {
        //     item.ableData = {
        //         total: item.children.length,
        //     }
        // });
        // //企业，代理商，平台 级别
        // [0].forEach(type => {
        //     let group = treeFlatArray.filter(node => {
        //         return node.type === type;
        //     });
        //     group.forEach(item => {
        //         item.ableData = item.children.reduce(function (acc, cur) {
        //             acc.total += cur.ableData.total;
        //             return acc;
        //         }, {total: 0})
        //     })
        // })
        return {
          totalNumber: treeFlatArray.filter((item) => item.type == 2).length || 0,
        };
      } else if (type == "xiaoNa" || type == "workSite") {
        let iconName = type == "xiaoNa" ? "absorption" : "worksite";
        //消纳场和工地的type是5,但是type == 5的也可能是镇(iconSkin = 'town')这个层级(所以用iconSkin这个字段来区分)
        let list = []
        let gdTotalNumber = treeFlatArray.filter(node => node.iconSkin == iconName).length || 0
        let ableNumber = treeFlatArray.filter(node => node.iconSkin == iconName && node.business_value).length || 0
        let unableList = treeFlatArray.filter(node => node.iconSkin == iconName && !node.business_value)
        let fleet5 = treeFlatArray.filter(node => {
          return node.type === 15;
        })
        fleet5.forEach(item => {
          item.onlineData = {
            online: item.children.filter(child => child.business_value).length,
            total: item.children.length,
          }
        });
        // fleet5.forEach((item) => {
        //   item.onlineData = {
        //     online: item.children.filter((child) => child.business_value).length,
        //     total: item.children.length,
        //   };
        // });
        let fleet = treeFlatArray.filter((node) => {
          return node.type === 4;
        });
        fleet.forEach((item) => {
          item.onlineData = {
            online: 0,
            total: 0,
          };
          if (!item.children.length) return;
          if (item.children[0].onlineData) {
            item.onlineData = item.children ? item.children.reduce(function (acc, cur) {
              acc.online += cur.onlineData.online;
              acc.total += cur.onlineData.total;
              return acc;
            }, { online: 0, total: 0 }) : { online: 0, total: 0 }
          } else {
            item.onlineData = {
              online: item.children.filter((child) => child.business_value).length,
              total: item.children.length,
            };
          }
        });
        //企业，代理商，平台 级别
        [3, 2, 1].forEach((type) => {
          let group = treeFlatArray.filter((node) => {
            return node.type === type;
          });
          group.forEach((item) => {
            item.onlineData = item.children.reduce(
              function (acc, cur) {
                acc.online += cur.onlineData ? cur.onlineData.online : cur.iconSkin == iconName && cur.business_value ? 1 : 0;
                acc.total += cur.onlineData ? cur.onlineData.total : cur.iconSkin == iconName ? 1 : 0;
                return acc;
              },
              { online: 0, total: 0 }
            );
          });
        });

        unableList.forEach((item) => {
          item.iconSkin = `${item.iconSkin} black`;
          list.push(item.id);
        });
        return {
          gdTotalNumber,
          ableNumber,
          unableNumber: unableList.length || 0,
          list,
        };
      } else {
        let list = [];
        let ableNumber = treeFlatArray.filter((node) => node.type == 5 && node.business_value).length || 0;
        let unableList = treeFlatArray.filter((node) => node.type == 5 && !node.business_value);

        let fleet = treeFlatArray.filter((node) => {
          return node.type === 4;
        });
        fleet.forEach((item) => {
          item.onlineData = {
            online: item.children.filter((child) => child.business_value).length,
            total: item.children.length,
          };
        });
        //企业，代理商，平台 级别
        [3, 2, 1].forEach((type) => {
          let group = treeFlatArray.filter((node) => {
            return node.type === type;
          });
          group.forEach((item) => {
            item.onlineData = item.children.reduce(
              function (acc, cur) {
                acc.online += cur.onlineData ? cur.onlineData.online : cur.business_value ? 1 : 0;
                acc.total += cur.onlineData ? cur.onlineData.total : cur.type == 5 ? 1 : 0;
                return acc;
              },
              { online: 0, total: 0 }
            );
          });
        });

        unableList.forEach((item) => {
          item.iconSkin = `${item.iconSkin} black`;
          list.push(item.id);
        });
        return {
          gdTotalNumber: treeFlatArray.filter((item) => item.type == 5).length || 0,
          ableNumber,
          unableNumber: unableList.length || 0,
          list,
        };
      }
    },
    // 给每层type == 4的节点赋值disabled
    setDisabled(treeData) {
      treeData.forEach((item) => {
        if (item.type == 4 && item.expired) {
          item.disabled = true;
        }
        if (item.children && item.children.length) {
          this.setDisabled(item.children);
        }
      });
    },
    //重新从接口获取树数据
    getNewData(){
      this.loading = true;
      this.initTree(this.type,true)
    },
    async initTree(type,newData = false) {
      let treeData;
      if (this.isVehicleState) {
        //动态数据来源
        treeData = await vehicleState.subscribe(type);
        vehicleState.$on("online", this.onlineChange);
        vehicleState.$on("offline", this.offlineChange);
        this.$once("hook:beforeDestroy", () => {
          vehicleState.unsubscribe(type);
          vehicleState.unsubscribe("userVehicle");
          sessionStorage.setItem("treeTypeSelect", JSON.stringify(false));
          vehicleState.$off("online", this.onlineChange);
          vehicleState.$off("offline", this.offlineChange);
        });
        // treeData = res ? res instanceof Array ? JSON.parse(JSON.stringify(res)) : [JSON.parse(JSON.stringify(res))] : [];
        // this.subscription.push(GPS.subscribe((msg)=>this.GPSList.push(msg)));
      } else {//静态数据来源
      //newData为true,从接口重新获取,否则拿缓存的
      let res
      if(newData){
        res = await this.$store.dispatch('ztreeData/refreshTreeData', type);
      }else {
        res = await this.$store.dispatch('ztreeData/getTreeData', type);
      }
        //如果defaultChecked为true，则是停车场进出查询页面获取树，要拿到树的id，默认选中
        if (this.defaultChecked) {
          this.defaultCheckedList = res.map((item) => item.id);
        }

        treeData = res ? (res instanceof Array ? JSON.parse(JSON.stringify(res)) : [JSON.parse(JSON.stringify(res))]) : [];
      }

      this.methodName = treeData == null || treeData.length == 0 ? "" : treeData[0].methodName;
      this.treeFlatArray = this.$utils.flatTree(treeData);

      this.treeData = treeData;
      if ((type == "vehicle" || type == "vehicleWithChannel") && this.expiredDisabled && this.checkMode && !this.hasPermission("expiredNoDisabled:admin")) {
        this.setDisabled(treeData);
      }
      let computedData
      let computedList = ["areaFenceTree","areaRoadFenceTree",'transportPoints', 'projectOnly', 'project', 'workSite', 'workSiteUnknow', 'xiaoNaUnknow', 
      'xiaoNa', 'changzhan', 'cloudCrossTraffic', 'xianxingqu', 'xiaquMgt', 'fenseTreeNew', 'xiaquMgtCross', 'xianxingquCross', 'transportPointsFenseTree']
      if (computedList.includes(type) || this.workSiteFilterList.includes(type) || this.workSiteOnlineFilterList.includes(type)) {
        computedData = this.computedChild(this.treeFlatArray, type)
      }
      this.loading = false;
      await this.$nextTick();
      if (this.workSiteFilterList.includes(type) || this.workSiteOnlineFilterList.includes(type) || this.type=='areaFenceTree'||this.type=='areaRoadFenceTree') {
        this.filterTree()
        if(this.type=='areaFenceTree'){
          this.$emit('filterFence',this.query.fence)
        }

      }
      //展开第一级
      if (this.$refs["tree"] && this.treeData.length) {
        this.$refs["tree"].getNode(this.treeData[0]).expand(null, true);
      }
      //初始化完成事件

      if (computedList.includes(type)) {
        this.$emit("mounted", this.$refs["tree"], computedData);
      } else {
        this.$emit("mounted", this.$refs["tree"]);
      }

      this._promiseResolve();
    },
  },
  created() {
    this.waitForInit = new Promise((resolve) => {
      this._promiseResolve = resolve;
    });
  },
  async mounted() {
    this.loading = true;
    if (this.hasPermission("monitor:alias")) {
      this.checkListDes.push({
        name: "别名",
        value: 2,
      });
      this.checkListDes1.push({
        name: "别名",
        value: 2,
      });
      this.checkListDes2.push({
        name: "别名",
        value: 2,
      });
    }
    if (this.hasPermission("monitor:STCarStatus")) {
      this.checkListDes2.push({
        value: 6,
        name: "滚筒状态",
      });
    }

    this.initShowConfig();
    if (
      (this.type == "vehicle" || this.type == "vehicleWithChannel") &&
      this.shezhiShow &&
      JSON.parse(sessionStorage.getItem("treeTypeSelect"))
    ) {
      this.initTree("userVehicle");
      this.userTreeType = 1;
    } else {
      this.initTree(this.type);
    }
    if(this.type=='areaFenceTree'){
    await this.getFenseTypeList()
    }
    this.$refs.treeWrap.addEventListener("scroll", (e) => {
      this.scrollRecordObj.scrollTop = e.target.scrollTop;
      this.scrollRecordObj.scrollLeft = e.target.scrollLeft;
    });
  },
  activated() {
    this.$refs.treeWrap.scrollTop = this.scrollRecordObj.scrollTop;
    this.$refs.treeWrap.scrollLeft = this.scrollRecordObj.scrollLeft;
  },
  beforeDestroy() {
    // this.subscription.forEach(item => item.unsubscribe())
    // this.subscription = []
  },
};
</script>

<style lang="scss">
@import "ElementTree";
</style>
<style scoped lang="scss">
/deep/ .element-tree-node.is-current > .element-tree-node__label {
  background-color: forestgreen !important;
}

// /deep/ .el-input__suffix{
//     top:3px;
// }
.button-top {
  top: 3px;
  position: relative;
}

.query-wrap {
  position: relative;
  .icon-no {
    position: absolute;
    right: 14px;
    top: 13px;
    height: 22px;
    line-height: 22px;
    cursor: pointer;
    i {
      font-size: 20px;
      color: var(--border-color-base-light);
    }
    .active {
      color: var(--color-primary);
    }
  }
}

.shezhi {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 99;
  top: 11px;
  right: 7px;
  width: 26px;
  height: 26px;
  border-radius: 5px;
  background-color: var(--color-primary);
  color: rgb(255, 255, 255);
  cursor: pointer;

  i {
    font-size: 20px;
  }
}

.show-checklist {
  .el-checkbox {
    line-height: 28px;
  }
}

.element-tree {
  height: 100%;
  display: flex;
  flex-direction: column;

  .query-wrap {
    padding: 10px 10px 0 35px;
    display: flex;
    position: relative;
    flex-wrap: nowrap;

    .tree-tab {
      overflow: hidden;
    }

    .filter-type {
      width: 120px;

      /deep/ .el-input__inner {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .filter-value {
      flex-grow: 1;

      /deep/ .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        &:not(:focus) {
          border-left-color: transparent;
        }
      }
    }
  }

  .tree-wrap {
    overflow: auto;
    padding: 10px;

    .custom-tree-node {
      display: flex;
      align-items: center;
      height: 24px;
      flex-grow: 1;
      padding-right: 5px;
      overflow: hidden;
      text-overflow: ellipsis;

      i {
        display: inline-block;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        flex-shrink: 0;
      }

      .tree-label {
        font-style: normal;
      }

      &__label {
        font-size: 12px;
      }

      &__extra {
        flex-grow: 1;
        display: flex;
        flex-direction: row-reverse;
      }
    }

    .element-tree-node {
      display: flex;
      align-items: center;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      border-radius: 2px;
      padding-right: 5px;
      padding-left: 2px;

      > i {
        display: inline-block;
        width: 21px;
        height: 21px;
        text-align: center;
        line-height: 21px;
        flex-shrink: 0;
        padding-right: 4px;
      }

      &__label {
        border-radius: 2px;
        padding: 4px;
        font-size: 12px;
        width: auto;
        overflow: hidden;
        text-overflow: ellipsis;

        .bill-color {
          color: #5ea441;
        }

        .is-video-support {
          vertical-align: -1px;
          margin-left: 3px;
          color: var(--color-primary);
        }

        // .yes-video-support {
        //     color: var(--color-primary);
        // }

        // .no-video-support {
        //     color: var(--color-info);

        // }

        &:hover {
          background-color: var(--background-color-tree-hover);
          text-decoration: none;
        }
      }
    }
  }

  .control-wrap {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    background-color: var(--border-color-light);
  }
}

::v-deep {
  .el-tree-node {
    min-width: 100% !important;
    // display: block !important;
    float: left;
  }
}
</style>
