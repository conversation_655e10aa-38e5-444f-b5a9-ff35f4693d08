/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/5/26 16:23
 * @LastEditors: xieyj
 * @LastEditTime: 2022/2/21 16:23
 * @Description: 车辆状态树的公共缓存
 */
import {getVehicleState, checkCarOffLine} from '@/view/monitor/util/monitorUtil';
import {GPS, TerminalLink, StatsInfo,JianhuaBillAndLoad} from "@/service/wsService";
import store from '@/store';
import * as utils from '@/util/common';
import {deviceOnline, getVehicleslastonlinetime} from '@/axios/api';
import EventEmitter from 'event-emitter';
import moment from 'moment';

let terminalLinkMsg = []
let timer = null
let reflashTimer = null
const subscriberCount = {
  follow: 0,
  userVehicle: 0,
  vehicle: 0,
  vehicleWithChannel: 0,
}
const subscription = {
  follow: null,
  userVehicle: null,
  terminalLink: null,
  vehicle: null,
  vehicleWithChannel: null,
  statsInfo: null,

}
const subscriptionStatsInfo = {
  userVehicle: null,
  vehicle: null,
  vehicleWithChannel: null,
}
const subscriptionJianhuaBillAndLoad = {
  userVehicle: null,
  vehicle: null,
  vehicleWithChannel: null,
}
const treeDataById = {
  userVehicle: null,
  vehicle: null,
  vehicleWithChannel: null,
}
const treeData = {
  userVehicle: null,
  vehicle: null,
  vehicleWithChannel: null,
}

function setIntervalStart(treeType, type) {
  if (timer) {
    clearInterval(timer)
  }
  timer = setInterval(() => {
    if (!terminalLinkMsg.length) return
    let handleList = terminalLinkMsg.slice(0, 200)
    handleList.sort((a, b) => +(a.times) - +(b.times))
    handleList.forEach(msg => {
      try {
        if (store.state.vehicle.vehicleOnlineJudge[msg.vehicleId] != 2) return
        let stateList = Object.keys(treeDataById)
        stateList.forEach(current => {
          if (!treeDataById[current]) return
          let data = treeDataById[current][msg.vehicleId];
          if (!data || !data.extraInfo) return;
          let state = getVehicleState(data.extraInfo);
          if (msg.status != data.extraInfo.terminalLinkStatus) {
            data.extraInfo.terminalLinkStatus = msg.status
            let vehicleState = checkCarOffLine(state)
            let count = 0;
            if (vehicleState) {
              count = -1;
            } else {
              count = 1;
            }
            if (count && current != 'follow') {
              let temp = data.__parent;
              while (temp) {
                if (!(count == -1 && temp.onlineData.online == 0)) {
                  temp.onlineData.online += count;
                }
                temp = temp.__parent;
              }
              output.$emit(!!(count + 1) ? 'online' : 'offline', data);
              if (current == 'vehicle') {
                let allData = treeData[current][0]
                if (allData) {
                  output.$emit('change', {
                    onlineNum: allData.onlineData.online,
                    offlineNum: allData.onlineData.total - allData.onlineData.online
                  });
                }
              }
            }
          }
          data.iconSkin = state;

        })


      } catch (error) {
        console.error(error.message)
      }
    })
    terminalLinkMsg = terminalLinkMsg.slice(handleList.length, terminalLinkMsg.length)

  }, 1000)
}

function vehicleOnlineState() {
  if (reflashTimer) {
    clearInterval(reflashTimer)
  }
  reflashTimer = setInterval(async () => {
    let result = await getVehicleslastonlinetime()
    if (!result || result.status != 200 || !result.data.length) return
    result.data.forEach(item => {
      if (store.state.vehicle.vehicleOnlineJudge[item.vehicle_id] == 2) return
      let stateList = Object.keys(treeDataById)
      stateList.forEach(current => {
        if (!treeDataById[current]) return
        let data = treeDataById[current][item.vehicle_id];
        if (!data || !data.extraInfo) return
        data.extraInfo.last_time_online = item.last_time_online
        let state = getVehicleState(data.extraInfo);
        if (data.iconSkin.split('_')[1] !== state.split('_')[1]) {
          // let node = this.$refs['tree'].getNode(data);
          let count = 0;
          // 上线
          if (data.iconSkin.split('_')[1] == 0) {
            count = 1;
          }
          // 下线
          if (state.split('_')[1] == 0) {
            count = -1;
          }

          if (count && current != 'follow') {
            let temp = data.__parent;
            while (temp) {
              if (!(count == -1 && temp.onlineData.online == 0)) {
                temp.onlineData.online += count;
              }
              temp = temp.__parent;
            }
            output.$emit(!!(count + 1) ? 'online' : 'offline', data);
            if (current == 'vehicle' && !JSON.parse(sessionStorage.getItem('treeTypeSelect')) || current == 'userVehicle' && JSON.parse(sessionStorage.getItem('treeTypeSelect'))) {
              let allData = treeData[current][0]
              if (allData) {
                // console.log('refchange-------------'+allData.onlineData.online);

                output.$emit('change', {
                  onlineNum: allData.onlineData.online,
                  offlineNum: allData.onlineData.total - allData.onlineData.online
                });
              }
            }

          }

        }
        data.iconSkin = state;
      })

    })
  }, 30 * 1000)
}

async function init(treeType, type) {
  let res = await store.dispatch('ztreeData/getTreeData', treeType);
  let _treeData = res instanceof Array ? JSON.parse(JSON.stringify(res)) : [JSON.parse(JSON.stringify(res))];
  let needTerminalLink = Object.values(store.state.vehicle.vehicleOnlineJudge).includes(2)
  let treeFlatArray;
  //车辆通道树的特殊处理
  if (treeType === 'vehicleWithChannel') {
    //只摊平到车辆节点
    treeFlatArray = utils.flatTree(_treeData, 'children', function (item) {
      return item.type < 4;
    });
  } else if (type) {
    treeFlatArray = _treeData[0].children;

  } else {
    treeFlatArray = utils.flatTree(_treeData);
  }
  //根据ID建立索引
  let nodes = {};
  treeFlatArray.forEach(item => nodes[item.id] = item);
  treeDataById[treeType] = nodes;
  //初始化车辆状态
  let terminalLinkObj = {}
  if (needTerminalLink) {
    let terminalLink = await deviceOnline({
      vehicleIdList: []
    })

    if (!terminalLink || terminalLink.status != 200 || !terminalLink.data || !terminalLink.data.length) return
    terminalLink.data.forEach(item => {
      terminalLinkObj[item.vehicleId] = item
    })
  }
  treeFlatArray.filter(it => it.type == 4).forEach(pos => {
    pos.alarmTime = null
    let nodeData = JSON.parse(JSON.stringify(pos))
    // 获取车辆状态
    let data = treeDataById[treeType][pos.id];
    if (!data) return
    //初始不用这个字段判断,因为这个跟gpsTime一样,等报警上来再加
    if (needTerminalLink) {
      nodeData.terminalLinkStatus = terminalLinkObj[data.id] ? terminalLinkObj[data.id].status : 1
      pos.terminalLinkStatus = terminalLinkObj[data.id] ? terminalLinkObj[data.id].status : 1
    }
    nodeData.acc = pos.acc == '开' ? true : false
    let state = getVehicleState(pos);
    data.iconSkin = state;
    data.extraInfo = nodeData
  });

  if (!type) {
    //初始化节点在线数值
    //设置车队
    let fleet = treeFlatArray.filter(node => {
      return node.type === 3 || node.type === 11;
    })
    fleet.forEach(item => {
      item.onlineData = {
        online: item.children.filter(child => child.iconSkin.split("_")[1] != 0).length,
        total: item.children.length,
      }
    });
    //企业，代理商，平台 级别
    [2, 1, 0].forEach(type => {
      let group = treeFlatArray.filter(node => {
        return node.type === type;
      });
      group.forEach(item => {
        item.onlineData = item.children.reduce(function (acc, cur) {
          acc.online += cur.onlineData.online;
          acc.total += cur.onlineData.total;
          return acc;
        }, {online: 0, total: 0})

      })
    })
  }
  //ws 订阅
  subscription[treeType] = GPS.subscribe((gps) => {
    // debugger;

    try {
      if (!treeDataById[treeType]) return
      let data = treeDataById[treeType][gps.id];

      if (!data) return;
      let rollerStateDes = ['未知', '正转', '反转', '停转']
      data.extraInfo = Object.assign(data.extraInfo ? data.extraInfo : {}, gps)
      if (data.alarmTime) {
        data.extraInfo.alarmTime = data.alarmTime
      }

      data.gpsTime = gps.gpsTime
      data.gpsSpeed = gps.gpsSpeed;
      data.location = gps.location;
      data.mile = gps.mile;
      data.acc = gps.acc ? '开' : '关'
      if (gps.rollerState || gps.rollerState == 0) {
        data.rollerState = rollerStateDes[gps.rollerState];
      }
      if (store.state.vehicle.vehicleOnlineJudge[gps.id] == 2) return
      let state = getVehicleState(data.extraInfo);

      if (data.iconSkin.split('_')[1] !== state.split('_')[1]) {
        let count = 0;
        // 上线
        if (data.iconSkin.split('_')[1] == 0) {
          count = 1;
        }
        // 下线
        if (state.split('_')[1] == 0) {
          count = -1;
        }

        if (count && !type) {
          let temp = data.__parent;
          while (temp && temp.onlineData) {

            if (!(count == -1 && temp.onlineData.online == 0)) {
              temp.onlineData.online += count;
            }
            temp = temp.__parent;
          }

          output.$emit(!!(count + 1) ? 'online' : 'offline', data);
          if ((treeType == 'vehicle' && !JSON.parse(sessionStorage.getItem('treeTypeSelect'))) || (treeType == 'userVehicle' && JSON.parse(sessionStorage.getItem('treeTypeSelect')))) {

            let allData = treeData[treeType][0]
            if (allData && allData.onlineData) {

              output.$emit('change', {
                onlineNum: allData.onlineData.online,
                offlineNum: allData.onlineData.total - allData.onlineData.online
              });
            }
          }

        }
      }
      data.iconSkin = state;


    } catch (error) {
      let time = moment().format('YYYY-MM-DD HH:mm:ss')
      // console.log('处理报错了.....'+time);
    }
  })
  subscriptionStatsInfo[treeType] = StatsInfo.subscribe((info) => {
    try {
      if (!info || !info.b.length) return;
      info.b.forEach((item) => {
        if (!treeDataById[treeType]) return
        let vehicle = treeDataById[treeType][item.a];
        if (!vehicle) return;
        vehicle.stopType = item.c
        vehicle.stopTime = item.d
        //新增行停时长判断的WS字段
        if (item.c == 0) {
          vehicle.driveTimeNow = item.e;
        }
        if (item.c > 0) {
          vehicle.displayTime = item.f;
        }
      });
    } catch (error) {
      console.error(error.message);
    }
  });
  subscriptionJianhuaBillAndLoad[treeType] = JianhuaBillAndLoad.subscribe((msg)=>{
    try {
      if (!msg ) return;
        let vehicle = treeDataById[treeType][msg.vehicleId];
        if(!vehicle) return
        vehicle.jianhua56_isbill = msg.isbill
        vehicle.jianhua56_isload = msg.isload
    } catch (error) {
      console.error(error.message);
    }
  })

  if (needTerminalLink && !subscription.terminalLink) {
    subscription.terminalLink = TerminalLink.subscribe((msg) => {
      terminalLinkMsg.push(msg)
    })

    setIntervalStart(treeType, type)
  }
  vehicleOnlineState(treeType, type)

  if (treeType == 'vehicle' || treeType == 'userVehicle' || treeType == 'vehicleWithChannel') {
    let allData = _treeData[0]
    if (allData) {
      output.$emit('change', {
        onlineNum: allData.onlineData.online,
        offlineNum: allData.onlineData.total - allData.onlineData.online
      });
    }
  }
  return treeData[treeType] = _treeData;
}

function getTreeData() {
  let allData = treeData['vehicle'][0]
  //setTimeOut一下,子组件优先于创建,等父组件mounted中的$on出发之后再传数量
  setTimeout(() => {
    output.$emit('change', {
      onlineNum: allData.onlineData.online,
      offlineNum: allData.onlineData.total - allData.onlineData.online
    });
  })

}

const output = {
  //type:只有关注树传true
  async subscribe(treeType, type = false) {
    try {
      // return await init(treeType,type)
      if (subscriberCount[treeType]) {
        if (treeType == 'vehicle' || treeType == 'userVehicle') {
          getTreeData()
        }
        return treeData[treeType];
      } else {
        return await init(treeType, type);
      }
    } catch (e) {
      console.error(e);
      subscriberCount[treeType]--;
    } finally {
      subscriberCount[treeType]++;
    }
  },
  changeCarIcon(id, state, alarmTime) {
    if (!treeDataById['vehicle']) return
    let data = treeDataById['vehicle'][id];
    if (!data) return
    if (!state) {
      let arr = data.iconSkin.split('_')
      data.iconSkin = arr[0] + '_' + arr[1] + '_' + '2' + '_' + arr[3]
    } else {
      data.iconSkin = state;

    }
    data.alarmTime = alarmTime;
    data.extraInfo.alarmTime = alarmTime;

    let data1 = treeDataById['follow'][id];
    if (!data1) return
    if (!state) {
      let arr = data1.iconSkin.split('_')
      data1.iconSkin = arr[0] + '_' + arr[1] + '_' + '2' + '_' + arr[3]
    } else {
      data1.iconSkin = state;

    }
    data1.alarmTime = alarmTime;
    data1.extraInfo.alarmTime = alarmTime;
  },
  unsubscribe(treeType) {
    if (subscriberCount[treeType] > 1) {
      subscriberCount[treeType]--;
    } else if (subscriberCount[treeType] === 1) {
      treeData[treeType] = null;
      treeDataById[treeType] = null;
      subscription[treeType].unsubscribe();
      if (subscription.terminalLink) {
        subscription.terminalLink.unsubscribe();
        subscription.terminalLink = null;
        terminalLinkMsg = []
      }
      if (subscription.statsInfo) {
        subscription.statsInfo.unsubscribe();
        subscription.statsInfo = null;
      }
      subscription[treeType] = null;
      subscription['terminalLink'] = null;
      clearInterval(timer)
      clearInterval(reflashTimer)

      subscriberCount[treeType]--;
    }
   if (subscriptionJianhuaBillAndLoad[treeType]) subscriptionJianhuaBillAndLoad[treeType].unsubscribe();

    subscriptionJianhuaBillAndLoad[treeType] = null;

  }
}
EventEmitter(output);
output.$emit = output.emit;
output.$on = output.on;
output.$off = output.off;
export default output;
