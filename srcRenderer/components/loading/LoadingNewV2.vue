<template>
  <div :class="['app-loading bg',company == 'ywhj' ? 'app-top1':'bg--header']" v-if="loading">
    <div class="logo" :style="company === 'ywhj' ? 'width:100%;height:100%;':''">
      <p v-if="companyObj[company]" :style="companyObj[company].style" >
        {{companyObj[company].company}}
      </p>
      <p style="font-size: 140px;font-weight: bold;line-height: 80px" v-else-if="SuperFleetList.includes(company)">
        SuperFleet
      </p>
      <div v-else-if="company === 'macao'">
        <h4 style="font-size: 60px;font-weight: bold;line-height: 80px">IP+人車安全智慧雲</h4>
        <span style="font-size: 40px">IPSAFETY+™ FMS</span>
      </div>
      
      <div v-else-if="company === 'ywhj'" style="width:100%;height:100%;background-color:#fff'" class="ywhj-loading">
        <img src="../../../static/imgNewVersion/userCustom/ywhj_home_loading.gif" alt="">
      </div>
      <p style="font-size: 80px;font-weight: bold;line-height: 80px" v-else>
        POLEMATICS
      </p>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

export default {
  name: "Loading",
  computed: {
    ...mapState('main', ['loading', 'company'])
  },
  data() {
    return {
      sid: null,
      SuperFleetList: ['foton', 'coldchain', 'safety', 'transport','ftcar'],
      companyObj:{
        jiande:{
          style:'font-size: 280px;font-weight: bold;line-height: 80px',
          company:'MCIT'
        },
        ptzt:{
          style:'font-size: 90px;font-weight: bold;line-height: 80px',
          company:'北斗三号应用示范'
        },
        nanp:{
          style:'font-size: 140px;font-weight: bold;line-height: 80px',
          company:'NANPING'
        },
        huaian:{
          style:'font-size: 140px;font-weight: bold;line-height: 80px',
          company:'TOPSUN'
        },
        xiapu:{
          style:'font-size: 140px;font-weight: bold;line-height: 80px',
          company:'XIA PU'
        },
        zhouning:{
          style:'font-size: 100px;font-weight: bold;line-height: 80px',
          company:'周宁县渣土系统'
        },
        jinyu:{
          style:'font-size: 140px;font-weight: bold;line-height: 80px',
          company:'锦宇汽车'
        },
        nfhb:{
          style:'font-size: 140px;font-weight: bold;line-height: 80px',
          company:'智慧环卫'
        },
        cangnanzfj:{
          style:'font-size: 100px;font-weight: bold;line-height: 80px',
          company:'苍南执法局'
        },
        jhjy:{
          style:'font-size: 90px;font-weight: bold;line-height: 80px',
          company:'金华监狱安全管理'
        },
        'sgs-cspc':{
          style:'font-size: 100px;font-weight: bold;line-height: 80px',
          company:'LOADING'
        },
      }
    }
  },
  watch: {
    loading: function (val, oldV) {
      if (val) {
        this.timeoutClose()
      } else {
        this.clearTimeout()
      }
    }
  },
  methods: {
    ...mapMutations('main', ['toggleLoading']),
    timeoutClose() {
      this.sid = setTimeout(() => {
        this.toggleLoading(false);
      }, 20 * 1000)
    },
    clearTimeout() {
      if (this.sid) {
        clearTimeout(this.sid);
        this.sid = null;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999999999;

  .logo {
    position: absolute;
    width: 800px;
    height: 250px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: transparent;
    letter-spacing: 1px;
    background: linear-gradient(to bottom, var(--color-text-header) 50%, var(--color-primary) 50%) 100% 0;
    background-clip: text;
    -webkit-background-clip: text;
    background-size: 100% 200%;
    animation: TextFlip 4s ease-in-out infinite;

    @keyframes TextFlip {
      0% {
        background-position: 100% 0;
      }

      100% {
        background-position: 100% 100%;
      }
    }
    .ywhj-loading {
      width:100%;
      height:100%;
      background-color:#fff;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        height: 524px;
      }
    }
  }

}
.app-top1 {
  top:-1px; 
  //加这个是因为comany===ywhj的时候，自定义加了一个loading样式，是白色的，但是如果主题是黑色的情况就很难看
  //所以在loading样式后面的div改为全屏的白色背景，但是top的部分有1px还是黑色的，就形成了一条黑色的线，所以加一个top：-1px
}
</style>

