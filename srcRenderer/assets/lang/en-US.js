exports.default = {
  common: {
    chnName: "CH.",
    index: "#",
    startTime: "Start",
    endTime: "End",
    query: "Load",
    location: "Address",
    export: "导出",
    plateNo: "车牌号",
    driver: "驾驶员",
    no: "无",
    to: "至",
    control: "操作",
    chooseCar: "请选择车辆",
    queryError: "查询出错",
    noData: "没有数据可以导出",
    nomassage: "未查询到消息",
    noexcelData: "不支持excel导出",
    details: "详情",
  },
  dangerEvent: {
    label: {
      today: "Today",
      history: "Histroy",
      allEvent: "All",
      unread: "Unread",
      read: "Read",
      overdue: "Delay",
      typeScreening: "Type screening",
      batchProcessing: "Handle",
      index: "#",
      group: "Group",
      plateNo: "Plate",
      startTime: "Start",
      endTime: "End",
      startLocation: "Start Address",
      endLocation: "End position",
      dealType: "Handle mode",
      dealUser: "Handler",
      remark: "Remark",
      control: "",
      detail: "Detail",
      vehicleSelect: "Vehicles",
      all: "All",
      status: "Status",
      manualProcessing: "Manual handle",
      automaticProcessing: "Automatic handle",
      unProcessing: "Unhandled",
      query: "Load",
      confirm: "confirm",
      close: "Close",
      none: "Innumerable data",
      region: "区域",
      regionNoFilter: "不限",
      regionNoRestriction: "非限行区",
      overdueTip: "超过3小时未处理",
      eventType: "事件类型",
      dealStatus: "处理状态",
      duration: "持续时间",
      maxSpeed: "最高车速",
      avgSpeed: "平均车速",
      export: "导出",
      filterExport: "筛选导出",
      regionFilter: "区域选择",
      eventSelect: "事件选择",
      hour: "时",
      minute: "分",
      second: "秒",
    },
    reportName: "Events Report",
    messageInfo: {
      0: "Select at least one event",
      1: "The start time cannot be greater than the end time",
      2: "The time interval cannot be more than one week",
      3: "No selected vehicles",
      4: "No data to export",
      5: "More than 10000 records, only the most recent 10000 pieces of data are exported",
      6: "跳转参数错误",
    },
  },
  video1078: {
    realTime: "Video",
    playback: "History video ",
  },
  realTime1078: {
    0: "不能添加相同的视频通道，已忽略",
    currentSelect: "Plate Number",
    none: "None",
    stopAll: "Stop All ",
    talkBack: "Intercom",
    refresh: "Refresh",
    switchTo: "切换",
    windowMode: "窗口模式",
    stopPlay: "秒后停止播放",
    will: "将于",
    all: "全部",
    clearSelected: "清空已选择",
    play: "播放",
    stop: "停止",
    messageInfo: {
      0: "不能添加相同的视频通道，已忽略",
    },
  },
  playback1078: {
    1232: {
      downloadList: "下载列表",
      cancelDownload: "取消下载",
      reUpload: "重新上传",
      download: "下载",
      currentChn: "当前通道",
    },
    startTime: "Start",
    endTime: "End",
    date: "Date",
    selectDate: "Select date",
    recordPosition: "Video location",
    multipleMode: "Multiple Channel Mode",
    refresh: "Refresh",
    recordList: "Video list",
    searching: "Loading",
    noResult: "No Data",
    index: "#",
    chn: "Channel",
    timeLength: "Duration",
    fileSize: "File size",
    fileName: "Name",
    state: "Status",
    loading: "Loading",
    progress: "Progress",
    clickHint: "Get Progress",
    clickHint2: "Getting...",
    clickHint3: "Uploading（ftp)",
    clickForDownload: "Download",
    clickForUpload: "Upload",
    control: " ",
    play: "Play",
    fileState: {
      0: "Uploading",
      1: "Failed",
      3: "-",
      4: "Finish",
    },
    messageInfo: [
      "The vehicle does not support video-related servicesPlease consult customer service for details",
      "Please select a vehicle",
      "Timeout for getting video list",
      "Timed out to get video list status",
      "Failed to request upload",
      "Start uploading videos from the device",
      "Please select a vehicle",
      "该时间段内没有可用的录像！",
      "暂不支持播放多段视频，为您播放选区的第一部分！",
      "暂不支持下载多段视频，为您下载选区的第一部分！",
      "使用FTP方式下载视频,需要终端支持",
      null,
    ],
    timeAxis: "Time",
    downloadList: "Download",
    cancelDownload: "Cancel Download",
    reUpload: "Re-upload",
    download: "Download",
    currentChn: "Ch",
    clickHint4: "ftp方式无进度信息",
    mode: "模式",
    test: {},
    stopAll: "全部停止",
    playAll: "全部播放",
  },
  stateTree: {
    onlyOnline: "Online",
  },
  videoView: {
    checkSimFlow: "Check traffic information",
    simFlowOver: "Traffic exceeds usage",
    messageInfo:
      "The vehicle does not support video-related servicesPlease consult customer service for details",
  },
  videoPlayerPony: {
    0: "信息下发失败，终端可能不在线，请重试！",
    play: "Play",
    screenShot: "Screenshot",
    pause: "Pause",
    stop: "Stop",
    tts: "tts语音下发",
    talkBack: "语音对讲",
    exit: "退出",
    fullscreen: "全屏",
    notSupportAudio: "该设备不支持音频输出",
    notSupportVideo: "该设备不支持视频功能",
    ttsTip: "请输入下发文字",
    messageInfo: {
      1: "下发成功",
      2: "获取视频地址失败",
    },
    testArr: {},
    tip: {
      0: "视频将在",
      1: "秒后自动断开以节省流量",
    },
  },
  scoreMonitor: {
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      detail: "详情",
      driveMile: "里程",
      deptName: "车队名",
      date: "日期",
      eventCount: "报警事件数量",
      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossTseScore: "路口超速",
      driveTime: "运行时长",
    },
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
    vehicleList: "车辆列表",
    companyList: "企业列表",
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    reportName: "评分监控历史",
    export: "导出",
    byDay: "按日显示",
  },
  car: {
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      detail: "详情",
      driveMile: "里程",
      deptName: "车队名",
      date: "日期",
      eventCount: "报警事件数量",
      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossTseScore: "路口超速",
      driveTime: "运行时长",
    },
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
    vehicleList: "车辆列表",
    companyList: "企业列表",
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    reportName: "评分监控历史",
    export: "导出",
    byDay: "按日显示",
  },
  enterprise: {
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      detail: "详情",
      driveMile: "里程",
      deptName: "车队名",
      date: "日期",
      eventCount: "报警数量",
      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossTseScore: "路口超速",
      driveTime: "运行时长",
    },
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
    vehicleList: "车辆列表",
    companyList: "企业列表",
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    reportName: "评分监控历史",
    export: "导出",
    byDay: "按日显示",
  },
  scoreDetail: {
    title: "Vehicle rating details",
    plateNo: "Plate",
    updateDate: "Update time",
    report: {
      safeScore: "Safety score",
      dimensionScore: "Dimension score",
      driveScore: "Score",
      valueUnit: "Score",
      chartTitle: "Scoring",
    },
    unit: {
      driveTime: "Km",
      driverMile: "H",
      maxSpeed: "Km/h",
      averageSpeed: "Km/h",
      eventExpresswayScore: "Score",
      eventUnExpresswaySafeScore: "Score",
      eventCount: "Secondary",
      eventDaySafeScore: "Score",
      eventNightSafeScore: "Score",
    },
    label: {
      driveTime: "Duration",
      driveMile: "Mileage",
      maxSpeed: "MaxSpeed",
      averageSpeed: "Average speed",
      eventExpresswayScore: "High speed scoring",
      eventUnExpresswaySafeScore: "Non-high speed scoring",
      eventCount: "Total event",
      eventDaySafeScore: "Day score",
      eventNightSafeScore: "Night score",

      accScore: "Acceleration",
      brakeScore: "Breaks",
      fcwScore: "FCW",
      hmwScore: "Distance",
      laneKeepScore: "LDW",
      pcwScore: "PCW",
      speedScore: "Speeding",
      turnLightScore: "Turn Light",
      turnScore: "Turns",
      smokeScore: "Smoking ",
      distractScore: "Distraction",
      lrreguarDriveScore: "Irregular driving",
      speedNoAdasScore: "Speed",
      telScore: "Phone",
      tiredNoAdasScore: "Fatigue",
      tiredScore: "Fatigue",
      crossTseScore: "路口超速",

      crossNolightScore: "路口未打灯",
      peaktermLineScore: "高峰期闯限行",
      noPasscheckScore: "无通行证闯限行",
      workOvertimeScore: "工作超时评分",
    },
    hint: {
      0: "危险，注意休息",
      accScore: [
        "Danger, watch out for rapid acceleration",
        "Pay attention to rapid acceleration",
        "Continue to maintain",
        "Continue to maintain",
      ],
      brakeScore: [
        "Danger, watch out for sharp deceleration",
        "Pay attention to sharp deceleration",
        "Continue to maintain",
        "Continue to maintain",
      ],
      fcwScore: [
        "Danger, pay attention to rear-end collision",
        "Pay attention to rear-end collision",
        "Continue to maintain",
        "Continue to maintain",
      ],
      hmwScore: [
        "Danger, pay attention to distance control",
        "Pay attention to distance control",
        "Continue to maintain",
        "Continue to maintain",
      ],
      laneKeepScore: [
        "Danger, pay attention to lane maintenance",
        "Pay attention to lane maintenance",
        "Continue to maintain",
        "Continue to maintain",
      ],
      pcwScore: [
        "Danger, pay attention to pedestrians and non-motor vehicles",
        "Pay attention to the collision between pedestrians and non-motor vehicles",
        "Continue to maintain",
        "Continue to maintain",
      ],
      speedScore: [
        "DangerWatch the speed",
        "Pay attention to the speed of the vehicle",
        "Continue to maintain",
        "Continue to maintain",
      ],
      turnLightScore: [
        "Danger, pay attention to the use of turn signal",
        "Pay attention to the use of turn signal",
        "Continue to maintain",
        "Continue to maintain",
      ],
      turnScore: [
        "Danger, watch the turn",
        "Pay attention to the turn",
        "Continue to maintain",
        "Continue to maintain",
      ],
      smokeScore: [
        "Dangerous, please do not smoke",
        "No Smoking",
        "Continue to maintain",
        "Continue to maintain",
      ],
      distractScore: [
        "DangerFocus",
        "Pay attention and concentrate",
        "Continue to maintain",
        "Continue to maintain",
      ],
      lrreguarDriveScore: [
        "Dangerous, standard driving",
        "Please regulate driving",
        "Continue to maintain",
        "Continue to maintain",
      ],
      speedNoAdasScore: [
        "DangerousKeep the speed",
        "Please keep your speed",
        "Continue to maintain",
        "Continue to maintain",
      ],
      telScore: [
        "It's dangerousPlease don't call",
        "No Phone Call",
        "Continue to maintain",
        "Continue to maintain",
      ],
      tiredNoAdasScore: [
        "Danger, pay attention to rest",
        "Please pay attention to rest",
        "Continue to maintain",
        "Continue to maintain",
      ],
      tiredScore: [
        "Danger, pay attention to rest",
        "Please pay attention to rest",
        "Continue to maintain",
        "Continue to maintain",
      ],
      crossTseScore: {
        0: "危险，注意车速",
        1: "请注意车速",
        2: "继续保持",
        3: "继续保持",
      },
      crossNolightScore: [
        "Danger, please pay attention to the lights at the junction",
        "please pay attention to the lights",
        "Continue to maintain",
        "Continue to maintain",
      ],
      peaktermLineScore: [
        "Danger, pay attention to peak period line",
        "Please pay attention to peak period line",
        "Continue to maintain",
        "Continue to maintain",
      ],
      noPasscheckScore: [
        "note that the pass is available",
        "please pay attention to the pass",
        "Continue to maintain",
        "Continue to maintain",
      ],
      workOvertimeScore: [
        "Danger, pay attention to job timeout ",
        "Please pay attention to job timeout",
        "Continue to maintain",
        "Continue to maintain",
      ],
    },
  },
  alarmDetailSearch: {
    label: {
      tabVehicle: "Object",
      tabAlarm: "Alert Type",
      vehicleList: "Vehicle",
      driverList: "Driver",
      index: "#",
      plateNo: "Plate",
      driver: "Driver",
      alarmTime: "Time",
      alarmType: "Alert type",
      alarmLevel: "Level",
      speed: "Speed",
      alarmLocation: "Position",
      video: "Video",
      company: "归属",
    },
    startTime: "Start",
    endTime: "End",
    query: "Load",
    export: "Export",
    hint: "",
    videoHint: "Please check it in two minutes",
    messageInfo: {
      0: "Please select a vehicle or driver",
      1: "Too much data, only the latest 10000 pieces of data are exported",
      2: "Search error",
      3: "跳转参数错误",
    },
    reportName: "Alert details search data",
    minSpeed: "Min Speed",
    maxSpeed: "Max Speed",
  },
  riskDimension: {
    hint: {
      selectVehicle: "Please select a vehicle",
      selectStartTime: "Select start date",
      selectEndTime: "Select end date",
    },
    label: {
      time: "time",
      query: "Load",
      export: "Export",
      index: "#",
      plateNo: "Plate",
      dataDate: "Data time",
      totalScore: "Total score",
      totalTime: "Duration",
      driveMile: "Mileage",
      accScore: "Acceleration",
      brakeScore: "Breaks",
      fcwScore: "FCW",
      hmwScore: "Distance",
      laneKeepScore: "LDW",
      pcwScore: "PCW",
      speedScore: "Speeding",
      turnLightScore: "Turn Light",
      turnScore: "Turns",
    },
    messageInfo: {
      0: "No selected vehicles",
      1: "Error searching data, please try again",
    },
    reportName: "Risk dimension report",
  },
  riskFactorReport: {
    hint: {
      selectVehicle: "Please select a vehicle",
      selectStartTime: "Select start date",
      selectEndTime: "Select end date",
    },
    label: {
      time: "time",
      query: "Load",
      export: "Export",
      index: "#",
      plateNo: "Plate",
      driveMile: "Mileage",
      total: "Risk coefficient",
      ufcw100: "UFCW/100km",
      ldw100: "LDW/100km",
      pcw100: "PCW/100km",
      hmw100: "HMW/100km",
      sli100: "SLI/100km",
      fcw100: "FCW/100km",
      distract: "Distracted driving / 100km",
      acc100: "Accelerated early warning / 100km",
      brake100: "Emergency brake warning / 100km",
      flc100: "Fast lane change / 100km",
      shield100: "Blind area alarm / 100km",
      gpssli100: "Gps Speeding / 100km",
      tsr100: "Steering overspeed / 100km",
      tb100: "Turn emergency brake / 100km",
      ta100: "Turn acceleration / 100km",
      ntl100: "Turn without lights / 100km",
    },
    messageInfo: {
      0: "No selected vehicles",
      1: "Error searching data, please try again",
    },
    reportName: "Risk coefficient report",
  },
  userCenter: {
    title: "User information",
    label: {
      currentUser: "Current user",
      realName: "Real name",
      createTime: "Creation time",
      telephone: "fixed telephone",
      deptName: "In the department",
      mobile: "Contact number",
      validate: "Expiration date",
      fax: "User fax",
      chapter: "E-chapter",
      userCenter: "My Account",
      passMgt: "Password Mgt.",
      password: "Original password",
      newPassword: "New password",
      confirmPW: "Confirm the new password",
      confirm: "Modification",
      close: "Close",
      other: "other",
      num: "Number of lines displayed per page",
    },
    messageInfo: {
      0: "Password",
      1: "Please enter the password again",
      2: "The two input passwords are not the same! ",
      3: "Please key in numbers",
    },
  },
  messageBox: {
    title: "Message",
    label: {
      all: "All",
      read: "Read",
      unread: "Unread",
    },
    messageInfo: {
      0: "No Messages",
    },
  },
  messageSend: {
    title: "Send out the message",
    label: {
      web: "Web terminal",
      miniProgram: "Wechat",
      preset: "Quick generation",
      msgTitle: "Message title",
      msgType: "Message type",
      msgContent: "Message content",
      send: "Send",
    },
    presetType: ["Custom format", "Update reminder", "Welcome"],
    msgType: ["Hint", "Warning", "information", "Alarm"],
    messageInfo: {
      0: "Please enter a title",
      1: "Please enter content",
      2: "Please select the user to push first! ",
      3: "Push successful",
      4: "Please choose the driver to push first! ",
    },
  },

  contactUS: {
    title: "contact information",
    label: {
      cancel: "Cancel",
      sure: "Sure",
    },
    presetType: ["Custom format", "Update reminder", "Welcome"],
    msgType: ["Hint", "Warning", "information", "Alarm"],
    messageInfo: {
      0: "Please enter a title",
      1: "Please enter content",
      2: "Please select the user to push first! ",
      3: "set up successful",
    },
  },
  home: {
    label: {
      userCenter: "My Account",
      msgSend: "Site Message",
      signOut: "Logout",
    },
  },
  tabMenu: {
    home: "Home",
    closeSelf: "Close",
    closeOther: "Close Others",
    closeAll: "Close All",
  },
  loginForm: {
    nameMsg: "User Name",
    passMsg: "Password",
    rememberMsg: "Remember",
    forgetMsg: "Forget? ",
    loginMsg: "Login",
    tipStartMsg: "Please dial",
    tipEndMsg:
      "Our internal staff will reset the password for you later.Please change your password after logging in with the new password.Thank you! ",
    knowMsg: "\n ",
    errorState: {
      "-1": "\n ",
      "-2": "User expiration",
      "-3": "No such user",
      "-4": "Not allowed",
      "-5": "System internal error",
    },
  },
  TerminalErrorSearch: {
    name: "Details of vehicle failure",
    alarmFilter: "Alert Type",
    search: "Load",
    export: "Export",
    index: "#",
    group: "Grouping",
    plateNo: "Plate",
    terminalNo: "Device ID",
    errorName: "Fault name",
    detail: "Details",
    message: {
      noVehicle: "Please select a vehicle",
      queryError: "Search error",
      noData: "No data was found",
      noDataExport: "No data to export",
    },
  },
  ztreeMatics: {
    placeholder: "Keywords...",
    message: {
      noData: "No data was found",
      current: "Please select the specified level",
    },
    select: [
      "Default",
      "Plate",
      "From No",
      "SIM ID",
      "Device ID",
      "Driver",
      "Company",
      "Fleet",
    ],
  },
  ztreeState: {
    all: "All",
    online: "Online",
    offline: "Offline",
  },
  monitor: {
    name: "TRACKING",
    vehicleTab: "Vehicle",
    alarmTab: "Alert List",
    eventTab: "Events",
    maticsTab: "Summary",
    lockline: "Lock Tracking",
    onlineVideo: "Video",
    playback: "Track History",
    command: "Send out instructions",
    more: "More",
    filterVehicle: "Region Search",
    changezhan: "场站",
    setting: {
      title: "Config",
      formTitle: ["table field", "current display"],
      returnDefault: "restore default",
      close: "close",
      commit: "confirm",
      tabList: [
        "Table Display Configuration",
        "Map Popup Configuration",
        "Other Settings",
        "Commercial Concrete Vehicle Status Configuration",
      ],
      cluster: {
        message1: "聚合范围 单位(像素px) 越大聚合范围越大 0代表不聚合",
        message2: "不聚合",
        message3: "最大聚合",
      },
      message: {
        operateOk: "操作成功",
      },
    },
    table: {
      index: "#",
      operation: " ",
      format_dept: "Groups",
      plate: "Plate",
      format_state: "Status",
      state: "Status (icon)",
      offlineTime: "Offline duration",
      gpsSpeed: "GPS Speed",
      format_dire: "Direction",
      format_acc: "ACC",
      format_gpsTime: "Time",
      offline_time: "Offline duration",
      pulseSpeed: "VSS speed",
      latlng: "Position",
      location: "Address",
      driver_name: "Driver",
      driver_phone: "Driver's phone",
      driver_idcardno: "Driver ID number",
      vin: "VIN code",
      vehicleType: "Model",
      transType: "Industry",
      simCode: "SIM ID",
      terminalNo: "Device ID",
      mile: "Mileage",
      color: "License plate color",
      height: "Elevation",
      video_name: "Video equipment",
      muck_nuclear_carry: "load",
      working_time: "working time",
      muck_cloth_cover: "Tarpaulin",
      muck_hopper_lift: "lift",
      muck_status_rotation: "rotation",
      tableName: "Vehicle real time status list",
      ftt_doorstate: "door magnetism",
      ftt_ysj: "cold machine",
      ftt_tempdesc: "temperature",
      ftt_zaiz: "load",
      moutai_lock_onStatus: "onStatus",
      moutai_lock_lockStatus: "lockStatus",
      moutai_lock_power: "power",
      moutai_lock_brokenStatus: "brokenStatus",
      moutai_lock_password: "password",
      basicInfo: "basic info",
      vehicleStaus: "vehicle status",
      lockStatus: "lock status",
      moutai_door_magnetism: "door magnetism",
      rollerState: "roller status",
    },
    message: {
      neverOnline: "The vehicle was never online",
      lockError: "Abnormal vehicle location",
      rangeNoVehicle: "No vehicles were screened",
      noDataExcel: "没有数据可以导出",
      collectOk: "收藏成功",
      canlCollectOk: "取消收藏成功",
    },
    extanddetail: "附加",
    collect: "收藏",
    vehicleTree: "车辆",
    gongdiTree: "工地",
    xncTree: "消纳场",
    txxlTree: "通行线路",
    projectTree: "Project",
  },
  monitorAlarm: {
    name: "Alert List",
    index: "#",
    company: "Groups",
    plate: "Plate",
    alarmTime: "Time",
    alarmType: "Alert type",
    speed: "Speed",
    location: "Address",
    message: "Alert positioning anomaly",
    maxSpeed: "Max Speed",
    minSpeed: "Min Speed",
    maxShow: "Max Show",
  },
  monitorEvent: {
    name: "Events",
    index: "#",
    company: "Groups",
    plate: "Plate",
    eventType: "Type",
    startTime: "Start",
    endTime: "End",
    location: "Address",
  },
  monitorStatics: {
    onlineT: "Online",
    yesterday: "Yesterday",
    runMile: "Mileage",
    runTime: "Duration",
    runAlarm: "Alerts /100km",
    tip: {
      1: "vehicle",
      2: "time",
      3: "ten thousand",
    },
  },
  monitorTree: {
    all: "All",
    online: "Online",
    offline: "Offline",
    deptNumber: "Groups",
    checkDept: "Selected",
  },
  monitorMatics: {
    all: "Total",
    checked: "Selected",
    online: "Online",
    offline: "Offline",
    working: "Work",
  },
  monitorPopup: {
    lockline: "Lock Tracking",
    onlineVideo: "Video",
    playback: "Track History",
    command: "Send out instructions",
    lock: "Locking",
    format_dept: "Group",
    driver_name: "Driver",
    driver_phone: "Phone",
    gpsSpeed: "Speed",
    format_acc: "ACC状态",
    mile: "Mileage",
    terminalNo: "ID",
    location: "Address",
    vehicleType: "Model",
    transType: "Industry",
    format_dire: "Direction",
    muck_nuclear_carry: "load",
    muck_cloth_cover: "Tarpaulin",
    muck_hopper_lift: "lift",
    // muck_status_rotation: 'rotation',
    height: "Elevation",
    simCode: "SIM ID",
    working_time: "working time",
    more: "more",
    extanddetail: "additional",
    collect: "collect",
    ftt_doorstate: "door magnetism",
    ftt_ysj: "cold machine",
    ftt_tempdesc: "temperature",
    ftt_zaiz: "load",
    moutai_lock_onStatus: "onStatus",
    moutai_lock_lockStatus: "lockStatus",
    moutai_lock_power: "power",
    moutai_lock_brokenStatus: "brokenStatus",
    moutai_lock_password: "password",
    moutai_door_magnetism: "door magnetism",
    rollerState: "roller status",
  },
  monitorBasic: {
    title: {
      driver_info: "Platform Driver Info",
      trans_info: "Trans Info",
      signal_status: "Vehicle Signal Status Bits",
      tire_status: "Tire Pressure",
      satelliteStatus: "Targeting",
      door_status: "Door Status",
      onLine_driver_info: "Equipment Driver Info",
    },
    basic: {
      color: "License plate color",
      format_acc: "ACC status",
      format_dept: "Groups",
      format_state: "Vehicle status",
      gpsSpeed: "Speed",
      location: "Address",
      mile: "Mileage",
      simCode: "SIM ID",
      terminalNo: "ID",
      transType: "Industry",
      vehicleType: "Model",
    },
    driver: {
      driver_idcardno: "ID number",
      driver_issDate: "Date of issue",
      driver_issOrg: "License issuing agency",
      driver_licNum: "Qualification certificate",
      driver_licState: "License status",
      driver_name: "Current driver",
      driver_staffNo: "Driver serial number",
      driver_phone: "Driver's cell phone number",
    },
    onLineDriver: {
      on_driver_name: "Driver name",
      on_driver_idcardno: "ID number",
      on_driver_issOrg: "Name of issuing Agency",
      on_driver_licNum: "Qualification certificate",
      on_driver_expirationDate: "Validity of certificate",
    },
    gsix: {
      gsix_cool_temp: "Engine coolant temperature",
      gsix_dpf: "DPF pressure difference",
      gsix_engine: "Engine speed",
      gsix_gas: "Air intake",
      gsix_oil: "Engine fuel flow",
      gsix_oil_rate: "Tank level",
      gsix_pressure: "Friction torque",
      gsix_rest: "Reactant allowance",
      gsix_src_lower: "SCR downstream NOx sensor output",
      gsix_src_temp1: "SCR inlet temperature",
      gsix_src_temp2: "SCR outlet temperature",
      gsix_src_upper: "SCR upstream NOx sensor output",
    },
    obdb: {
      gsix_cvn: "Calibration verification code CVN",
      gsix_errors_count: "Total number of fault codes",
      gsix_errors_list: "Fault code information list",
      gsix_iupr: "IUPR value",
      gsix_mil: "MIL status",
      gsix_protocol: "OBD protocol status",
      gsix_scn: "Software calibration identification number",
    },
    obdz: {
      bpcsms: "Monitoring of pressurized pressure control system",
      ccms: "Total composition monitoring",
      cms: "Catalytic converter monitoring",
      csasms: "Cold start Auxiliary system Monitoring",
      dpfms: "DFF monitoring",
      egr: "EGB/VVT system monitoring",
      egshms: "Exhaust sensor heater monitoring",
      egsms: "Exhaust sensor monitoring",
      esms: "Evaporation system monitoring",
      fsms: "Fuel system monitoring",
      hcms: "Heating catalytic converter monitoring",
      mms: "Fire monitoring",
      nmhc: "NMHC sample Catalytic Converter Monitoring",
      nox: "NOx catalytic reduction / adsorber monitoring",
      sasms: "Secondary air system monitoring",
      srms: "Refrigerant monitoring in A _ C _ system",
    },
    screen: {
      media_auxStroStatus: "Disaster preparedness storage",
      media_fadeSignal: "Signal occlusion",
      media_lostSignal: "Signal loss",
      media_mainStroStatus: "Main storage",
      media_numBeyond: "Video recording reaches storage threshold",
      media_otherStroStatus: "Other video equipment failure",
    },
    trans: {
      trans_CardEndDate:
        "Road transport permit is valid at the end of its validity period",
      trans_CardNo: "Transport permit number",
      trans_CardOrg: "Road transport license issuing agency",
      trans_CardStartDate:
        "From the period of validity of the road transport permit",
      trans_Goods: "Transport goods",
      trans_vehicleVerifyStatus: "Vehicle inspection status",
    },
    vehicle: {
      stand_single: "Recorder signal",
      channelCount: "Number of channels",
      format_dire: "Direction",
      height: "Height",
      latlng: "Position",
      stand_dianState: "Electric circuit",
      stand_doorState: "Vehicle door",
      stand_exporEventId: "Manual confirmation alarm event ID",
      stand_inandoutline: "Line driving",
      stand_oilNumber: "Oil quantity",
      stand_oilState: "Oil path",
      stand_operateState: "Operating status",
      stand_overspeedState: "Speeding",
      stand_zhzState: "Loading status",
      stand_clzz: "Vehicle load",
      terminalNo: "ID",
      vin: "VIN code",
      muck_nuclear_carry: "load",
      muck_cloth_cover: "Tarpaulin",
      muck_hopper_lift: "lift",
      muck_status_rotation: "rotation",
      mini_temp: "Temperature",
      mini_io_status: "IO Condition",
      driving_state: "Driving State",
      forward_collision_warning:
        "Forward Collision Warning Collected by Emergency Vehicle Inspection System",
      deviation_warning: "Lane Departure Warning",
      load_state: "Passenger Status",
      position: "Position",
      encryption:
        "Whether the latitude and longitude is encrypted by the security plug-in",
      mini_analog: "Analog",
      gsmSingle: "Network signal strength",
      mini_waybill: "Electronic Waybill",
    },
    tab: {
      basic: "Basic status",
      gsix: "engine",
      alarm: "Device storage alarm",
      driver: "Driver / operator",
      gps: "Satellite Status Parameters",
    },
    other: {
      gsix_t1: "Vehicle terminal collects data",
      gsix_t2: "OBD basic data",
      gsix_t3: "OBD support data",
      gsix_t4: "Fault code list",
      support: "Support",
      unsupport: "Not supported",
      teskyes: "Test completed / not supported",
      teskno: "The test is not completed",
    },
    gps: {
      bdsListNum: "Number of Beidou satellites",
      gpsListNum: "Number of GPS satellites",
      glonassListNum: "Number of GLONASS satellites",
      galileoListNum: "Number of GALILEO satellites",
      satelliteNo: "Satellite number",
      elevation: "Elevation",
      azimuth: "Azimuth",
      ratio: "Carrier to noise ratio",
      gps: "Location",
    },
  },
  monitorCommand: {
    message: {
      loading: "Sending instructions",
    },
    tab: {
      phone: "Call back",
      photo: "Take a picture",
      text: "Text distribution",
      dianming: "Vehicle roll call",
      send: "Send instruction",
    },
    basic: {
      acc: "ACC status",
      terminalNo: "ID",
      simCode: "SIM ID",
    },
    photo: {
      pass: "CH.",
      type: "Photo mode",
      number: "Number of photos",
      size: "Resolution",
      quality: {
        name: "Image quality",
        message:
          "The smaller the value, the better the image quality and the larger the picture volume",
      },
      interval: {
        name: "Time interval",
        message:
          "Photo interval / recording duration; 0 means taking pictures at minimum intervals or recording all the time",
      },
      compare: "Contrast ratio",
      saturated: "Saturation degree",
      light: "Brightness",
      color: "Chrominance",
      saveWay: {
        name: "Save Fla",
        value: ["Preservation", "Upload"],
      },
      photoWay: {
        0: "Stop filming",
        1: "Start taking pictures",
        "-1": "Start recording",
      },
    },
    phone: {
      name: "Pattern",
      value: ["Ordinary call", "Monitor"],
      number: "Phone",
    },
    text: {
      sign: "Mark",
      urgent: "urgent",
      add_show: "Advertising screen display",
      terminal_monitor: "Device Display",
      terminal_tts: "TTS",
      type: "Information type",
      ttsmodel: "TTS model",
      typeValue: [
        "Central navigation information",
        "CAN fault code information",
      ],
      content: "Distribute content",
    },
  },
  playBack: {
    showSetting: "Show Setting",
    name: "TRACK",
    timeSelect: "Time Slot",
    startTime: "Start",
    endTime: "End",
    alarmFilter: "Alert screening",
    search: "Load",
    excel: "Export",
    tableLine: {
      name: "Track",
      index: "index",
      formatTime: "GPS time",
      acc: "ACC",
      gpsSpeed: "Speed",
      milebet: "Mile range",
    },
    tableAlarm: {
      name: "Alarm",
      filter: "Filter",
      index: "index",
      formatTime: "GPS time",
      gpsSpeed: "Speed",
      alarmName: "Alert type",
    },
    tableStop: {
      name: "Stop Point",
      filter: "Filter",
      index: "index",
      begin_time: "begin time",
      time: "Duration",
    },
    tablefense: {
      name: "Fense",
      tableList: ["site", "accommodation", "parking", "jurisdiction"],
      index: "index",
      passTimeView: "time",
      areaName: "fense name",
      areaTypeName: "fense type",
    },
    control: {
      speedName: "Speed Line",
      speedList: ["slow", "normal", "fast", "Soon"],
    },
    popupStop: {
      time: "Time",
      timel: "Duration",
      address: "Address",
    },
    timeSelectDesc: {
      0: "today",
      1: "yesterday",
      2: "two days ago",
      "-1": "custom",
    },
    message: {
      noData: "No data found",
      noVehicle: "Please choose a vehicle first! ",
      queryError: "Search failed! ",
    },
  },
  playBackControl: {
    name: "Show Setting",
    speed: "Speed Setting",
    detail: "Detail display settings",
    detailList: {
      driver: "Driver",
      gpsSpeed: "Speed",
      mile: "Mile",
      milebet: "Mile range",
      location: "Address",
      acc_word: "ACC",
      ft_zaiz: "load",
      ft_fdj: "engine",
      ft_menc: "door",
      ft_ysj: "compressor",
      moutai_lock_onStatus: "onStatus",
      moutai_lock_lockStatus: "lockStatus",
      rollerStatus: "rollerStatus",
    },
    map: "Map display settings",
    mapList: {
      inter: "Interest point",
      alarm: "Alarm point",
      stop: "Stop point",
      gd: "Construction site",
      xnc: "Accommodation",
    },
  },
  playBackPopup: {
    driver: "Driver",
    gpsSpeed: "Speed",
    mile: "Mile",
    milebet: "Mile range",
    location: "Address",
    acc_word: "ACC状态",
    ft_zaiz: "load",
    ft_fdj: "engine",
    ft_menc: "door",
    ft_ysj: "compressor",
    moutai_lock_onStatus: "onStatus",
    moutai_lock_lockStatus: "lockStatus",

    cold_acc: "ACC State",
    cold_engineSpeed: "engine speed",
    cold_oilRate: "Remaining oil",
    cold_batteryVoltage: "Battery voltage",
    cold_waterTemp: "cold water Temp",
    cold_coolLevel: "Coolant level",
    cold_oilTemp: "engine oil temp",
    cold_weight: "load",
    cold_PTO: "PTO state",
    cold_throttleOpen: "throttle Open",
    cold_brakeState: "Brake switch status",
    cold_brakeSignal: "Engine brake activation signal",
    cold_brakeHand: "Parking brake",
    cold_lightLeft: "Signal left",
    cold_lightRight: "Signal right",
    cold_ASR: "ASR active",
    cold_EBS: "EBS switch",
    cold_curiseState: "curise State",
    cold_AEBS: "AEBS State",
    cold_LDWS: "LDWS signal",
    cold_lightDanger: "light Danger",
    cold_engineIdleOilConsume: "engine Idle Oil Consume",
    cold_engineIdleTime: "engine Idle Time",
    cold_engineRunTime: "engine Run Time",
    cold_engineOilConsume: "engine Oil Consume",
    cold_mile: "mile",
    rollerStatus: "rollerStatus",
  },
  playbackstop: {
    stopSetting: "Setup",
    showCheck: "Display Parking Spot",
    index: "#",
    start: "Start",
    time: "Duration",
    location: "Address",
    modal: {
      name: "Custom Search",
      way: "Search mode",
      wayValue: ["Search by rules", "Custom search"],
      time: "Time",
      timeTip: "Minute",
      type: "Search type",
      typeValue: ["Search by ACC", "Search by speed"],
      speed: "Speed",
    },
    message: {
      queryError: "Search error",
      noData: "No data was found",
    },
    popup: {
      time: "Time",
      continued: "Duration",
      address: "Address",
    },
  },
  playbackmatics: {
    driveTime: "Duration",
    driveMile: "Mileage",
    maxSpeed: "Max(km/h)",
    avgSpeed: "Avg(km/h)",
  },
  alarmMap: {
    name: "ALERT MAP",
    vehiclequery: "Vehicle",
    driverquery: "Driver",
    startTime: "Start",
    endTime: "End",
    alarmFilter: "Alert Filter",
    search: "Load",
    cluterName: "Scatter",
    hotName: "Heatmap",
    top: {
      name: "Tops",
      alarmTop5: "Alert type TOP5",
      driverTop5: "Driver alarm TOP5",
      vehicleTop5: "Vehicle alarm TOP5",
    },
    vehicle: {
      name: "Vehicle",
      index: "#",
      company: "Group",
      plateNo: "Plate",
      alarmType: "Alert type",
      speed: "Speed",
      alarmTime: "Time",
      location: "Address",
    },
    message: {
      noParmas: "Please select at least one driver or vehicle",
      queryError: "Search error",
      noData: "No alarm data was found",
    },
  },
  lockMap: {
    name: "LOCK TRACKING",
    index: "index",
    company: "Group",
    plateNo: "Plate",
    alarmType: "Alert Type",
    speed: "Speed",
    alarmTime: "Time",
    location: "Address",
    message: {
      noVehicle: "No such vehicle",
      hadVehicle: "The vehicle already exists",
      limitState: "The number of monitoring has reached online",
    },
  },
  lockTreePart: {
    all: "All",
    online: "Online",
    offline: "Offline",
    message: "Please select a vehicle",
  },
  lockWindow: {
    tool: {
      close: "Close window",
      line: "Open the line",
      fullScreen: "Full Screen",
      timeRadio: "Video",
      playback: "Track History",
      backLock: "Return to the vehicle perspective",
      lockCar: "Lock the vehicle",
      clear: "Clear all current alarms",
    },
    basic: {
      format_dept: "Group",
      format_gpsTime: "Time",
      format_state: "Status",
      driver_name: "Driver",
      driver_phone: "Phone",
      mile: "Mileage",
      format_dire: "Direction",
      simCode: "SIM ID",
      terminalNo: "Device ID",
      location: "Address",
    },
  },
  alarmVideo: {
    startTime: "Start",
    endTime: "End",
    fileType: "Type",
    fileList: {
      0: "Picture",
      2: "Video",
      "-1": "All",
    },
    alarmFilter: "Alert Filter",
    search: "Load",
    message: {
      noVehice: "Please select the vehicle first",
      noAlarm: "Please select at least one alarm type",
      queryError: "Search error",
      noData: "No data was found",
    },
  },
  vehicleRunTrack: {
    name: "Vehicle track",
    queryTime: "Date",
    search: "Load",
    export: "Export",
    modal: {
      name: "Speed Curve",
      title: "Vehicle Speed Curve",
      avgSpeed: "AVG Speed",
      mile: "Mileage",
      time: "Duration",
    },
    table: {
      index: "#",
      plate: "Plate",
      gpsTime: "Time",
      lng: "Longitude",
      lat: "Latitude",
      speed: "Speed",
      vsspeed: "VSS speed",
      dire: "Direction",
      acc: "ACC",
      mile: "Mileage",
      location: "Address",
    },
    message: {
      noVehice: "Please select the vehicle first",
      queryError: "Search error",
      noData: "No track data was found",
      noDataExport: "No data that can be exported is detected",
    },
    startTime: "开始时间",
    endTime: "结束时间",
    yAxisName: "速度",
  },
  ftVehicleRunTrack: {
    name: "Vehicle track",
    queryTime: "Date",
    search: "Load",
    export: "Export",
    modal: {
      name: "Speed Curve",
      title: "Vehicle Speed Curve",
      avgSpeed: "AVG Speed",
      mile: "Mileage",
      time: "Duration",
      tableTitle: "Config",
    },
    table: {
      index: "#",
      plate: "Plate",
      gpsTime: "Time",
      lng: "Longitude",
      lat: "Latitude",
      speed: "Speed",
      vsspeed: "VSS speed",
      dire: "Direction",
      acc: "ACC",
      mile: "Mileage",
      location: "Address",
      oil_consum: "Fuel consumption",
      rota_speed: "Rotate speed",
    },
    message: {
      noVehice: "Please select the vehicle first",
      queryError: "Search error",
      noData: "No track data was found",
      noDataExport: "No data that can be exported is detected",
    },
    startTime: "开始时间",
    endTime: "结束时间",
    yAxisName: "速度",
  },
  roadLimitSpeed: {
    name: "Speed limit marking map",
    chooseAll: "Select all",
    rangeFilter: "Fence Search",
    all: "Total",
    filter: "Sieve",
  },
  alarmBox: {
    name: "Messages",
    ruleSet: "Configuration Rul",
    bellState: "Toggle ringing status",
    refresh: "Refresh rules",
    close: "Close",
    alarm: "Alert",
    event: "Event",
    bussiness: "Business",
    breakdown:"Fault",
    reflshOk: "reflsh success",
  },
  alarmFilter: {
    name: "Alert Filter",
    gps: "GPS Beidou positioning system",
    adas: "ADAS anti-collision early warning system",
    dms: "DMS driver detection system",
    vadas: "VADAS vehiclebody attitude Detection system",
    vdss: "VDSS event",
    tbox_driver: "TBOX-DRIVER alarm",
    tbox_fault: "TBOX-FAULT alarm",
    business: "zha tu che alarm",
    cold: "cold alarm",
    bsd: "BSD alarm",
    unknow: "No alarm classification defined",
  },
  customFilter: {
    checkall: "Complete selection",
  },
  alarmVideoWindow: {
    name: "Event video window",
    wait: "Waiting for request",
    parmasError: "Parameter error",
    getting: "Requesting",
    getError: "Failed to get video list",
    way: "CH.",
    offline:
      "The vehicle is offline and the video cannot be viewed for the time being",
    waitBack: "Waiting for return",
    notBack: "The handle result was not returned",
    waitLine: "Queuing, current location: ",
    handle: "In handle",
    timeOut: "Timeout for getting media files",
  },
  singleState: {
    gsm: "Gsm network upload signal (determinant of data transmission stability)",
    gps: "Gps positioning signal (there may be data loss / inaccurate position when the signal is weak)",
  },
  popupAlarm: {
    company: "Company",
    alarm: "Alarm",
    speed: "Speed",
    location: "Address",
    alarmVideo: "Event video",
    alarmLink: "Alert details",
  },
  maticsMap: {
    mapChange: "Maps",
    mapTool: "Tools",
    bussiness: "Fence Tools",
    gaode: "Amap",
    gaodeSatel: "Amap Satellite",
    google: "Google",
    googleSatel: "Google Satellite",
    tianditu: "Sky map",
    tiandituSatel: "Sky Satellite",
    map_manyou: "Roaming",
    map_lkenlarge: "Zoom In",
    map_lknarrow: "Zoom Out",
    map_traffic: "Traffic Condition",
    map_ranging: "Distance Measure",
    map_paint: "Area Measure",
    map_fullScreen: "Full Screen",
    map_ssq: "regional planning",
    map_areaSearch: "Search vehicle in region",
    map_linePlan: "Path Planning",
    map_tagging: "Marking point",
    map_view: "My View",
    map_fence: "Fence",
    map_inter: "Spot",
    map_line: "bus Route",
    maproute: "route Line",
    mapgd: "construction",
    mapxnc: "accommodation",
    map_search: "Address Search",
    map_tool: "map tool",
    return_tool: "return tool",
    hide_tool: "hide",
    map_default: "tool",
    mapcz: "场站",
    setting: {
      name: "show setting",
      lukuang: "traffic",
      gongdi: "construction",
      xiaonacha: "accommodation",
      tongxingxl: "TrafficLine",
    },
  },
  mapSearchControl: {
    placeholder: "Please enter content",
    placeholderS: "Please select",
    message: "Location failedPlease select the city manually",
  },
  scoreCompare: {
    name: "Selection and comparison of vehicle selection",
    searchObj: "Search object",
    searchValue: {
      0: "Company",
      1: "Vehicle",
      2: "Driver",
    },
    startDate: "Start date",
    endDate: "End date",
    click: "Click to select",
    simpleParmas: "Same search condition detected",
    noQuery: "Please select the comparison object to search",
    queryError: "No result was found,Please select another object",
    adasAlarm100: {
      adas_fcw: "Pre-FCW collision warning",
      adas_hkm: "Total number of alerts",
      adas_hmw: "HMW vehicle distance too close warning",
      adas_ldw: "LDW lane departure warning",
      adas_lldw: "LLDW left lane departure warning",
      adas_pcw: "PCW pedestrian reminder",
      adas_pcwc: "Anterior blind area of PCW-C",
      adas_pcwl: "Left posterior blind area of PCW-L",
      adas_pcwr: "PCW-R right posterior blind area",
      adas_pcww: "PCW pedestrian forecast warning",
      adas_rldw: "RLDW right lane departure warning",
      adas_ufcw: "UFCW collision warning before low speed",
      adas_vb: "VB virtual bumper warning",
      name: "ADAS alarm (per hundred kilometers)",
    },
    dmsAlarm100: {
      dms_close_eyes: "Eyes closed ",
      dms_distract: "Fatigue",
      dms_hkm: "Total number of alerts",
      dms_smoke: "Smok",
      dms_tel: "Hand cellphone",
      dms_tired: "Distraction",
      dms_yawn: "Yawn",
      name: "DMS alarm (per hundred kilometers)",
    },
    driverScore: {
      name: "Score",
      score_day: "Daytime score",
      score_expressway: "High speed scoring",
      score_night: "Night score",
      score_normalway: "Non-high speed scoring",
      score_safe: "Safety score",
    },
    mileDetail: {
      mile_avg: "Average mileage",
      mile_day: "Daytime mileage",
      mile_expressway: "High speed mileage",
      mile_night: "Night mileage",
      mile_normalway: "Non-highway mileage",
      mile_sli: "Speeding mileage",
      mile_total: "Mileage",
      name: "Mileage details (km)",
    },
    runRoad: {
      name: "Stroke",
      trip_avg_mile: "Average mileage",
      trip_avg_time: "Average travel duration",
      trip_day: "Trip frequency (times / day)",
      trip_hkm: "Travel frequency (times / 100km)",
      trip_max_mile: "Maximum mileage",
      trip_max_time: "Maximum travel duration",
    },
    scoreType: {
      dimension_acc: "Acceleration",
      dimension_brake: "Breaks",
      dimension_fcw: "FCW",
      dimension_hmw: "Distance",
      dimension_ldw: "LDW",
      dimension_lrreguar: "Irregular driving behavior",
      dimension_pcw: "Pedestrian and non-motor vehicle collision",
      dimension_sli: "Speeding",
      dimension_tired: "Fatigue",
      dimension_turn: "Turns",
      dimension_turn_light: "Turn Light",
      name: "Dimension score (score)",
    },
    speedType: {
      name: "Speed distribution",
      speed_avg_day: "AVG Speed (during the day)",
      speed_avg_expressway: "AVG Speed (high speed)",
      speed_avg_night: "AVG Speed (night)",
      speed_avg_normalway: "AVG Speed (non-high speed)",
      speed_between_20_40: "20 ~ 40km/h",
      speed_between_40_60: "40 ~ 60km/h",
      speed_between_60_80: "60 ~ 80km/h",
      speed_between_80_100: "80 ~ 100km / h, ",
      speed_greater_100: "100km/h",
      speed_less_20: "&lt; 20km/h",
      speed_max_day: "Maximum speed (, during the day)",
      speed_max_expressway: "Maximum speed (, high speed)",
      speed_max_night: "Maximum speed (at night)",
      speed_max_normalway: "Maximum speed (non, high speed)",
    },
    timeDetail: {
      name: "Details of duration (hours)",
      time_avg: "Average duration",
      time_day: "Day duration",
      time_expressway: "High speed duration",
      time_night: "Night duration",
      time_normalway: "Non-highway duration",
      time_sli: "Speeding duration",
      time_total: "Duration",
    },
    vadasAlarm100: {
      name: "VADS alarm (per hundred kilometers)",
      vadas_acc: "Acceleration",
      vadas_brake: "Breaks",
      vadas_hkm: "Total number of alerts",
      vadas_left_turn: "Sharp Left Turn",
      vadas_left_turn_no_light: "Turn left without lights",
      vadas_right_turn: "Sharp Right Turn",
      vadas_right_turn_no_light: "Turn right without lights",
    },
    vdssAlarm100: {
      name: "VDSS vehicle status (per 100 km)",
      vdss_LLDW: "Left lane change",
      vdss_RLDW: "Right lane change",
      vdss_brake: "brake",
      vdss_left_turn_light: "Left turn signal",
      vdss_right_turn_light: "Right turn signal",
    },
  },
  compareLayout: {
    compare: "Add contrast",
    returnTop: "Return to the top",
  },
  operateMap: {
    name: "TRACK MAP",
    searchTab: "Loading",
    controlTab: "Control",
    searchDate: "Date",
    alarmFilter: "Alert Filter",
    lineMap: "line map",
    alarmMap: "alarm map",
    stopMap: "stop map",
    message: {
      noVehice: "Please choose at least one vehicle",
      queryError: "Query error",
    },
  },
  alarmSetting: {
    alarmTab: "Alert class",
    eventTab: "Event class",
    bussTab: "Business class",
    loading: "Loading configuration",
    unsetBtn: "Cancel the settin",
    setBtn: "Batch setup",
    common: {
      title: "Settin",
      index: "#",
      warnSound: "Prompt sound",
      bussType: "Business type",
      warnIngnoe: "Whether to focus",
      warnPopup: "play the box",
      warnLight: "Turn on the cue light",
      warnRepart: "Repetition times",
      warnInter: "Playback interval",
      warnClass: "Icon name",
      default: "Default",
      yes: "Yes",
      no: "No",
    },
    filter: {
      all: "All",
      gps: "GPS Beidou positioning system",
      adas: "ADAS anti-collision early warning system",
      dms: "DMS driver detection system",
      vadas: "VADAS vehicle body attitude Detection system",
      vdss: "VDSS event",
      tbox_driver: "TBOX-DRIVER alarm",
      tbox_fault: "TBOX-FAULT alarm",
      bussiness: "Truck alarm",
      cold: "Cold Chain alarm",
      business: "BUSSINESS business",
      bsd: "BSD alarm",
    },
    setting: {
      all: "All",
      yesSet: "Configured",
      noSet: "Not configured",
    },
    query: {
      name: "Alert name",
      type: "Alert type",
      message: "Keywords...",
    },
    alarmTable: {
      alarmName: "Alert name",
      alarmRemark: "Alert description",
      alarmIcon: "Alert icon",
      alarmCode: "Alert code",
      alarmType: "Alert type",
      operate: " ",
    },
    alarmModal: {
      title: "Alert details",
      name: "Alert name",
      alarmIcon: "Alert icon",
      alarmType: "Alert type",
      alarmSort: "Alert sorting",
      remark: "Alert remarks",
      alarmInter: "Alert description",
    },
    message: {
      noQuery: "Please select an object",
      queryError: "Request error",
    },
    eventTable: {
      name: "Event name",
      eventIcon: "Event icon",
      eventType: "Type",
    },
  },
  talkBack: {
    unknowStep: "未知状态",
    StepMsg: {},
    stepMsg: {
      0: "点击开始对讲",
      1: "正在请求麦克风权限",
      2: "浏览器不支持，请检查相关设置",
      3: "请求麦克风权限成功",
      4: "请求麦克风权限失败，请确认开启麦克风权限或检查音频输入设备。",
      5: "点击以连接设备",
      6: "正在连接设备",
      7: "连接设备成功,正在对讲",
      8: "连接设备失败，请重试",
      9: "设备已断开，请重新连接",
    },
  },
  alarmDetail: {
    label: {
      copyPlate: "复制车牌",
      eventType: "事件类型",
      todayCount: "今日发生次数",
      region: "区域",
      notSet: "未设置",
      maxSpeed: "最高速度",
      avgSpeed: "平均速度",
      startTime: "开始时间",
      driveDuration: "行驶时长",
      endTime: "结束时间",
      noEnd: "未结束",
      mileage: "里程",
      startLocation: "开始位置",
      speed: "车速",
      endLocation: "结束位置",
      count: "次",
      chn: "通道",
      deal: "处理",
      hasDeal: "已经处理过了",
      cancel: "取消",
      custom: "自定义",
      defaultTemplate: "默认消息模板",
      company: "归属",
      driver: "驾驶员",
    },
    messageInfo: {
      0: "复制成功",
      1: "查询轨迹失败",
      2: "下发成功",
      3: {
        0: "信息下发失败，终端",
        1: "错误code：",
        2: "不在线",
      },
      4: "处理失败",
      5: "点击复制",
    },
  },
  elementTree: {
    filterType: {
      0: "默认搜索",
      1: "车牌号",
      2: "别名",
      3: "自编号",
      4: "SIM卡号",
      5: "终端号",
      6: "驾驶员",
      7: "企业",
      8: "部门/车队",
    },
    onlyOnline: "仅显示在线",
  },
  treeMapLayer: {
    all: "all",
    choose: "choose",
    able: "effective",
    unable: "invalid",
    formal: "formal",
    temporary: "temporary",
  },
  vehicleTrackSpeed: {
    timeFrame: "时间范围:",
    overspeedDuration: "超速时长:",
    speedLimit: "限速:",
    overspeedRate: "超速率：",
    query: "查询",
    export: "导出",
    collect: "汇总",
    index: "序号",
    unit: "单位",
    plateNo: "车牌号",
    beginTime: "开始时间",
    endTime: "结束时间",
    count: "次数",
    detail: "详情",
    maxSpeed: "最大速度",
    minSpeed: "最小速度",
    avgSpeed: "平均速度",
    sliRatio: "超速率(%)",
    sli: "限速(km/h)",
    driveTime: "持续时间(分)",
    driveMile: "持续里程(km)",
    beginLocation: "开始位置",
    endLocation: "结束位置",
    chooseCar: "请选择车辆",
    queryError: "查询出错",
    noData: "没有数据可以导出",
    overspeedlist: "超速统计汇总",
    overspeeddetail: "超速统计详情",
  },
  vehicleTrip: {
    statistics: "统计",
    unit: "单位",
    cycle: "行程数",
    mile: "里程(km)",
    drivingScore: "驾驶评分",
    control: "操作",
    runningData: "轨迹回放",
    detail: "明细",
    faceImage: "人脸图片",
    workImage: "考勤图片",
    beginLocation: "开始位置",
    endLocation: "结束位置",
    drivermile: "行驶里程",
    nodetailmassage: "无明细消息",
    carmilestatistics: "车辆行程统计",
    driverdetails: "行程详情",
  },
  fenseMgnt: {
    dept: "部门",
    area: "区域",
    fenceType: "围栏类型",
    fenceValue: "围栏名称",
    fencekTip: "请输入关键字查询",
    all: "全部",
    query: "查询",
    add: "新增",
    index: "序号",
    unit: "单位",
    fenceName: "围栏名称",
    attributionArea: "归属区域",
    fenceShape: "围栏形状",
    fenceStyle: "围栏样式",
    showVertices: "是否显示顶点",
    yes: "是",
    no: "否",
    control: "操作",
    detail: "详情",
    copy: "复制新增",
    Modify: "修改",
    remove: "删除",
    append: "附加",
    REC: "矩形",
    circular: "圆形",
    NGon: "多边形",
    line: "线路",
    filecontrol: "操作失败",
    successcontrol: "操作成功",
    removefence: "此操作将永久删除次围栏,是否继续?",
    Hint: "提示",
    sure: "确定",
    cancel: "取消",
    removefile: "删除出错",
    removesuccess: "删除成功",
    choosequeryobject: "请先选择查询对象!",
    queryfile: "查询出错",
    noqueryData: "未查询到数据",
  },
  fenseProject: {
    dept: "部门",
    area: "区域",
    fenceType: "围栏类型",
    fenceValue: "围栏名称",
    fencekTip: "请输入关键字查询",
    all: "全部",
    query: "查询",
    add: "新增",
    index: "序号",
    unit: "单位",
    fenceName: "围栏名称",
    attributionArea: "归属区域",
    fenceShape: "围栏形状",
    fenceStyle: "围栏样式",
    showVertices: "是否显示顶点",
    yes: "是",
    no: "否",
    control: "操作",
    detail: "详情",
    copy: "复制新增",
    Modify: "修改",
    remove: "删除",
    append: "附加",
    REC: "矩形",
    circular: "圆形",
    NGon: "多边形",
    line: "线路",
    filecontrol: "操作失败",
    successcontrol: "操作成功",
    removefence: "此操作将永久删除次围栏,是否继续?",
    Hint: "提示",
    sure: "确定",
    cancel: "取消",
    removefile: "删除出错",
    removesuccess: "删除成功",
    choosequeryobject: "请先选择查询对象!",
    queryfile: "查询出错",
    noqueryData: "未查询到数据",
  },
  styleSelect: {
    customStyle: "自定义样式",
    default: "默认",
  },
  fenseExtand: {
    Lineadditional: "线路附加信息绑定",
    Roadspeed: "路段限速",
    index: "序号",
    COORDINATE: "经纬度",
    nature: "属性",
    control: "操作",
    Modify: "修改",
    addall: "添加全部",
    saveset: "保存配置",
    keypoint: "关键点",
    scope: "范围",
    ETA: "预定到达时间",
    ETD: "预定离开时间",
    remove: "删除",
    KeyPointPosition: "关键点位置",
    inputcontent: "请输入内容",
    KeyPointRange: "关键点范围",
    mi: "米",
    APIT: "任意时间点",
    RoadWidth: "路段宽度",
    MaxSpeed: "最高速度",
    Overspeed: "可超速阈值",
    Driverover: "行驶过长阈值",
    Underdrive: "行驶不足阈值",
    nigetMaxSpeed: "夜间最高速度",

    drawpoints: "请先画点",
    selectkeypoint: "请先选择关键点范围",
    choosetime: "请选择时间",
    pointsuccess: "取点成功",
    pleasedraw: "请画点",
    Pleasetakefirst: "请先取点",
    S: "秒",
  },
  fenseModal: {
    fensetype: "围栏类型",
    department: "归属部门",
    choosedept: "请选择部门",
    rangearea: "区域规划",
    area: "归属区域",
    choosearea: "请选择区域",
    jurisdiction: "归属辖区",
    choose: "请选择",
    fensegroup: "围栏分组",
    fenseshape: "围栏形状",
    fensewidth: "围栏宽度",
    fensestyle: "围栏样式",
    fontsize: "字体大小",
    vertices: "显示顶点",
    fensename: "围栏名称",
    save: "保存",
    close: "关闭",
    draw: "画图",
    cleardraw: "清楚重画",
    choosefensestyle: "请选择围栏样式",
    choosefensetype: "请选择围栏类型",
    choosefensegroup: "请选择围栏分组",
    choosejurisdiction: "请选择归属辖区",
    choosefenseShape: "请选择围栏形状",
    inputfensewidth: "请输入围栏宽度 / 半径",
    inputfontsize: "请输入字体大小",
    showvertices: "是否显示顶点",
    inputfensename: "请输入围栏名称",
    choosebelongarea: "请选择归属区域",
    choosebelongdept: "请选择归属部门",
    addfense: "新增围栏",
    Modifyfense: "修改围栏",
    pleacedrawfense: "请先绘制围栏",
    chooseprovince: "请选择省市区!",
    choosefensestyle1: "请选择围栏样式",
    saveAndAdd: '保存并继续添加'
  },
  fenseGroupMgnt: {
    query: "查询",
    inputgroupname: "请输入分组名称",
    add: "新增",
    index: "序号",
    groupname: "分组名称",
    department: "归属单位",
    containingtype: "包含类型",
    CreationTime: "创建时间",
    CreationUser: "创建用户",
    Notes: "备注",
    control: "操作",
    Modify: "修改",
    remove: "删除",
    addgroup: "新增分组",
    belongdept: "归属部门",
    choosedept: "请选择部门",
    activitynature: "活动性质",
    choosedepartment: "请选择归属部门",
    chooseoneactivity: "请至少选择一个活动性质",
    choosegroupquery: "请选择分组查询",
    queryfail: "查询出错",
    noData: "未查询到数据",
    ConfirmDelete: "确认解除",
    group: "分组",
    Prompt: "提示",
    ensure: "确定",
    cancel: "取消",
    controlfail: "操作出错",
    removesuccess: "删除成功",
    addsuccess: "新增成功",
  },
  limitReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    choosearea: "区域选择",
    restrictedarea: "非限行区",
    startTime: "开始时间",
    endTime: "结束时间",
    chooseDate: "选择日期",
    query: "查询",
    map: "地图",
    count: "总违规次数",
    avgMile: "平均单日里程",
    avgTime: "平均单日时长",
    avgSpeed: "平均速度",
    maxSpeed: "最高速度",
    smoke: "抽烟",
    phone: "打电话",
    tired1: "疲劳驾驶一级",
    tired2: "疲劳驾驶二级",
    distract: "注意力不集中",
    speed: "连续超速",
    pcw: "行人碰撞",
    fcw: "车辆碰撞",
    ldw: "频繁压线",
    hmw: "跟车过近",
    noPasscheck: "无通行证限行",
    peaktermLine: "高峰期限行",
    form: "表格",
    excel: "导出",
    index: "序号",
    drivingArea: "行驶区域",
    unit: "单位",
    plateNo: "车牌号",
    blackOrWhite: "黑白名单",
    ranking: "安全评分",
    onlineRate: "上线率",
    verbCount: "近期违章次数",
    notVerbCount: "未处理违章次数",
    driveMile: "行驶里程(km)",
    breakLineMile: "闯限行里程(km)",
    driveTime: "行驶时长(h)",
    breakLineTime: "闯限行时长(h)",
    faceOdd: "人脸识别异常",
    driverOdd: "驾驶员异常",
    chooseCar: "请选择车辆",
    chooseDriver: "请选择司机",
    chooseFense: "请选择围栏",
    queryfail: "查询出错",
    noData: "未查询到数据",
    noDataExcel: "没有数据可以导出",
    vehicleArea: "车辆区域违规统计",
  },
  xiaquReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    choosearea: "辖区选择",
    restrictedarea: "非辖区",
    startTime: "开始时间",
    chooseDate: "选择日期",
    endTime: "结束时间",
    query: "查询",
    map: "地图",
    count: "总违规次数",
    sumMile: "总里程(千米)",
    sumTime: "总时长(小时)",
    avgSpeed: "平均速度",
    maxSpeed: "最高速度",
    smoke: "抽烟",
    phone: "打电话",
    tired1: "疲劳驾驶一级",
    tired2: "疲劳驾驶二级",
    distract: "注意力不集中",
    speed: "连续超速",
    pcw: "行人碰撞",
    fcw: "车辆碰撞",
    ldw: "频繁压线",
    hmw: "跟车过近",
    noPasscheck: "无通行证限行",
    peaktermLine: "高峰期限行",
    form: "表格",
    excel: "导出",
    index: "序号",
    drivingArea: "行驶区域",
    unit: "单位",
    plateNo: "车牌号",
    blackOrWhite: "黑白名单",
    ranking: "安全评分",
    onlineRate: "上线率",
    verbCount: "近期违章次数",
    notVerbCount: "未处理违章次数",
    driveMile: "行驶里程(km)",
    breakLineMile: "闯限行里程(km)",
    driveTime: "行驶时长(h)",
    breakLineTime: "闯限行时长(h)",
    faceOdd: "人脸识别异常",
    driverOdd: "驾驶员异常",
    chooseCar: "请选择车辆",
    chooseDriver: "请选择司机",
    chooseFense: "请选择辖区",
    queryfail: "查询出错",
    noData: "未查询到数据",
    noDataExcel: "没有数据可以导出",
    vehicleArea: "车辆区域违规统计",
  },
  crossReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    chooseroad: "路口选择",
    unknownroad: "未知路口",
    startTime: "开始时间",
    chooseDate: "选择日期",
    endTime: "结束时间",
    wayspeed: "直行速度",
    leftspeed: "左转速度",
    rightspeed: "右转速度",
    query: "查询",
    map: "地图",
    form: "表格",
    excel: "导出",
    statistics: "统计",
    index: "序号",
    plateNo: "车牌号",
    unit: "单位",
    speed: "路口直行速度",
    left: "左转弯速度",
    right: "右转弯速度",
    sbspeed: "路口直行超速",
    sbleft: "左转弯超速",
    sbright: "右转弯超速",
    leftl: "左转未打灯",
    rightl: "右转未打灯",
    alarmCount: "总违规次数",
    thoughCount: "总通过次数",
    pingci: "频次",
    crossCount: "总违规次数/通过次数",
    alertsPass: "违规次数/通过次数",
    roadstatistics: "路口统计",
    roadname: "路口名称",
    details: "详情",
    road: "路口",
    speedtype: "违规类型",
    velocity: "速度",
    sblight: "转向灯",
    speedTime: "超速时间",
    choosecar: "请选择车辆",
    choosedriver: "请选择司机",
    choosefense: "请选择围栏",
    queryfail: "查询出错",
    noData: "未查询到数据",
    carroadspeed: "车辆路口违规统计",
    operate: "operation",
  },
  MapLineSearch: {},
  mapLineSearch: {
    startPosition: "开始位置",
    endPosition: "结束位置",
    pathType: "路径类型",
    choose: "请选择",
    speed: "速度优先(不考虑路况最快)",
    cost: "费用优先(不走收费路段,时间最短)",
    Distance: "距离优先(不考虑路况,距离最短)",
    speed1: "速度优先(不走快速路)",
    congestion: "躲避拥堵",
    analyze: "开始分析",
    DIST: "距离",
    Traffic: "路况",
    destinationMap: "请先在地图上标注好起终点!",
    routePlanningfail: "路线规划出错!",
    duration: "时长:",
  },
  mappoisearch: {
    city: "所在城市",
    provinceorcity: "请输入省市区",
    keywords: "关 键 字",
    inputkeywords: "请输入关键字",
    query: "查询",
    total: "共计",
    seeAll: "查看全部",
    result: "个结果",
    noResult: "未查询到结果",
    inputkeyquery: "输入关键字查询",
  },
  mapTagging: {
    inputkeywords: "请输入关键字",
    delete: "批量删除",
    addPoints: "新增标注点",
    pointMessage: "标记点信息",
    name: "名称:",
    inputname: "请输入名称",
    remark: "备注:",
    inputremark: "请输入备注",
    save: "保存",
    remove: "删除",
    inputpointsname: "请输入标注点名称",
    controlfail: "操作失败",
    savesuccess: "保存成功",
    chooseremovepoints: "请选需要删除的标注点(灰色为无权删除的点)",
    removepoints: "此操作将删除这些标注点,是否继续?",
    hint: "提示",
    ensure: "确定",
    cancel: "取消",
    removesuccess: "删除成功",
    checkall: "  全选",
  },
  carMgt: {
    label: {
      choosePlease: "请选择",
      plateNo: "车牌号码",
      terminalNo: "设备号",
      simCode: "SIM卡号",
      driverName: "驾驶员姓名",
      keyPlease: "请输入关键字",
      noData: "暂无数据",
      index: "序号",
      group: "单位",
      plateNoAlias: "车牌号",
      carTypes: "车辆类型",
      terminalTypes: "终端类型名",
      mainTerminalNo: "主终端手机号",
      mainSimCode: "主终端ICCID卡号",
      bindedDriver: "绑定司机",
      channelNum: "视频通道",
      load: "吨位",
      "white&blackList": "黑白名单",
      operate: "操作",
      statusSwitch: "切换状态",
      modify: "修改",
      search: "查询",
      export: "导出",
      multipleImport: "批量导入",
      add: "新增",
      whiteList: "白名单",
      blackList: "黑名单",
      unset: "未设置",
      del: "删除",
      report: "报备",
      tip: "提示",
      enable: "启用",
      disable: "停用",
      successReport: "报备成功",
    },
    expression: {
      0: "车辆已{0}",
    },
    messageInfo: {
      0: "将永久删除车辆、车辆司机绑定、准驾记录, 是否继续?",
      1: "删除成功！",
      2: "删除失败！",
      3: "确认切换状态?",
      4: "切换状态失败",
      5: "导出车辆.xlsx",
    },
  },
  carInfo: {
    label: {
      baseInfo: "基本信息",
      plateNo: "车牌号码",
      plateColor: "车牌颜色",
      choosePlease: "请选择",
      carAlias: "车辆别名",
      group: "车辆归属",
      deptChoosePlease: "请选择归属部门",
      industry: "车辆行业",
      workRoad: "行驶线路",
      roadName: "请选择或输入线路名",
      transportIndustry: "运输行业",
      carTypes: "车辆类型",
      carVIN: "车辆VIN",
      carManufacturer: "车辆厂家",
      carRegisterPlace: "车籍地",
      regionChoosePlz: "请选择所属区域",
      allowedDrivers: "允许司机",
      deptChoosePlz: "请先选择部门",
      transportGoods: "运输货物",
      carDevices: "车辆设备",
      carLoad: "车辆吨位",
      channelNum: "视频路数",
      "white&blackList": "黑白名单",
      unset: "未设置",
      blackList: "黑名单",
      whiteList: "白名单",
      carTypeCode: "车型编码",
      lightBoxCode: "灯箱号",
      terminalType: "终端类型",
      terminalTypePlz: "请选择或输入终端类型",
      terminalNo: "终端号",
      terminalNoPlz: "请输入终端号",
      simCode: "sim卡号",
      simCodePlz: "请选择或输入SIM卡",
      installer: "安装人员",
      installTime: "安装时间",
      datePlz: "选择日期",
      expireTime: "到期时间",
      nuclear_carry: "车辆容积",
      remark: "备注",
      carOperation: "车辆运营证",
      transportLicense: "许可证号",
      licenseFrom: "发证机构",
      operationStatus: "营运状态",
      validateStart: "有效期起",
      validateEnd: "有效期止",
      checkResult: "审验结果",
      attachmentPic: "图片附件",
      drivingLicense: "行驶证详情",
      owner: "所有人",
      useNature: "使用性质",
      annualExamination: "年审到期",
      insurance: "车辆保单信息",
      insuranceNO: "保单号",
      insuranceValidDate: "保险到期",
      cancel: "取消",
      addCar: "新增车辆",
      addConfirm: "确认新增",
      modifyCar: "修改车辆",
      modifyConfirm: "确认修改",
      fensePlz: "请选择围栏",
      mainTerminal: "主终端",
      videoTerminal: "视频终端",
      subTerminal: "辅助终端",
      addSuccess: "添加成功",
      modifySuccess: "修改成功",
      brand: "车辆品牌",
      terminalConfig: "终端配置",
    },
    expression: {
      0: "起：{0}",
      1: "终：{0}",
      2: "{0}公里",
    },
    messageInfo: {
      0: "请输入车牌号码",
      1: "请选择车牌颜色",
      2: "请选择所属部门",
      3: "请选择所属车辆行业",
      4: "请选择车辆部门",
      5: "请选择运输行业",
      6: "请先输入车牌号码",
      7: "至少选择到市级别！",
      8: "请选择企业或者车队（部门）级别！",
      9: "查询司机列表失败",
      10: "添加失败:",
      11: "修改失败:",
    },
  },
  FenseLineModal: {
    department: "归属部门",
    choosedept: "请选择部门",
    area: "归属区域",
    choosearea: "请选择区域",
    jurisdiction: "归属辖区",
    choose: "请选择",
    linename: "线路名称",
    roadname: "通行道路名称",
    save: "保存",
    close: "关闭",
    Cleardrawing: "清除重画",
    draw: "画图",
    choosefensetype: "请选择围栏类型",
    choosejurisdiction: "请选择归属辖区",
    choosefenseShape: "请选择围栏形状",
    inputfensewidth: "请输入围栏宽度 / 半径",
    choosefensestyle: "请选择围栏样式",
    inputfontsize: "请输入字体大小",
    showvertices: "是否显示顶点",
    inputfensename: "请输入围栏名称",
    inputroadname: "请输入路段名称",
    choosebelongarea: "请选择归属区域",
    choosebelongdept: "请选择归属部门",
    fense: "围栏",
    pleacedrawfense: "请先绘制围栏",
    chooseprovince: "请选择省市区",
  },
  fenseLineMgnt: {
    dept: "部门",
    area: "区域",
    query: "查询",
    add: "新增",
    index: "序号",
    unit: "单位",
    fenceName: "围栏名称",
    attributionArea: "归属区域",
    fenceType: "",
    fenceShape: "围栏形状",
    fenceStyle: "围栏样式",
    showVertices: "是否显示顶点",
    yes: "是",
    no: "否",
    control: "操作",
    detail: "详情",
    Modify: "修改",
    remove: "删除",
    REC: "矩形",
    circular: "圆形",
    NGon: "多边形",
    line: "线路",
    filecontrol: "操作失败",
    successcontrol: "操作成功",
    removefence: "此操作将永久删除次围栏,是否继续?",
    Hint: "提示",
    sure: "确定",
    cancel: "取消",
    removefile: "删除出错",
    removesuccess: "删除成功",
    choosequeryobject: "请先选择查询对象!",
    queryfile: "查询出错",
    noqueryData: "未查询到数据",
  },
  driverMgt: {
    label: {
      choosePlease: "请选择",
      phone: "登录手机号",
      identityNo: "身份证号",
      name: "姓名",
      keyPlease: "请输入关键字",
      noData: "暂无数据",
      index: "序号",
      group: "所属分组",
      sex: "性别",
      workNo: "工号",
      age: "年龄",
      driverLicenseValidate: "驾照有效期",
      checkStatus: "审核状态",
      bindedCar: "绑定车辆",
      "white&blackList": "黑白名单",
      operate: "操作",
      modify: "修改",
      del: "删除",
      bind: "绑定",
      search: "查询",
      add: "新增",
      male: "男",
      female: "女",
      checkUnpass: "审核未通过",
      uncheck: "未审核",
      checkSuccess: "审核通过",
      checkResign: "已离职",
      completed: "已完成",
      deleted: "已删除",
      whiteList: "白名单",
      blackList: "黑名单",
      unset: "未设置",
      tip: "提示",
      confirm: "确定",
      cancel: "取消",
      delSuccess: "删除成功",
    },
    messageInfo: {
      0: "此操作不可恢复，确认删除司机?",
    },
  },
  driverInfo: {
    label: {
      driverDetails: "驾驶员详情",
      phone: "手机号码",
      phonePlz: "请输入手机号",
      group: "归属",
      deptChoosePlease: "请选择归属部门",
      name: "姓名",
      workNo: "司机工号",
      checkStatus: "审核状态",
      choosePlease: "请选择",
      photo: "照片",
      identifyPhoto: "人脸识别头像",
      "white&blackList": "黑白名单",
      unset: "未设置",
      blackList: "黑名单",
      whiteList: "白名单",
      remark: "备注",
      identifyNo: "身份证号码",
      identifyPic: "身份证照片",
      awardDate: "发证日期",
      selectDatePlz: "选择日期",
      validateStart: "有效期起",
      validateEnd: "有效期止",
      valid: "有效",
      invalid: "失效",
      allowedCarType: "准驾车型",
      licenseFrom: "发证机构",
      extraInfo: "额外信息",
      profession: "职业",
      birthDate: "生日",
      address: "住址",
      sex: "性别",
      age: "年龄",
      male: "男",
      female: "女",
      cancel: "取消",
      addDriver: "新增司机",
      addConfirm: "确认新增",
      modifyDriver: "修改司机",
      modifyConfirm: "确认修改",
      checkUnpass: "审核未通过",
      uncheck: "未审核",
      checkSuccess: "审核通过",
      checkResign: "已离职",
      namePlz: "请输入姓名",
      addSuccess: "添加成功",
      modifySuccess: "修改成功",
      workLicense: "从业资格证",
      licenseStatus: "证照状态",
      licenseOrganization: "签发机关",
      licensePicLeft: "证件照片（左页）",
      licensePicRight: "证件照片（右页）",
      driverLicense: "驾驶证",
      driverLicenseNo: "驾驶证号",
      driverLicenseAwardDate: "初次领证日期",
      driverLicensePics: "驾驶证照片",
      workStartDate: "入职时间",
      workEndDate: "离职时间",
    },
    messageInfo: {
      0: "请输入司机手机号码",
      1: "请选择所属部门",
      2: "请输入身份证号码",
      3: "请先输入姓名",
      4: "请选择企业或者车队（部门）级别！",
      5: "添加司机信息成功, 暂未添加FaceID信息, 您可以稍后进行修改操作",
      6: "修改司机信息成功, FaceID信息暂未修改, 您可以稍后修改该项内容",
    },
  },
  carBind: {
    label: {
      carBind: "车辆绑定",
      currentBinded: "当前绑定：",
      unbound: "未绑定",
      unbind: "解除绑定",
      changeBinding: "更改绑定：",
      confirm: "确认绑定",
      cancel: "取消",
    },
    messageInfo: {
      0: "车辆绑定成功",
      1: "车辆绑定失败",
      2: "车辆解绑成功",
      3: "车辆解绑失败",
    },
  },
  SelectTreeInput: {
    label: {
      confirm: "确定",
      clear: "清除选中",
      vehicle: "辆",
    },
    expression: {
      0: "已选择 {0} {1}",
    },
  },
  imageUploaderGroup: {
    label: {
      disabled: "已禁用",
    },
  },
  imageUploader: {
    label: {
      disabled: "已禁用",
    },
    expression: {
      0: "点击选择文件\r\n（{0}M以内）",
      1: "正在上传，已完成{0}%",
    },
    messageInfo: {
      0: "上传失败，请重试!",
      1: "上传文件失败",
      2: "上传文件超过大小限制",
    },
  },
  driverBind: {
    label: {
      bindDriver: "绑定驾驶员",
      plateNo: "车牌号码",
      group: "归属部门",
      currentBinded: "当前绑定",
      noData: "无",
      unbindedTime: "绑定时间",
      unbound: "未绑定",
      changeBinding: "更改绑定",
      driverPlz: "请先选择新驾驶员",
      unbind: "解绑",
      change: "更改",
      cancel: "取消",
    },
  },
  multipleImport: {
    label: {
      multipleImport: "批量导入",
      noData: "暂无数据",
      index: "序号",
      group: "单位",
      plateNoAlias: "车牌号",
      plateColor: "车牌颜色",
      terminalTypes: "终端类型名",
      mainTerminalNo: "主终端设备号",
      mainSimCode: "主终端SIM卡号",
      status: "状态",
      operate: "操作",
      chooseFilePlz: "选择文件",
      confirm: "确认导入",
      exportBaseTemplate: "导出车辆范本",
      clear: "清空",
      export: "导出",
      waitForImport: "待导入",
      importSuccess: "导入成功",
      importFailure: "导入失败",
      parseFailure: "解析失败",
      systemError: "系统错误",
      carData: "车辆数据",
      transIndustry: "运输行业",
      vehicleIndustry: "车辆行业",
      vehicleType: "车辆类型",
      vin: "车辆VIN",
      channelCount: "视频通道数(n)",
    },
    expression: {
      0: "共{0}条数据",
      1: "解析成功，共解析{0}条数据",
    },
    messageInfo: {
      0: "没有需要导入的数据",
      1: "导入车辆_范本",
      2: "没有可以导出的数据",
    },
  },
  operatingReport: {
    label: {
      areaPlz: "请选择区域",
      to: "至",
      m3: "方",
      index: "序号",
      isRegular: "性质",
      area: "所属区域",
      vehicle: "运输车辆数",
      search: "查询",
      export: "导出",
      last7days: "近七天",
      thisWeek: "本周",
      lastWeek: "上周",
      thisMonth: "本月",
      lastMonth: "上月",
      totalWork: "工地总数",
      totalAbsorption: "消纳场总数",
      validWork: "有效工地数",
      operatingWork: "作业工地数",
      operatingAbsorption: "作业消纳场",
      out: "出土",
      in: "消纳",
      workSite: "工地",
      absorption: "消纳场",
    },
    expression: {
      0: "总{0}量",
      1: "合规{0}量",
      2: "最大{0}量",
      3: "平均{0}量",
      4: "{0}名称",
      5: "{0}次数",
      6: "{0}量（方）",
      7: "{0}天数",
      8: "最近{0}时间",
      9: "{0}详情",
    },
    messageInfo: {
      0: "有效消纳场数",
      1: "疑似黑工地/消纳场数",
    },
    StationOperationReport: "场站作业报表",
  },
};
