/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-11-14 10:26:17
 * @LastEditors: yezy
 * @LastEditTime: 2019-11-14 10:26:17
 * @Description: 调用方法
 *  $t('common.test') 返回 common.test
 *  在name为dangerMonitor的组件中调用 $ct('label.today')会返回 dangerMonitor.label.today
 *  也可以在name为xxx的组件中设置
 *    export default {
 *          name: 'playback1078',
 *          _i18Name = 'aaaa'
 *    }
 *  这时候调用 $ct('label.today')会返回 aaaa.label.today
 *  node 有引用所以采取了 CommonJs 规范
 *
 *
 *  修改时可以采用辅助工具 /脚本工具/修改zh-CN.js自动影响其他语言文件.js 进行修改
 */
exports.default = {
  common: {
    chnName: "通道",
    index: "序号",
    startTime: "开始时间",
    endTime: "结束时间",
    query: "查询",
    location: "位置",
    export: "导出",
    plateNo: "车牌号",
    driver: "驾驶员",
    no: "无",
    to: "至",
    control: "操作",
    chooseCar: "请选择车辆",
    queryError: "查询出错",
    noData: "没有数据可以导出",
    nomassage: "未查询到消息",
    noexcelData: "不支持excel导出",
    details: "详情",
  },
  dangerEvent: {
    label: {
      today: "当日监控",
      history: "历史事件",
      allEvent: "全部事件",
      unread: "未读",
      read: "已读",
      overdue: "延时未处理",
      typeScreening: "类型筛选",
      batchProcessing: "批量处理",
      index: "序号",
      group: "单位",
      plateNo: "车牌号码",
      startTime: "开始时间",
      endTime: "结束时间",
      startLocation: "开始位置",
      endLocation: "结束位置",
      dealType: "处理方式",
      dealUser: "处理人",
      remark: "备注",
      control: "操作",
      detail: "明细",
      vehicleSelect: "车辆选择",
      eventSelect: "事件选择",
      all: "全部",
      status: "状态",
      manualProcessing: "人工处理",
      automaticProcessing: "自动处理",
      unProcessing: "未处理",
      query: "查询",
      confirm: "确认",
      close: "关闭",
      none: "无数据",
      region: "区域",
      regionFilter: "区域选择",
      regionNoFilter: "不限",
      regionNoRestriction: "非限行区",
      overdueTip: "超过3小时未处理",
      eventType: "事件类型",
      dealStatus: "处理状态",
      duration: "持续时间",
      maxSpeed: "最高车速",
      avgSpeed: "平均车速",
      export: "导出",
      filterExport: "筛选导出",
      hour: "时",
      minute: "分",
      second: "秒",
    },
    reportName: "报警事件报表",
    messageInfo: {
      0: "至少选择一个事件",
      1: "开始时间不能大于结束时间",
      2: "时间间隔不能大于一周",
      3: "没有选中的车辆",
      4: "没有可以导出的数据",
      5: "记录大于10000条，仅导出最近的10000条数据",
      6: "跳转参数错误",
    },
  },
  video1078: {
    realTime: "实时视频",
    playback: "录像回放",
  },
  realTime1078: {
    currentSelect: "当前选择车辆",
    none: "无",
    stopAll: "停止播放全部窗口",
    talkBack: "对讲",
    refresh: "刷新",
    switchTo: "切换",
    windowMode: "窗口模式",
    stopPlay: "秒后停止播放",
    will: "将于",
    play: "播放",
    all: "全部",
    stop: "停止",
    clearSelected: "清空已选择",
    messageInfo: ["不能添加相同的视频通道，已忽略"],
  },
  playback1078: {
    startTime: "开始时间",
    endTime: "结束时间",
    date: "日期",
    selectDate: "选择日期",
    recordPosition: "录像位置",
    stopAll: "全部停止",
    playAll: "全部播放",
    multipleMode: "多通道模式",
    refresh: "刷新",
    timeAxis: "时间轴",
    downloadList: "服务器存储列表",
    recordList: "录像列表",
    searching: "正在查询",
    noResult: "查询无结果",
    index: "序号",
    chn: "通道",
    timeLength: "时长",
    fileSize: "文件大小",
    fileName: "名称",
    state: "状态",
    loading: "正在加载",
    download: "上传至服务器",
    currentChn: "当前通道",
    progress: "进度",
    clickHint: "点击获取进度信息",
    clickHint2: "正在获取进度信息",
    clickHint3: "正在上传（ftp)",
    clickHint4: "ftp方式无进度信息",
    mode: "模式",
    clickForDownload: "点击下载视频",
    cancelDownload: "取消下载",
    reUpload: "重新上传",
    clickForUpload: "点击请求上传",
    control: "操作",
    play: "播放视频",
    fileState: {
      0: "正在上传",
      4: "上传完成",
      1: "上传失败",
      3: "待上传至服务器",
    },
    messageInfo: [
      "该车辆不支持视频相关服务，请刷新页面或咨询运维",
      "请选择车辆",
      "获取录像列表超时",
      "获取录像列表状态超时",
      "请求上传失败",
      "开始从设备上传视频",
      "请选择车辆",
      "该时间段内没有可用的录像！",
      "暂不支持播放多段视频，为您播放选区的第一部分！",
      "暂不支持下载多段视频，为您下载选区的第一部分！",
      "FTP模式将会保证音视频最佳的清晰度，但由于格式原因，无法在平台播放，需将视频下载到本地通过其他影音软件播放（使用FTP方式下载视频，需要终端支持）",
    ],
  },
  videoView: {
    checkSimFlow: "检查流量信息",
    simFlowOver: "流量超出使用",
    messageInfo: "该车辆不支持视频相关服务，请刷新页面或咨询运维",
  },
  videoPlayerPony: {
    play: "播放",
    screenShot: "截图",
    pause: "暂停",
    stop: "停止",
    notSupportAudio: "该设备不支持音频输出",
    notSupportVideo: "该设备不支持视频功能",
    tts: "tts语音下发",
    talkBack: "语音对讲",
    exit: "退出",
    fullscreen: "全屏",
    setting: "设置",
    ttsTip: "请输入下发文字",
    messageInfo: [
      "信息下发失败，终端可能不在线，请重试！",
      "下发成功",
      "获取视频地址失败",
    ],
    tip: ["视频将在", "秒后自动断开以节省流量"],
  },
  scoreMonitor: {
    vehicleList: "车辆列表",
    companyList: "企业列表",
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    export: "导出",
    byDay: "按日显示",
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      detail: "详情",
      driveTime: "运行时长",
      driveMile: "里程",
      deptName: "车队名",
      date: "日期",
      eventCount: "报警事件数量",

      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      crossTseScore: "路口超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      nodriverScore: '无司机',
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
    },
    reportName: "评分监控历史",
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
  },
  car: {
    vehicleList: "车辆列表",
    companyList: "企业列表",
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    export: "导出",
    byDay: "按日显示",
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      rank:'行业内排名',
      detail: "详情",
      driveTime: "时长(h)",
      driveMile: "里程(km)",
      deptName: "车队名",
      driverName: "驾驶员",
      date: "日期",
      eventCount: "报警数量",

      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      crossTseScore: "路口超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      nodriverScore: '无司机',
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossLeftTseScore: "路口左转弯超速",
      crossRightTseScore: "路口右转弯超速",
      crossStrightTseScore: "路口直行超速",
      policeScoreName: "交警评分",
      dayExScore: "日间高速评分",
      dayNoExScore: "日间非高速评分",
      nightExScore: "夜间高速评分",
      nightNoExScore: " 夜间非高速评分",
    },
    reportName: "评分监控历史",
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
  },
  enterprise: {
    monitor: "实时评分",
    history: "历史评分",
    query: "查询",
    queryTip: "查询时自动过滤未行驶的车辆",
    export: "导出",
    byDay: "按日显示",
    label: {
      index: "序号",
      plateNo: "车牌号",
      totalScore: "总分",
      detail: "详情",
      driveTime: "运行时长(h)",
      driveMile: "里程(km)",
      deptName: "车队名",
      date: "日期",
      eventCount: "报警数量",

      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      crossTseScore: "路口超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      nodriverScore: '无司机',
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossLeftTseScore: "路口左转弯超速",
      crossRightTseScore: "路口右转弯超速",
      crossStrightTseScore: "路口直行超速",
      policeScoreName: "交警评分",
      dayExScore: "日间高速评分",
      dayNoExScore: "日间非高速评分",
      nightExScore: "夜间高速评分",
      nightNoExScore: " 夜间非高速评分",
    },
    reportName: "评分监控历史",
    messageInfo: {
      0: "未选中车辆",
      1: "未选中企业",
      2: "查询数据出错，请重试",
      3: "系统错误",
      4: "没有可以导出的数据",
    },
  },
  scoreDetail: {
    title: "车辆评分详情",
    plateNo: "车牌号",
    updateDate: "更新时间",
    report: {
      safeScore: "安全评分",
      dimensionScore: "维度评分",
      driveScore: "驾驶评分",
      valueUnit: "分",
      chartTitle: "评分",
    },
    unit: {
      driveTime: "km",
      driverMile: "h",
      maxSpeed: "km/h",
      averageSpeed: "km/h",
      eventExpresswayScore: "分",
      eventUnExpresswaySafeScore: "分",
      eventCount: "次",
      eventDaySafeScore: "分",
      eventNightSafeScore: "分",
    },
    label: {
      driveTime: "时长",
      driveMile: "里程",
      maxSpeed: "最高车速",
      averageSpeed: "平均车速",
      eventExpresswayScore: "高速评分",
      eventUnExpresswaySafeScore: "非高速评分",
      eventCount: "总事件",
      eventDaySafeScore: "日间评分",
      eventNightSafeScore: "夜间评分",

      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
      smokeScore: "抽烟风险",
      distractScore: "注意力不集中",
      nodriverScore: '无司机',
      lrreguarDriveScore: "不规范驾驶",
      speedNoAdasScore: "速度控制",
      telScore: "打电话评分",
      tiredNoAdasScore: "疲劳驾驶评分",
      tiredScore: "疲劳驾驶评分",
      crossTseScore: "路口超速",

      crossNolightScore: "路口未打灯",
      peaktermLineScore: "高峰期闯限行",
      noPasscheckScore: "无通行证闯限行",
      workOvertimeScore: "工作超时评分",
      crossLeftTseScore: "路口左转弯超速",
      crossRightTseScore: "路口右转弯超速",
      crossStrightTseScore: "路口直行超速",
      policeScore: "交警评分",
    },
    hint: {
      accScore: ["危险，注意急加速", "注意急加速", "继续保持", "继续保持"],
      brakeScore: ["危险，注意急减速", "注意急减速", "继续保持", "继续保持"],
      fcwScore: ["危险，注意追尾碰撞", "注意追尾碰撞", "继续保持", "继续保持"],
      hmwScore: ["危险，注意车距控制", "注意车距控制", "继续保持", "继续保持"],
      laneKeepScore: [
        "危险，注意车道保持",
        "注意车道保持",
        "继续保持",
        "继续保持",
      ],
      pcwScore: [
        "危险，注意行人和非机动车",
        "注意行人和非机动车碰撞",
        "继续保持",
        "继续保持",
      ],
      speedScore: ["危险，注意车速", "注意车速", "继续保持", "继续保持"],
      turnLightScore: [
        "危险，注意转向灯使用",
        "注意转向灯使用",
        "继续保持",
        "继续保持",
      ],
      turnScore: ["危险，注意转弯", "注意转弯", "继续保持", "继续保持"],
      smokeScore: ["危险，请勿抽烟", "请勿吸烟", "继续保持", "继续保持"],
      distractScore: [
        "危险，注意集中精神",
        "注意集中精神",
        "继续保持",
        "继续保持",
      ],
      lrreguarDriveScore: [
        "危险，规范驾驶",
        "请规范驾驶",
        "继续保持",
        "继续保持",
      ],
      speedNoAdasScore: [
        "危险，保持车速",
        "请保持车速",
        "继续保持",
        "继续保持",
      ],
      telScore: ["危险，请勿打电话", "请勿打电话", "继续保持", "继续保持"],
      tiredNoAdasScore: [
        "危险，注意休息",
        "请注意休息",
        "继续保持",
        "继续保持",
      ],
      tiredScore: ["危险，注意休息", "请注意休息", "继续保持", "继续保持"],
      crossTseScore: ["危险，注意车速", "请注意车速", "继续保持", "继续保持"],

      crossNolightScore: [
        "危险，注意路口打灯",
        "请注意打灯",
        "继续保持",
        "继续保持",
      ],
      peaktermLineScore: [
        "危险，注意高峰期限行",
        "请注意高峰期限行",
        "继续保持",
        "继续保持",
      ],
      noPasscheckScore: [
        "注意通行证可用",
        "请注意通行证",
        "继续保持",
        "继续保持",
      ],
      workOvertimeScore: [
        "危险，注意作业超时",
        "请注意作业超时",
        "继续保持",
        "继续保持",
      ],
      crossLeftTseScore: [
        "危险，保持车速",
        "请保持车速",
        "继续保持",
        "继续保持",
      ],
      crossRightTseScore: [
        "危险，保持车速",
        "请保持车速",
        "继续保持",
        "继续保持",
      ],
      crossStrightTseScore: [
        "危险，保持车速",
        "请保持车速",
        "继续保持",
        "继续保持",
      ],
      policeScore: [
        "注意，请遵守交通法律法规",
        "请遵守交通法律法规",
        "继续保持",
        "继续保持",
      ],
      nodriverScore: [
        "注意，请遵守交通法律法规",
        "请遵守交通法律法规",
        "继续保持",
        "继续保持",
      ],
    },
  },
  alarmDetailSearch: {
    label: {
      tabVehicle: "车辆选择",
      tabAlarm: "报警选择",
      vehicleList: "车辆列表",
      driverList: "驾驶员列表",
      index: "序号",
      company: "归属",
      plateNo: "车牌号",
      driver: "驾驶员",
      alarmTime: "报警时间",
      alarmType: "报警类型",
      alarmLevel: "报警级别",

      speed: "车速",
      alarmLocation: "报警位置",
      video: "视频",
    },
    startTime: "开始时间",
    endTime: "结束时间",
    minSpeed: "最小速度",
    maxSpeed: "最大速度",
    query: "查询",
    export: "导出",
    hint: "提示:最新的报警视频请在5分钟之后查看,非重点报警视频不会上报",
    videoHint: "请在两分钟之后查看",

    messageInfo: {
      0: "请选择车辆或驾驶员",
      1: "数据过多，仅导出最新10000条数据",
      2: "查询错误",
      3: "跳转参数错误",
    },
    reportName: "报警明细查询数据",
  },
  riskDimension: {
    hint: {
      selectVehicle: "请选择车辆",
      selectStartTime: "选择开始日期",
      selectEndTime: "选择结束日期",
    },
    label: {
      time: "时间",
      query: "查询",
      export: "导出",
      index: "序号",
      plateNo: "车牌号",
      dataDate: "数据时间",
      totalScore: "总分",
      totalTime: "时长",
      driveMile: "里程",
      accScore: "急加速",
      brakeScore: "急减速",
      fcwScore: "追尾风险",
      hmwScore: "车距控制",
      laneKeepScore: "车道保持",
      pcwScore: "行人和非机动车碰撞",
      speedScore: "超速",
      turnLightScore: "转向灯使用",
      turnScore: "急转弯",
    },
    messageInfo: {
      0: "没有选中的车辆",
      1: "查询数据出错，请重试",
    },
    reportName: "风险维度报表",
  },
  riskFactorReport: {
    hint: {
      selectVehicle: "请选择车辆",
      selectStartTime: "选择开始日期",
      selectEndTime: "选择结束日期",
    },
    label: {
      time: "时间",
      query: "查询",
      export: "导出",
      index: "序号",
      plateNo: "车牌号",
      driveMile: "里程",
      total: "风险系数",

      ufcw100: "UFCW/100km",
      ldw100: "LDW/100km",
      pcw100: "PCW/100km",
      hmw100: "HMW/100km",
      sli100: "SLI/100km",
      fcw100: "FCW/100km",
      distract: "分心驾驶/100km",
      acc100: "加速预警/100km",
      brake100: "急刹预警/100km",
      flc100: "快速变道/100km",
      shield100: "盲区报警/100km",
      gpssli100: "gps超速/100km",
      tsr100: "转向超速/100km",
      tb100: "转弯急刹/100km",
      ta100: "转弯加速/100km",
      ntl100: "转弯未打灯/100km",
    },
    messageInfo: {
      0: "没有选中的车辆",
      1: "查询数据出错，请重试",
    },
    reportName: "风险系数报表",
  },
  userCenter: {
    title: "用户信息",
    label: {
      currentUser: "当前用户",
      realName: "真实姓名",
      createTime: "创建时间",
      telephone: "固定电话",
      deptName: "所在部门",
      mobile: "联系电话",
      validate: "到期日期",
      fax: "用户传真",
      chapter: "电子章",
      userCenter: "个人中心",
      passMgt: "密码管理",
      password: "原密码",
      newPassword: "新密码",
      confirmPW: "确认新密码",
      confirm: "修改",
      close: "关闭",
      other: "其他",
      num: "每页显示条数",
    },
    messageInfo: {
      0: "请输入密码",
      1: "请再次输入密码",
      2: "两次输入密码不一致!",
      3: "请输入数字",
    },
  },
  messageBox: {
    title: "消息管理",
    label: {
      all: "全部",
      read: "已读",
      unread: "未读",
    },
    messageInfo: {
      0: "暂无消息",
    },
  },
  messageSend: {
    title: "消息下发",
    label: {
      web: "web端",
      miniProgram: "小程序",
      preset: "快捷生成",
      msgTitle: "消息标题",
      msgType: "消息类型",
      msgContent: "消息内容",
      send: "发送",
    },
    presetType: ["自定义格式", "更新提醒", "欢迎使用"],
    msgType: ["提示", "警告", "信息", "报警", "更新"],
    messageInfo: {
      0: "请输入标题",
      1: "请输入内容",
      2: "请先选择要推送的用户！",
      3: "推送成功",
      4: "请先选择要推送的司机！",
    },
  },
  contactUs: {
    title: "联系方式",
    label: {
      cancel: "取消",
      sure: "确定",
    },
    presetType: ["自定义格式", "更新提醒", "欢迎使用"],
    msgType: ["提示", "警告", "信息", "报警", "更新"],
    messageInfo: {
      0: "请输入标题",
      1: "请输入内容",
      2: "请先选择要推送的用户！",
      3: "设置成功",
    },
  },
  home: {
    label: {
      userCenter: "个人中心",
      msgSend: "消息推送",
      signOut: "退出登录",
    },
  },
  tabMenu: {
    home: "首页",
    closeSelf: "关闭本页面",
    closeOther: "关闭其他",
    closeAll: "关闭所有",
  },
  loginForm: {
    nameMsg: "请输入用户名",
    passMsg: "请输入密码",
    rememberMsg: "记住密码",
    forgetMsg: "忘记密码?",
    loginMsg: "登录",
    tipStartMsg: "请拨打",
    tipEndMsg:
      "我们内部人员稍后将为您重置密码,使用新密码登录平台后请修改密码 ,谢谢!祝您生活愉快~",
    knowMsg: "谢谢我知道了",
    errorState: {
      "-1": "用户名密码错误",
      "-2": "用户过期",
      "-3": "无此用户",
      "-4": "不允许登录",
      "-5": "系统内部错误",
    },
  },
  TerminalErrorSearch: {
    name: "车辆故障详情",
    alarmFilter: "报警筛选",
    search: "查询",
    export: "导出",
    index: "序号",
    group: "分组",
    plateNo: "车牌号",
    terminalNo: "终端号",
    errorName: "故障名称",
    detail: "详情",
    message: {
      noVehicle: "请选择车辆",
      queryError: "查询出错",
      noData: "未查询到数据",
      noDataExport: "没有数据可以导出",
    },
  },
  ztreeMatics: {
    placeholder: "请输入关键字",
    message: {
      noData: "未查询到树形数据",
      current: "请选择指定级别",
    },
    select: [
      "默认搜索",
      "车牌号",
      "别名",
      "自编号",
      "SIM卡号",
      "终端号",
      "驾驶员",
      "企业",
      "部门/车队",
      "终端ID",
    ],
  },
  ztreeState: {
    all: "全部",
    online: "在线",
    offline: "离线",
  },

  monitor: {
    name: "实时监控",
    vehicleTab: "车辆列表",
    alarmTab: "报警列表",
    eventTab: "事件列表",
    maticsTab: "统计信息",
    lockline: "锁定跟踪",
    onlineVideo: "在线视频",
    playback: "轨迹回放",
    command: "指令下发",
    more: "更多",
    extanddetail: "附加",
    collect: "收藏",
    filterVehicle: "区域查车",
    vehicleTree: "车辆",
    gongdiTree: "工地",
    xncTree: "消纳场",
    txxlTree: "通行线路",
    projectTree: "工程项目",
    changezhan: "场站",
    setting: {
      title: "表格显示配置",
      formTitle: ["表格字段", "当前显示"],
      returnDefault: "恢复默认",
      close: "关闭",
      commit: "确认",
      tabList: ["表格显示配置", "地图弹框配置", "其他设置", "地图车辆状态配置"],
      cluster: {
        message1: "聚合范围 单位(像素px) 越大聚合范围越大 0代表不聚合",
        message2: "不聚合",
        message3: "最大聚合",
      },
      message: {
        operateOk: "操作成功",
      },
    },
    table: {
      tableName: "车辆实时状态列表",
      index: "序号",
      operation: "操作",
      format_dept: "企业车队",
      plate: "车牌号",
      format_state: "状态",
      state: "状态(icon)",
      offlineTime: "离线时长",
      gpsSpeed: "GPS速度",
      format_dire: "方向",
      format_acc: "ACC",
      format_gpsTime: "定位时间",
      offline_time: "离线时长",
      pulseSpeed: "VSS速度",
      latlng: "经纬度",
      location: "地址",
      driver_name: "司机",
      driver_phone: "司机电话",
      driver_idcardno: "司机身份证号",
      vin: "VIN码",
      vehicleType: "车型",
      transType: "行业",
      simCode: "SIM卡号",
      terminalTypeName: "终端类型",
      positioned: "定位",
      deviceId: "终端ID",
      deviceImei: "IMEI号码",
      terminalNo: "终端号",
      mile: "里程",
      color: "车牌颜色",
      height: "海拔",
      video_name: "视频设备",
      muck_nuclear_carry: "载重状态",
      working_time: "作业时长",
      muck_cloth_cover: "篷布状态",
      muck_hopper_lift: "举升状态",
      muck_status_rotation: "罐体转向",
      moutai_door_magnetism: "门磁",
      coldchain_job_status: '车辆作业状态',
      jianhua56_isbill: '运单',
      jianhua56_isload: '载重(物流)',
      unloadVSload:'运输台数',
      ftt_doorstate: "门磁",
      ftt_ysj: "冷机",
      ftt_tempdesc: "温度",
      ftt_zaiz: "载重(福田)",
      coldchain_rota_speed: "转速",
      coldchain_oil_consum: "油耗",
      fuelEconomy: "平均燃油经济性",
      moutai_lock_onStatus: "锁开关状态",
      moutai_lock_lockStatus: "施解封状态",
      moutai_lock_power: "电压",
      moutai_lock_brokenStatus: "防拆开关状态",
      moutai_lock_batteryPercent: "电量",
      moutai_lock_chargeState: "充电状态",
      moutai_lock_motionState: "运动传感器状态",

      moutai_lock_password: "电子锁密码",
      basicInfo: "基础信息",
      gpsInfo: "定位信息",
      statisticsInfo: "统计信息",
      vehicleStaus: "车身状态",
      lockStatus: "电子锁状态",
      rollerState: "滚筒状态",
      dayMile: "日里程",
      popupAlarm: "报警",
    },
    message: {
      neverOnline: "该车辆从未上线",
      lockError: "车辆定位异常",
      rangeNoVehicle: "未筛选到车辆",
      noDataExcel: "没有数据可以导出",
      collectOk: "收藏成功",
      canlCollectOk: "取消收藏成功",
    },
  },
  monitorAlarm: {
    name: "实时报警明细",
    index: "序号",
    company: "企业车队",
    plate: "车牌号",
    alarmTime: "报警时间",
    alarmType: "报警类型",
    speed: "速度",
    location: "位置",
    message: "报警定位异常",
    maxSpeed: "最大速度",
    minSpeed: "最小速度",
    maxShow: "最多显示",
  },
  monitorEvent: {
    name: "实时报警事件",
    index: "序号",
    company: "企业车队",
    plate: "车牌号",
    eventType: "事件类型",
    startTime: "开始时间",
    endTime: "结束时间",
    location: "位置",
  },
  monitorStatics: {
    onlineT: "今日上线",
    yesterday: "昨日",
    runMile: "行驶里程",
    runTime: "行驶时长",
    runAlarm: "百公里报警",
    tip: {
      1: "车",
      2: "次",
      3: "万",
    },
  },
  monitorTree: {
    all: "全部",
    online: "在线",
    offline: "离线",
    deptNumber: "部门总数",
    checkDept: "选中部门",
  },
  monitorMatics: {
    all: "全部",
    checked: "选中",
    online: "在线",
    offline: "离线",
    working: "作业",
  },
  monitorPopup: {
    lockline: "锁定跟踪",
    onlineVideo: "在线视频",
    playback: "轨迹回放",
    command: "指令下发",
    lock: "锁定",
    format_dept: "部门",
    driver_name: "司机",
    driver_phone: "电话",
    gpsSpeed: "速度",
    format_acc: "ACC状态",
    mile: "里程",
    terminalNo: "设备",
    location: "位置",
    vehicleType: "车型",
    transType: "行业",
    format_dire: "方向",
    muck_nuclear_carry: "载重",
    muck_cloth_cover: "篷布",
    muck_hopper_lift: "举升",
    // muck_status_rotation: '罐体',
    height: "海拔",
    simCode: "SIM卡",
    terminalTypeName: "终端类型",
    positioned: "定位",
    working_time: "时长",
    more: "更多",
    extanddetail: "附加",
    collect: "收藏",
    ftt_doorstate: "门磁",
    ftt_ysj: "冷机",
    ftt_tempdesc: "温度",
    ftt_zaiz: "载重",
    moutai_lock_onStatus: "锁开关状态",
    moutai_lock_lockStatus: "施解封状态",
    moutai_lock_power: "电压",
    moutai_lock_brokenStatus: "防拆开关状态",
    moutai_lock_password: "电子锁密码",
    moutai_door_magnetism: "门磁",
    moutai_lock_batteryPercent: "电量",
      moutai_lock_chargeState: "充电状态",
      moutai_lock_motionState: "运动传感器状态",
    rollerState: "滚筒状态",
    dayMile: "日里程",
    popupAlarm: "报警",
    coldchain_job_status: '作业状态',
    unloadVSload:'运输台数',
    jianhua56_isbill: '运单',
    jianhua56_isload: '载重(物流)',
  },
  monitorBasic: {
    title: {
      driver_info: "平台司机信息",
      trans_info: "运营信息",
      signal_status: "车辆信号状态位",
      tire_status: "胎压",
      satelliteStatus: "定位方式",
      door_status: "门状态",
      onLine_driver_info: "设备司机信息",
    },
    basic: {
      color: "车牌颜色",
      format_acc: "ACC状态",
      format_dept: "企业部门",
      format_state: "车辆状态",
      gpsSpeed: "速度",
      location: "位置",
      mile: "里程",
      simCode: "SIM卡号",
      terminalTypeName: "终端类型",
      positioned: "定位",
      deviceId: "终端ID",
      deviceImei: "IMEI号码",
      terminalNo: "设备号码",
      transType: "行业",
      vehicleType: "车型",
    },
    driver: {
      driver_idcardno: "身份证号",
      driver_issDate: "发证日期",
      driver_issOrg: "发证机构",
      driver_licNum: "从业资格证",
      driver_licState: "证照状态",
      driver_name: "当前司机",
      driver_staffNo: "司机工号",
      driver_phone: "司机手机号",
    },
    onLineDriver: {
      on_driver_name: "驾驶员姓名",
      on_driver_idcardno: "身份证号",
      on_driver_issOrg: "发证机构名称",
      on_driver_licNum: "从业资格证",
      on_driver_expirationDate: "证件有效期",
    },
    gsix: {
      gsix_cool_temp: "发动机冷却液温度",
      gsix_dpf: "DPF压差",
      gsix_engine: "发动机转速",
      gsix_gas: "进气量",
      gsix_oil: "发动机燃料流量",
      gsix_oil_rate: "油箱液位",
      gsix_pressure: "摩擦扭矩",
      gsix_rest: "反应剂余量",
      gsix_src_lower: "SCR下游NOx传感器输出",
      gsix_src_temp1: "SCR入口温度",
      gsix_src_temp2: "SCR出口温度",
      gsix_src_upper: "SCR上游NOx传感器输出",
    },
    obdb: {
      gsix_cvn: "标定验证码CVN",
      gsix_errors_count: "故障码总数",
      gsix_errors_list: "故障码信息列表",
      gsix_iupr: "IUPR值",
      gsix_mil: "MIL状态",
      gsix_protocol: "OBD协议状态",
      gsix_scn: "软件标定识别号",
    },
    obdz: {
      bpcsms: "增压压力控制系统监控",
      ccms: "总和成分监控",
      cms: "催化转化器监控",
      csasms: "冷启动辅助系统监控",
      dpfms: "DFF监控",
      egr: "EGB/VVT系统监控",
      egshms: "排气传感器加热器监控",
      egsms: "排气传感器监控",
      esms: "蒸发系统监控",
      fsms: "燃油系统监控",
      hcms: "加热催化转化器监控",
      mms: "失火监控监控",
      nmhc: "NMHC样话催化器监控",
      nox: "NOx催化还原/吸附器监控",
      sasms: "二次空气系统监控",
      srms: "A/C系统制冷剂监控",
    },
    screen: {
      media_auxStroStatus: "灾备存储",
      media_fadeSignal: "信号遮挡",
      media_lostSignal: "信号丢失",
      media_mainStroStatus: "主存储器",
      media_numBeyond: "录像达到存储阈值",
      media_otherStroStatus: "其他视频设备故障",
    },
    trans: {
      trans_CardEndDate: "道路运输证有效期止",
      trans_CardNo: "运输许可证号",
      trans_CardOrg: "道路运输证发证机构",
      trans_CardStartDate: "道路运输证有效期起",
      trans_Goods: "运输货物",
      trans_vehicleVerifyStatus: "车辆审验状态",
    },
    vehicle: {
      stand_single: "记录仪信号",
      channelCount: "通道数",
      format_dire: "方向",
      height: "高度",
      latlng: "经纬度",
      stand_dianState: "电路",
      stand_doorState: "车门",
      stand_exporEventId: "人工确认报警事件ID",
      stand_inandoutline: "线路行驶",
      stand_oilNumber: "油量",
      stand_oilState: "油路",
      stand_operateState: "营运状态",
      stand_overspeedState: "超速状态",
      stand_zhzState: "装载状态",
      stand_clzz: "车辆载重",
      terminalNo: "设备号码",
      vin: "VIN码",
      muck_nuclear_carry: "载重状态",
      muck_cloth_cover: "篷布状态",
      muck_hopper_lift: "举升状态",
      muck_status_rotation: "罐体转向",
      mini_temp: "车厢温度",
      mini_io_status: "IO状态",
      driving_state: "行驶状态",
      forward_collision_warning: "紧急查车系统采集的前撞预警",
      deviation_warning: "车道偏离预警",
      position: "定位",
      load_state: "客载状态",
      encryption: "经纬度是否经过保密插件加密",
      mini_analog: "模拟量",
      gsmSingle: "网络信号强度",
      mini_waybill: "电子运单",
    },
    tab: {
      basic: "基础状态",
      gsix: "发动机",
      alarm: "设备存储报警",
      driver: "司机/运营",
      gps: "卫星状态参数",
    },
    other: {
      gsix_t1: "车辆终端采集数据",
      gsix_t2: "OBD基础数据",
      gsix_t3: "OBD支持数据",
      gsix_t4: "故障码列表",
      support: "支持",
      unsupport: "不支持",
      teskyes: "测试完成 / 不支持",
      teskno: "测试未完成",
    },
    gps: {
      bdsListNum: "北斗卫星数量",
      gpsListNum: "GPS卫星数量",
      glonassListNum: "GLONASS卫星数量",
      galileoListNum: "GALILEO卫星数量",
      satelliteNo: "卫星编号",
      elevation: "仰角",
      azimuth: "方位角",
      ratio: "载噪比",
      gps: "位置",
    },
  },

  monitorCommand: {
    message: {
      loading: "正在发送指令",
    },
    tab: {
      phone: "电话回拨",
      photo: "拍照",
      text: "文本下发",
      dianming: "车辆点名",
      send: "发送指令",
    },
    basic: {
      acc: "ACC 状态",
      terminalNo: "设备号",
      simCode: "SIM 卡号",
    },
    photo: {
      pass: "通道",
      type: "拍照方式",
      number: "拍照张数",
      size: "分辨率",
      quality: {
        name: "图像质量",
        message: "数值越小，图像质量越好，图片体积越大",
      },
      interval: {
        name: "时间间隔",
        message: "拍照间隔 / 录像时长; 0 表示按最小间隔拍照或一直录像",
      },
      compare: "对比度",
      saturated: "饱和度",
      light: "亮度",
      color: "色度",
      saveWay: {
        name: "保存标志",
        value: ["保存", "实时上传"],
      },
      photoWay: {
        0: "停止拍摄",
        "-1": "开始录像",
        1: "开始拍照",
      },
    },
    phone: {
      name: "模式",
      value: ["普通通话", "监听"],
      number: "电话",
    },
    text: {
      sign: "标志",
      urgent: "紧急",
      add_show: "广告屏显示",
      terminal_monitor: "终端显示器显示",
      terminal_tts: "终端TTS播报",
      type: "信息类型",
      text_type: "文本类型",
      ttsmodel: "TTS模板",
      typeValue: ["中心导航信息", "CAN 故障码信息"],
      textTypeValue: ["通知", "服务"],

      content: "下发内容",
    },
  },
  playBack: {
    showSetting: "显示设置",
    name: "轨迹回放",
    timeSelect: "快捷方式",
    startTime: "开始时间",
    endTime: "结束时间",
    alarmFilter: "报警筛选",
    search: "查询",
    excel: "导出",

    tableLine: {
      name: "轨迹",
      index: "序号",
      formatTime: "GPS时间",
      acc: "ACC",
      gpsSpeed: "速度",
      milebet: "里程差",
      mile: "里程",
      rollerStatus: "滚筒状态",
      location: "位置",
    },

    tableAlarm: {
      name: "报警",
      filter: "筛选",
      index: "序号",
      formatTime: "GPS时间",
      gpsSpeed: "速度",
      alarmName: "类型",
      location: "位置",
    },

    tableStop: {
      name: "停车点",
      filter: "筛选",
      index: "序号",
      begin_time: "开始时间",
      time: "持续时长",
      location: "位置",
    },

    tablefense: {
      name: "围栏",
      tableList: ["工地", "消纳场", "停车场", "辖区"],
      index: "序号",
      passTimeView: "时间",
      areaName: "围栏名称",
      areaTypeName: "围栏类型",
    },

    control: {
      speedName: "速度曲线",
      speedList: ["慢速", "正常", "快速", "极快"],
    },

    popupStop: {
      time: "时间",
      timel: "时长",
      address: "地址",
    },

    timeSelectDesc: {
      "-1": "自定义",
      0: "今天",
      1: "昨天",
      2: "前天",
    },

    message: {
      noData: "未查询到数据",
      noVehicle: "请先选择一个车辆!",
      queryError: "查询失败！",
    },
  },

  playBackControl: {
    name: "显示设置",
    speed: "速度设置",
    detail: "详情显示设置",
    detailList: {
      driver: "司机",
      driverTerminal:"司机（登签）",
      gpsSpeed: "速度",
      mile: "里程",
      milebet: "里程差",
      location: "位置",
      acc_word: "ACC",
      ft_zaiz: "载重",
      ft_fdj: "发动机",
      ft_menc: "门磁",
      ft_ysj: "压缩机",
      moutai_lock_onStatus: "锁开关状态",
      moutai_lock_lockStatus: "施解封状态",
      rollerStatus: "滚筒状态",
      curvature:'曲率半径',
      jianhua56_isbill: '运单',
      jianhua56_isload: '载重(物流)',
    },
    map: "地图显示设置",
    mapList: {
      inter: "兴趣点",
      alarm: "报警点",
      stop: "停车点",
      gd: "工地",
      xnc: "消纳场",
    },
  },

  playBackPopup: {
    driver: "司机",
    gpsSpeed: "速度",
    mile: "里程",
    milebet: "里程差",
    driverTerminal:"司机（登签）",
    location: "位置",
    acc_word: "ACC状态",
    ft_zaiz: "载重",
    ft_fdj: "发动机",
    ft_menc: "门磁",
    ft_ysj: "压缩机",
    moutai_lock_onStatus: "锁开关状态",
    moutai_lock_lockStatus: "施解封状态",
    jianhua56_isbill: '运单',
    jianhua56_isload: '载重(物流)',
    cold_acc: "ACC状态",
    cold_engineSpeed: "发动机转速",
    cold_oilRate: "剩余油量百分比",
    cold_batteryVoltage: "蓄电池电压",
    cold_waterTemp: "冷却水温度",
    cold_coolLevel: "冷却剂液位",
    cold_oilTemp: "机油温度",
    cold_weight: "载重",
    cold_PTO: "取力器PTO状态",
    cold_throttleOpen: "油门开度",
    cold_brakeState: "制动开关状态",
    cold_brakeSignal: "发动机制动激活信号",
    cold_brakeHand: "驻车制动",
    cold_lightLeft: "左转向灯",
    cold_lightRight: "右转向灯",
    cold_ASR: "ASR制动控制激活",
    cold_ABS: "ABS激活",
    cold_EBS: "EBS制动开关",
    cold_curiseState: "巡航控制状态",
    cold_AEBS: "AEBS使能开关状态",
    cold_LDWS: "LDWS使能信号",
    cold_lightDanger: "危险报警灯开关",
    cold_engineIdleOilConsume: "累计怠速燃油消耗量",
    cold_engineIdleTime: "累计怠速时间",
    cold_engineRunTime: "累计运行时间",
    cold_engineOilConsume: "累计燃油消耗量",
    cold_mile: "发动机累计里程",
    rollerStatus: "滚筒状态",
    curvature: "曲率半径"
  },

  playbackstop: {
    stopSetting: "停车点设置",
    showCheck: "显示停车点",
    index: "序号",
    start: "停车开始时间",
    time: "停车时长",
    location: "位置",
    modal: {
      name: "自定义查询",
      way: "查询方式",
      wayValue: ["按规则查询", "自定义查询"],
      time: "停车时间",
      timeTip: "分钟",
      type: "查询类型",
      typeValue: ["按ACC查询", "按车速查询"],
      speed: "行驶速度",
    },
    message: {
      queryError: "查询出错",
      noData: "未查询到数据",
    },
    popup: {
      time: "时间",
      continued: "时长",
      address: "地址",
    },
  },

  playbackmatics: {
    driveTime: "总时长",
    driveMile: "总里程",
    maxSpeed: "最大速度",
    avgSpeed: "平均速度",
  },

  alarmMap: {
    name: "风险地图",
    vehiclequery: "车辆报警查询",
    driverquery: "司机报警查询",
    startTime: "开始时间",
    endTime: "结束时间",
    alarmFilter: "报警筛选",
    search: "查询",
    cluterName: "聚合图",
    hotName: "热力图",
    filterVehicle: "区域筛选",

    top: {
      name: "TOP列表",
      alarmTop5: "报警类型TOP5",
      driverTop5: "驾驶员报警TOP5",
      vehicleTop5: "车辆报警TOP5",
    },
    vehicle: {
      name: "车辆列表",
      index: "序号",
      company: "单位",
      plateNo: "车牌号",
      alarmType: "报警类型",
      speed: "车速",
      alarmTime: "报警时间",
      location: "位置",
    },

    message: {
      noParmas: "请至少选择一个司机或者车辆",
      queryError: "查询出错",
      noData: "未查询到报警数据",
    },
  },
  driverActionMap: {
    name: "驾驶行为地图",
    vehiclequery: "车辆报警查询",
    driverquery: "司机报警查询",
    startTime: "开始时间",
    endTime: "结束时间",
    alarmFilter: "报警筛选",
    search: "查询",
    cluterName: "聚合图",
    hotName: "热力图",
    filterVehicle: "区域筛选",

    top: {
      name: "TOP列表",
      alarmTop5: "报警类型TOP5",
      driverTop5: "驾驶员报警TOP5",
      vehicleTop5: "车辆报警TOP5",
    },
    vehicle: {
      name: "车辆列表",
      index: "序号",
      company: "单位",
      plateNo: "车牌号",
      alarmType: "报警类型",
      speed: "车速",
      alarmTime: "报警时间",
      location: "位置",
    },

    message: {
      noParmas: "请至少选择一个司机或者车辆",
      queryError: "查询出错",
      noData: "未查询到报警数据",
    },
  },
  lockMap: {
    name: "锁定跟踪",
    index: "序号",
    company: "单位",
    plateNo: "车牌号",
    alarmType: "报警类型",
    speed: "车速",
    alarmTime: "报警时间",
    location: "位置",

    message: {
      noVehicle: "无此车辆",
      hadVehicle: "该车辆已存在",
      limitState: "监控数量已达上线",
    },
  },

  lockTreePart: {
    all: "全部",
    online: "在线",
    offline: "离线",
    message: "请选择车辆",
  },

  lockWindow: {
    tool: {
      close: "关闭窗口",
      line: "开启线路",
      fullScreen: "全屏",
      timeRadio: "实时视频",
      playback: "轨迹回放",
      backLock: "返回车辆视角",
      lockCar: "锁定车辆",
      clear: "清除当前所有报警",
    },

    basic: {
      format_dept: "部门",
      format_gpsTime: "定位时间",
      format_state: "状态",
      driver_name: "司机",
      driver_phone: "电话",
      mile: "里程",
      format_dire: "方向",
      simCode: "SIM卡号",
      terminalNo: "设备号码",
      location: "位置",
    },
  },
  alarmVideo: {
    startTime: "开始时间",
    endTime: "结束时间",
    fileType: "文件类型",
    fileList: {
      "-1": "全部",
      0: "图片",
      2: "视频",
    },
    alarmFilter: "报警筛选",
    search: "查询",

    message: {
      noVehice: "请先选择车辆",
      noAlarm: "请至少选择一个报警类型",
      queryError: "查询出错",
      noData: "未查询到数据",
    },
  },
  vehicleRunTrack: {
    name: "车辆行驶轨迹",
    queryTime: "查询周期",
    search: "查询",
    export: "导出",
    startTime: "开始时间",
    endTime: "结束时间",
    yAxisName: "速度",
    modal: {
      name: "速度曲线",
      title: "车辆速度波动曲线图",
      avgSpeed: "平均速度",
      mile: "行驶里程",
      time: "行驶时长",
    },
    table: {
      index: "序号",
      plate: "车牌号",
      gpsTime: "定位时间",
      lng: "经度",
      lat: "纬度",
      speed: "速度",
      vsspeed: "VSS速度",
      dire: "方向",
      acc: "ACC",
      mile: "里程",
      location: "位置",
    },
    message: {
      noVehice: "请先选择车辆",
      queryError: "查询出错",
      noData: "未查询到轨迹数据",
      noDataExport: "未检测到可以导出的数据",
    },
  },
  ftVehicleRunTrack: {
    name: "车辆行驶轨迹",
    queryTime: "查询周期",
    search: "查询",
    export: "导出",
    startTime: "开始时间",
    endTime: "结束时间",
    yAxisName: "速度",
    modal: {
      name: "速度曲线",
      title: "车辆速度波动曲线图",
      avgSpeed: "平均速度",
      mile: "行驶里程",
      time: "行驶时长",
      tableTitle: "表格显示配置",
    },
    table: {
      index: "序号",
      plate: "车牌号",
      gpsTime: "定位时间",
      lng: "经度",
      lat: "纬度",
      speed: "速度",
      vsspeed: "VSS速度",
      dire: "方向",
      acc: "ACC",
      mile: "里程",
      location: "位置",
      oil_consum: "油耗",
      rota_speed: "转速",
    },
    message: {
      noVehice: "请先选择车辆",
      queryError: "查询出错",
      noData: "未查询到轨迹数据",
      noDataExport: "未检测到可以导出的数据",
    },
  },
  roadLimitSpeed: {
    name: "限速标识地图",
    chooseAll: "选择全部",
    rangeFilter: "区域筛选",
    all: "总",
    filter: "筛",
  },
  alarmBox: {
    name: "实时消息列表",
    ruleSet: "配置规则",
    bellState: "切换响铃状态",
    refresh: "刷新规则",
    close: "关闭",
    alarm: "报警类",
    event: "事件类",
    bussiness: "业务类",
    breakdown:"故障类",
    reflshOk: "规则刷新成功",
  },
  alarmFilter: {
    name: "报警筛选",
    gps: "GPS 北斗定位系统",
    adas: "ADAS 防撞预警系统",
    dms: "DMS 驾驶员检测系统",
    vadas: "VADAS 车身姿态检测系统",
    vdss: "VDSS 事件",
    tbox_driver: "TBOX-DRIVER 驾驶行为检测系统",
    tbox_fault: "TBOX-FAULT 车辆故障诊断系统",
    business: "渣土报警",
    cold: "冷链报警",
    tbox: "BUSSINESS 业务",
    bsd: "BSD 报警",
    unknow: "未定义报警分类",
    elock: "LOCK 电子锁",
    container: "CONTAINER 货箱识别",
    newEnergy:"Renewable 新能源",
    aeb:"AEB 制动"


  },
  customFilter: {
    checkall: "全选",
  },
  alarmVideoWindow: {
    name: "报警视频窗口",
    wait: "等待请求",
    parmasError: "参数错误",
    getting: "正在请求",
    getError: "获取视频列表失败",
    way: "通道",
    offline: "该车辆已离线，暂无法查看视频",
    waitBack: "等待返回",
    notBack: "处理结果未返回",
    waitLine: "排队中, 当前位置:",
    handle: "处理中",
    timeOut: "获取媒体文件超时",
  },
  singleState: {
    gsm: "gsm网络上传信号(数据传输稳定性决定因素)",
    gps: "gps定位信号(信号弱时有可能存在数据丢失/位置不准的情况)",
  },
  popupAlarm: {
    company: "企业",
    alarm: "报警",
    speed: "速度",
    location: "位置",
    alarmVideo: "报警视频",
    alarmLink: "报警明细",
  },
  treeMapLayer: {
    all: "全部",
    choose: "选中",
    able: "有效",
    unable: "无效",
    formal: "正式",
    temporary: "临时",
  },
  maticsMap: {
    mapChange: "地图切换",
    mapTool: "地图工具",
    bussiness: "业务拓展",
    gaode: "高德地图",
    gaodeSatel: "高德卫星",
    google: "谷歌地图",
    googleSatel: "谷歌卫星",
    tianditu: "天地图",
    tiandituSatel: "天地图卫星",
    map_manyou: "漫游",
    map_lkenlarge: "拉框放大",
    map_lknarrow: "拉框缩小",
    map_traffic: "路况",
    map_ranging: "测距",
    map_paint: "测量",
    map_fullScreen: "全屏",
    map_ssq: "区域规划",
    map_areaSearch: "区域查车",
    map_linePlan: "路径规划",
    map_tagging: "标注点",
    map_view: "我的视野",
    map_fence: "围栏",
    map_inter: "兴趣点",
    map_area_fence:'区域围栏',
    map_line: "公交线路",
    map_navigation: "城市导航",
    maproute: "通行线路",
    mapgd: "工地",
    mapxnc: "消纳场",
    map_search: "地理信息查询",
    map_project: "工程项目",
    mapcz: "场站",
    map_rangearea: "区域规划",
    setting: {
      name: "显示设置",
      lukuang: "路况",
      gongdi: "工地",
      xiaonacha: "消纳场",
      tongxingxl: "通行线路",
    },

    map_tool: "地图工具",
    return_tool: "返回工具箱",
    hide_tool: "隐藏",
    map_default: "地图工具",
  },
  mapSearchControl: {
    placeholder: "请输入内容",
    placeholderS: "请选择",
    message: "定位失败，请手动选择所在城市",
  },
  scoreCompare: {
    name: "选择对比车辆选择",
    searchObj: "查询对象",
    searchValue: {
      1: "车辆",
      2: "驾驶员",
      0: "企业",
    },
    startDate: "开始日期",
    endDate: "结束日期",
    click: "点击选择",
    simpleParmas: "检测到相同查询条件",
    noQuery: "请选择要查询的对比对象",
    queryError: "未查询到结果,请另选对象",

    adasAlarm100: {
      adas_fcw: "FCW前碰撞预警",
      adas_hkm: "总报警数",
      adas_hmw: "HMW车距过近预警",
      adas_ldw: "LDW车道偏离预警",
      adas_lldw: "LLDW左车道偏离预警",
      adas_pcw: "PCW行人提醒",
      adas_pcwc: "PCW-C前侧盲区",
      adas_pcwl: "PCW-L左后盲区",
      adas_pcwr: "PCW-R右后盲区",
      adas_pcww: "PCW行人预计警示",
      adas_rldw: "RLDW右车道偏离预警",
      adas_ufcw: "UFCW低速前碰撞预警",
      adas_vb: "VB虚拟保险杠预警",
      name: "ADAS报警(个/百公里)",
    },
    dmsAlarm100: {
      dms_close_eyes: "闭眼",
      dms_distract: "疲劳驾驶",
      dms_hkm: "总报警数",
      dms_smoke: "吸烟",
      dms_tel: "打电话",
      dms_tired: "注意力分散",
      dms_yawn: "打哈欠",
      name: "DMS报警(个/百公里)",
    },
    driverScore: {
      name: "驾驶评分(分)",
      score_day: "白天评分",
      score_expressway: "高速评分",
      score_night: "夜间评分",
      score_normalway: "非高速评分",
      score_safe: "安全评分",
    },
    mileDetail: {
      mile_avg: "平均里程",
      mile_day: "白天里程",
      mile_expressway: "高速里程",
      mile_night: "夜间里程",
      mile_normalway: "非高速里程",
      mile_sli: "超速里程",
      mile_total: "总里程",
      name: "里程详情(千米)",
    },
    runRoad: {
      name: "行程",
      trip_avg_mile: "平均行程里程",
      trip_avg_time: "平均行程时长",
      trip_day: "行程频次(次/天)",
      trip_hkm: "行程频次(次/100km)",
      trip_max_mile: "最大行程里程",
      trip_max_time: "最大行程时长",
    },
    scoreType: {
      dimension_acc: "急加速",
      dimension_brake: "急减速",
      dimension_fcw: "追尾",
      dimension_hmw: "车距控制",
      dimension_ldw: "车道保持",
      dimension_lrreguar: "不规范驾驶行为",
      dimension_pcw: "行人及非机动车碰撞",
      dimension_sli: "超速",
      dimension_tired: "疲劳驾驶",
      dimension_turn: "急转弯",
      dimension_turn_light: "转向灯使用",
      name: "维度评分(分)",
    },
    speedType: {
      name: "车速分布",
      speed_avg_day: ",平均速度(白天)",
      speed_avg_expressway: "平均速度(高速)",
      speed_avg_night: "平均速度(夜间)",
      speed_avg_normalway: "平均速度(非高速)",
      speed_between_20_40: "20 ~ 40km/h",
      speed_between_40_60: "40 ~ 60km/h",
      speed_between_60_80: "60 ~ 80km/h",
      speed_between_80_100: "80 ~ 100km/h,",
      speed_greater_100: "> 100km/h",
      speed_less_20: "< 20km/h",
      speed_max_day: "最高速度(,白天)",
      speed_max_expressway: "最高速度(,高速)",
      speed_max_night: "最高速度(夜间)",
      speed_max_normalway: "最高速度(非,高速)",
    },
    timeDetail: {
      name: "时长详情(小时)",
      time_avg: "平均时长",
      time_day: "白天时长",
      time_expressway: "高速时长",
      time_night: "夜间时长",
      time_normalway: "非高速时长",
      time_sli: "超速时长",
      time_total: "总时长",
    },
    vadasAlarm100: {
      name: "VADS报警(个/百公里)",
      vadas_acc: "急加速",
      vadas_brake: "急减速",
      vadas_hkm: "总报警数",
      vadas_left_turn: "左急转弯",
      vadas_left_turn_no_light: "左转弯未打灯",
      vadas_right_turn: "右急转弯",
      vadas_right_turn_no_light: "右转弯未打灯",
    },
    vdssAlarm100: {
      name: "VDSS车辆状态(个/百公里)",
      vdss_LLDW: "左变道",
      vdss_RLDW: "右变道",
      vdss_brake: "刹车",
      vdss_left_turn_light: "左转向灯",
      vdss_right_turn_light: "右转向灯",
    },
  },
  compareLayout: {
    compare: "添加对比",
    returnTop: "返会顶部",
  },
  operateMap: {
    name: "运营地图",
    searchTab: "查询",
    controlTab: "控制",
    searchDate: "查询日期",
    alarmFilter: "报警筛选",
    lineMap: "轨迹运营图",
    alarmMap: "报警运营图",
    stopMap: "停车运营图",

    message: {
      noVehice: "请至少选择一辆车",
      queryError: "查询出错",
    },
  },

  alarmSetting: {
    alarmTab: "报警类",
    eventTab: "事件类",
    bussTab: "业务类",
    loading: "正在加载配置",
    unsetBtn: "取消设置",
    setBtn: "批量设置",
    common: {
      title: "设置",
      index: "序号",
      bussType: "业务类型",
      soundWarn: "声音提示",
      warnSound: "提示声音",
      warnIngnoe: "是否关注",
      warnPopup: "是否弹框",
      warnLight: "开启提示灯",
      warnRepart: "重复次数",
      warnInter: "播放间隔",
      warnClass: "图标名称",
      default: "默认",
      yes: "是",
      no: "否",
    },
    filter: {
      all: "全部",
      gps: "GPS 北斗定位系统",
      adas: "ADAS 防撞预警系统",
      dms: "DMS 驾驶员检测系统",
      vadas: "VADAS 车身姿态检测系统",
      vdss: "VDSS 事件",
      tbox_driver: "TBOX-DRIVER 驾驶行为检测系统",
      tbox_fault: "TBOX-FAULT 车辆故障诊断系统",
      bussiness: "渣土报警",
      business: "BUSSINESS 业务",
      cold: "冷链报警",
      bsd: "BSD 报警",
      elock: "LOCK 电子锁",
      container: "CONTAINER 货箱识别",
      newEnergy:'Renewable 新能源',
      aeb:"AEB 制动"
    },
    setting: {
      all: "全部",
      yesSet: "已配置",
      noSet: "未配置",
    },
    query: {
      name: "报警名称",
      type: "报警类型",
      message: "请输入关键字",
    },
    alarmTable: {
      alarmName: "报警名称",
      alarmRemark: "报警说明",
      alarmIcon: "报警图标",
      alarmCode: "报警码",
      alarmType: "报警类型",
      operate: "操作",
    },
    alarmModal: {
      title: "报警详情",
      name: "报警名称",
      alarmIcon: "报警图标",
      alarmType: "报警类型",
      alarmSort: "报警排序",
      remark: "报警备注",
      alarmInter: "报警说明",
    },
    message: {
      noQuery: "请选择对象",
      queryError: "请求出错",
    },
    eventTable: {
      name: "事件名称",
      eventIcon: "事件图标",
      eventType: "事件类型",
    },
  },

  talkBack: {
    unknowStep: "未知状态",
    stepMsg: [
      "点击开始对讲",
      "正在请求麦克风权限",
      "浏览器不支持，请检查相关设置",
      "请求麦克风权限成功",
      "请求麦克风权限失败，请确认开启麦克风权限或检查音频输入设备。",
      "点击以连接设备",
      "正在连接设备",
      "连接设备成功,正在对讲",
      "连接设备失败，请重试",
      "设备已断开，请重新连接",
    ],
  },

  alarmDetail: {
    label: {
      copyPlate: "复制车牌",
      eventType: "事件类型",
      todayCount: "今日发生次数",
      region: "区域",
      notSet: "未设置",
      maxSpeed: "最高速度",
      avgSpeed: "平均速度",
      startTime: "开始时间",
      driveDuration: "行驶时长",
      endTime: "结束时间",
      noEnd: "未结束",
      mileage: "里程",
      startLocation: "开始位置",
      speed: "车速",
      endLocation: "结束位置",
      count: "次",
      chn: "通道",
      deal: "处理",
      hasDeal: "已经处理过了",
      cancel: "取消",
      custom: "自定义",
      defaultTemplate: "默认消息模板",
      company: "归属",
      driver: "驾驶员",
    },
    messageInfo: [
      "复制成功",
      "查询轨迹失败",
      "下发成功",
      ["信息下发失败，终端", `错误code：`, "不在线"],
      "处理失败",
      "点击复制",
    ],
  },

  elementTree: {
    onlyOnline: "仅显示在线",
    filterType: [
      "默认搜索",
      "车牌号",
      "別名",
      "自编号",
      "SIM卡号",
      "终端号",
      "驾驶员",
      "VIN码",
      "终端ID",
    ],
  },
  vehicleTrackSpeed: {
    timeFrame: "时间范围:",
    overspeedDuration: "超速时长:",
    speedLimit: "限速:",
    overspeedRate: "超速率：",
    query: "查询",
    export: "导出",
    collect: "汇总",
    index: "序号",
    unit: "单位",
    plateNo: "车牌号",
    beginTime: "开始时间",
    endTime: "结束时间",
    count: "次数",
    detail: "详情",
    maxSpeed: "最大速度",
    minSpeed: "最小速度",
    avgSpeed: "平均速度",
    sliRatio: "超速率(%)",
    sli: "限速(km/h)",
    driveTime: "持续时间(分)",
    driveMile: "持续里程(km)",
    beginLocation: "开始位置",
    endLocation: "结束位置",
    chooseCar: "请选择车辆",
    queryError: "查询出错",
    noData: "没有数据可以导出",
    overspeedlist: "超速统计汇总",
    overspeeddetail: "超速统计详情",
  },
  vehicleTrip: {
    statistics: "统计",
    unit: "单位",
    cycle: "行程数",
    mile: "里程(km)",
    drivingScore: "驾驶评分",
    control: "操作",
    runningData: "轨迹回放",
    detail: "明细",
    faceImage: "人脸图片",
    workImage: "考勤图片",
    beginLocation: "开始位置",
    endLocation: "结束位置",
    drivermile: "行驶里程",
    nodetailmassage: "无明细消息",
    carmilestatistics: "车辆行程统计",
    driverdetails: "行程详情",
  },
  fenseMgnt: {
    dept: "部门",
    area: "区域",
    fenceType: "围栏类型",
    fenceValue: "围栏名称",
    fencekTip: "请输入关键字查询",
    all: "全部",
    query: "查询",
    add: "新增",
    index: "序号",
    unit: "单位",
    fenceName: "围栏名称",
    attributionArea: "归属区域",
    fenceShape: "围栏形状",
    fenceStyle: "围栏样式",
    showVertices: "是否显示顶点",
    yes: "是",
    no: "否",
    control: "操作",
    detail: "详情",
    Modify: "修改",
    remove: "删除",
    append: "附加",
    REC: "矩形",
    circular: "圆形",
    NGon: "多边形",
    line: "线路",
    filecontrol: "操作失败",
    successcontrol: "操作成功",
    removefence: "此操作将永久删除次围栏,是否继续?",
    Hint: "提示",
    sure: "确定",
    cancel: "取消",
    removefile: "删除出错",
    removesuccess: "删除成功",
    choosequeryobject: "请先选择查询对象!",
    queryfile: "查询出错",
    noqueryData: "未查询到数据",
    keypointslist: "请设置关键点列表",
    keyDialog: "关键点",
    setKey: "设置关键点",
  },
  fenseProject: {
    query: "查询",
    inputname: "请输入工程名称",
    export: "导出",
    deptName: "单位",
    mixingStationName: "搅拌站",
    constructionSite: "施工部位",
    concreteLabel: "砼标号",
    name: "工程名称",
    constructionUnit: "施工单位",
    contractNo: "合同单号",
    contactName: "联系人",
    contactTel: "联系电话",
    address: "地址",
    createTimeView: "创建时间",
    remark: "备注",
    control: "操作",
    choosegroupquery: "请选择工程查询",
    queryfail: "查询出错",
    noData: "未查询到数据",
    choosegroupqueryexport: "请选择工程导出",
    map: "查看地图",
  },
  styleSelect: {
    customStyle: "自定义样式",
    default: "默认",
  },
  fenseExtand: {
    Lineadditional: "线路附加信息绑定",
    Roadspeed: "顶点/拐点列表",
    speedForm: "限速列表",
    speed: "限速",
    beginLocation: "起始位置",
    endLocation: "结束位置",
    begin: "起始",
    end: "结束",
    index: "序号",
    COORDINATE: "经纬度",
    nature: "属性",
    control: "操作",
    Modify: "修改",
    addall: "修改全部",
    saveset: "保存配置",
    keypoint: "关键点",
    scope: "范围",
    ETA: "预定到达时间",
    ETD: "预定离开时间",
    remove: "删除",
    KeyPointPosition: "关键点位置",
    inputcontent: "请输入内容",
    KeyPointRange: "关键点范围",
    mi: "米",
    APIT: "任意时间点",
    RoadWidth: "路段宽度",
    MaxSpeed: "最高速度",
    Overspeed: "可超速阈值",
    Driverover: "行驶过长阈值",
    Underdrive: "行驶不足阈值",
    nigetMaxSpeed: "夜间最高速度",
    drawpoints: "请先画点",
    selectkeypoint: "请先选择关键点范围",
    choosetime: "请选择时间",
    pointsuccess: "取点成功",
    nightMaxSpeed: "夜间最高速度",
    pleasedraw: "请画点",
    Pleasetakefirst: "请先取点",
    S: "秒",
    speedLimit: "请输入限速",
  },
  fenseModal: {
    fensetype: "围栏类型",
    department: "归属部门",
    choosedept: "请选择部门",
    rangearea: "区域规划",
    area: "归属区域",
    choosearea: "请选择区域",
    jurisdiction: "归属辖区",
    choose: "请选择",
    fensegroup: "围栏分组",
    fenseshape: "围栏形状",
    fensewidth: "围栏宽度",
    fensestyle: "围栏样式",
    fontsize: "字体大小",
    vertices: "显示顶点",
    fensename: "围栏名称",
    save: "保存",
    close: "关闭",
    draw: "画图",
    cleardraw: "清除重画",
    choosefensestyle: "围栏样式选择",
    choosefensetype: "请选择围栏类型",
    choosefensegroup: "请选择围栏分组",
    choosejurisdiction: "请选择归属辖区",
    choosefenseShape: "请选择围栏形状",
    inputfensewidth: "请输入围栏宽度 / 半径",
    choosefensestyle1: "请选择围栏样式",
    inputfontsize: "请输入字体大小",
    showvertices: "是否显示顶点",
    inputfensename: "请输入围栏名称",
    choosebelongarea: "请选择归属区域",
    choosebelongdept: "请选择归属部门",
    addfense: "新增围栏",
    Modifyfense: "修改围栏",
    pleacedrawfense: "请先绘制围栏",
    chooseprovince: "请选择省市区!",
    saveAndAdd: '保存并继续添加'
  },
  FenseLineModal: {
    department: "归属部门",
    choosedept: "请选择部门",
    area: "归属区域",
    choosearea: "请选择区域",
    jurisdiction: "归属辖区",
    choose: "请选择",
    linename: "线路名称",
    roadname: "通行道路名称",
    save: "保存",
    close: "关闭",
    Cleardrawing: "清除重画",
    draw: "画图",
    choosefensetype: "请选择围栏类型",
    choosejurisdiction: "请选择归属辖区",
    choosefenseShape: "请选择围栏形状",
    inputfensewidth: "请输入围栏宽度 / 半径",
    choosefensestyle: "请选择围栏样式",
    inputfontsize: "请输入字体大小",
    showvertices: "是否显示顶点",
    inputfensename: "请输入围栏名称",
    inputroadname: "请输入路段名称",
    choosebelongarea: "请选择归属区域",
    choosebelongdept: "请选择归属部门",
    fense: "围栏",
    pleacedrawfense: "请先绘制围栏",
    chooseprovince: "请选择省市区",
  },
  fenseLineMgnt: {
    dept: "部门",
    area: "区域",
    query: "查询",
    add: "新增",
    index: "序号",
    unit: "单位",
    fenceName: "围栏名称",
    attributionArea: "归属区域",
    fenceType: "",
    fenceShape: "围栏形状",
    fenceStyle: "围栏样式",
    showVertices: "是否显示顶点",
    yes: "是",
    no: "否",
    control: "操作",
    detail: "详情",
    copy: "复制新增",
    Modify: "修改",
    remove: "删除",
    REC: "矩形",
    circular: "圆形",
    NGon: "多边形",
    line: "线路",
    filecontrol: "操作失败",
    successcontrol: "操作成功",
    removefence: "此操作将永久删除此围栏,是否继续?",
    Hint: "提示",
    sure: "确定",
    cancel: "取消",
    removefile: "删除出错",
    removesuccess: "删除成功",
    choosequeryobject: "请先选择查询对象!",
    queryfile: "查询出错",
    noqueryData: "未查询到数据",
  },
  fenseGroupMgnt: {
    query: "查询",
    inputgroupname: "请输入分组名称",
    add: "新增",
    index: "序号",
    groupname: "分组名称",
    department: "归属单位",
    containingtype: "包含类型",
    CreationTime: "创建时间",
    CreationUser: "创建用户",
    Notes: "备注",
    control: "操作",
    Modify: "修改",
    remove: "删除",
    addgroup: "新增分组",
    belongdept: "归属部门",
    choosedept: "请选择部门",
    activitynature: "类型",
    choosedepartment: "请选择归属部门",
    chooseoneactivity: "请至少选择一个类型",
    choosegroupquery: "请选择分组查询",
    queryfail: "查询出错",
    noData: "未查询到数据",
    ConfirmDelete: "确认解除",
    group: "分组",
    Prompt: "提示",
    ensure: "确定",
    cancel: "取消",
    controlfail: "操作出错",
    removesuccess: "删除成功",
    addsuccess: "新增成功",
  },
  limitReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    choosearea: "限行区",
    restrictedarea: "非限行区",
    startTime: "开始时间",
    chooseDate: "选择日期",
    endTime: "结束时间",
    query: "查询",
    map: "地图",
    count: "总违规次数",
    avgMile: "平均单日里程",
    avgTime: "平均单日时长",
    avgSpeed: "平均速度",
    maxSpeed: "最高速度",
    smoke: "抽烟",
    phone: "打电话",
    tired1: "疲劳驾驶一级",
    tired2: "疲劳驾驶二级",
    distract: "注意力不集中",
    speed: "连续超速",
    pcw: "行人碰撞",
    fcw: "车辆碰撞",
    ldw: "频繁压线",
    hmw: "跟车过近",
    noPasscheck: "无通行证限行",
    peaktermLine: "高峰期限行",
    form: "表格",
    excel: "导出",
    index: "序号",
    drivingArea: "行驶区域",
    unit: "单位",
    plateNo: "车牌号",
    blackOrWhite: "黑白名单",
    ranking: "安全评分",
    onlineRate: "上线率",
    verbCount: "近期违章次数",
    notVerbCount: "未处理违章次数",
    driveMile: "行驶里程(km)",
    breakLineMile: "闯限行里程(km)",
    driveTime: "行驶时长(h)",
    breakLineTime: "闯限行时长(h)",
    faceOdd: "人脸识别异常",
    driverOdd: "驾驶员异常",
    chooseCar: "请选择车辆",
    chooseDriver: "请选择司机",
    chooseFense: "请选择限行区",
    queryfail: "查询出错",
    noData: "未查询到数据",
    noDataExcel: "没有数据可以导出",
    vehicleArea: "车辆限行区违规统计",
  },
  xiaquReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    choosearea: "辖区选择",
    restrictedarea: "非辖区",
    startTime: "开始时间",
    chooseDate: "选择日期",
    endTime: "结束时间",
    query: "查询",
    map: "地图",
    count: "总违规次数",
    sumMile: "总里程(千米)",
    sumTime: "总时长(小时)",
    avgSpeed: "平均速度",
    maxSpeed: "最高速度",
    smoke: "抽烟",
    phone: "打电话",
    tired1: "疲劳驾驶一级",
    tired2: "疲劳驾驶二级",
    distract: "注意力不集中",
    speed: "连续超速",
    pcw: "行人碰撞",
    fcw: "车辆碰撞",
    ldw: "频繁压线",
    hmw: "跟车过近",
    noPasscheck: "无通行证限行",
    peaktermLine: "高峰期限行",
    form: "表格",
    excel: "导出",
    index: "序号",
    drivingArea: "行驶区域",
    unit: "单位",
    plateNo: "车牌号",
    blackOrWhite: "黑白名单",
    ranking: "安全评分",
    onlineRate: "上线率",
    verbCount: "近期违章次数",
    notVerbCount: "未处理违章次数",
    driveMile: "行驶里程(km)",
    breakLineMile: "闯限行里程(km)",
    driveTime: "行驶时长(h)",
    breakLineTime: "闯限行时长(h)",
    faceOdd: "人脸识别异常",
    driverOdd: "驾驶员异常",
    chooseCar: "请选择车辆",
    chooseDriver: "请选择司机",
    chooseFense: "请选择辖区",
    queryfail: "查询出错",
    noData: "未查询到数据",
    noDataExcel: "没有数据可以导出",
    vehicleArea: "车辆辖区违规统计",
  },
  crossReport: {
    chooseObj: "对象选择",
    car: "车辆",
    driver: "驾驶员",
    chooseroad: "路口选择",
    unknownroad: "未知路口",
    startTime: "开始时间",
    chooseDate: "选择日期",
    endTime: "结束时间",
    wayspeed: "直行速度",
    leftspeed: "左转速度",
    rightspeed: "右转速度",
    query: "查询",
    map: "地图",
    form: "表格",
    excel: "导出",
    statistics: "统计",
    index: "序号",
    plateNo: "车牌号",
    unit: "单位",
    speed: "路口直行速度",
    left: "左转弯速度",
    right: "右转弯速度",
    sbspeed: "路口直行超速",
    sbleft: "左转弯超速",
    sbright: "右转弯超速",
    leftl: "左转未打灯",
    rightl: "右转未打灯",
    alarmCount: "总违规次数",
    thoughCount: "总通过次数",
    pingci: "频次",
    crossCount: "总违规次数/通过次数",
    alertsPass: "违规次数/通过次数",
    roadstatistics: "路口统计",
    roadname: "路口名称",
    details: "详情",
    road: "路口",
    speedtype: "违规类型",
    velocity: "速度",
    sblight: "转向灯",
    speedTime: "违规时间",
    choosecar: "请选择车辆",
    choosedriver: "请选择司机",
    choosefense: "请选择围栏",
    queryfail: "查询出错",
    noData: "未查询到数据",
    carroadspeed: "车辆路口违规统计",
    operate: "操作",
  },
  mapLineSearch: {
    startPosition: "开始位置",
    endPosition: "结束位置",
    pathType: "路径类型",
    choose: "请选择",
    speed: "速度优先(不考虑路况最快)",
    cost: "费用优先(不走收费路段,时间最短)",
    Distance: "距离优先(不考虑路况,距离最短)",
    speed1: "速度优先(不走快速路)",
    congestion: "躲避拥堵",
    analyze: "开始分析",
    DIST: "距离",
    Traffic: "路况",
    destinationMap: "请先在地图上标注好起终点!",
    routePlanningfail: "路线规划出错!",
    duration: "时长:",
  },
  mappoisearch: {
    city: "所在城市",
    provinceorcity: "请输入省市区",
    keywords: "关 键 字",
    inputkeywords: "请输入关键字",
    query: "查询",
    total: "共计",
    seeAll: "查看全部",
    result: "个结果",
    noResult: "未查询到结果",
    inputkeyquery: "输入关键字查询",
  },
  mapTagging: {
    inputkeywords: "请输入关键字",
    checkall: "  全选",
    delete: "批量删除",
    addPoints: "新增标注点",
    pointMessage: "标记点信息",
    name: "名称:",
    inputname: "请输入名称",
    remark: "备注:",
    inputremark: "请输入备注",
    save: "保存",
    remove: "删除",
    inputpointsname: "请输入标注点名称",
    controlfail: "操作失败",
    savesuccess: "保存成功",
    chooseremovepoints: "请选需要删除的标注点(灰色为无权删除的点)",
    removepoints: "此操作将删除这些标注点,是否继续?",
    hint: "提示",
    ensure: "确定",
    cancel: "取消",
    removesuccess: "删除成功",
    icon: "图标:",
    latlng: "位置:",
    local: "地址:",
    longitude: "经度:",
    latitude: "纬度:",
  },
  carMgt: {
    label: {
      choosePlease: "请选择",
      plateNo: "车牌号码",
      terminalNo: "设备号",
      simCode: "SIM卡号",
      driverName: "驾驶员姓名",
      keyPlease: "请输入关键字",
      noData: "暂无数据",
      index: "序号",
      group: "单位",
      plateNoAlias: "车牌号",
      carTypes: "车辆类型",
      terminalTypes: "终端类型名",
      mainTerminalNo: "主终端手机号",
      mainSimCode: "主终端ICCID卡号",
      bindedDriver: "绑定司机",
      channelNum: "视频通道",
      load: "吨位",
      "white&blackList": "黑白名单",
      operate: "操作",
      statusSwitch: "切换状态",
      modify: "修改",
      search: "查询",
      export: "导出",
      multipleImport: "批量导入",
      add: "新增",
      whiteList: "白名单",
      blackList: "黑名单",
      unset: "未设置",
      del: "删除",
      report: "报备",
      tip: "提示",
      enable: "启用",
      disable: "停用",
      successReport: "报备成功",
    },
    expression: ["车辆已{0}"],
    messageInfo: [
      "将永久删除车辆、车辆司机绑定、准驾记录, 是否继续?",
      "删除成功！",
      "删除失败！",
      "确认切换状态?",
      "切换状态失败",
      "导出车辆.xlsx",
    ],
  },
  carInfo: {
    label: {
      baseInfo: "基本信息",
      plateNo: "车牌号码",
      plateColor: "车牌颜色",
      choosePlease: "请选择",
      carAlias: "车辆别名",
      group: "车辆归属",
      deptChoosePlease: "请选择归属部门",
      industry: "车辆行业",
      workRoad: "行驶线路",
      roadName: "请选择或输入线路名",
      transportIndustry: "运输行业",
      carTypes: "车辆类型",
      carVIN: "车辆VIN",
      carManufacturer: "车辆厂家",
      carRegisterPlace: "车籍地",
      regionChoosePlz: "请选择所属区域",
      allowedDrivers: "允许司机",
      deptChoosePlz: "请先选择部门",
      transportGoods: "运输货物",
      carDevices: "车辆设备",
      carLoad: "车辆吨位",
      channelNum: "视频路数",
      "white&blackList": "黑白名单",
      unset: "未设置",
      blackList: "黑名单",
      whiteList: "白名单",
      carTypeCode: "车型编码",
      lightBoxCode: "灯箱号",
      terminalType: "终端类型",
      terminalTypePlz: "请选择或输入终端类型",
      terminalNo: "终端号",
      terminalNoPlz: "请输入终端号",
      simCode: "sim卡号",
      simCodePlz: "请选择或输入SIM卡",
      installer: "安装人员",
      installTime: "安装时间",
      datePlz: "选择日期",
      expireTime: "到期时间",
      nuclear_carry: "车辆容积",
      remark: "备注",
      carOperation: "车辆营运证",
      transportLicense: "许可证号",
      licenseFrom: "发证机构",
      operationStatus: "营运状态",
      validateStart: "有效期起",
      validateEnd: "有效期止",
      checkResult: "审验结果",
      attachmentPic: "图片附件",
      drivingLicense: "车辆行驶证",
      owner: "所有人",
      useNature: "使用性质",
      annualExamination: "年审到期",
      insurance: "车辆保单信息",
      insuranceNO: "保单号",
      insuranceValidDate: "保险到期",
      cancel: "取消",
      addCar: "新增车辆",
      addConfirm: "确认新增",
      modifyCar: "修改车辆",
      modifyConfirm: "确认修改",
      fensePlz: "请选择围栏",
      mainTerminal: "主终端",
      videoTerminal: "视频终端",
      subTerminal: "辅助终端",
      addSuccess: "添加成功",
      modifySuccess: "修改成功",
      brand: "车辆品牌",
      terminalConfig: "终端配置",
    },
    expression: ["起：{0}", "终：{0}", "{0}公里"],
    messageInfo: [
      "请输入车牌号码",
      "请选择车牌颜色",
      "请选择所属部门",
      "请选择所属车辆行业",
      "请选择车辆部门",
      "请选择运输行业",
      "请先输入车牌号码",
      "至少选择到市级别！",
      "请选择企业或者车队（部门）级别！",
      "查询司机列表失败",
      "添加失败:",
      "修改失败:",
    ],
  },
  driverBind: {
    label: {
      bindDriver: "绑定驾驶员",
      plateNo: "车牌号码",
      group: "归属部门",
      currentBinded: "当前绑定",
      noData: "无",
      unbindedTime: "绑定时间",
      unbound: "未绑定",
      changeBinding: "更改绑定",
      driverPlz: "请先选择新驾驶员",
      unbind: "解绑",
      change: "更改",
      cancel: "取消",
    },
  },
  multipleImport: {
    label: {
      multipleImport: "批量导入",
      noData: "暂无数据",
      index: "序号",
      group: "单位",
      plateNoAlias: "车牌号",
      plateColor: "车牌颜色",
      terminalTypes: "终端类型名",
      mainTerminalNo: "主终端设备号",
      mainSimCode: "主终端ICCID卡号",
      status: "状态",
      operate: "操作",
      chooseFilePlz: "选择文件",
      confirm: "确认导入",
      exportBaseTemplate: "导出车辆范本",
      clear: "清空",
      export: "导出",
      waitForImport: "待导入",
      importSuccess: "导入成功",
      importFailure: "导入失败",
      parseFailure: "解析失败",
      systemError: "系统错误",
      carData: "车辆数据",
      transIndustry: "运输行业",
      vehicleIndustry: "车辆行业",
      vehicleType: "车辆类型",
      vin: "车辆VIN",
      channelCount: "视频通道数(n)",
    },
    expression: ["共{0}条数据", "解析成功，共解析{0}条数据"],
    messageInfo: ["没有需要导入的数据", "导入车辆_范本", "没有可以导出的数据"],
  },
  driverMgt: {
    label: {
      choosePlease: "请选择",
      phone: "登录手机号",
      identityNo: "身份证号",
      name: "姓名",
      keyPlease: "请输入关键字",
      noData: "暂无数据",
      index: "序号",
      group: "所属分组",
      sex: "性别",
      workNo: "工号",
      age: "年龄",
      driverLicenseValidate: "驾照有效期",
      checkStatus: "审核状态",
      bindedCar: "绑定车辆",
      "white&blackList": "黑白名单",
      operate: "操作",
      modify: "修改",
      del: "删除",
      bind: "绑定",
      search: "查询",
      add: "新增",
      male: "男",
      female: "女",
      checkUnpass: "审核未通过",
      uncheck: "未审核",
      checkSuccess: "审核通过",
      checkResign: "已离职",
      completed: "已完成",
      deleted: "已删除",
      whiteList: "白名单",
      blackList: "黑名单",
      unset: "未设置",
      tip: "提示",
      confirm: "确定",
      cancel: "取消",
      delSuccess: "删除成功",
    },
    messageInfo: ["此操作不可恢复，确认删除司机?"],
  },
  driverInfo: {
    label: {
      driverDetails: "驾驶员详情",
      phone: "手机号码",
      phonePlz: "请输入手机号",
      group: "归属",
      deptChoosePlease: "请选择归属部门",
      name: "姓名",
      workNo: "司机工号",
      checkStatus: "审核状态",
      choosePlease: "请选择",
      photo: "照片",
      identifyPhoto: "人脸识别头像",
      "white&blackList": "黑白名单",
      unset: "未设置",
      blackList: "黑名单",
      whiteList: "白名单",
      remark: "备注",
      identifyNo: "身份证号码",
      identifyPic: "身份证照片",
      workLicense: "从业资格证",
      awardDate: "发证日期",
      selectDatePlz: "选择日期",
      validateStart: "有效期起",
      validateEnd: "有效期止",
      licenseStatus: "证照状态",
      valid: "有效",
      invalid: "失效",
      licenseOrganization: "核发机关",
      licensePicLeft: "证件照片（左页）",
      licensePicRight: "证件照片（右页）",
      driverLicense: "驾驶证",
      driverLicenseNo: "驾驶证号",
      driverLicenseAwardDate: "初次领证日期",
      allowedCarType: "准驾车型",
      licenseFrom: "发证机构",
      driverLicensePics: "驾驶证照片",
      extraInfo: "额外信息",
      profession: "职业",
      birthDate: "生日",
      address: "联系地址",
      sex: "性别",
      age: "年龄",
      male: "男",
      female: "女",
      cancel: "取消",
      addDriver: "新增司机",
      addConfirm: "确认新增",
      modifyDriver: "修改司机",
      modifyConfirm: "确认修改",
      checkUnpass: "审核未通过",
      uncheck: "未审核",
      checkSuccess: "审核通过",
      checkResign: "已离职",
      namePlz: "请输入姓名",
      addSuccess: "添加成功",
      modifySuccess: "修改成功",
      workStartDate: "入职时间",
      workEndDate: "离职时间",
    },
    messageInfo: [
      "请输入司机手机号码",
      "请选择所属部门",
      "请输入身份证号码",
      "请先输入姓名",
      "请选择企业或者车队（部门）级别！",
      "添加司机信息成功, 暂未添加FaceID信息, 您可以稍后进行修改操作",
      "修改司机信息成功, FaceID信息暂未修改, 您可以稍后修改该项内容",
    ],
  },
  carBind: {
    label: {
      carBind: "车辆绑定",
      currentBinded: "当前绑定：",
      unbound: "未绑定",
      unbind: "解除绑定",
      changeBinding: "更改绑定：",
      confirm: "确认绑定",
      cancel: "取消",
    },
    expression: [],
    messageInfo: [
      "车辆绑定成功",
      "车辆绑定失败",
      "车辆解绑成功",
      "车辆解绑失败",
    ],
  },
  SelectTreeInput: {
    label: {
      confirm: "确定",
      clear: "清除选中",
      vehicle: "辆",
    },
    expression: ["已选择 {0} {1}"],
  },
  imageUploaderGroup: {
    label: {
      disabled: "已禁用",
    },
  },
  imageUploader: {
    label: {
      disabled: "已禁用",
    },
    expression: ["点击选择文件\r\n（{0}M以内）", "正在上传，已完成{0}%"],
    messageInfo: ["上传失败，请重试!", "上传文件失败", "上传文件超过大小限制"],
  },
  operatingReport: {
    label: {
      areaPlz: "请选择区域",
      to: "至",
      m3: "方",
      index: "序号",
      isRegular: "性质",
      area: "所属区域",
      vehicle: "运输车辆数",
      search: "查询",
      export: "导出",
      last7days: "近七天",
      thisWeek: "本周",
      lastWeek: "上周",
      thisMonth: "本月",
      lastMonth: "上月",
      totalWork: "工地总数",
      totalAbsorption: "消纳场总数",
      validWork: "有效工地数",
      operatingWork: "作业工地数",
      operatingAbsorption: "作业消纳场",
      out: "出土",
      in: "消纳",
      workSite: "工地",
      absorption: "消纳场",
    },
    expression: [
      "总{0}量",
      "合规{0}量",
      "最大{0}量",
      "平均{0}量",
      "{0}名称",
      "{0}次数",
      "{0}量（方）",
      "{0}天数",
      "最近{0}时间",
      "{0}详情",
    ],
    messageInfo: ["有效消纳场数", "疑似黑工地/消纳场数"],
    StationOperationReport: "场站作业报表",
  },
};
