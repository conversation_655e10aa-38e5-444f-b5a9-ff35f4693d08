/**
 * Create by <<EMAIL>> on 2019-03-29 10:03:04
 * Introduce 主要的state
 */
 import Vue from 'vue';
 import {axios} from '@/axios/api.js'
 
 const state = {
     //公司名称 取决于访问路径的前缀
     company: 'pony',
     changeNameList: {
      'sgs-cspc':'sgs_cspc'
     },
     //locale i18n 每个国家对应的语言Locale和国家代码对照表https://www.cnblogs.com/jacksoft/p/5771130.html
     locale: 'zh-CN',
     //最外层的loading
     loading: false,
     //打开的页面列表 Array<VueRouteInstance>
     aliveView: [],
     //对讲机功能 对讲目标信息
     talkBackInfo: {
         vehicle: 'asdasda',
         terminalId: '41244312312'
     },
     scrollObj:{},
     customData: {},
     theme:"",
     //进首页,是否需要加载大屏,如果服务器有配置的话 第一次登录有效
     homeOpen:true  
 }
 const mutations = {
     setCustomData(state, value) {
         state.customData = value
     },
     setCompany(state, value) {
         state.company = value;
     },
     toggleLoading(state, value) {
         if (value !== undefined) {
             state.loading = value;
         } else {
             state.loading = !state.loading;
         }
     },
     setHomeOpen(state, value){
        state.homeOpen = value;

     },
     setTheme(state,value){
        state.theme = value
     },
     setAliveViewList(state, value) {
         state.aliveView = value;
     },
     setScrollObj(state, value) {
      state.scrollObj = value;
    },
     setTalkBackInfo(state, value) {
         state.talkBackInfo = value;
     },
     setLocale(state, value) {
         state.locale = value;
         axios.defaults.headers.post['Content-Language'] = value;
         document.documentElement.setAttribute('lang', value);
     },
     
 
 }
 const actions = {
     //变更用户时需要清除用户相关的vuex数据
     clearUserCache({dispatch, commit}, value) {
         commit('setAliveViewList', []);
         //清除其他namespace中的内容
         commit('ztreeData/clearAll', null, {root: true});
         commit('auth/setUserInfo', null, {root: true});
     }
 }
 
 const getters = {
     getCustomSetting(state) {
         const sourceData = JSON.parse(JSON.stringify(state.customData))
         let currentData
         if(sourceData.language) {
             let languageSource = JSON.parse(JSON.stringify(sourceData.language[state.locale]))
             let keys = Object.keys(languageSource)
             keys.forEach(item => {
                 Object.assign(sourceData[item], languageSource[item])
             })
             delete sourceData.language
             currentData = sourceData
         } else {
             currentData = sourceData
         }
         return currentData
     },
     
     need2CheckSim(state) {
         return !!(['pony', 'clzcl'].indexOf(state.company) + 1)
     }
 }
 
 export default {
     namespaced: true,
     actions,
     state,
     mutations,
     getters,
 }
