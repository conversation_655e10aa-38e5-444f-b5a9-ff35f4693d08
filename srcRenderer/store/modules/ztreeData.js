/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-04-03 13:54:37
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-08-26 09:55:12
 * @Description:
 * ①各种树的数据缓存 第一次调用getTreeData action时会从服务器拉取，之后会直接返回缓存数据,
 * ②可同时调用多个action，针对同一个type只会调用一次api
 *
 *  2020-04-29 14:27:43  应业务需求取消缓存功能
 */
import * as api from "../../axios/api";
import * as utils from "@/util/common";
import store from "../../store";
import { getDataItemTreeSuperFleet, getProjectTree, getRentalVehicle, getRentalVehicleTree } from "../../axios/api";
const request = {
  refreshDataInfo: async (type) => {
    let res, temp;
    let typeKey = type.split(":");
    switch (typeKey[0]) {
      case "follow": //  关注树
        res = await api.operateUserVehicleFllow({ operate_type: 2 });
        break;
      case "company": //  企业数
        temp = await api.getTreeDepartment();
        res = utils.filterTree([temp], "children", function (item) {
          return item.type <= 2;
        })[0];
        break;
      case "department": //  部门树
        res = await api.getTreeDepartment();
        break;
      case "vehicle": //  车辆数
        res = await api.getTreeDepartmentAndVehicle({
          page: "carExpirationTime",
        });
        break;
      case "vehicleERP": //  车辆数,过滤只有商砼的车
        res = await api.getTreeDepartmentAndVehicle({
          page: "seniorDispatch",
        });
        break;
      case "vehicleRoot":
        res = await api.getTreeDepartmentAndVehicle({
          page: "FenseMonitor",
        });
        break;

      case "vehicleWithChannel": //  车辆+通道号
        if (request["vehicle"]) {
          res = JSON.parse(JSON.stringify(request["vehicle"]));
        } else {
          res = await api.getTreeDepartmentAndVehicle();
        }

        // temp = utils.flatTree([res]);
        // temp.forEach((item) => {
        //   if (item.type !== 4) {
        //     return;
        //   }
        //   let channelList = []
        //   item.channelValid.split('').forEach((chanl, index) => {
        //       if(+chanl == 1) {
        //           channelList.push(index + 1)
        //       }
        //   })
        //   item.children = new Array(item.channelCount)
        //     .fill(0)
        //     .map((child, index) => {
        //       // let chn = index + 1;
        //       return {
        //         id: item.id + "chn" + channelList[index],
        //         name: "通道" + channelList[index],
        //         chn:channelList[index],
        //         type: 5,
        //         iconSkin: "channl",
        //       };
        //     });
        // });
        break;
      case "menuTable": //  菜单树
        res = await api.getMenuTableTree();
        break;
      case "permission": //  权限树
        res = await api.getPermissionTree();
        break;
      case "temperatureTree": //  权限树
        res = await api.getTreeEquipmentInfo();
        break;
      case "temperatureChannelTree": //  权限树
        res = await api.getTreeEquipmentInfo({
          page: "temperatureQuery",
        });
        break;
      case "userCompany": //  企业数（层级到企业）
        res = await api.getTreeUserCompany();
        break;
      case "clearCompany": //  企业数（层级到企业）
        res = await api.getTreeUserCompany({
          page: "clearWaybillMgt",
        });
        break;
      case "departmentTable":
        res = await api.getAllDeptTableByVue();
        break;
      case "approval": //[0]审批业务大类, [1]树类型key, [2]区域参数, [3]用户参数
        res = await api.getPassApprovalTree({
          key: typeKey[1],
          area_id: typeKey[2],
          user_id: typeKey[3],
        });
        break;
      case "area": //  区域树
        res = await api.getTreeArea();
        break;
      case "areaWithXiaoNaAndWorkSite": //  区域含消纳场和工地
        res = await api.getTreeAreaWithBusiness();
        [res] = utils.filterTree([res], "children", function (node) {
          return node.type === 6 || node.children.length;
        });
        break;
      case "transportArea":
        res = await api.getTreeAreaWithBusiness();
        [res] = utils.filterTree([res], "children", function (node) {
          return node.type === 6 || node.children.length;
        });
        [res] = utils.filterTree([res], "children", function (node) {
          return node.type <= 4;
        });
        break;
      case "treeBusinessArea": //  业务区域带参, 传参示例[treeBusinessArea:workSiteMgt:0:0:-1]
        res = await api.getTreeAreaTerminal({
          web_page_key: typeKey[1], // 区域类型,如:workSiteMgt(工地), xiaoNaMgt(消纳场)
          fill_unknow: typeKey[2], // 传1最上层多一个未知节点, 默认0
          get_stop: typeKey[3], // 传1查询停用的业务区域数据, 默认0
          protocol: typeKey[4], // 按协议类型过滤业务区域下的终端,如:128(GB28181), 140(GA1400)
        });
        break;
      case "workSiteUnknow": //  未知工地
        res = await api.getTreeAreaTerminal({
          web_page_key: "workSiteMgt",
          fill_unknow: 1,
          get_stop: 1,
        });
        break;
      case "xiaoNaUnknow": //  未知消纳场
        res = await api.getTreeAreaTerminal({
          web_page_key: "xiaoNaMgt",
          fill_unknow: 1,
          get_stop: 1,
        });
        break;
      case "workSite": //  工地
        res = await api.getTreeAreaTerminal({
          web_page_key: "workSiteMgt",
          fill_unknow: 0,
        });
        break;
      case "workSiteBlack": //  工地(黑白名单)
        res = await api.getTreeAreaTerminal({
          web_page_key: "workSiteMgt",
          fill_unknow: 0,
          black: 1,
        });
        break;
      case "workSitePass": //  工地
        res = await api.getTreeAreaTerminal({
          web_page_key: "workSiteMgt",
          fill_unknow: 0,
          protocol: "140,124",
        });
        break;
      case "workSiteVideo": //  工地
        res = await api.getTreeAreaTerminal({
          web_page_key: "workSiteMgt",
          fill_unknow: 0,
          protocol: "128,124",
        });
        break;
      case "xiaoNa": //  消纳场
        res = await api.getTreeAreaTerminal({
          web_page_key: "xiaoNaMgt",
          fill_unknow: 0,
        });
        break;
      case "xiaoNaBlack": //  消纳场(黑白名单)
        res = await api.getTreeAreaTerminal({
          web_page_key: "xiaoNaMgt",
          fill_unknow: 0,
          black: 1,
        });
        break;
      case "xiaoNaPass": //  消纳场
        res = await api.getTreeAreaTerminal({
          web_page_key: "xiaoNaMgt",
          fill_unknow: 0,
          protocol: "140,124",
        });
        break;
      case "xiaoNaVideo": //  消纳场
        res = await api.getTreeAreaTerminal({
          web_page_key: "xiaoNaMgt",
          fill_unknow: 0,
          protocol: "128,124",
        });
        break;
      case "cloudCrossTraffic":
        res = await api.getTreeAreaTerminal({
          web_page_key: "cloudCrossMgt",
          fill_unknow: 0,
        });
        break;
      case "changzhan":
        res = await api.getTreeAreaTerminal({
          web_page_key: "placeMgt",
          fill_unknow: 0,
        });
        break;
      case "transportPoints":
        res = await api.getTreeAreaTerminal({
          web_page_key: "shippingPointMgt",
          fill_unknow: 1,
          get_stop: 1,
        });
        break;
      //苍南环卫区域树
      case "collectLine":
        res = await api.getTreeAreaTerminal({
          web_page_key: "collectLineMgt",
          fill_unknow: 0,
        });
        break;
      case "collectArea":
        let resData = await api.wasteCollectArea({
          operateType: 21,
          point: false,
          line: false,
        });
        res = resData.data;
        break;
      case "collectAreaLine":
        let resDataLine = await api.wasteCollectArea({
          operateType: 21,
          point: false,
          line: true,
        });
        res = resDataLine.data;
        break;
      case "collectAreaPoint":
        let resDataPoint = await api.wasteCollectArea({
          operateType: 21,
          point: true,
          line: false,
        });
        res = resDataPoint.data;
        break;
      case "areaTable":
        res = await api.getAreaTableTree();
        break;
      case "terminal": //  终端
        res = await api.getTerminalNoAndType();
        break;
      case "fenceTree": // 围栏
        res = await api.getTreeFenseInfo();
        break;
      case "poiTree": //  兴趣点
        res = await api.getTreeMopPoi();
        break;
      case "fenceRule": //  围栏规则
        res = await api.getTreeExRuleInfo();
        break;
      case "userTree": //  账号
        res = await api.getSysUserTree();
        break;
      case "driverTree": //  司机
        res = await api.getTreeDriver();
        break;
      case "roadTree": //  道路
        res = await api.getTreeRoadInfo();
        res = res.data;
        break;
      case "lineTree": //  线路
        res = await api.getTreeVehicleLine();
        break;
      case "menuTree": //  菜单
        res = await api.getMenuTree({ version: 3 });
        break;
      case "roleTree": //  规则
        res = await api.getTreeRoleMenu();
        break;
      case "fenseTreeNew": //  新围栏树 （yes）
        res = await api.getFenseInfoTreeAssetsV2();
        break;
      case "xiaqutree": //  辖区智障树 （yes）
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 16 });
        break;
      case "jiaobanzhantree": //
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 18 });
        break;
      case "deptFenseTree": //  部门围栏树
        res = await api.getPassAreaFenseTree({ version: 2 });
        break;
      case "deptFenseTreeV3": //  部门围栏树
        res = await api.getPassAreaFenseTree({ version: 3 });
        break;
      case "parkingFenseTree": //  停车场围栏树
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 13 });
        break;
      case "limitFenseTree": //  限速圈围栏树
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 9 });
        break;
      case "transportPointsFenseTree": //  运输点围栏树
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 19 });
        break;

      case "selectLinkUp":
        res = await api.selectLinkUpParamTree();
        break;
      case "zhongdui": //  中队树
        res = await api.getPassApprovalTree({
          key: "APPROVAL_JURISDICTION",
          user_id: store.state.auth.userInfo.id,
        });
        break;
      case "xianxingqu":
        res = await api.getPassApprovalTree({
          key: "APPROVAL_LIMIT_AREA",
          user_id: store.state.auth.userInfo.id,
        });
        break;
      case "xianxingquCross":
        res = await api.getPassApprovalTree({
          key: "APPROVAL_LIMIT_AREA",
          user_id: store.state.auth.userInfo.id,
          mount: "crossReport",
        });
        break;
      case "xiaquMgt": //  限行区树
        res = await api.getPassApprovalTree({
          key: "APPROVAL_JURISDICTION_V2",
        });
        break;
      case "xiaquMgtCross":
        res = await api.getPassApprovalTree({
          key: "APPROVAL_JURISDICTION_V2",
          user_id: store.state.auth.userInfo.id,
          mount: "crossReport",
        });
        break;
      case "mixingStationTree": // 搅拌站树
        res = await api.getMixingStationTree();
        break;
      case "vehicleType": //车辆类型树
        res = await api.getCommonListByKey({
          key: "collect_vehicle_type",
        });
        break;
      case "getDataItemTreeSuperFleet": // 福田车辆数据明细表格配置项
        res = await api.getDataItemTreeSuperFleet({
          energyType: typeKey[1] ? typeKey[1] : 0,
        });
        break;
      case "getDataItemTreeTableConfig": // 福田车辆轨迹表格配置左侧树
        res = await api.getCommonListByKey({
          key: "history_gps_business_tree",
        });
        break;
      case "project": // 查询工程项目树
        res = await api.getProjectTree({
          isProject: true,
        });
        break;
      case "projectOnly": // 查询工程项目树(仅展示搅拌站)
        res = await api.getProjectTree({
          isProject: true,
          isProjectWork: true,
        });
        break;
      case "rentalVehicle": // 租赁车辆树
        res = await api.getRentalVehicleTree();
        break;
      case "userVehicle": // 租赁车辆树
        res = await api.treeUserVehicleGroup();
        break;
      case "logTree": // 操作日志树
        res = await api.getLogTree();
        break;
      case "collectPointTree": //收集点树
        let resobj = await api.wasteCollectArea({
          operateType: 21,
          point: true,
          line: false,
        });
        res = resobj.data;
        break;
      case "circleFenceTree": //圆形围栏树
        res = await api.getFenseInfoTreeAssetsV2({
          fense_shape_list: [1],
        });
        break;
      case "polygonFenceTree": //多边形形围栏树
        res = await api.getFenseInfoTreeAssetsV2({
          fense_shape_list: [0, 2],
          point_num_max: 10,
        });
        break;
      case "pointTerminal": //收集点挂终端树
        res = await api.areaTerminalTree({
          type: 34,
          terminal: 1,
        });
        break;
      case "pointTerminalWorksite": //收集点挂终端树
        res = await api.areaTerminalTree({
          type: 34,
          terminal: 1,
          protocol: "128,124",
        });
        break;
      case "pointTerminalPass": //收集点挂终端树
        res = await api.areaTerminalTree({
          type: 34,
          terminal: 1,
          protocol: "140,124",
        });
        break;
      case "unPointTerminal": //收集点不挂终端树
        res = await api.areaTerminalTree({
          type: 34,
        });
        break;
      case "stopTerminal": //停车场挂终端树
        res = await api.areaTerminalTree({
          type: 13,
          terminal: 1,
        });
        break;
      case "stopTerminalWorksite": //停车场挂终端树
        res = await api.areaTerminalTree({
          type: 13,
          terminal: 1,
          protocol: "128,124",
        });
        break;
      case "stopTerminalPass": //停车场挂终端树
        res = await api.areaTerminalTree({
          type: 13,
          terminal: 1,
          protocol: "140,124",
        });
        break;
      case "unStopTerminal": //停车场不挂终端树
        res = await api.areaTerminalTree({
          type: 13,
        });
        break;
      //设备数通用,传参数不挂终端版
      case "areaTerminalTree": //停车场不挂终端树
        res = await api.areaTerminalTree({
          type: typeKey[1],
        });
        break;
      case "wayBillTableConfig": //建华运单表格配置树
        res = await api.getCommonListByKey({
          key: "jianhua_trans_tree",
        });
        break;
      case "fenseTableConfig": //建华围栏采集表格配置树
        res = await api.getCommonListByKey({
          key: "fense_info_tree",
        });
        break;
      case "dumpPointsFenseTree": //  倾倒点围栏树
        res = await api.getFenseInfoTreeAssetsV2({ fense_type: 33 });
        break;
      case "wayBillLineTree": //中外运长途运单线路树
        res = await api.getFenseInfoTreeAssetsV2({ fense_shape: 3 });
        break;
      //围栏树参数类型
      case "fenseParams":
        res = await api.getFenseInfoTreeAssetsV2({
          fense_type: Number(typeKey[1]),
        });
        break;

      // 区域围栏树
      case "areaFenceTree":
        res = await api.getAreaFenceTree({
          fenseTypes: [],
        });
        break;
      // 区域围栏树(仅展示路口)
      case "areaRoadFenceTree":
        res = await api.getAreaFenceTree({
          fenseTypes: [4],
        });
        break;
    }

    request[type] = res;
    return res;
  },

  getTypeTree: async (type) => {
    if (request[type]) {
      return request[type];
    }
    let res = await request.refreshDataInfo(type);
    return res;
  },
  clear(type) {
    request[type] = null;
  },
};

const state = {
  treeData: {
    follow: {
      loading: false,
      data: null,
    },
    workSiteVideo: {
      loading: false,
      data: null,
    },
    xiaoNaVideo: {
      loading: false,
      data: null,
    },
    stopTerminal: {
      loading: false,
      data: null,
    },
    pointTerminal: {
      loading: false,
      data: null,
    },
    pointTerminalWorksite: {
      loading: false,
      data: null,
    },
    pointTerminalPass: {
      loading: false,
      data: null,
    },
    stopTerminalWorksite: {
      loading: false,
      data: null,
    },
    stopTerminalPass: {
      loading: false,
      data: null,
    },
    menuTree: {
      loading: false,
      data: null,
    },
    roadTree: {
      loading: false,
      data: null,
    },
    lineTree: {
      loading: false,
      data: null,
    },
    department: {
      loading: false,
      data: null,
    },
    userVehicle: {
      loading: false,
      data: null,
    },
    vehicle: {
      loading: false,
      data: null,
    },
    vehicleWithChannel: {
      loading: false,
      data: null,
    },
    menuTable: {
      loading: false,
      data: null,
    },
    departmentTable: {
      loading: false,
      data: null,
    },
    permission: {
      loading: false,
      data: null,
    },
    area: {
      loading: false,
      data: null,
    },
    areaTable: {
      loading: false,
      data: null,
    },
    terminal: {
      loading: false,
      data: null,
    },
    fenceTree: {
      loading: false,
      data: null,
    },
    poiTree: {
      loading: false,
      data: null,
    },
    fenceRule: {
      loading: false,
      data: null,
    },
    userTree: {
      loading: false,
      data: null,
    },
    driverTree: {
      loading: false,
      data: null,
    },
    fenseTreeNew: {
      loading: false,
      data: null,
    },
    deptFenseTree: {
      loading: false,
      data: null,
    },
    selectLinkUp: {
      loading: false,
      data: null,
    },
    vehicleRoot: {
      loading: false,
      data: null,
    },
    mixingStationTree: {
      loading: false,
      data: null,
    },
    getDataItemTreeSuperFleet: {
      loading: false,
      data: null,
    },
    clearCompany: {
      loading: false,
      data: null,
    },
    userCompany: {
      loading: false,
      data: null,
    },
    collectPointTree: {
      loading: false,
      data: null,
    },
    dumpPointsFenseTree: {
      loading: false,
      data: null,
    },
    wayBillLineTree: {
      loading: false,
      data: null,
    },
    collectArea: {
      loading: false,
      data: null,
    },
  },
  timer: null,
  treeFlag: false,
  changeTreeList: null,
};

const mutations = {
  setTreeData(state, { key, value }) {
    if (!state.treeData[key]) return;
    state.treeData[key].data = value;
  },
  toggleTreeLoading(state, { key, value }) {
    if (!state.treeData[key]) return;
    state.treeData[key].loading = value === undefined ? !state[key].loading : value;
  },
  clearAll(state) {
    for (let [key, value] of Object.entries(state.treeData)) {
      state.treeData[key].data = null;
      request[key] = null;
    }
  },
  changeTimer(state, value) {
    state.timer = value;
  },
  changeTreeFlag(state, value) {
    state.treeFlag = value.type;
    state.changeTreeList = value.value;
  },

  // clearTree(state,type){
  //     if(treeCount[type]>1){
  //       treeCount[type]--
  //     }else {
  //       treeCount[type] = 0
  //       request[type] = null;
  //     }
  // }
};
const actions = {
  treeVehicleChange({ state, commit, dispatch }) {
    // if (!(request["vehicle"] || request["vehicleWithChannel"])) return;
    // if(store.state.auth.loginState){
    //     clearTimeout(state.timer)
    //     commit("changeTimer", null);
    // }
    if (state.timer) return;
    let timer = setInterval(async () => {
      if (store.state.auth.loginState) {
        clearTimeout(state.timer);
        commit("changeTimer", null);
        return;
      }
      let res = await api.treeVehicleChange({
        duration: 300,
        version: 2,
      });
      if (res && res.status == 200) {
        if (res.data.modify) {
          // await dispatch('refreshTreeData','vehicle')
          // await dispatch('refreshTreeData','vehicleWithChannel')
          commit("changeTreeFlag", {
            type: true,
            value: res.data.list,
          });
        }
      }
    }, 5 * 60 * 1000);
    commit("changeTimer", timer);
  },
  async getTreeData({ state, commit, dispatch }, params) {
    let type,
      fromServer = true;
    let treeType = [
      "menuTable",
      "areaTable",
      "departmentTable",
      "xianxingqu",
      "xiaquMgt",
      "project",
      "projectOnly",
      "jiaobanzhantree",
      "parkingFenseTree",
      "department",
      "fenceRule",
    ]; //这几个树需要走缓存，所以下面做了一下判断。
    if (typeof params === "string") {
      type = params;
    } else if (typeof params === "object") {
      type = params.type;
      fromServer = params.fromServer;
    } else {
      throw new Error("params must be String or Object");
    }
    if (!fromServer && state.treeData[type] && state.treeData[type].data) {
      return state.treeData[type].data;
    } else {
      if (treeType.includes(type)) {
        commit("toggleTreeLoading", { key: type, value: true });
        commit("setTreeData", { key: type, value: [] });
        fromServer && request.clear(type);
        let result = await request.getTypeTree(type);
        commit("setTreeData", { key: type, value: result });
        commit("toggleTreeLoading", { key: type, value: false });
        if (type == "vehicle" || type == "vehicleWithChannel") {
          dispatch("treeVehicleChange");
        }
        return result;
      } else {
        let result = await request.getTypeTree(type);
        if (type == "vehicle" || type == "vehicleWithChannel") {
          dispatch("treeVehicleChange");
        }
        return result;
      }
    }
  },
  async refreshTreeData({ state, commit, dispatch }, type) {
    // await dispatch('initTreeData', type);
    let res = await request.refreshDataInfo(type);
    return res;
  },
};
export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
