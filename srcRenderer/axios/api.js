import { get, post, postForm, postWithoutTime, customInstance, getWithoutTime, importTable } from "./customAxios";

// post(url,boolen) 第二个参数是, 为true时,第二次请求会打断第一次请求的axios

export { get, post } from "./customAxios";
export const axios = customInstance;
//终端属性查询保存接口
export const saveTerminalInfo = post("/ponysafety2/a/message/saveTerminalInfo");
//车辆行程报表
export const vehicleTripReport = post("/ponysafety2/a/report/superfleet/trip/stats");
//高级调度设置车辆状态
export const setERPVehicleState = post("/ponysafety2/a/erp/setERPVehicleState");
//高级调度数据
export const getLastERPState = post("/ponysafety2/a/erp/getLastERPState");
//报警统计查询i
export const getAlarmStatsData = post("/ponysafety2/a/report/getAlarmStatsData");
//维修单管理
export const operateDeviceRepairBill = post("/ponysafety2/a/device/operateDeviceRepairBill");
//维修单查询
export const queryDeviceRepairBill = post("/ponysafety2/a/device/queryDeviceRepairBill");
//按角色查找用户
export const queryOperatorByRole = post("/ponysafety2/a/device/queryOperatorByRole");
//安装单审核
export const operateDeviceInstallBill = post("/ponysafety2/a/device/operateDeviceInstallBill");
//新增项目
export const operateDeviceInstallProject = post("/ponysafety2/a/device/operateDeviceInstallProject");
//安装工单详情查询
export const queryDeviceInstallBill = post("/ponysafety2/a/device/queryDeviceInstallBill");
//安装工单项目查询
export const queryDeviceInstallProject = post("/ponysafety2/a/device/queryDeviceInstallProject");
//设备温度明细查询
export const getEquipmentTempDetail = post("/ponysafety2/a/temp/getEquipmentTempDetail");
//温度计报警处理
export const handleEquipmentAlarm = post("/ponysafety2/a/temp/handleEquipmentAlarm");
//温度计报警查询
export const getEquipmentAlarm = post("/ponysafety2/a/temp/getEquipmentAlarm");
//温度计树
export const getTreeEquipmentInfo = post("/ponysafety2/a/temp/getTreeEquipmentInfo");
//实时监控（温度计最后信息）
export const getLastEquipmentInfo = post("/ponysafety2/a/temp/getLastEquipmentInfo");
//设备管理
export const operateSysEquipmentInfo = post("/ponysafety2/a/temp/operateSysEquipmentInfo");
//场地容量报警处理
export const handleTransAlarmCapacity = post("/ponysafety2/a/hireport/handleTransAlarmCapacity");
//场地容量报警查询
export const queryTransAlarmCapacity = post("/ponysafety2/a/hireport/queryTransAlarmCapacity");
//企业处置证报警统计
export const queryTransAlarmDept = post("/ponysafety2/a/hireport/queryTransAlarmDept");
//车辆处置证报警统计
export const queryTransAlarmVehicle = post("/ponysafety2/a/hireport/queryTransAlarmVehicle");
//处置证管理
export const operatePassMuckTrans = post("/ponysafety2/a/hireport/operatePassMuckTrans");
//处置证查询
export const getMuckMgt = post("/ponysafety2/a/hireport/queryPassMuckTrans");
//管理宣传文章图片上传
export const fileupload = post("/ponysafety2/a/fileupload/common_20210412");
//图片上传
export const fileImgUpload = post("/ponysafety2/a/fileupload/common");
//管理宣传文章
export const operateArticle = post("/ponysafety2/a/ningde/operateArticle");
//获取宣传文章列表
export const getArticle = post("/ponysafety2/a/ningde/getArticle");
//获取配置节点层级
export const precinctLevel = post("/ponysafety2/a/jurisdiction/precinctLevel");
//删除车辆白名单
export const deleteVehicleWhitelist = post("/ponysafety2/a/jurisdiction/deleteVehicleWhitelist");
//白名单校验
export const vehicleWhitelist = post("/ponysafety2/a/jurisdiction/vehicleWhitelist");
//新增白名单
export const addVehicleWhitelist = post("/ponysafety2/a/jurisdiction/addVehicleWhitelist");
//修改/删除辖区
export const updateJurisdiction = post("/ponysafety2/a/jurisdiction/updateJurisdiction");
//新增辖区
export const newJurisdiction = post("/ponysafety2/a/jurisdiction/newJurisdiction");
//辖区管理查询
export const getJurisdiction = post("/ponysafety2/a/jurisdiction/queryJurisdiction");
//处理
export const temperatureAlarmsDeal = post("/ponysafety2/a/temp/alarms/deal");
//温度报警监控
export const temperatureAlarms = post("/ponysafety2/a/temp/alarms");
// 温度报警监控汇总
export const temperatureAlarmsSummary = post("/ponysafety2/a/temp/alarms/area");
//温度明细查询
export const temperatureDetails = get("/ponysafety2/a/temp/timeRange");

//查询当前温度配置
export const getTempConfiguration = post("/ponysafety2/a/temp/getTempConfiguration");
//新增/修改温度配置
export const updateTempConfiguration = post("/ponysafety2/a/temp/updateTempConfiguration");
//出车详情
export const scheduledTripStatistics = post("/ponysafety2/a/dispatching/scheduledTripStatistics");
//新增调度管理
export const newDispatchingManagement = post("/ponysafety2/a/dispatching/newDispatchingManagement");
//调度管理查询
export const getDispatchingManagement = post("/ponysafety2/a/dispatching/getDispatchingManagement");
//停运历史详情
export const outageHistoryDetails = post("/ponysafety2/a/activiti_v3/outageHistoryDetails");
//停运历史查询
export const outageHistory = post("/ponysafety2/a/activiti_v3/outageHistory");
//车辆红码查询
export const redCodeCheck = post("/ponysafety2/a/activiti/redCodeCheck");
//日期查询
export const validityCheck = post("/ponysafety2/a/activiti_v3/validityCheck");
//流量使用查询
export const flowUseQuery = post("/ponysafety2/a/sim/getPlatformTerminalTraffic");
//左右转弯限速配置
export const sendTurnSpeed = post("/ponysafety2/a/message/sendTurnSpeed");
//企业安全码查询
export const getSeurityCodeQuery = post("/ponysafety2/a/activiti/trafficCompanyPoliceScoreQuery");
// 停车场进出查询
export const getParkAreaIO = post("/ponysafety2/a/report/queryParkAreaIO");
// 通行证单条和批量撤销
export const removePassApplyBack = post("/ponysafety2/a/passApproval/removePassApplyBack");
// 联系我们内容传递
export const sendContent = post("/ponysafety2/a/sysconfig/operateBusinessContactUs");
// 批量通过获取状态
export const getMoreStatus = post("/ponysafety2/a/activiti/promptDuringApproval");
export const getMoreStatusV3 = post("/ponysafety2/a/activiti_v3/promptDuringApproval");

export const addSysUserMenu = get("/ponysafety2/a/usermenu/addSysUserMenu");
export const getSysUserMenu = post("/ponysafety2/a/usermenu/getSysUserMenu");
export const deleteSysUserMenu = post("/ponysafety2/a/usermenu/deleteSysUserMenu");
export const getSysUserMenuAll = post("/ponysafety2/a/usermenu/getSysUserMenuAll");
export const getSysUserMenuByDict = get("/ponysafety2/a/usermenu/getSysUserMenuByDict");

export const getUnReadMsgCount = get("/ponysafety2/a/message/getunreadmsgcount");
export const getMsgList = post("/ponysafety2/a/message/selectmessagelist");
export const getMsgInfoAll = post("/ponysafety2/a/message/selectmessagelist");
export const getMsgDetailAll = get("/ponysafety2/a/message/selectmessage");
export const updateMsgList = post("/ponysafety2/a/message/updatemessage");
export const insertMessageFun = post("/ponysafety2/a/message/insertmessage");
export const insertOrBySysUserInfo = post("/ponysafety2/a/user/changesysuser");
export const login = post("/ponysafety2/login");
export const logout = get("/ponysafety2/loginout");
export const getPermission = get("/ponysafety2/a/demo/getPermission");
export const getSysUser = get("/ponysafety2/a/demo/getsysuser");
export const getAccessKey = post("/ponysafety2/a/map/getAccessKey");
export const getVehicleTerminalNo = get("/ponysafety2/a/common/getVehicleTerminalNo");
export const getTreeDepartment = get("/ponysafety2/a/common/getTreeDepartmentV2");
export const getTreeDepartmentAndVehicle = get("/ponysafety2/a/common/getTreeDepartmentAndVehicleV2");
export const getTreeDriver = get("/ponysafety2/a/common/getTreeDriver");
export const getDeptyDayReport = get("/ponysafety2/a/report/getdeptydayreport");
export const getDept30DayDriveScore = get("/ponysafety2/a/report/getdept30daydrivescore");
export const getDeptDriveScoreByDate = get("/ponysafety2/a/report/getdeptdrivescorebydate");
export const getDeptScoreormileByDate = get("/ponysafety2/a/report/getdeptscoreormilebydate");
export const insertAdminComment = post("/ponysafety2/a/report/insertadmincomment");
export const deleteCommentByid = get("/ponysafety2/a/report/deletecommentbyid");
export const getAdminComment = get("/ponysafety2/a/report/getadmincomment");
export const getCommentBy = get("/ponysafety2/a/report/getcommentby");
export const getCycleByUser = get("/ponysafety2/a/report/getcyclebyuser");
export const getMileVehicleReport = get("/ponysafety2/a/report/getmilevehiclereport");
export const getMileDriverReport = get("/ponysafety2/a/report/getmiledriverreport");
export const getVehiclemileReport = post("/ponysafety2/a/report/getvehiclemilereport");
export const getDrivermileReport = post("/ponysafety2/a/report/getdrivermilereport");
export const getFleetmileReport = post("/ponysafety2/a/report/getfleetmilereport");
export const getUserConfig = post("/ponysafety2/a/report/getuserconfig");
export const updateUserConfig = post("/ponysafety2/a/report/updateuserconfig");
export const getDeptScoreReport = get("/ponysafety2/a/report/getdeptscorereport");
export const getVehicleScoreReport = get("/ponysafety2/a/report/getvehiclescorereport");
export const getDriverScoreReport = get("/ponysafety2/a/report/getdriverscorereport");
export const getDeptAlarmReport = get("/ponysafety2/a/report/getdeptalarmreport");
export const getVehicleAlarmReport = get("/ponysafety2/a/report/getvehiclealarmreport");
export const getDriverAlarmReport = get("/ponysafety2/a/report/getdriveralarmreport");
export const getDeptcphsReport = get("/ponysafety2/a/report/getdeptcphsreport");
export const getVehiclecphsReport = get("/ponysafety2/a/report/getvehiclecphsreport");
export const getDrivercphsReport = get("/ponysafety2/a/report/getdrivercphsreport");
export const getAdasScoreReport = post("/ponysafety2/a/report/getadasscorereportV2");
export const getAlarmTypeDefList = get("/ponysafety2/a/report/getalarmtypedeflist");
export const getFleetByCompanyId = get("/ponysafety2/a/common/getFleetByCompanyId");
export const getDriverInfoByDeptId = get("/ponysafety2/a/common/getDriverInfoByDeptId");
export const getVehicleInfoByDeptId = get("/ponysafety2/a/common/getVehicleInfoByDeptId");
export const postAlarmCount = post("/ponysafety2/a/report/getalarmcount");
export const getAlarmCount = post("/ponysafety2/a/report/getalarmcount");
export const postGpsRecordInfo = post("/ponysafety2/a/report/getgpsrecordinfo");
export const getCompareInfoAll = post("/ponysafety2/a/user/getCompareInfoAll");
export const getCompareInfoByVehicle = post("/ponysafety2/a/user/getCompareInfoByVehicle");
export const getCompareInfoByDriver = post("/ponysafety2/a/user/getCompareInfoByDriver");
export const selectSysUserMenuByName = get("/ponysafety2/a/usermenu/selectSysUserMenuByName");
export const getDeptVehicleAlarmReport = get("/ponysafety2/a/report/getGpsAlarmByDept");
export const getVehicleLineDetail = post("/ponysafety2/a/report/getvehiclelinedetail");
export const getDriverRunStopInfo = post("/ponysafety2/a/report/getdriverrunstopinfo");
export const getDriverRunStopInfoD = post("/ponysafety2/a/report/getdriverrunstopinfoD");
export const getVehicleRunStopInfo = post("/ponysafety2/a/report/getvehiclerunstopinfo");
export const getVehicleRunStopInfoD = post("/ponysafety2/a/report/getvehiclerunstopinfoD");
export const selectSysConfigRunAndStop = get("/ponysafety2/a/sysconfig/selectsysconfigRunAndStop");
export const addSysConfigRunAndStop = post("/ponysafety2/a/sysconfig/addsysconfigRunAndStop");
export const getVehicleScheduleList = post("/ponysafety2/a/report/getvehicleScheduleList");
export const getVehicleScheduleListD = post("/ponysafety2/a/report/getvehicleScheduleListD");
export const getDriverScheduleList = post("/ponysafety2/a/report/getdriverScheduleList");
export const getDriverScheduleListD = post("/ponysafety2/a/report/getdriverScheduleListD");
export const getAdasAlamByVehicle = post("/ponysafety2/a/report/getAdasAlamByVehicle");
export const getAdasAlamByVehicleOver = post("/ponysafety2/a/report/getAdasAlamByVehicleOver");
export const getAdasAlamByVehicleDeal = post("/ponysafety2/a/report/getAdasAlamByVehicleDeal");
export const getAdasAlamByVehicleDealAll = post("/ponysafety2/a/report/getAdasAlamByVehicleDealAll");
export const getGpsAlamByVehicle = post("/ponysafety2/a/report/getGpsAlarmUnDeal");
export const getGpsAlamByVehicleOver = post("/ponysafety2/a/report/getGpsAlarmDeal");
export const getVehicleByFeeend = post("/ponysafety2/a/report/getVehicleFeeEndList");
export const updateVehicleFeeEndFun = post("/ponysafety2/a/vehicle/updatevehiclefeeend");
export const getVehicleIsLine = post("/ponysafety2/a/report/getVehicleIsLine");
export const getVehicleAlamAll = post("/ponysafety2/a/report/getVehicleAlamAll");
export const getAlarmEventRec = post("/ponysafety2/a/report/getAlarmEventRec");
export const getAlarmEventRecV2 = post("/ponysafety2/a/report/getAlarmEventRecV2");
export const getAlarmEventRecByTime = post("/ponysafety2/a/report/getAlarmEventRecByTime");
export const handleAlarmEventByEventIdV2 = post("/ponysafety2/a/report/handleAlarmEventByEventIdV2");
export const handleAlarmDetail = post("/ponysafety2/a/report/handleAlarmDetail");
export const addSysDriverInfo = post("/ponysafety2/a/driver/addFaceIdDriver");
export const modifySysDriverInfo = post("/ponysafety2/a/driver/updateFaceIdDriver");
export const deleteSysDriverInfo = post("/ponysafety2/a/driver/deleteFaceIdDriver");
export const deleteSysDriverInfoArray = get("/ponysafety2/a/driver/deletesysdriverinfoarray");
export const getVehicleListByUnbind = post("/ponysafety2/a/vehicle/getvehiclelistbyunbind");
export const addBindDriverVehicle = get("/ponysafety2/a/vehicledriverbind/addbinddrivervehicle");
export const insertSysConfigRunAndStop = post("/ponysafety2/a/sysconfig/addsysconfigRunAndStop");
export const selectMediaUploadConfigInfo = post("/ponysafety2/a/user/getvehiclemediaupload");
export const selectMediaUploadType = get("/ponysafety2/a/user/getVMUByTerminalType");
export const selectMediaUploadTypeAll = get("/ponysafety2/a/user/getVMUTypeByAll");
export const addMediaFunInfo = post("/ponysafety2/a/user/addMediaUploadConfig");
export const removeVehicleUpload = post("/ponysafety2/a/user/deleteMediaUploadConfig");
export const updateMediaFunInfo = post("/ponysafety2/a/user/updateMediaUploadConfig");
export const getSysVehicleInfo = post("/ponysafety2/a/vehicle/getsysvehicleinfo");
export const getSysVehicleInfoV2 = post("/ponysafety2/a/vehicle/getSysVehicleInfoV2");

export const addSysVehicleInfo = post("/ponysafety2/a/vehicle/addsysvehicleinfo");
export const getTerminalTypeList = get("/ponysafety2/a/terminaltype/getterminaltypelist");
export const uploadFileToTable = post("/ponysafety2/a/vehicle/readExcelVehixleToTable");
export const excelTableTosql = postWithoutTime("/ponysafety2/a/vehicle/readExcelTableVehicle");
export const selectLineInfe = post("/ponysafety2/a/driverline/getsysdriveline");
export const selectVehicleAllById = get("/ponysafety2/a/vehicle/getsysvehicleallbyid");
export const modifySysVehicleInfo = post("/ponysafety2/a/vehicle/modifysysvehicleinfo");
export const removeSysVehicleInfo = get("/ponysafety2/a/vehicle/deletesysvehicleinfo");
export const removeSysVehicleInfoArray = get("/ponysafety2/a/vehicle/deletesysvehicleinfoarray");
export const selectDriverInfoBind = post("/ponysafety2/a/driver/getdriverlistbyunbind");
export const getDriverFaceImgs = post("/ponysafety2/a/driver/getDriverFaceImgs");
export const vehicleBindDriverInfo = get("/ponysafety2/a/vehicledriverbind/addbinddrivervehicle");
export const vehivelUnbindToQrcode = get("/ponysafety2/a/vehicle/deletebindQrcode");
export const selectUnbindQrcode = post("/ponysafety2/a/qrcode/getsysqrcodeinfo");
export const selectMileage = post("/ponysafety2/a/vehicle/selectmileagevehicle");
export const addVehicleMileage = post("/ponysafety2/a/vehicle/addmileagevehicle");
export const vehicleBindQrcode = get("/ponysafety2/a/vehicle/addbindqrcodevehicle");
export const vehivelUnbindToDriver = get("/ponysafety2/a/vehicledriverbind/deletevehicledriverbind");
export const getCodeTypeList = get("/ponysafety2/a/common/getSysDictByCode");
export const getAllDeptTableByVue = post("/ponysafety2/a/tabletree/getdepttabletree");
export const getAllGroupByVue = post("/ponysafety2/a/department/getAllDepartmentByVue");
export const getTreeArea = get("/ponysafety2/a/sysarea/gettreesysarea");

export const getTreeAreaTerminal = get("/ponysafety2/a/sysarea/gettreeareaterminal");
export const insertGroup = post("/ponysafety2/a/department/insertDepartment");
export const updateGroup = post("/ponysafety2/a/department/updateDepartment");
export const removeSysGroupInfo = get("/ponysafety2/a/department/deleteDepartment");
export const selectStatsCycle = get("/ponysafety2/a/statscycle/getStatsCycle");
export const insertStatsCycle = post("/ponysafety2/a/statscycle/addStatsCycle");
export const selectSysConfig = get("/ponysafety2/a/sysconfig/getSysConfig");
export const selectSysConfigSpeed = get("/ponysafety2/a/sysconfig/getSysConfigSpeed");
export const insertSysConfig = post("/ponysafety2/a/sysconfig/addSysConfig");
export const getSysConfigFee = get("/ponysafety2/a/sysconfig/getSysConfigFee");
// 配置表格数据
export const getEventRulerConfig = post("/ponysafety2/a/sysconfig/geteventrulerconfig");
// export const getCodeType = get('/ponysafety2/a/common/getSysDictByCode');
export const getAllTerminalTypeByVue = post("/ponysafety2/a/terminaltype/getsysterminaltype");
export const insertTerminalType = post("/ponysafety2/a/terminaltype/addsysTerminalType");
export const updateTerminalType = post("/ponysafety2/a/terminaltype/modifysysTerminalType");
export const removeTerminalType = get("/ponysafety2/a/terminaltype/deletesysTerminalType");
export const getCompanyIdByUserId = get("/ponysafety2/a/common/getCompanyInfo");
export const getQrcodeByVue = post("/ponysafety2/a/qrcode/getallsysqrcode");
export const removeQrcodeSelect = get("/ponysafety2/a/qrcode/deletesysqrcodeinfoarray");
export const getBigQrcodeTwo = get("/ponysafety2/a/qrcode/getBigqrcodeinfo");
export const insertQrcodeToSql = post("/ponysafety2/a/qrcode/addallsysqrcode");
export const selectQrcodeVehicleInfoBind = post("/ponysafety2/a/qrcode/getqrcodeinfoallbyid");
export const getAllRoadLineByVue = post("/ponysafety2/a/driverline/getsysdrivelinepage");
export const insertRoadLine = post("/ponysafety2/a/driverline/addsysdriverline");
export const updateRoadLine = post("/ponysafety2/a/driverline/modifysysdriverline");
export const removeRoadLine = get("/ponysafety2/a/driverline/deletesysdriverline");
export const dsagsfahsdahg = post("/ponysafety2/a/report/getVehicleAlarmEventAll");
export const getAllAccidentByVue = post("/ponysafety2/a/accident/getvehicleaccidentinfo");
export const insertAccident = post("/ponysafety2/a/accident/addvehicleaccidentinfo");
export const updateAccident = post("/ponysafety2/a/accident/modifyvehicleaccidentinfo");
export const removeAccidentInfo = get("/ponysafety2/a/accident/deletevehicleaccidentinfo");
// export const getAllAccidentTypeCode = get('/ponysafety2/a/common/getSysDictByCode');
// export const getAllAccidentDutyCode = get('/ponysafety2/a/common/getSysDictByCode');
export const getAllApairByVue = post("/ponysafety2/a/repair/getvehiclerepairinfo");
export const insertApair = post("/ponysafety2/a/repair/addvehiclerepairinfo");
export const updateApair = post("/ponysafety2/a/repair/modifyvehiclerepairinfo");
export const removeApairInfo = get("/ponysafety2/a/repair/deletevehiclerepairinfo");
export const getSysWaybillInfo = post("/ponysafety2/a/waybill/getwaybillinfoV2");
export const selectCustomerNameList = get("/ponysafety2/a/waybill/getcustnameV2");
export const addWayBillInfe = post("/ponysafety2/a/waybill/addwaybillinfonew");
export const updateWayBillInfe = post("/ponysafety2/a/waybill/modifywaybillinfonew");
export const removeWayBillInfo = get("/ponysafety2/a/waybill/deletewaybillinfo");
export const getTreeDataScopeD = get("/ponysafety2/a/common/getTreeDeptAndDriByUserDeptId");
export const allotWayBillInfo = get("/ponysafety2/a/waybill/allotwaybillinfonew");
// export const getAllRelateTypeCode = get('/ponysafety2/a/common/getSysDictByCode');
export const getAllFenseRelateByVue = post("/ponysafety2/a/fenserelate/getFenseRelate");
export const insertFenseRelate = post("/ponysafety2/a/fenserelate/addFenseRelate");
export const updateFenseRelate = post("/ponysafety2/a/fenserelate/modifyFenseRelate");
export const removeFenseRelateInfo = get("/ponysafety2/a/fenserelate/deleteFenseRelate");
export const getAllMenuByVue = post("/ponysafety2/a/tabletree/getmenutabletree");
export const insertMenu = post("/ponysafety2/a/permission/insertPermission");
export const updateMenu = post("/ponysafety2/a/permission/updatePermission");
export const removeMenuInfo = get("/ponysafety2/a/permission/deletePermission");
export const getAllUserByVue = post("/ponysafety2/a/user/getAllUserByVue");
export const insertUser = post("/ponysafety2/a/user/insertUserV2");
export const updateUser = post("/ponysafety2/a/user/updateUserV2");
export const removeSysUserInfo = get("/ponysafety2/a/user/deleteUser");
export const batchdelUser = post("/ponysafety2/a/user/batchdeluser");
export const getTreeDataScope = get("/ponysafety2/a/common/getTreeDeptAndVehiByUserDeptId");
export const getTreeRoleMenu = get("/ponysafety2/a/permission/getPermissionTree");
export const getDataScopeAndMenuById = get("/ponysafety2/a/user/getDataScopeByUserId");
export const selectIpAndCityList = get("/ponysafety2/a/demo/selectuserrulebycityandip");
export const removeIpByUserId = get("/ponysafety2/a/demo/deleteuserrulebyip");
export const removeCityByUserId = post("/ponysafety2/a/demo/deleteuserrulebycity");
export const addIpByUserIdFun = get("/ponysafety2/a/demo/setuserrulebyip");
export const addCityByUserIdFun = post("/ponysafety2/a/demo/setuserrulebycity");
export const getUserIndustryBind = get("/ponysafety2/a/user/getUserIndustrybind");
export const getVehicleIndustryBind = get("/ponysafety2/a/vehicle/getVehicleIndustryBind");
export const bindUserAlarm = post("/ponysafety2/a/user/bindUserAlarm");
export const getUserLog = get("/ponysafety2/a/common/log");
export const getOperationLog = post("/ponysafety2/a/report/getoperationlog");
export const getLogStatsByUser = post("/ponysafety2/a/report/getlogstatsbyuser");
export const selectIpInfo = get("/ponysafety2/selectIpInfo");
export const getCompanyLogoByIdFun = get("/ponysafety2/a/department/getDeptLogoByComId");
export const updateCompanyLogoFun = post("/ponysafety2/a/department/updateDeptLogoByComId");
export const selectVehiuclesim = post("/ponysafety2/a/user/insertAjaxJson");
export const getTerminalNoAndType = post("/ponysafety2/a/common/getTreeVehicleTerNo");
export const updateVehiuclesim = post("/ponysafety2/a/user/updateAjaxJson");
export const updateVehicleSpeedAndTime = post("/ponysafety2/a/message/sendM1SpeedAndTime");
export const selectVehicleSpeed = post("/ponysafety2/a/message/selectSendM1SpeedAndTime");
export const updateVehiucleM1Photo = post("/ponysafety2/a/message/selectSendM1Photo");
export const getVehicleALLFunss = post("/ponysafety2/a/report/getVehicleQuanjuReport");
export const selectVehicleM1M = post("/ponysafety2/a/message/selectSendM1M");
export const updateVehicleM = post("/ponysafety2/a/message/sendM1M");
export const selectVehicleM1k = post("/ponysafety2/a/message/selectSendM1K");
export const updateVehicleK = post("/ponysafety2/a/message/sendM1K");
export const selsectSessionTimeFun = get("/ponysafety2/a/demo/selectUserSessionTime");
export const addOrUpSessionTimeFun = get("/ponysafety2/a/demo/addOrUpdateUserSessionTime");
export const deleteSessionTimeFun = get("/ponysafety2/a/demo/deleteUserSessionTime");
export const getTreeOpenUser = get("/ponysafety2/a/common/gettreeopenuser");
export const getInterfaceInfo = post("/ponysafety2/a/interface/getinterfaceinfo");
export const addOrUpInterfaceInfo = post("/ponysafety2/a/interface/addorupinterfaceinfo");
export const delInterfaceInfo = get("/ponysafety2/a/interface/delinterfaceinfo");
export const getUserEnabledInterface = get("/ponysafety2/a/interface/getuserenabledinterface");
export const enableInterfaceSingle = get("/ponysafety2/a/interface/enableinterfacesingle");
export const disableInterfaceSingle = get("/ponysafety2/a/interface/disableinterfacesingle");
export const updateSysLineInfo = post("/ponysafety2/a/user/updateSysLineInfo");
export const insertSysLineInfo = post("/ponysafety2/a/user/insertSysLineInfo");
export const getTheTreeDriver = get("/ponysafety2/a/common/getTreeDriver");
export const getRecentTrailState = get("/ponysafety2/a/map/getRecentTrailState");
export const getTrailRecord = post("/ponysafety2/a/map/getTrailRecord");
export const getPlaybackWorkTime = post("/ponysafety2/a/report/getplaybackworktime");
export const selectSysLineInfo = post("/ponysafety2/a/user/selectSysLineInfo");
export const deleteSysLineInfo = post("/ponysafety2/a/user/deleteSysLineInfo");
export const getGpsRecords = post("/ponysafety2/a/map/getGpsRecord");
export const getMonitorReport = get("/ponysafety2/a/report/getmonitorreport");
export const getShowFenseInfoById = get("/ponysafety2/a/fense/getfenseinfobyid");
// export const getCodeTypeInfo = get('/ponysafety2/a/common/getSysDictByCode');
export const getListFenseStyle = get("/ponysafety2/a/fensestyle/getFenseStyleIdAndName");
export const deleteFenseInfoByDate = get("/ponysafety2/a/fense/deletefenseinfo");
export const updateFenseInfoByDate = post("/ponysafety2/a/fense/modifyfenseinfo");
export const updateMapPoiInfoByDate = post("/ponysafety2/a/moppoi/modifymappoiinfo");
export const getMapPoiInfoList = post("/ponysafety2/a/moppoi/getMapPoiInfoAll");
export const addFenseStyleByDate = post("/ponysafety2/a/fensestyle/addFenseStyle");
export const updateFenseStyleByDate = post("/ponysafety2/a/fensestyle/modifyFenseStyle");
export const getFenseStyleById = get("/ponysafety2/a/fensestyle/selectFenseStyleById");
export const updateMopPoiStyleByDate = post("/ponysafety2/a/moppoistyle/modifyMapPoiStyle");
export const addMopPoiStyleByDate = post("/ponysafety2/a/moppoistyle/addMapPoiStyle");
export const getMapPoiStyleById = get("/ponysafety2/a/moppoistyle/selectMapPoiStyleById");
export const deleteFenseStyleByDate = get("/ponysafety2/a/fensestyle/deleteFenseStyle");
export const deleteMopPoiStyleByDate = get("/ponysafety2/a/moppoistyle/deleteMapPoiStyle");
export const getMapPoiByIdDate = get("/ponysafety2/a/moppoi/getmappoiinfobyid");
export const getMopPoiStyleIdAndName = get("/ponysafety2/a/moppoistyle/getMapPoiStyleIdAndName");
export const deleteMapPoiByDate = get("/ponysafety2/a/moppoi/deleteMapPoiinfo");
export const getListFenseInfo = post("/ponysafety2/a/fense/getFenseinfoAll");
export const getAlarmList = post("/ponysafety2/a/map/getAdasAlarm");
export const getAlarmStatistic = post("/ponysafety2/a/map/getAlarmStatistic");
export const getTinyAlarmList = post("/ponysafety2/a/map/getAlarmForMap");
//举升地图
export const getliftList = post("/ponysafety2/a/map/liftMapQuery");
export const getPageAlarmList = post("/ponysafety2/a/map/getAlarm");
export const getAlarmInfo = get("/ponysafety2/a/map/getAlarmInfo");
export const getCompanyInfo = get("/ponysafety2/a/common/getCompanyInfo");
export const getDeptByCompanyId = get("/ponysafety2/a/common/getFleetByCompanyId");
export const getVehicleByDeptId = get("/ponysafety2/a/common/getVehicleInfoByDeptId");
export const getDirverByDeptId = get("/ponysafety2/a/common/getDriverInfoByDeptId");
export const getAlarmDict = post("/ponysafety2/a/map/getAlarmDict");
export const gatAllVehicleInfo = get("/ponysafety2/a/map/getAllVehicles");
export const selectTerminalNoByVehicleId = get("/ponysafety2/a/user/selectTerminalNoByVehicleId");
export const getVehicleStopInfo = post("/ponysafety2/a/report/getvehiclestopinfo");
export const selectMapPoiInfoByDeptIds = get("/ponysafety2/a/moppoi/selectmappoiinfobydeptids");
export const getAlarmEventRecDetail = post("/ponysafety2/a/report/getAlarmEventRecDetail");
export const getAlarmEventRecDetailV2 = post("/ponysafety2/a/report/getAlarmEventRecDetailV2");
export const insertMessage = post("/ponysafety2/a/message/insertmessage");
export const updateAlarmEventRecById = post("/ponysafety2/a/report/updateAlarmEventRecById");
export const getUpDownLine = get("/ponysafety2/a/linereport/getUpDownLine");
export const getSysDriverInfo = post("/ponysafety2/a/driver/getsysdriverinfo");
export const getSysDriverPageV2 = post("/ponysafety2/a/driver/getSysDriverPageV2");

export const getDistractedAlamDeal = post("/ponysafety2/a/report/getDistractedAlamDeal");
export const updateFaceIdDriver = post("/ponysafety2/a/driver/updateFaceIdDriver");
export const addFaceIdDriver = post("/ponysafety2/a/driver/addFaceIdDriver");
export const getVehicleQuanju = post("/ponysafety2/a/report/getVehicleQuanju");
export const getStationDangerMapInfo = post("/ponysafety2/a/linereport/getStationDangerMapInfo");
export const getLiveVideoVehicleInfo = get("/ponysafety2/a/user/getLiveVideoVehicleInfo");
export const getScorePanorama = get("/ponysafety2/a/report/getScorePanorama");
export const getIndustryTrend = get("/ponysafety2/a/report/getIndustryTrend");
export const getMenuTableTree = post("/ponysafety2/a/tabletree/getmenutabletree");
export const addMenuTable = post("/ponysafety2/a/permission/insertPermission");
export const updateMenuTable = post("/ponysafety2/a/permission/updatePermission");
export const removeMenuTable = get("/ponysafety2/a/permission/deletePermission");
export const getPermissionTree = get("/ponysafety2/a/permission/getPermissionTree");
export const insertDepartment = post("/ponysafety2/a/department/insertDepartment");
export const updateDepartment = post("/ponysafety2/a/department/updateDepartment");
export const deleteDepartment = get("/ponysafety2/a/department/deleteDepartment");
export const getMediaPlatformUrl = post("/ponysafety2/a/sysconfig/getmediaplatformurl");
export const addMediaPlatformUrl = post("/ponysafety2/a/sysconfig/addorupmediaplatformurl");
export const delMediaPlatformUrl = post("/ponysafety2/a/sysconfig/delmediaplatformurl");
export const getVehicleMediaPlatInfo = get("/ponysafety2/a/vehicle/getVehicleMediaPlatInfo");
export const getPerUser = get("/ponysafety2/a/user/getsysuser");
export const updatePerUser = post("/ponysafety2/a/user/changesysuser");
export const getGpsAlarmpage = post("/ponysafety2/a/report/getgpsalarmpage");
export const getVehicleworktime = post("/ponysafety2/a/report/getvehicleworktime");
export const getVehicleWorkTimeV2 = post("/ponysafety2/a/report/getvehicleworktimev2");
export const getTrailDeptWorkTime = post("/ponysafety2/a/report/gettraildeptworktime");
export const getTrailVehicleWorkTime = post("/ponysafety2/a/report/gettrailvehicleworktime");
export const getGpsAlarmcount = post("/ponysafety2/a/report/getgpsalarmcount");
export const getVehicleLineInfos = get("/ponysafety2/a/report/getVehicleLineInfos");
export const getVehiclerepairservice = post("/ponysafety2/a/vehiclerepairservice/getvehiclerepairservice");
export const insertVehiclerepairservice = post("/ponysafety2/a/vehiclerepairservice/insertvehiclerepairservice");
export const modifyVehiclerepairservice = post("/ponysafety2/a/vehiclerepairservice/modifyvehiclerepairservice");
export const getDistractedAlamAll = post("/ponysafety2/a/report/getDistractedAlamAll");
export const getCustAlarmInfos = post("/ponysafety2/a/report/getCustAlarmInfos");
export const getCustAlarmCount = post("/ponysafety2/a/report/getCustAlarmCount");
export const getDriveWorkState = post("/ponysafety2/a/driver/getDriveWorkState");
export const getVehicleAlarmEventAll = post("/ponysafety2/a/report/getVehicleAlarmEventAll");
export const getDriverRTImage = post("/ponysafety2/a/report/getDriverRTImage");
export const getSysDictCodes = get("/ponysafety2/a/common/getSysDictCodes");
export const getSysDictByCode = get("/ponysafety2/a/common/getSysDictByCode");
export const deleteSysDictItem = post("/ponysafety2/a/common/deleteSysDictItem");
// export const getSysDictByCodes = post('/ponysafety2/a/common/getSysDictByCodes');
export const addSysDictItem = post("/ponysafety2/a/common/addSysDictItem");
export const updateSysDictItem = post("/ponysafety2/a/common/updateSysDictItem");
export const getSysAlarmTypes = post("/ponysafety2/a/common/getSysAlarmTypes");

export const bindCompanyAlarm = post("/ponysafety2/a/department/bindCompanyAlarm");
export const bindIndustryAlarm = post("/ponysafety2/a/department/bindIndustryAlarm");
export const getComIndustryBind = get("/ponysafety2/a/department/getComIndustryBind");
export const bindComImdustry = post("/ponysafety2/a/department/bindComImdustry");
export const addSysAlarmType = post("/ponysafety2/a/common/addSysAlarmType");
export const updateSysAlarmType = post("/ponysafety2/a/common/updateSysAlarmType");
export const deleteSysAlarmType = post("/ponysafety2/a/common/deleteSysAlarmType");
export const getVehicleQuanjuReport = post("/ponysafety2/a/report/getVehicleQuanjuReport");
export const getUserSelfIndustry = get("/ponysafety2/a/user/getUserIndustrybind");
export const getRecordsli = post("/ponysafety2/a/report/getrecordsli");
export const getRecordslipre = post("/ponysafety2/a/report/getrecordslipre");
export const getAlarmMediaUploadConfig = post("/ponysafety2/a/sysconfig/getalarmmediauploadconfig");
export const addOrUpAlarmMediaUploadConfig = post("/ponysafety2/a/sysconfig/addorupalarmmediauploadconfig");
export const getAllVehicleInfo = get("/ponysafety2/a/map/getAllVehicles");
export const getVehicleslastonlinetime = post("/ponysafety2/a/map/getvehicleslastonlinetime");
export const addPoiInfo = post("/ponysafety2/a/moppoi/modifymappoiinfo");
export const addPoiGroupInfo = post("/ponysafety2/a/moppoistyle/addMapPoiStyle");
export const searchGroupDetail = get("/ponysafety2/a/moppoistyle/selectMapPoiStyleById");
export const searchPioDetail = get("/ponysafety2/a/moppoi/getmappoiinfobyid");
export const deleteGroupInfo = get("/ponysafety2/a/moppoistyle/deleteMapPoiStyle");
export const deletePoiInfo = get("/ponysafety2/a/moppoi/deleteMapPoiinfo");
export const updatePoiGroup = post("/ponysafety2/a/moppoistyle/modifyMapPoiStyle");
export const updateFenceGroup = post("/ponysafety2/a/fensestyle/modifyFenseStyle");
export const addFenceGroup = post("/ponysafety2/a/fensestyle/addFenseStyle");
export const getFenceGroupDetail = get("/ponysafety2/a/fensestyle/selectFenseStyleById");
export const getFenceDetail = get("/ponysafety2/a/fense/getfenseinfobyid");
export const updateFenceInfo = post("/ponysafety2/a/fense/modifyfenseinfo");
export const getFenceList = post("/ponysafety2/a/fense/getFenseinfoAll");
export const deleteFenceGroup = get("/ponysafety2/a/fensestyle/deleteFenseStyle");
export const deleteFenceInfo = get("/ponysafety2/a/fense/deletefenseinfo");
export const getRoadlist = get("/ponysafety2/a/road/getroadlist");
export const getRoadScore = post("/ponysafety2/a/road/getroadscore");
export const getRoadSectionAlarm = post("/ponysafety2/a/road/getroadsectionalarm");
export const getVehicleVideo = post("/ponysafety2/a/vehicle/getVehicleExistMedia");
export const getVehicleBindedDriver = get("/ponysafety2/a/vehicle/getVehicleBindedDriver");
export const getMiledeptReport = get("/ponysafety2/a/report/getmiledeptreport");
export const selectLinkParamConfigByAll = get("/ponysafety2/a/link/selectLinkParamConfigByAll");
export const selectLinkParamConfigById = get("/ponysafety2/a/link/selectLinkParamConfigById");
export const addLinkParamConfig = post("/ponysafety2/a/link/addLinkParamConfig");
export const updateLinkParamConfig = post("/ponysafety2/a/link/updateLinkParamConfig");
export const deleteLinkParamConfig = get("/ponysafety2/a/link/deleteLinkParamConfig");
export const upAndDownLinkLogin = post("/ponysafety2/a/link/upAndDownLinkLogin");
export const registerCar = post("/ponysafety2/a/link/registerCar");
export const registerDriver = post("/ponysafety2/a/link/registerDriver");
export const selectLinkUpParamTree = get("/ponysafety2/a/link/selectLinkUpParamTree");
export const addLinkUpParam = post("/ponysafety2/a/link/addLinkUpParam");
export const updateLinkUpParam = post("/ponysafety2/a/link/updateLinkUpParam");
export const deleteLinkUpParam = get("/ponysafety2/a/link/deleteLinkUpParam");
export const getTreeDepartmentByUserIdV2 = get("/ponysafety2/a/common/getTreeDepartmentByUserIdV2");
export const selectLinkUserByDept = get("/ponysafety2/a/link/selectLinkUserByDept");
export const getAddressBylnglat = get("/ponysafety2/a/road/getaddressbylnglat");
export const addorupSysroadwithmarkInfo = post("/ponysafety2/a/road/addorupsysroadwithmarkinfo");
export const getDistractedAlamPage = post("/ponysafety2/a/report/getDistractedAlamPage");
export const registerGps = post("/ponysafety2/a/link/registerGps");
export const selectSimStatus = get("/ponysafety2/a/iot/selectSimStatus");
export const selectSimStatusAll = post("/ponysafety2/a/iot/selectSimStatusAll");
export const selectSimInfo = get("/ponysafety2/a/iot/selectSimInfo");
export const selectSimInfoAll = post("/ponysafety2/a/iot/selectSimInfoAll");
export const selectSimFlow = get("/ponysafety2/a/iot/selectSimFlow");
export const selectSimFlowAll = post("/ponysafety2/a/iot/selectSimFlowAll");
export const selectSimContent = get("/ponysafety2/a/iot/selectSimContent");
export const selectSimFlowPoolAll = get("/ponysafety2/a/iot/selectSimFlowPoolAll");
export const selectSimFlowPoolInfo = get("/ponysafety2/a/iot/selectSimFlowPoolInfo");
export const selectSimFlowPoolData = get("/ponysafety2/a/iot/selectSimFlowPoolData");
export const selectSimAlarmLog = post("/ponysafety2/a/iot/selectSimAlarmLog");
export const selectSimDetail = get("/ponysafety2/a/iot/selectSimDetail");
export const selectSimFlowOnMonth = get("/ponysafety2/a/iot/selectSimFlowOnMonth");
export const selectSimFlowOnYear = get("/ponysafety2/a/iot/selectSimFlowOnYear");
export const getSimFlowUsedInfo = get("/ponysafety2/a/sim/getsimflowusedinfo");
export const modifySysRoleInfo = post("/ponysafety2/a/role/updateRole2");
export const addSysRoleInfo = post("/ponysafety2/a/role/insertRole2");
export const getRoleMenuById = get("/ponysafety2/a/role/getMenuByRoleId2");
export const getRoleUserMenuById = post("/ponysafety2/a/role/findUserByRoleIdByVue");
export const getSysRoleInfo = post("/ponysafety2/a/role/getAllRoleByVue2");
export const deleteSysRoleInfo = get("/ponysafety2/a/role/deleteRole2");
export const selectRoleByDeptIdFun = get("/ponysafety2/a/role/selectRoleModelByUserId");
export const getCommandConfigInfo = post("/ponysafety2/a/command/selectCommandConfig");
export const selectTerminalTypeListInfo = post("/ponysafety2/a/command/selectTerminalTypeList");
export const addCommandConfigFun = post("/ponysafety2/a/command/insertCommandConfig");
export const updateCommandConfigfun = post("/ponysafety2/a/command/updateCommandConfig");
export const deleteCommandConfigFun = get("/ponysafety2/a/command/deleteCommandConfig");
export const setVersionInfo = post("/ponysafety2/a/version/addorupsysversioninfo");
export const deleteVersion = get("/ponysafety2/a/version/delsysversioninfobyid");
export const getVersionInfoList = post("/ponysafety2/a/version/getsysversioninfo");
export const getLineScheduleInfo = post("/ponysafety2/a/linereport/getlinescheduleinfo");
export const getLineBindVehicles = get("/ponysafety2/a/linereport/getlinebindvehicles");
export const getSimpleDriverInfo = post("/ponysafety2/a/linereport/getsimpledriverinfo");
export const operateLineScheduleInfo = post("/ponysafety2/a/linereport/operatelinescheduleinfo");
export const hikServiceDownload = post("/ponysafety2/hkiservicedownload");
export const hikServiceToken = post("/ponysafety2/hkiservicetoken");
export const getTerminalFenseRelate = post("/ponysafety2/a/fenserelate/getterminalfenserelate");
export const setFenseRelateToTerminal = post("/ponysafety2/a/fenserelate/setfenserelatetoterminal");
export const delTerminalFenseRelate = post("/ponysafety2/a/fenserelate/delterminalfenserelate");
export const getMainTerminalNoByVehicleId = get("/ponysafety2/a/terminal/getmainterminalnobyvehicleid");
export const getReportData = get("/getReportData");
export const getComponents = get("/getComponents");
export const getMiniCmdEvent = post("/ponysafety2/a/cmdex/getminicmdevent");
export const getMiniCmdSms = post("/ponysafety2/a/cmdex/getminicmdsms");
export const getMiniCmdContacts = post("/ponysafety2/a/cmdex/getminicmdcontacts");
export const setMiniCmdEvent = post("/ponysafety2/a/cmdex/setminicmdevent");
export const setMiniCmdContacts = post("/ponysafety2/a/cmdex/setminicmdcontacts");
export const setMiniCmdSms = post("/ponysafety2/a/cmdex/setminicmdsms");
export const getUserAlarmWarnSound = post("/ponysafety2/a/sysconfig/getuseralarmwarnsound");
export const changeAlarmWarnSoundSingle = post("/ponysafety2/a/sysconfig/operateuseralarmwarnsoundsingle");
export const changeAlarmWarnSound = post("/ponysafety2/a/sysconfig/operateuseralarmwarnsound");
export const getTheTreeVehicle = get("/ponysafety2/a/common/getTreeDepartmentAndVehicleV2");
export const getTreeUserCompany = get("/ponysafety2/a/common/getTreeUserCompany");
export const getTreeVehicleLine = get("/ponysafety2/a/common/getTreeVehicleLine");
export const getTreeVehicleByCompany = get("/ponysafety2/a/common/getTreeDeptAndVehiByUserDeptId");
export const getTreeFenseInfo = get("/ponysafety2/a/fense/gettreefenseinfoassets");
export const getMapTreeViewMenu = post("/ponysafety2/a/common/getMapTreeviewMenu");
export const getTreeDepart = get("/ponysafety2/a/common/getTreeDepartmentV2");
export const getMenuTree = get("/ponysafety2/a/demo/getMenuTree");
export const getSysUserTree = get("/ponysafety2/a/user/getsysusertree");
export const getTreeMopPoi = get("/ponysafety2/a/moppoi/gettreemappoiinfo");
export const getTreeExRuleInfo = post("/ponysafety2/a/sysconfig/gettreeexruleinfo");
export const getCustomUserRemoteIpaddr = get("/ponysafety2/getcustomuserremoteipaddr");
export const getRealtimeTerminalType = post("/ponysafety2/a/terminaltype/getrealtimeterminaltype");
export const getCarInfoAndGpsAlarm = post("/ponysafety2/a/map/getCarInfoAndGpsAlarm");
export const getGsixInfo = get("/ponysafety2/a/map/getGsixInfo");
export const getTreeRoadInfo = get("/ponysafety2/a/road/gettreeroadinfo");
export const sendMiniStandardCommon = post("/ponysafety2/a/message/sendministandardcommon");
export const cancelMediaRequest = get("/ponysafety2/a/map/cancelMediaRequest");
export const getMedia = get("/ponysafety2/a/map/getMedia");
// 疫情地图 或用户分页配置
export const getCommonListByKey = post("/ponysafety2/a/common/getcommonlistbykey");
export const apiHeartBeat = get("/ponysafety2/a/user/apiheartbeat");
export const queryAlarmEventRuleConfiguration = post("/ponysafety2/a/sysconfig/queryAlarmEventRuleConfiguration");
export const getCustomGPSRecord = post("/ponysafety2/a/map/getcustomgpsrecord");
export const operateFaceidHkParams = post("/ponysafety2/a/message/operatefaceidhkparams");
export const getVehicleDimensionRiskScore = post("/ponysafety2/a/report/getvehicledimensionriskscore");
export const voiceBroadCastV2 = post("/ponysafety2/a/message/voiceBroadcastV2");
export const queryVoiceBroadCast = post("/ponysafety2/a/message/queryVoiceBroadcast");
export const changeVoiceBroadcastV2 = post("/ponysafety2/a/message/changeVoiceBroadcastV2");
export const vehicleRealTimeDimensionScoreHead = post("/ponysafety2/a/report/vehicleRealTimeDimensionScoreHead");
export const vehicleHistoryDimensionScoreHead = post("/ponysafety2/a/report/vehicleHistoryDimensionScoreHead");
export const fleetRealTimeDimensionScoreHead = post("/ponysafety2/a/report/fleetRealTimeDimensionScoreHead");
export const fleetHistoryDimensionScoreHead = post("/ponysafety2/a/report/fleetHistoryDimensionScoreHead");
export const fleetVehicleDimensionScoreMonitoring = post("/ponysafety2/a/report/fleetVehicleDimensionScoreMonitoring");
export const fleetDimensionScore = post("/ponysafety2/a/report/fleetDimensionScore");
export const realTimeDetailsPage = post("/ponysafety2/a/report/realTimeDetailsPage");
export const historicalDetailsPage = post("/ponysafety2/a/report/historicalDetailsPage");
export const vehicleHistoricalDimensionScore = post("/ponysafety2/a/report/vehicleHistoricalDimensionScore");
export const fleetHistoryDimensionScore = post("/ponysafety2/a/report/fleetHistoryDimensionScore");
export const operateCustomReportStructureJson = post("/ponysafety2/a/component/operatecustomreportstructurejson");
export const queryCustomReportStructureJson = post("/ponysafety2/a/component/querycustomreportstructurejson");
export const getDeptBaseProperty = post("/ponysafety2/a/common/getdeptbaseproperty");
export const bindStructureUser = post("/ponysafety2/a/component/bindstructureuser");
export const getTsrSampleStates = get("/ponysafety2/a/map/gettsrsamplestats");
export const queryCommonCompare = post("/ponysafety2/a/report/querycommoncompare");
export const vehicleEquipmentFaultAlarmInquiry = post("/ponysafety2/a/report/vehicleEquipmentFaultAlarmInquiry");
export const decodEdrData = post("/ponysafety2/a/vehicle/decodedrdata");
export const queryUserWarnConfigv2 = post("/ponysafety2/a/sysconfig/queryuserwarnconfigv2");
export const getAlarmMapSplit24 = post("/ponysafety2/a/map/getalarmmapsplit24");
export const getalarmmapsplit24V2 = post("/ponysafety2/a/map/getalarmmapsplit24V2");
export const operateUserWarnConfigv2 = post("/ponysafety2/a/sysconfig/operateuserwarnconfigv2");
export const fenceRelatedReportCheckFence = post("/ponysafety2/a/report/fenceRelatedReportCheckFence");
export const sendDataColorLed = post("/ponysafety2/a/message/senddatacolorled");
export const queryPassInformation = post("/ponysafety2/a/sysconfig/queryPassInformation");
export const getBlackJdRealTimeStats = post("/ponysafety2/a/map/getblackjdrealtimestats");
export const getCityWeatherByName = get("/ponysafety2/a/map/getcityweatherbyname");
export const getTenPhotoOfNow = post("/ponysafety2/a/map/gettenphotoofnow");
export const excavationPointOperationReport = post("/ponysafety2/a/report/excavationPointOperationReport");
export const queryFenseInfoAssets = post("/ponysafety2/a/fense/queryfenseinfoassets");
export const intersectionViolationInquiry = post("/ponysafety2/a/report/intersectionViolationInquiry");
export const intersectionViolationInquiryV2 = post("/ponysafety2/a/report/junctionRiskAssessment");
export const restrictedAreaViolationReport = post("/ponysafety2/a/report/restrictedAreaViolationReport");
export const operateUserVehicleFllow = post("/ponysafety2/a/user/operateuservehiclefllow");
export const downloadVideoLog = post("/ponysafety2/a/log/downloadVideoLog");
export const queryVideoDownloadLog = post("/ponysafety2/a/log/queryVideoDownloadLog");
export const getSysSimInfoAll = post("/ponysafety2/a/sim/getsyssiminfoall");
export const addSysSimInfo = post("/ponysafety2/a/sim/addsyssiminfo");
export const modifySysSimInfo = post("/ponysafety2/a/sim/modifysysSimInfo");
export const deleteSysSimInfo = get("/ponysafety2/a/sim/deletesysSimInfo");
export const addSysSimInfoBatch = post("/ponysafety2/a/sim/addsyssiminfobatch");
export const getVehicleAllowedDriver = get("/ponysafety2/a/vehicle/getvehiclealloweddriver");
export const modifyCarStatus = post("/ponysafety2/a/vehicle/modifycarstatus");
export const pushVehicleInfo2JTAQJC = get("/ponysafety2/a/vehicle/pushvehicleinfo2jtaqjc");
export const getChannelNoByVehicleId = get("/ponysafety2/a/terminal/getchannelcountbyvehicleid");
export const getFenseNull = post("/ponysafety2/a/sysconfig/getFenseNull");
export const updatePassBindingManually = post("/ponysafety2/a/sysconfig/updatePassBindingManually");
export const getTheStartPositionOfTheDay = post("/ponysafety2/a/map/getthestartpositionoftheday");
export const modifyAndDeleteAlarmEventRuleConfiguration = post(
  "/ponysafety2/a/sysconfig/modifyAndDeleteAlarmEventRuleConfiguration"
);
export const alarmEventRuleConfiguration = post("/ponysafety2/a/sysconfig/alarmEventRuleConfiguration");
export const operateUserTableConfig = post("/ponysafety2/a/user/operateusertableconfig");
export const getTerminalInfoV2 = post("/ponysafety2/a/terminal/getterminalinfov2");
export const addSysTerminal = post("/ponysafety2/a/terminal/addsysTerminal");
export const deleteSysTerminal = get("/ponysafety2/a/terminal/deletesysTerminal");
export const modifySysTerminal = post("/ponysafety2/a/terminal/modifysysTerminal");
export const getMiniDeviceStatus = post("/ponysafety2/a/cmdex/getminidevicestatus");
export const changeBlackAndWhite = post("/ponysafety2/a/report/changeBlackAndWhite");
export const getBlackAndWhite = post("/ponysafety2/a/report/getBlackAndWhite");
export const getVehicleRepairInfoV2 = post("/ponysafety2/a/repair/getvehiclerepairinfov2");
export const queryRoadSpeed = post("/ponysafety2/a/report/queryRoadSpeed");
export const queryHikFaceIdParamSet = post("/ponysafety2/a/message/queryhikfaceidparamset");
export const queryConsolidatedStatement = post("/ponysafety2/a/report/queryConsolidatedStatement");
export const queryVehicleAreaSpeed = post("/ponysafety2/a/message/queryvehicleareaspeed");
export const operateVehicleAreaSpeed = post("/ponysafety2/a/message/operatevehicleareaspeed");
export const getAreaTableTree = post("/ponysafety2/a/tabletree/getareatabletree");
export const operateSysArea = post("/ponysafety2/a/sysarea/operatesysarea");
export const queryFaceBuildErr = post("/ponysafety2/a/report/queryFaceBuildErr");
export const queryVehicleAreaSpeedV2 = post("/ponysafety2/a/message/queryvehicleareaspeedv2");
export const operateVehicleAreaSpeedV2 = post("/ponysafety2/a/message/operatevehicleareaspeedv2");
export const getPassMission = post("/ponysafety2/a/report/getPassMission");
export const getPassMissions = post("/ponysafety2/a/report/getPassMissions");
export const getPassMissionDetails = post("/ponysafety2/a/report/getPassMissionDetails");
export const queryBusinessArea = post("/ponysafety2/a/fense/querybusinessarea");
export const operateBusinessArea = post("/ponysafety2/a/fense/operatebusinessarea");
export const queryMuckCase = post("/ponysafety2/a/case/querymuckcase");
export const operateMuckCase = post("/ponysafety2/a/case/operatemuckcase");
export const queryMuckCaseCount = post("/ponysafety2/a/case/querymuckcasecountbybusiness");
export const updateOffOrOn = post("/ponysafety2/a/sysconfig/updateOffOrOn");
export const sendControlVehiclePlusex = post("/ponysafety2/a/message/sendcontrolvehicleplusex");
export const getUnknowBusinessArea = get("/ponysafety2/a/map/getunknowbusinessarea");
export const getMuckSoiltransFlowdirecstats = get("/ponysafety2/a/map/getmucksoiltransflowdirecstats");
export const getOptionalArea = post("/ponysafety2/a/report/getOptionalArea");
export const getKanBanDetails = post("/ponysafety2/a/report/getKanBanDetails");
export const getStandardBusinessArea = get("/ponysafety2/a/map/getStandardBusinessArea");
export const getMuckSoilTransflowDirecStats = post("/ponysafety2/a/map/getmucksoiltransflowdirecstats");
export const sendMuckCert = post("/ponysafety2/a/message/sendMuckCert");
export const getAbsorptionWorkTripData = post("/ponysafety2/a/report/getAbsorptionWorkTripData");
export const getNounVerb = post("/ponysafety2/a/report/getNounVerb");
export const getUserPassAreaList = get("/ponysafety2/a/user/getuserpassarealist");
export const setUserPassAreaList = post("/ponysafety2/a/user/setuserpassarealist");
export const getTreeAreaWithBusiness = post("/ponysafety2/a/sysarea/gettreeareawithbusiness");
export const operateFenseInfoAssetsV2 = post("/ponysafety2/a/fense/operateFenseInfoAssetsV2");
export const getFenseInfoTreeAssetsV2 = post("/ponysafety2/a/fense/getFenseInfoTreeAssetsV2");
export const queryFenseInfoAssetsV2 = post("/ponysafety2/a/fense/queryFenseInfoAssetsV2");
export const operateUserFreqMapMark = post("/ponysafety2/a/user/operateUserFreqMapMark");
export const queryUserFreqMapMark = post("/ponysafety2/a/user/queryUserFreqMapMark");
export const upDateTypeGroup = post("/ponysafety2/a/fenseTypeGroup/upDateTypeGroup");
export const addFenseTypeGroup = post("/ponysafety2/a/fenseTypeGroup/addFenseTypeGroup");
export const getFenseTypeGroup = post("/ponysafety2/a/fenseTypeGroup/getFenseTypeGroup");
export const getMiniCmdTerminalTopNum = post("/ponysafety2/a/cmdex/getminicmdterminaltopnum");
export const queryVehicleRepairService = post("/ponysafety2/a/vehiclerepairservice/queryVehicleRepairService");
export const queryPassTaskByType = post("/ponysafety2/a/activiti/queryPassTaskByType");
export const queryPassTaskDetail = post("/ponysafety2/a/activiti/queryPassTaskDetail");
export const addPassTaskDetail = post("/ponysafety2/a/activiti/addPassTaskDetail");
export const getPassAreaFenseTree = post("/ponysafety2/a/fense/getPassAreaFenseTree");
export const addPassApproval = post("/ponysafety2/a/passApproval/addPassApproval");
export const updatePassApproval = post("/ponysafety2/a/passApproval/updatePassApproval");
export const getLinkStatus = post("/ponysafety2/a/link/getlinkstatus");
export const getVehicleByTimeLoc = post("/ponysafety2/a/vehicle/getvehiclebytimeloc");
export const exportPdf = post("/ponysafety2/a/passApproval/exportPdf");
export const querySysContractInfo = post("/ponysafety2/a/contract/querySysContractInfo");
export const operateSysContractInfo = post("/ponysafety2/a/contract/operateSysContractInfo");
export const operateExamQuestion = post("/ponysafety2/a/exam/operateexamquestion");
export const queryExamQuestion = post("/ponysafety2/a/exam/queryexamquestion");
export const queryExamPaperList = post("/ponysafety2/a/exam/queryexampaperlist");
export const queryExamPaper = post("/ponysafety2/a/exam/queryexampaper");
export const operateExamPaper = post("/ponysafety2/a/exam/operateexampaper");
export const batchApplicationFilling = post("/ponysafety2/a/activiti/batchApplicationFilling");
export const batchApplicationFillingV3 = post("/ponysafety2/a/activiti_v3/batchApplicationFilling");

export const addPassApprovalJH = post("/ponysafety2/a/passApproval/addPassApprovalJH");
export const updatePassApprovalJH = post("/ponysafety2/a/passApproval/updatePassApprovalJH");
export const queryPassTaskByList = post("/ponysafety2/a/activiti/queryPassTaskByList");
export const queryPassTaskByListNew = post("/ponysafety2/a/activiti_v3/queryPassTaskByList");

export const detailsAndApproval = post("/ponysafety2/a/activiti/detailsAndApproval");
export const getProcessOfJudge = post("/ponysafety2/a/passApproval/getProcessOfJudge");
export const buttonManage = post("/ponysafety2/a/activiti/buttonManage");
export const queryApprovalBlack = post("/ponysafety2/a/activiti/queryApprovalBlack");
// export const trafficPoliceScoreQuery = post('/ponysafety2/a/activiti/trafficPoliceScoreQuery');
export const trafficPoliceScoreQuery = post("/ponysafety2/a/activiti_v3/trafficPoliceScoreQuery");

export const getRoleInformation = post("/ponysafety2/a/activiti/getRoleInformation");
export const operatePlatformLicense = post("/ponysafety2/a/user/operatePlatformLicense");
export const exportPlatformLicense = post("/ponysafety2/a/user/exportPlatformLicense");
export const operateGpsTLTask = post("/ponysafety2/a/vehicle/operateGpsTLTask");
export const queryexamresult = post("/ponysafety2/a/exam/queryexamresult");
export const getCloudCrossTrafficFlow = post("/ponysafety2/a/report/getCloudCrossTrafficFlow");
export const exportexamresult = post("/ponysafety2/a/exam/exportexamresult");
export const getTerminalLastParam = post("/ponysafety2/a/terminal/getTerminalLastParam");
export const faceTimeAttendanceInquiry = post("/ponysafety2/a/faceRecognition/faceTimeAttendanceInquiry");
export const listQueryOfSuspectedConstructionSite = get("/ponysafety2/a/report/listQueryOfSuspectedConstructionSite");
export const detailQueryOfSuspectedConstructionSite = get("/ponysafety2/a/report/detailQueryOfSuspectedConstructionSite");
export const getBusinessAreaWorkReport = post("/ponysafety2/a/hireport/getBusinessAreaWorkReport");
export const manMadeDamageQuery = post("/ponysafety2/a/activiti/manMadeDamageQuery");
export const manMadeDamageOperate = post("/ponysafety2/a/activiti/manMadeDamageOperate");
export const getAlarmEventRecV2Count = post("/ponysafety2/a/report/getAlarmEventRecV2Count");
export const statisticsOfOperationDuration = post("/ponysafety2/a/report/statisticsOfOperationDuration");
export const updateOfSuspectedConstructionSite = post("/ponysafety2/a/report/updateOfSuspectedConstructionSite");
export const listQueryOfSuspectedConstructionSiteLCar = post("/ponysafety2/a/report/listQueryOfSuspectedConstructionSiteLCar");
export const listQueryOfSuspectedConstructionSiteSCar = post("/ponysafety2/a/report/listQueryOfSuspectedConstructionSiteSCar");
export const dangerEventReport = post("/ponysafety2/a/report/dangerEventReport");
export const loginMonitor = post("/ponysafety2/a/user/loginMonitor");
export const passedVehicleRecordQuery = post("/ponysafety2/a/report/passedVehicleRecordQuery");
export const queryexruleinfolite = post("/ponysafety2/a/sysconfig/queryexruleinfolite");
export const operateexruleinfolite = post("/ponysafety2/a/sysconfig/operateexruleinfolite");
export const queryfenseinfoassets = post("/ponysafety2/a/fense/queryfenseinfoassets");
export const queryfenserelatelite = post("/ponysafety2/a/fenserelate/queryfenserelatelite");
export const operatefenserelatelite = post("/ponysafety2/a/fenserelate/operatefenserelatelite");
export const getPassApprovalTree = post("/ponysafety2/a/sysarea/getPassApprovalTree");
export const removePassApproval = post("/ponysafety2/a/passApproval/removePassApproval");
export const queryJurisdiction = post("/ponysafety2/a/activiti/queryJurisdiction");
export const queryAccessRoutes = post("/ponysafety2/a/activiti/queryAccessRoutes");
export const searchDriverCheck = post("/ponysafety2/a/report/queryHistoryOfVehicleInspection");
export const searchCompanyRecord = post("/ponysafety2/a/department/queryDeptCustom");
export const editCompanyRecord = post("/ponysafety2/a/department/operateDeptCustom");
export const getCheckSetting = post("/ponysafety2/a/sysconfig/queryVehicleSafetyInspectionConfiguration");
export const editCheckSetting = post("/ponysafety2/a/sysconfig/vehicleSafetyInspectionConfiguration");
export const operateMedia1078Params = post("/ponysafety2/a/message/operateMedia1078Params");
export const operatebusinessarea = post("/ponysafety2/a/fense/operatebusinessarea");
export const exportCompanyRecord = post("/ponysafety2/a/department/exportDeptCustom");
export const importCompanyRecord = postWithoutTime("/ponysafety2/a/department/importDeptCustomStep2");
export const getTTS = get("/ponysafety2/a/sysconfig/queryTtsTemplateConfig");
export const addTTS = post("/ponysafety2/a/sysconfig/insertTtsTemplateConfig");
export const editTTS = post("/ponysafety2/a/sysconfig/updateTtsTemplateConfig");
export const delTTS = get("/ponysafety2/a/sysconfig/deleteSysConfig");
export const getSpeedSetting = post("/ponysafety2/a/sysconfig/operateSpeedConfig");
export const getVehicleWash = post("/ponysafety2/a/vehicleWash/getVehicleWashRecords");
export const getGpsRecordBAreaIO = post("/ponysafety2/a/map/getGpsRecordBAreaIO");
export const getTerminalType = get("/ponysafety2/a/terminaltype/getTerminalTypeListV2");
export const getSwitchDoor = post("/ponysafety2/a/door/report");
export const getLastTemp = get("/ponysafety2/a/temp/lastTemp");
export const getTimeRange = get("/ponysafety2/a/temp/timeRange");
export const restrictedAreaSwitch = get("/ponysafety2/a/jurisdiction/restrictedAreaSwitch");
export const jurisdictionSwitch = get("/ponysafety2/a/jurisdiction/jurisdictionSwitch");
export const getEquipmentGpsRecord = post("/ponysafety2/a/temp/getEquipmentGpsRecord");
export const queryRfid = post("/ponysafety2/a/rfid/queryRfid");
export const getMixingStationTree = post("/ponysafety2/a/erp/treeMixingStation");
export const getFenseProjectList = post("/ponysafety2/a/erp/getERPProject");
export const exportFenseProjectList = post("/ponysafety2/a/erp/exportERPProject");
export const erpOrderList = post("/ponysafety2/a/erp/erpOrder");
export const getApprovalTable = post("/ponysafety2/a/approval/absorption/get/table");
export const getApprovalTableRowDetail = post("/ponysafety2/a/approval/absorption/get/detail");
export const insertApprovalTableRow = post("/ponysafety2/a/approval/absorption/insert");
export const sunmitApproval = post("/ponysafety2/a/approval/absorption/update");
export const removeApprovalTableRow = post("/ponysafety2/a/approval/absorption/back");
//工地审批
export const getWorkApprovalTable = post("/ponysafety2/a/approval/work/get/table");
export const getWorkApprovalTableRowDetail = post("/ponysafety2/a/approval/work/get/detail");
export const insertWorkApprovalTableRow = post("/ponysafety2/a/approval/work/insert");
export const sunmitWorkApproval = post("/ponysafety2/a/approval/work/update");
export const removeWorkApprovalTableRow = post("/ponysafety2/a/approval/work/back");
//运输许可证审批
export const getTransportationApprovalTable = post("/ponysafety2/a/approval/trans/get/table");
export const getTransportationApprovalTableRowDetail = post("/ponysafety2/a/approval/trans/get/detail");
export const insertTransportationApprovalTableRow = post("/ponysafety2/a/approval/trans/insert");
export const sunmitTransportationApproval = post("/ponysafety2/a/approval/trans/update");
export const removeTransportationApprovalTableRow = post("/ponysafety2/a/approval/trans/back");
//好运连连案件管理
export const wyCaseMgtList = post("/ponysafety2/a/approval/case/get/table");
export const wyCaseAdd = post("/ponysafety2/a/approval/case/insert");
export const wyCaseDetail = post("/ponysafety2/a/approval/case/get/detail");
export const wyCaseDel = post("/ponysafety2/a/approval/case/delete");
export const wyCaseUpdate = post("/ponysafety2/a/approval/case/update");
// 福田车辆数据明细
export const getDataItemTreeSuperFleet = post("/ponysafety2/a/map/getDataItemTreeSuperFleet");
export const getCarDataSearch = post("/ponysafety2/a/report/superfleet/history/get");
// 茅台经销商管理
export const searchDealer = post("/ponysafety2/a/foton/moutai/dealer/get/table");
export const insertDealer = post("/ponysafety2/a/foton/moutai/dealer/insert");
export const updateDealer = post("/ponysafety2/a/foton/moutai/dealer/update");
export const deleteDealer = post("/ponysafety2/a/foton/moutai/dealer/delete");
export const exportDealer = post("/ponysafety2/a/foton/moutai/dealer/export");
// 茅台运单管理
export const insertWaybill = post("/ponysafety2/a/foton/moutai/waybill/insert");
export const searchWaybill = post("/ponysafety2/a/foton/moutai/waybill/query");
export const updateWaybill = post("/ponysafety2/a/foton/moutai/waybill/update");
export const deleteWqybill = get("/ponysafety2/a/foton/moutai/waybill/delete");
//茅台电子锁
//最后状态查询
export const lockLastQuery = post("/ponysafety2/a/foton/moutai/lock/last");
//管控
export const lockSend = post("/ponysafety2/a/foton/moutai/lock/send");
// 开锁记录查询
export const lockedQuery = post("/ponysafety2/a/foton/moutai/lock/operations");
// 电子锁状态记录查询
export const lockStatusQuery = post("/ponysafety2/a/foton/moutai/lock/status");
// 开关门记录查询
export const switchDoorQuery = post("/ponysafety2/a/door/report");
// 运单监控
// 运单监控最新数据接口
export const billMonitorLast = post("/ponysafety2/a/foton/moutai/bill/monitor/last");
//打卡申请
export const billLockApproval = post("/ponysafety2/a/foton/moutai/lock/process");
//茅台运单详情
export const billDetailTable = post("/ponysafety2/a/foton/moutai/bill/table");
export const billDetailDis = post("/ponysafety2/a/foton/moutai/bill/detail");
export const autoLogin = get("/ponysafety2/auto");
export const getTempAreaConfiguration = post("/ponysafety2/a/temp/getTempAreaConfiguration");
export const updateTempAreaConfiguration = post("/ponysafety2/a/temp/updateTempAreaConfiguration");
//福田车辆油耗查询
export const getFuTianCarFuelConsumption = post("/ponysafety2/a/foton/fuelconsumption/report");
// 车辆到期服务查询
export const getcarExpirationTime = post("/ponysafety2/a/vehicle/valid/get");
// 设置车辆到期服务时间
export const changecarExpirationTime = post("/ponysafety2/a/vehicle/valid/set");
// 义乌交警自定义扣分
export const policeScoreYW = post("/ponysafety2/a/report/policeScoreYW");
// 司机费用查询
export const expenseQuery = post("/ponysafety2/a/foton/fee/query");
// 终端属性查看
export const getTerminalHWInfo = post("/ponysafety2/a/terminal/getTerminalHWInfo");
// 报警统计查询
export const getAlarmSummaryInfoPage = post("/ponysafety2/a/report/getalarmstatsvehicle");
// 商砼滚筒正反转信号模拟
export const erpRollerSignal = post("/ponysafety2/a/erp/erpRollerSignal");

// 部标2.0迁移过来的接口
export const getMiniCmdEBill = post("/ponysafety2/a/cmdex/getminicmdebill");
// 驾驶员身份识别
export const getMiniCmdDriverVerify = post("/ponysafety2/a/cmdex/getminicmddriververify");
export const GetTerminalNoAndType = post("/ponysafety2/a/common/getTreeVehicleTerNo");
export const GetCodeTypeList = get("/ponysafety2/a/common/getSysDictByCode");
export const Sendministandardcommon = post("/ponysafety2/a/message/sendministandardcommon");
export const Decodedrdata = post("/ponysafety2/a/vehicle/decodedrdata");

// 透传查看
export const getMiniCmdDataTransfer = post("/ponysafety2/a/cmdex/getminicmddatatransfer");
//压缩数据上报
export const getMiniCmdCompressData = post("/ponysafety2/a/cmdex/getminicmdcompressdata");
// 多媒体数据上报
export const getMiniCmdMultiMediaOperate = post("/ponysafety2/a/cmdex/minicmdmultimediaoperate");
//多媒体事件上报
export const getMiniCmdMediaEvent = post("/ponysafety2/a/cmdex/getminicmdmediaevent");

// 里程统计
export const minidrivemilequery = post("/ponysafety2/a/report/minidrivemilequery");
// 部门上下线统计
export const getDeptIsLine = post("/ponysafety2/a/report/getDeptIsLine");
// 车辆上下线统计
export const GetVehicleIsLine = post("/ponysafety2/a/report/getVehicleIsLine");
export const Getvehiclelinedetail = post("/ponysafety2/a/report/getvehiclelinedetail");
// can数据查询
export const getMiniCmdCanData = post("/ponysafety2/a/cmdex/getminicmdcandata");
//报警类型校验
export const checkConfirmAlarmType = post("/ponysafety2/a/cmdex/checkConfirmAlarmType");

// 轨迹完整率查询
export const statsGpsRecordValid = post("/ponysafety2/a/cmdex/statsGpsRecordValid");
//设备状态
export const deviceOnline = post("/ponysafety2/a/cmdex/deviceOnline");
// 获取指令树
export const getInstructionType = post("/ponysafety2/a/cmdex/miniCmdTree");
// 获取指令日志
export const getInstructionList = post("/ponysafety2/a/cmdex/miniCmdRecord");
// 视频日志
export const setVideoLog = post("/ponysafety2/a/cmdex/mediaLog");
// 音视频参数解析
export const analysis = post("/ponysafety2/a/cmdex/mini1078ParamAnalysis");
//视屏通道顺序配置
export const operateChannelSort = post("/ponysafety2/a/sysconfig/operateChannelSort");
// 视频流量统计
export const miniTrafficStats = post("/ponysafety2/a/cmdex/miniTrafficStats");
// 查询车辆综合信息
export const getCarInformation = post("/ponysafety2/a/cmdex/miniVehicleInfo");
//录像文件（视频调阅）
export const mediaQuery = post("/ponysafety2/a/cmdex/mediaQuery");
// M600冷链终端参数解析
export const m600ParamAnalysis = post("/ponysafety2/a/message/m600ParamAnalysis");
//油耗分析维度数据接口
export const fuelconsumptionNew = post("/ponysafety2/a/foton/fuelconsumption/report/dimension");
// 油耗日历
export const fuelConsumptionCalendar = post("/ponysafety2/a/foton/fuelconsumption/report/calendar");
// 实时风险监控台查询数据
export const riskMonitor = post("/ponysafety2/a/report/riskMonitor");
// 疲劳驾驶查询
export const FatiguedrivieQuery = post("/ponysafety2/a/report/nanpingTired");

// 赛科车辆评分查询
export const seccoDimensionScore = post("/ponysafety2/a/report/seccoDimensionScore");

//运单(pt)
export const billQueryPT = post("/ponysafety2/a/putian/bill/query");
export const addBillQueryPT = post("/ponysafety2/a/putian/bill/insert");
export const modifyBillQueryPT = post("/ponysafety2/a/putian/bill/update");
export const deleteBillQueryPT = get("/ponysafety2/a/putian/bill/delete");

// 案件管理（PT）
export const caseQueryPT = post("/ponysafety2/a/putian/case/query");
export const addCaseQueryPT = post("/ponysafety2/a/putian/case/insert");
export const modifyCaseQueryPT = post("/ponysafety2/a/putian/case/update");
export const deleteCaseQueryPT = get("/ponysafety2/a/putian/case/delete");

// 莆田评分
export const carScorePT = post(" /ponysafety2/a/putian/case/score");
// 报警处理统计
export const alarmDealStats = post("/ponysafety2/a/cmdex/alarmDealStats");
// 方量报表查询
export const erpOrderCube = post(" /ponysafety2/a/erp/erpOrderCube");
// 工程项目树
export const getProjectTree = post(" /ponysafety2/a/erp/treeMixingStation");
// erp送货单
export const addErp = post("/ponysafety2/a/erp/operateERPOrder");
// 路口右转未停车
export const analysisCrossRightTurn = post("/ponysafety2/a/report/analysisCrossRightTurn");
export const setERPOrderState = post("/ponysafety2/a/erp/setERPOrderState");
// 修改erp工程项目
export const operateERPProject = post("/ponysafety2/a/erp/operateERPProject");
// 查询行驶记录管理记录仪
export const getDBDRData = post("/ponysafety2/a/cmdex/getDBDRData");
// 乘客超载报警
export const manualAlarm = post("/ponysafety2/a/cmdex/manualAlarm");
// 指令下发0110解析
export const mini808ParamAnalysis = post("/ponysafety2/a/cmdex/mini808ParamAnalysis");
// 乘客流量查询
export const terminalPassenger = post("/ponysafety2/a/cmdex/terminalPassenger");
// 配置1078
export const operateMini1078Config = post("/ponysafety2/a/sysconfig/operateMini1078Config");
export const storeMiniVideoList = post("/ponysafety2/a/cmdex/storeMiniVideoList");
// 保存视频抓图
export const operateVideoScreenshot = post("/ponysafety2/a/cmdex/operateVideoScreenshot");
// 租赁车辆树
export const getRentalVehicleTree = post("/ponysafety2/a/putian/tree/rent/vehicle");
// 808转发配置
export const operateForward808Config = post("/ponysafety2/a/sysconfig/operateForward808Config");
// 公开页面莆田企业评分
export const getScore = get("/ponysafety2/open/putian/score");
// 赛科报警分析报警详情
export const seccoAlarmDetail = post(" /ponysafety2/a/report/seccoAlarmDetail");
// 赛科报警分析报警统计
export const seccoAlarmStats = post(" /ponysafety2/a/report/seccoAlarmStats");
//车辆分组操作接口
export const operateUserVehicleGroup = post("/ponysafety2/a/user/operateUserVehicleGroup");
//车辆分组树
export const treeUserVehicleGroup = post("/ponysafety2/a/user/treeUserVehicleGroup");
//在线率统计
export const onlineRate = post("/ponysafety2/a/yixing/online/area");
// 事件报警等级配置
export const operateWarnColorConfig = post(" /ponysafety2/a/sysconfig/operateWarnColorConfig");
// 实时监控查询要变色的报警
export const getAlarmHeight = post("/ponysafety2/a/yixing/alarm/level/high");
//车辆树是否有修改的操作
export const treeVehicleChange = post("/ponysafety2/a/vehicle/updated/tree/vehicle");
// 冷机状态监控
export const compressor = post("/ponysafety2/a/temp/m600/monitor/compressor");
// 冷机状态监控远程管控
export const sendCompressor = post("/ponysafety2/a/temp/m600/send/compressor");
// 上下级平台交换分组管理修改
export const editPlatformGrouping = post("/ponysafety2/a/link/updateLinkUpParam");
// 上下级平台交换分组管理新增
export const addPlatformGrouping = post("/ponysafety2/a/link/addLinkUpParam");
// 上下级平台交换分组管理删除
export const delPlatformGrouping = post("/ponysafety2/a/link/deleteLinkUpParam");
// 上下线统计
export const analysisVehicleOnlineJWD = post("/ponysafety2/a/report/analysisVehicleOnlineJWD");
// 停车统计
export const parkingStatistics = post("/ponysafety2/a/rrd/stop/stats");
// 行车统计
export const trafficStatistics = post("/ponysafety2/a/rrd/run/stats");

//流量卡查询
export const dataFlowCard = post("/ponysafety2/a/dataFlowCard/select");
export const updateDataFlowCard = post("/ponysafety2/a/dataFlowCard/update");
export const dataFlowCardPool = post("/ponysafety2/a/dataFlowCard/select/pool");
export const updateDataFlowCardPool = post("/ponysafety2/a/dataFlowCard/update/pool");
export const planDataFlowCard = post("/ponysafety2/a/dataFlowCard/select/plan");
export const importDataFlowCard = post("/ponysafety2/a/dataFlowCard/import");

// 司机人脸相关信息查询
// export const getDriverFaceInfo = post("/ponysafety2/a/driver/getDriverFaceInfo")
//大华人脸识别查询司机消息
export const getDriverFaceInfo = post("/ponysafety2/a/driver/getDriverFaceInfo");
//大华人脸数据打包解包
export const operateFaceIdDhParams = post("/ponysafety2/a/message/operateFaceIdDhParams");
export const analysisVehicleOfflineDetailJWD = post("/ponysafety2/a/report/analysisVehicleOfflineDetailJWD");
export const analysisVehicleOfflineHandleJWD = post("/ponysafety2/a/report/analysisVehicleOfflineHandleJWD");

//新首页相关接口
//今日全部车辆
export const getWatchBoardAll = get("/ponysafety2/a/watchBoard/all");
//今日报警统计
export const getTodayAlarm = get("/ponysafety2/a/watchBoard/today");
//昨日车辆
export const getWatchBoardYesterDay = get("/ponysafety2/a/watchBoard/yesterday");
//提醒中心
export const getNotify = get("/ponysafety2/a/watchBoard/notify");
//消息中心
export const getMessage = get("/ponysafety2/a/watchBoard/message");

// 查询报警统计
export const getAlarmStats = post("/ponysafety2/a/rrd/alarm/stats");
export const getAlarmInfoPage = post("/ponysafety2/a/report/getalarminfopage");
// 查询报警分组
export const getAlarmGroup = post("/ponysafety2/a/user/operateUserAlarmGroup");
// 查询用户分组树
export const getUserAlarmGroupTree = post("/ponysafety2/a/user/treeUserAlarmGroup");
//用户管理密码转换
export const pwdTransfor = get("/ponysafety2/a/user/pwd");

// 莆田开放页面企业详情
export const openDetail = get("/ponysafety2/open/department");
// 莆田通知公告
export const scoreManage = post("/ponysafety2/a/putian/case/article");
//历史位置(俊伟达)
export const historyAddress = post("/ponysafety2/a/rrd/position/history");

// 视频统计
export const videosTraffic = post("/ponysafety2/a/monitor/traffic/video");
//未处理报警
export const alarmUndeal = post("/ponysafety2/a/rrd/alarm/undeal");
//未处理报警置顶/取消置顶
export const alarmUndealTop = post("/ponysafety2/a/rrd/alarm/undeal/top");

//清运运单
//运单列表查询
export const clearWayBillList = post("/ponysafety2/a/small5/list");
//运单详情
export const clearWayBillDetail = post("/ponysafety2/a/small5/detail");
//新增运单
export const addClearWayBill = post("/ponysafety2/a~5/add");
//指派司机、取消运单
export const clearWayBillProcess = post("/ponysafety2/a/small5/process");
//运单司机、车辆列表查询
export const getList = post("/ponysafety2/a/small5/object");
//商砼大屏
export const alarmCountInfo = get("/ponysafety2/a/monitor/board/shangtong/alarm/cnt");
export const vehicleAlarmList = get("/ponysafety2/a/monitor/board/shangtong/vehicle/alarm/topn");
export const coreAlarmInfo = get("/ponysafety2/a/monitor/board/shangtong/alarm/report");
export const mileData = get("/ponysafety2/a/monitor/board/shangtong/mile");
export const baseData = get("/ponysafety2/a/monitor/board/shangtong/base");
export const vehicleAlarmShangtong = get("/ponysafety2/a/monitor/board/shangtong/vehicle/alarm");
// 查询小程序二维码
export const getcommonlistbykey = post("/ponysafety2/a/common/getcommonlistbykey");

//车辆备案批量修改
export const updateBatch = post("/ponysafety2/a/vehicle/update/batch");
//黑工地处理报表
export const statsSuspectedConstructionSite = post("/ponysafety2/a/report/statsSuspectedConstructionSite");
//黑工地消纳场审批
export const handleSuspectedConstructionSite = post("/ponysafety2/a/report/handleSuspectedConstructionSite");

//操作日志树
export const getLogTree = get("/ponysafety2/a/common/tree/log/type");
export const callCenterPhone = post("/ponysafety2/a/anonymity/callCenterPhone");
export const callCenterRec = post("/ponysafety2/a/report/callCenterRec");

//企业备案
//查询
export const queryCompany = post("/ponysafety2/a/department/v2/query");
//增删改 (0:新增；1：修改/ -2:删除)
export const operateCompany = post("/ponysafety2/a/department/v2/operate");
//数据导入
export const importDepartFile = postWithoutTime("/ponysafety2/a/department/v2/import2");
//批量导入
export const exportDepartFile = post("/ponysafety2/a/department/v2/export/demo");

//龙港大屏
export const longgang = post("/ponysafety2/a/component/visual/LG");

//证件时效
export const validsquery = post("/ponysafety2/a/vehicle/valids/query");
export const validsupdate = post("/ponysafety2/a/vehicle/valids/update");

//首页erp接口
export const erp = get("/ponysafety2/a/watchBoard/service/erp");

//批量修改
export const multiplemodify1 = post("/ponysafety2/a/department/v2/batch/modify2");
//批量导入
export const multipledriver = postWithoutTime("/ponysafety2/a/driver/readExcelDriverToSql");
export const tablecrossLG = post("/ponysafety2/a/component/table/cross/LG");
// 故障记录查询
export const queryTrouble = post("/ponysafety2/a/device/queryTrouble");
// 故障记录处理
export const operateTrouble = post("/ponysafety2/a/device/operateTrouble");
export const eventShare = post("/ponysafety2/a/report/event/share");

// 分享链接
export const eventShareOpen = get("/ponysafety2/open/event/share");
// 安全日报
export const safetyReport = post("/ponysafety2/a/component/visual/vcare");
// 导出企业备案
export const exportGroup = post("/ponysafety2/a/department/v2/export/dept");
//事故台账
export const getAllAccidentV2 = post("/ponysafety2/a/accident/getvehicleaccidentinfoV2");
export const insertAccidentV2 = post("/ponysafety2/a/accident/addVehicleAccidentinfoV2");
export const updateAccidentV2 = post("/ponysafety2/a/accident/modifyVehicleAccidentinfoV2");
export const removeAccidentInfoV2 = get("/ponysafety2/a/accident/deleteVehicleAccidentinfoV2");

export const getvehiclebyplate = post("/ponysafety2/a/vehicle/getvehiclebyplate");

export const operateRealtimeMonitorAlarmConfig = post("/ponysafety2/a/sysconfig/operateRealtimeMonitorAlarmConfig");

//批量修改用户主页
export const updateUserMainPages = postWithoutTime("/ponysafety2/a/forward/user/updateuser/batch");

//未知车辆运输统计
export const getUnknowVehicle = get("/ponysafety2/a/forward/report/getAbsorptionWorkTripDataUnknowVehicle");
// 查询报警里程
export const getAlarmMile = post("/ponysafety2/a/report/alarm/detail");

//区域查车接口
export const getVehicleIdByArea = post("/ponysafety2/a/map/getVehicleIdByArea");
//短倒时长配置
export const getShortHaulConfig = get("/ponysafety2/a/forward/sysconfig/getShortHaulConfig");
//短倒时长配置修改
export const updateShortHaulConfig = post("/ponysafety2/a/forward/sysconfig/updateShortHaulConfig");
export const getShippingPointShortTripData = post("/ponysafety2/a/forward/report/getShippingPointShortTripData");
//上级平台交互接口
export const upAndDownLink = post("/ponysafety2/a/link/cmd");
//事件短信下发
export const sendMessage = post("/ponysafety2/a/common/cloud/sms");
//收集点管理
export const collectPoint = post(" /ponysafety2/a/cangnan/collect/point");
//任务管理(苍南)
export const collectTask = post("/ponysafety2/a/cangnan/collect/task"); //苍南环卫
//线路管理
export const collectLine = post("/ponysafety2/a/waste/collect/line");
export const getCollectPointTree = get("/ponysafety2/a/sysarea/gettreeareaterminal?web_page_key=collectPointMgt&fill_unknow=0");

export const collectRecord = post("/ponysafety2/a/cangnan/collect/record");
//客服列表
export const getCseList = post("/ponysafety2/a/report/event/cse/list");
export const getCseReport = post("/ponysafety2/a/report/event/cse/report");

export const sgsRecordList = post("/ponysafety2/a/forward/sgs/record/list");
export const sgsRecordRead = get("/ponysafety2/a/forward/sgs/record/read");
export const sgsRecordShare = post("/ponysafety2/a/forward/sgs/record/share");

//工时报表查询
export const getWorkTimeList = post("/ponysafety2/a/sgs/worktime/list");
//疲劳驾驶报表
export const getWorkStatsList = post("/ponysafety2/a/sgs/tiredstats/list");
export const getWorkStatsListYD = post("/ponysafety2/a/sgs/tiredstats/list/yingdi");

//视频配置
export const sgsTaskList = postWithoutTime("/ponysafety2/a/forward/sgs/task/list");
export const sgsTaskIns = postWithoutTime("/ponysafety2/a/forward/sgs/task/ins");
export const sgsTaskMod = postWithoutTime("/ponysafety2/a/forward/sgs/task/mod");
export const sgsTaskDel = postWithoutTime("/ponysafety2/a/forward/sgs/task/del");
//累计驾驶
export const sgsWorkStats = post("/ponysafety2/a/sgs/workstats/list");
export const changeRiskLevel = get("/ponysafety2/a/forward/report/event/risk/level");
export const syncTtsTemplateConfig = postWithoutTime("/ponysafety2/a/forward/sysconfig/syncTtsTemplateConfig");
//风险监控 关注/取消车辆
export const riskFollow = get("/ponysafety2/a/report/event/param");

//装卸货信息
export const sgsBillList = post("/ponysafety2/a/forward/sgs/bill/list");
//事件统计
export const getAlarmEventAnalysisV2 = post("/ponysafety2/a/forward/report/getAlarmEventAnalysisV2");
export const vcareComment = post("/ponysafety2/a/component/visual/vcare/comment");
//中外运大屏接口
export const longWayBill = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/online");
//中外运查询
export const sinotransList = post("/ponysafety2/a/sinotrans/bill/list");
export const sinotransDetail = post("/ponysafety2/a/sinotrans/bill/detail");
export const excelTableTosqlLong = postWithoutTime("/ponysafety2/a/sinotrans/bill/excel/import");
export const sinotransProcess = post("/ponysafety2/a/sinotrans/bill/process");
export const zwyBillNum = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/report");

export const videoSign = post("/ponysafety2/a/forward/sgs/record/remark");

export const sgsRecordReport = post("/ponysafety2/a/forward/sgs/record/report");
export const videoCut = post("/ponysafety2/ffmpeg/cut");
export const videoCutRecord = post("/ponysafety2/a/report/ffmpeg/cut/record");
export const videoCutSave = post("/ponysafety2/a/report/ffmpeg/cut/save");

// 车辆检查报告
export const checkReportQuery = post("/ponysafety2/a/component/visual/vcare/repair");
//sgs终止
export const sgsTaskStop = post("/ponysafety2/a/forward/sgs/task/stop");

//sgs自动录像
export const sgsTaskCover = post("/ponysafety2/a/forward/sgs/record/report/over");
export const sgsTaskRecover = post("/ponysafety2/a/forward/sgs/record/report/recover");
//中外运大屏
export const getZwyBase = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/base");
export const getZwyVehicle = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/trip");
export const getZwyshipping = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/shippingPoints/duration");
export const getZwyAlarm = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/alarm/cnt");
export const getZwyEfficiencyReport = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/efficiency/report");
export const getZwyBill = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill");
export const getZwyBillReport = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/report");
export const getZwyBillOnline = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/online");
export const getZwyAlarmReport = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/alarm");

//新电子锁
// 获取围栏点数据
export const getLockFenceList = post("/ponysafety2/a/sinotrans/lock/fense/list");
//查询围栏ID
export const getLockFenceIdList = post("/ponysafety2/a/sinotrans/lock/fense/idbynum");

//货车线路导航

// export const getLineBillRoute = post("/ponysafety2/a/jianhua/bill/route")
export const getLineBillRoute = post("/ponysafety2/a/common/cloud/route");
//建华运单
export const getJHwayBillList = post("/ponysafety2/a/jianhua/bill/list");
export const getJHwayBillDetail = post("/ponysafety2/a/jianhua/bill/detail");
export const getJHwayBillprocess = post("/ponysafety2/a/jianhua/bill/process");
export const operateJHwayBill = post("/ponysafety2/a/jianhua/bill/operate");
export const importJHwayBill = postWithoutTime("/ponysafety2/a/jianhua/bill/excel/import");
export const getJHoffsetList = post("/ponysafety2/a/jianhua/bill/stats/offset");
export const getJHBillObject = post("/ponysafety2/a/jianhua/bill/object");
//gps监控记录
export const getGpsMonitorList = post("/ponysafety2/a/guming/bill/list");
//中外运个性化表格导出
export const zwyExcelList = post("/ponysafety2/a/sinotrans/export/trip/short");
//删除长途运单
export const deleteLongBillWay = post("/ponysafety2/a/sinotrans/bill/operate");
export const startLongBillWay = post(" /ponysafety2/a/sinotrans/bill/process");

//曝光记录查询
export const getExposure = post("/ponysafety2/a/ningde/getExposure");

//苍南环卫
export const wasteCollectPoint = post("/ponysafety2/a/waste/collect/point");
export const wasteCollectArea = post("/ponysafety2/a/waste/collect/area");

export const wasteCollectTask = post("/ponysafety2/a/waste/collect/task");
export const wasteCollectLine = post("/ponysafety2/a/waste/collect/line");
export const wasteCollectContract = postWithoutTime("/ponysafety2/a/waste/collect/contract");
export const wasteCollectStatistics = post("/ponysafety2/a/waste/collect/statistics");

export const wasteCollectReportDetail = post("/ponysafety2/a/waste/collect/report/detail");
export const deviceHandleTrouble = post("/ponysafety2/a/device/handleTrouble");
//场地设备绑定
export const areaTerminalTree = get("/ponysafety2/a/fense/area/terminal/tree");
export const areaTerminalBind = post("/ponysafety2/a/fense/area/terminal/bind");

export const areaTerminalType = get("/ponysafety2/a/fense/area/terminal/type");
export const passedVehicleRecordStats = post("/ponysafety2/a/report/passedVehicleRecordStats");

//金华大屏
export const monthStatistics = post("/ponysafety2/a/forward/monitor/vehicle/month/statistics");
export const dayStatistics = get("/ponysafety2/a/forward/monitor/vehicle/day/statistics");

export const cangnanDetainRelease = post("/ponysafety2/a/cangnan/park/detain/release");
export const cangnanParkAlarm = post("/ponysafety2/a/forward/cangnan/park/detain/alarm");
//曝光台热力图以及报表
export const operateExposure = post("/ponysafety2/a/ningde/operateExposure");
//扣车记录查询
export const passedVehicleRecordException = post("/ponysafety2/a/report/passedVehicleRecordException");
// 虚拟终端列表
export const getBindingRecords = get("/ponysafety2/a/forward/sysvehicle/getBindingRecords");
//中外运长途大屏新接口
export const zwyLongTrip = get(" /ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/trip");
export const zwyLongRecord = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/record");
export const zwyLongOnlineV2 = get("/ponysafety2/a/forward/monitor/board/zhongwaiyun/vehicle/waybill/onlineV2");
//扫码查看工地\消纳场
export const getWorkSiteOpen = post("/ponysafety2/open/querybusinessarea");

export const vcareEvent = post("/ponysafety2/a/component/visual/vcare/event");

//钥匙柜
export const keycabinetList = post("/ponysafety2/a/forward/keycabinet/list");
export const keycabinetInfo = post("/ponysafety2/a/forward/keycabinet/info");
export const transTimeManageV3 = post("/ponysafety2/a/sinotrans/sinoiov/save/apis/transTimeManageV3");

export const sinotransObject = post("/ponysafety2/a/sinotrans/bill/object");

//中交兴路轨迹回放
export const playbackZX = post("/ponysafety2/a/sinotrans/sinoiov/save/apis/routerPath");
//福田加油记录
export const refuel = post(" /ponysafety2/a/forward/foton/fuelconsumption/report/refuel");

// 车辆经济报表
export const economicReport = post("/ponysafety2/a/forward/operation/analysis/economic/report");
export const economicAverage = post("/ponysafety2/a/forward/operation/analysis/economic/report/average");
export const refuelDetail = post("/ponysafety2/a/forward/foton/fuelconsumption/report/refuel/detail");
//里程明细，行程明细
export const mileageDetail = post("/ponysafety2/a/forward/operation/analysis/mileage");
export const tripDetail = post("/ponysafety2/a/forward/operation/analysis/trip");
//古茗驾驶行为
export const driverStatus = post("/ponysafety2/a/forward/guming/report/stats/drive");
//福田车辆订阅
export const vehicleSubscribe = post("/ponysafety2/a/forward/foton/fuelconsumption/do/subscribe");
export const vehicleSubscribeList = post("/ponysafety2/a/foton/fuelconsumption/report/subscribe");
// post(url,boolen) 第二个参数是, 为true时,第二次请求会打断第一次请求的axios
export const fotonFaultDetails = post("/ponysafety2/a/forward/foton/fault/details");
export const fotonFaultHandle = post("/ponysafety2/a/forward/foton/fault/handle");
//环卫调度
export const wasteCollectDispatch = post("/ponysafety2/a/forward/waste/collect/dispatch");
export const wasteCollectfleet = post("/ponysafety2/a/forward/waste/collect/fleet");

//油耗系数配置
export const fuelCoefficient = post("/ponysafety2/a/sysconfig/vehicle/fuel/coefficient");
//故障码统计

export const faultStatistics = post(" /ponysafety2/a/forward/foton/fault/statistics");

// 矿车大屏
export const mineCarAlarm = get("/ponysafety2/a/forward/monitor/mine/car/alarm?lastDays=1");
export const mineCarBase = get("/ponysafety2/a/forward/monitor/mine/car/base");
export const mineCarDetails = get("/ponysafety2/a/forward/monitor/mine/car/details");

//胎压明细
export const tpmsDetail = post("/ponysafety2/a/forward/tpms/detail");
//油量异常处理
export const fuelRefuelOperate = post("/ponysafety2/a/forward/foton/fuelconsumption/report/refuel/operate");
//普田综合物流大屏
export const complexPT = post("/ponysafety2/a/foton/56/visual/complex");
//普田运单大屏
export const waybillPT = post("/ponysafety2/a/foton/56/visual/waybill");
//普田运单管理
export const waybillMgtPT = post("/ponysafety2/a/forward/foton/56/operate/waybill");
//普田运单分配
export const waybillDistributionPT = post("/ponysafety2/a/forward/foton/56/operate/waybill/distribution");
//车型维护
export const vehicleMaintenance = post("/ponysafety2/a/foton/56/model");
export const importVehicleMaintenance = postWithoutTime("/ponysafety2/a/foton/56/model/excel/import");
export const importWayBillFile = postWithoutTime("/ponysafety2/a/foton/56/waybill/excel/import");
export const importWayInfoFile = postWithoutTime("/ponysafety2/a/foton/56/info/excel/import");
export const importWayInfoFileV2 = postWithoutTime("/ponysafety2/a/foton/56/excel/waybill/import");
// 单价维护
export const standardPrice = post("/ponysafety2/a/forward/foton/56/operate/price");
export const importStandardPrice = importTable("/ponysafety2/a/foton/56/excel/price/import");

export const operateWayMile = post("/ponysafety2/a/forward/foton/56/operate/mile");
export const fotonModelQuery = post("/ponysafety2/a/foton/56/model");

//里程维护
export const standardMileage = post("/ponysafety2/a/forward/foton/56/operate/mile");
export const importStandardMileage = importTable("/ponysafety2/a/foton/56/excel/mileage/import");
export const eventTypeModify = post("/ponysafety2/a/report/event/type/modify");

//获取全部车辆id
export const getsysvehiclepageid = post("/ponysafety2/a/vehicle/getsysvehiclepageid");
//标准单价操作接口
export const operatePrice = post("/ponysafety2/a/forward/foton/56/operate/price");
//单价区域
export const customArea = post("/ponysafety2/a/foton/56/custom/area");
export const importPriceArea = postWithoutTime("/ponysafety2/a/foton/56/custom/area/excel/import");
//行业限速
export const industryOperate = post("/ponysafety2/a/forward/industry/operate");
export const getLastSuperFleet = post("/ponysafety2/a/map/getLastSuperFleet");
//收入结算
export const operateStats = post("/ponysafety2/a/forward/foton/56/operate/cost/stats");
//安全报表
export const getSafetyDay = post("/ponysafety2/a/forward/component/report/foton/safety/day");
//反馈报表
export const getFeedback = post("/ponysafety2/a/forward/waste/collect/feedback");
//油脂清运点位
export const greasePoint = post("/ponysafety2/a/forward/cangnan/grease/point/operate");
//油脂清运作业
export const greaseTask = post("/ponysafety2/a/forward/cangnan/grease/task/operate");
//建华TMS油耗查询
export const jianhuaTMSoil = post("/ponysafety2/a/forward/jianhua/tms/stats/oil");
//sgsLMS运单
export const sgsCspcBillList = post("/ponysafety2/a/forward/sgs/cspc/bill/list");
// 工单巡检
export const operateInspection = post("/ponysafety2/a/forward/device/operateInspection");
export const downloadPdfInspection = post("/ponysafety2/a/device/inspection/pdf");
export const backgroundTask = post("/ponysafety2/a/forward/user/background/task");

//建华订单
export const operateGoodsJH = post("/ponysafety2/a/forward/jianhua/56/operate/goods");
export const operateProjectJH = post("/ponysafety2/a/forward/jianhua/56/operate/project");
export const operateCustomJH = post("/ponysafety2/a/forward/jianhua/56/operate/custom");
export const operateWaybillorOrderJH = post("/ponysafety2/a/forward/jianhua/56/operate/waybill/distribution");
export const operateTableJH = postWithoutTime("/ponysafety2/a/jianhua/waybill/excel/import");
// 平衡计分卡
export const operateRuleDimensionConfig = post("/ponysafety2/a/forward/sysconfig/operateRuleDimensionConfig");

//平衡记分卡
export const dimensionScore = post("/ponysafety2/a/forward/component/report/balance/dimension/score");
//货物代码查询
export const jianhuaGoodsCode = post("/ponysafety2/a/forward/jianhua/56/operate/goods");
export const jianhuaOperateContact = post("/ponysafety2/a/forward/jianhua/56/operate/contact");

export const eventHandleOvertime = post("/ponysafety2/a/forward/report/event/handle/overtime");
//钥匙柜
export const keycabinetRecord = post("/ponysafety2/a/forward/keycabinet/use/record");
export const keycabinetLog = post("/ponysafety2/a/forward/keycabinet/box/log");

export const operateCustomModel = post("/ponysafety2/a/forward/jianhua/56/operate/custom/model");

export const operateTransportPlan = post("/ponysafety2/a/forward/jianhua/56/operate/transport/plan");
export const refuelStatistics = post("/ponysafety2/a/forward/foton/fuelconsumption/report/refuel/statistics");
// 挂车管理
export const trailerOperate = post("/ponysafety2/a/forward/trailer/operate");
export const importTrailer = postWithoutTime("/ponysafety2/a/trailer/info/excel/import");
//车辆绑定相关操作
export const gettrailerlistbyunbind = post("/ponysafety2/a/vehicle/gettrailerlistbyunbind");
export const trailerbind = get("/ponysafety2/a/vehicle/trailer/bind");
export const trailerunbind = get("/ponysafety2/a/vehicle/trailer/unbind");
//车辆明细
export const operateSinograin = post("/ponysafety2/a/vehicle/sinograin");

//sgs 统一处理接口
export const cspcOperate = post("/ponysafety2/a/forward/sgs/cspc/bill/operate");
//承运商列表
export const getCarrierList = get("/ponysafety2/a/forward/sgs/cspc/bill/carrier/list");
// 汇总表导出
export const exportSummary = post("/ponysafety2/a/sgs/export/report/cspc/lms");
//用车记录
export const vehicleRec = post("/ponysafety2/a/forward/vehicle/application/operate/rec");
export const getDriverBindRecordByVehicleId = get("/ponysafety2/a/forward/sysvehicle/getDriverBindRecordByVehicleId");
export const getVehicleBindRecordByDriverId = get("/ponysafety2/a/forward/sysvehicle/getVehicleBindRecordByDriverId");

export const workPlaceImport = postWithoutTime("/ponysafety2/a/fense/excel/import");
export const getTrashWasteTripData = post("/ponysafety2/a/forward/report/getTrashWasteTripData");
//菜单绑定模版
export const getMenuRoles = get("/ponysafety2/a/permission/getMenuRoles");
export const menuExcelImport= postWithoutTime('/ponysafety2/a/permission/excel/import')
//区域围栏树
export const getAreaFenceTree= postWithoutTime("/ponysafety2/a/forward/sysarea/gettreesysareaFense")
//建华路口违规统计
export const crossStatsJH = post("/ponysafety2/a/forward/jianhua/cross/stats");
//28181上级平台管理
export const terminalGBPlatform = post("/ponysafety2/a/forward/terminal/GB/platform");
//28181联网注册设备
export const getRegisterDevice = post("/ponysafety2/a/forward/terminal/GB/device");
//人脸识别
export const faceRecognition = post("/ponysafety2/a/forward/guming/face/compare");
//ocr识别
export const ocrRecognition = post("/ponysafety2/a/forward/ocr/identify");
// 获取车辆在线日期
export const getVehicleOnlineDate = post("/ponysafety2/a/forward/map/getVehicleOnlineDate");