/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/9/27 10:00
 * @LastEditors: yezy
 * @LastEditTime: 2020/9/27 10:00
 * @Description:
 */
const merge = require('webpack-merge');
process.env.LOGIN_USER = process.env.LOGIN_USER || 'pony';


/**
 *
 * 定制登入页面的基本配置信息字典 省的你不晓得啥子意思撒
 * @url （./static/imgNewVersion/userCustom）
 * @tip 本页面使用的所有配置均为图片名称
 *
 */
const defaultModules = {
    // 浏览器头部样式撒
    net: {
        // 浏览器头部的名称
        title: '',

        // 浏览器头部的图标
        href: ''
    },

    // 登入地图样式撒
    layout: {
        // 模板类型名字 （提供给默认调用者 原先的那些定制均作为调用模板向外提供）
        name: '',

        // 默认语言格式  有这个的前提需要在本页面书写好多语言的文字显示哦
        language: '',

        // 【定制背景图片 格式限制 jpg】
        background: '',

        // 【定制图片 限高 500px jpg】          仅在 type == leftview / rightview 生效  （登录框边上的那个图片）
        personalview: '',
        //登陆成功后直接跳转大屏的配置,值是跳转的大屏的路由
        loginSuccess: "xiangtan",
        /**
         * 仅在 type == leftview / rightview 生效  （登入的行业轮播图片）
         * 1： 公交
         * 2： 货运
         * 3： 冷链
         * 4： 拖车
         * 5： 危化品
         * 6： 校车
         * 7： 网约车
         */
        defaultViews: [1,2,3,4,5,6,7],

        // 登录框的高度 一般有默认值的
        viewHeight: '480px',

        //  模板位置 [left center right]
        position: '',

        // [viewonleft, viewonright] 登录框的那个玩意 和 那个 view 的那个啥的位置  type == centerview 无效
        viewPosition: '',

        // 地下的版权信息
        copyRight: false,
    },


    // 登入框框的
    modal: {
        // 框框的样式，基本不用动，除非特殊的 多数只为了合适 padding String
        style: '',

        // 【png 其他无限制只要你效果图放得下就行】 登入的logo
        mainLogo: '',
        //【登入框框标题logo的样式】
        mainLogoStyle:'',
        //【定位在登入框框左上角的logo】
        mainLogoTop:'',
        //【定位在登入框框左上角的logo的样式】
        mainLogoTopStyle:'',
        // logo的位置 多为上 [ top, down ]     仅在 mainlogo 有的时候生效
        logoPosition: '',

        // 【定制图片 多数的话限制等高否则默认底下对齐 png】 拓展logo 合作伙伴可以贴上去哦
        extandLogo: [],

        // 登入的主色调 多为 logo 的主要覆盖颜色，反正随你便
        mainColor: '',

        // 模式  [ black, white ]
        pattern: '',

        // 登入按钮大小
        loginBtnSize: '',
        //登入按钮自定义样式
        loginBtnStyle:'',

        // 记住密码 和 忘记密码权限
        rember: true,
        forget: false,
        loginIcon:true , //true:显示图标  false：显示用户名  和密码,
        bottomImg:''//底部的小字图片
    },


    // 引导页
    guide: {
        // [ custom, template ] 类型  如果不过分可以直接走配置，有些过分的留下名字 （来将可留姓名！）
        type: '',

        // [viewonleft, viewonright] 中心区域有一段文字 + 图片的样子可以变更位置
        position: '',

        // 左上角的可以支持你放个logo 啥的 现在也只有一个页面这么弄过 恶心死
        logo: '',

        // 【可改的非定制图片！！！】 [png]  不能自己乱加的哦有方案可以选择的哦 地下的那仨也是这样的哦 不穿就不显示哦
        topRight: '',

        bottomLeft: '',

        // 中心显示图片
        view: '',

        // 遮罩的阴影浓度
        viewFilter: '50',

        // 文字反正你看这些 [ h1, 为大标题头， h2 为 2级标题， 不写就是默认大小 ] 一个大括号一个自然段（换一行的意思）
        word: [
            {
                text: '一给我哩giao',
                type: 'h1',
            }
        ]
    },

    // 菜单部分可以允许在最中间放上一个图片  当主菜单不负责的哦
    menuHeader: {
        show: false,

        position: '',

        url: '',

        size: '',
    },

    // 特殊的附加配置
    extand: {

    }
}




/**
 *  模板样式  —————————————————————————————— PONY [/pony]
 */
const pony = {
    net: {
        title: '营运车辆安全运输管理系统',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        name: 'pony',
        pattern: 'white',
        language: 'zh-CN',
        background: 'pony_login_bg',
        personalview: '',
        viewHeight: '480px',
        defaultViews: [1,2,3,4,5,6,7],
        position: 'center',
        viewPosition: 'viewonleft',
        copyRight: true,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'pony_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#FF5C33',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            {
                text: 'WOLCOME',
                type: 'h1',
            }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
    language: {
        'zh-CN': {
            net: {
                title: '营运车辆安全运输管理系统',
            },
            modal: {
                mainLogo: 'pony_login_logo'
            },
            guide: {
                word: [
                    { text: '道路运输安全智慧云平台', type: 'h1' },
                    { text: 'Dedicated to the field of car safety driving', type: 'h2' },
                    { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
                    { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
                ]
            }
        },
        'en-US': {
            net: {
                title: 'Safe transportation management system for operating vehicles',
            },
            modal: {
                mainLogo: 'pony_US_login_logo'
            },
            guide: {
                word: [
                    { text: 'Smart cloud platform for road transportation safety', type: 'h1' },
                    { text: 'Dedicated to the field of car safety driving', type: 'h2' },
                    { type: 'h3', text: "Through the real-time upload of multi-dimensional vehicle data, combined with large-scale geographic data analysis, and using scientific driving analysis model, we can find the advantages and disadvantages of drivers' driving, find the accident black spots of daily driving sections, and find the dangerous driving conditions that really need manual intervention, so as to make scientific and accurate analysis after the accident and find out the reasons." },
                    { type: 'h3', text: 'The system can improve the efficiency of information processing of the fleet management system, multi-dimensional analysis of business reports, help enterprises quickly find problems, and put forward effective suggestions and measures, greatly improving the labor efficiency.' }
                ]
            }
        },
        'zh-TW': {
            net: {
                title: '營運車輛安全運輸管理系統',
            },
            modal: {
                mainLogo: 'pony_TW_login_logo'
            },
            guide: {
                word: [
                    { text: '道路運輸安全智慧雲平臺', type: 'h1' },
                    { text: 'Dedicated to the field of car safety driving', type: 'h2' },
                    { type: 'h3', text: '能够通過實时上傳的多維度車輛數據，結合大規模的地理資料分析，利用科學的駕駛分析模型，找到司機駕車的優缺點，找到日常駕駛路段的事故黑點，找到真正需要人工介入干預的危險駕駛狀態，更好的在事故發生後進行科學準確的分析，找到原因。' },
                    { type: 'h3', text: '系統能够提高車隊管理系統的資訊處理效率，多維度分析的業務報表，幫助企業迅速找到問題，並提出有效的建議措施，大大提高人工效率' }
                ]
            }
        }
    }
}



const wdczn = {
    net: {
        title: '智慧安全车辆管理系统',
        href: './static/imgNewVersion/userCustom/wdczn_tab.png'
    },
    layout: {
        name: 'wdczn',
        pattern: 'white',
        language: 'zh-CN',
        background: 'wdczn_login_bg',
        personalview: 'wdczn_login_view',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewonright',
        copyRight: true,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'wdczn_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#00AFF0',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '道路运输安全智慧云平台', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const clzcl = {
    net: {
        title: '城市道路救援安全服务监管系统',
        href: './static/imgNewVersion/userCustom/clzcl_tab.png'
    },
    layout: {
        name: 'clzcl',
        pattern: 'white',
        language: 'zh-CN',
        background: 'clzcl_login_bg',
        personalview: '',
        viewHeight: '480px',
        defaultViews: [],
        position: 'left',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px; width: 530px',
        mainLogo: 'clzcl_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#FF5C33',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: '',
        bottomLeft: 'blue-bl',
        view: 'clzcl-view',
        viewFilter: '50',
        extandLogo: ['clzcl-logo_title'],
        extandSize: '',
        word: [
            { text: '城市道路救援安全服务监管系统 V 2.0', type: 'h1' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: 'center',
        url: 'clzcl-header',
        size: '',
    },
}



const zglt = {
    net: {
        title: '营运车辆安全运输管理系统',
        href: './static/imgNewVersion/userCustom/zglt_tab.png'
    },
    layout: {
        name: 'zglt',
        pattern: 'white',
        language: 'zh-CN',
        background: 'zglt_login_bg',
        personalview: 'zglt_login_view',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewonright',
        copyRight: true,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'zglt_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#F32837',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        word: [
            { text: '道路运输安全智慧云平台', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const jhztc = {
    net: {
        title: '金华市渣土车智慧监管平台',
        href: './static/imgNewVersion/userCustom/jhztc_tab.png'
    },
    layout: {
        name: 'jhztc',
        pattern: 'white',
        language: 'zh-CN',
        background: 'jhztc_login_bg',
        personalview: '',
        viewHeight: '500px',
        defaultViews: [],
        position: 'left',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px; width: 530px',
        mainLogo: 'jhztc_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#15C597',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: '',
        bottomLeft: 'default-bl',
        view: 'jhztc-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '金华市渣土车智慧监管平台', type: 'h1' },
            { type: 'h3', text: '通过车载终端实时上传的多维度车辆运行数据，结合大规模的地理数据分析， 基于科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点， 找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，寻找事故成因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const hfjt = {
    net: {
        title: '恒风集团道路运输安全智慧云平台',
        href: './static/imgNewVersion/userCustom/ywky_tab.png'
    },
    layout: {
        name: 'hfjt',
        pattern: 'white',
        language: 'zh-CN',
        background: 'hfjt_login_bg',
        personalview: '',
        viewHeight: '500px',
        defaultViews: [],
        position: 'left',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px 50px 50px; width: 530px',
        mainLogo: 'hfjt_login_logo',
        logoPosition: 'top',
        extandLogo: ['sponsor_ai', 'sponsor_hk'],
        mainColor: '#009BBE',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: '',
        bottomLeft: 'default-bl',
        view: 'hfjt-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '恒风集团道路运输安全智慧云平台', type: 'h1' },
            { type: 'h3', text: '通过车载终端实时上传的多维度车辆运行数据，结合大规模的地理数据分析， 基于科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点， 找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，寻找事故成因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const jiande = {
    net: {
        title: '建德市鹰眼护航智慧管控平台',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        name: 'jiande',
        pattern: 'white',
        language: 'zh-CN',
        background: 'jiande_login_bg',
        personalview: 'jiande_login_view',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewonleft',
        copyRight: true,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'jiande_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#F32837',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '建德市鹰眼护航智慧管控平台', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '通过车载终端实时上传的多维度车辆运行数据，结合大规模的地理数据分析， 基于科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点， 找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，寻找事故成因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const zljt = {
    net: {
        title: '中联交通安全服务监管系统',
        href: './static/imgNewVersion/userCustom/zljt_tab.png'
    },
    layout: {
        name: 'zljt',
        pattern: 'white',
        language: 'zh-CN',
        background: 'zljt_login_bg',
        personalview: '',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewonleft',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px; width: 530px',
        mainLogo: 'zljt_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#F32837',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: '',
        bottomLeft: '',
        view: 'zljt-view',
        viewFilter: '',
        extandLogo: ['zljt-logo', 'clzcl-logo'],
        extandSize: '40px',
        word: [
            { text: '全国智能交通安全大数据中心', type: 'h1' },
            { text: '——中联交通安全服务监管系统', type: 'h2' },
            { type: 'h3', text: '通过对“人—车—路”信息采集与挖掘，实现对交通道路施工、施救、勘察现场及人员工作行为进行分析、通过实时上传的多维度车辆数据，结合大规模的地理数据分析，能够有效减少交通事故的发生，为国家交通提供安全保护，是适用于国家高速公路及城市运营、消防、急救等安全应急车辆的综合性监控管理服务平台。' }
        ]
    },
    menuHeader: {
        show: false,
        position: 'center',
        url: 'zljt-header',
        size: '',
    },
}


const hipony = {
    net: {
        title: '海马建筑垃圾清运智慧管理系统',
        href: './static/imgNewVersion/userCustom/hipony_tab.png'
    },
    layout: {
        name: 'hipony',
        pattern: 'white',
        language: 'zh-CN',
        background: 'hipony_login_bg',
        personalview: 'hipony_login_view',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewonright',
        copyRight: true,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: '',
        logoPosition: 'top',
        extandLogo: ['sponsor_hk'],
        mainColor: '#2A80E0',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '海马建筑垃圾清运智慧管理系统', type: 'h1' },
            { type: 'h3', text: '海马系统，是海康威视面向渣土车，商砼车等城市工程车辆，进行安全监管和运营服务的一体化软件平台。基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const jhpolice = {
    net: {
        title: '金华市工程车辆监管及通行证审批系统',
        href: './static/imgNewVersion/userCustom/jhpolice_tab.png'
    },
    layout: {
        name: 'jhpolice',
        pattern: 'white',
        language: 'zh-CN',
        background: 'jhztc_login_bg',
        personalview: '',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px; width: 535px',
        mainLogo: 'jhpolice_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#15C597',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: '',
        bottomLeft: 'default-bl',
        view: 'jhztc-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '金华市工程车辆监管及通行证审批系统', type: 'h1' },
            { type: 'h3', text: '通过车载终端实时上传的多维度车辆运行数据，结合大规模的地理数据分析， 基于科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点， 找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，寻找事故成因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}


const macao = {
    net: {
        title: 'IP+人車安全智慧雲',
        href: './static/imgNewVersion/userCustom/macao_tab.png'
    },
    layout: {
        name: 'macao',
        pattern: 'white',
        language: 'zh-TW',
        background: 'macao_login_bg',
        personalview: '',
        viewHeight: '480px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        style: 'padding: 40px 50px; width: 530px',
        mainLogo: 'macao_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#2c80de',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'macao-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: 'IP+人車安全智慧雲', type: 'h1' },
            { text: 'IPSafety+™ FMS', type: 'h2' },
            { type: 'h3', text: '能够通過實时上傳的多維度車輛數據，結合實際的地理資料分析，以及實時路況智能辨識，利用駕駛分析模型，找到司機駕車的安全點，找到日常駕駛路段的事故黑點，找到實際需要人工介入干預的危險駕駛狀態，更好的在事故發生前後進行準確的判斷分析，找到原因。' },
            { type: 'h3', text: '系統能够提高車隊管理的資訊處理效率，多維度分析的業務報表，幫助車隊及管理人員迅速找到問題，並提出有效的建議措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const truck = {
    net: {
        title: '城市渣土运输智慧管理系统',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        name: 'truck',
        pattern: 'white',
        language: 'zh-CN',
        background: 'truck_login_bg',
        personalview: 'truck_login_view',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'truck_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#0f4c81',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '城市渣土运输智慧管理系统', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}
const junk = {
    net: {
        title: '垃圾清运智慧一体化监管平台',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        name: 'junk',
        pattern: 'white',
        language: 'zh-CN',
        background: 'junk_login_bg',
        personalview: 'junk_login_view',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
		shadow: false

    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'junk_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#009b83',
        pattern: 'junk',
        loginBtnSize: '80%',
        rember: true,
        forget: false,
	    loginIcon: false

    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '垃圾清运智慧一体化监管平台', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}
const loop = {
    net: {
        title: '综合性固体废弃物循环利用信息化系统',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        name: 'truck',
        pattern: 'white',
        language: 'zh-CN',
        background: 'truck_login_bg',
        personalview: 'truck_login_view',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'loop_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#0f4c81',
        pattern: 'white',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '综合性固体废弃物循环利用信息化系统', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}
const nfhb = {
    net: {
        title: "智慧环卫管理系统",
        href: "./static/imgNewVersion/userCustom/nfhb_tab.png"
    },
    layout: {
        name: "truck",
        pattern: "nfhb",
        language: "zh-CN",
        background: "truck_login_bg",
        personalview: "nfhb_login_view",
        viewHeight: "500px",
        defaultViews: [],
        positio: "center",
        viewPosition: "viewoncenter",
        viewPositionBgc:"nobgc",
        copyRight: false
    },
    modal: {
        style: "padding: 30px 30px; width: 300px;border-radius:0 15px 15px 0",
        mainLogo: "nfhb_login_logo",
        mainLogoTop:"nfhb_logo",
        mainLogoStyle:'min-height:50px',
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#0f4c81",
        pattern: "nfhb",
        loginBtnSize: "75%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "智慧环卫管理系统",
                "type": "h1"
            },
            {
                "text": "Dedicated to the field of car safety driving",
                "type": "h2"
            },
            {
                "type": "h3",
                "text": "能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。"
            },
            {
                "type": "h3",
                "text": "系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。"
            }
        ]
    },
    menuHeader: {
        show: false,
        position: "",
        url: "",
        size: ""
    },
    language: null
}

const transport = {
    theme: 'lightBlue',
    net: {
        title: '主动安全车队管理系统',
        href: './static/imgNewVersion/userCustom/transport_tab.png'
    },
    layout: {
        name: 'foton',
        pattern: 'white',
        language: 'zh-CN',
        background: 'futian_login_bg',
        personalview: '',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
    },
    modal: {
        style: 'padding: 20px 60px 50px 60px; width: 520px; border-radius: 6px',
        mainLogo: 'futian_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#4676FB',
        pattern: 'black',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '主动安全车队管理系统', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}



const foton = {
    theme: 'lightBlue',
    net: {
        title: '主动安全车队管理系统',
        href: './static/imgNewVersion/userCustom/futian_tab.png'
    },
    layout: {
        name: 'foton',
        pattern: 'white',
        language: 'zh-CN',
        background: 'futian_login_bg',
        personalview: '',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
    },
    modal: {
        style: 'padding: 20px 60px 50px 60px; width: 520px; border-radius: 6px',
        mainLogo: 'futian_login_logo',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#4676FB',
        pattern: 'black',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: '',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '主动安全车队管理系统', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}

const htgf = {
    net: {
        title: '海通固废厨余垃圾清运服务管理系统',
        href: './static/imgNewVersion/userCustom/haitong_login_logo.png'
    },
    layout: {
        name: 'junk',
        pattern: 'white',
        language: 'zh-CN',
        background: 'junk_login_bg',
        personalview: 'haitong_login_left',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
		shadow: false

    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'haitong_login_title',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#009b83',
        pattern: 'junk',
        loginBtnSize: '80%',
        rember: true,
        forget: false,
	    loginIcon: false

    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            // { text: '海通固废垃圾清运系统', type: 'h1' },
            // { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            // { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            // { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}
const ywhj = {
    net: {
        title: '义乌市环境集团环卫作业车辆安全防御系统',
        href: './static/imgNewVersion/userCustom/yw_logo_icon.png'
    },
    layout: {
        name: 'junk',
        pattern: 'white',
        language: 'zh-CN',
        background: 'junk_login_bg',
        personalview: 'haitong_login_left',
        viewHeight: '500px',
        defaultViews: [],
        position: 'center',
        viewPosition: 'viewoncenter',
        copyRight: false,
		shadow: false

    },
    modal: {
        style: 'padding: 60px 30px; width: 300px',
        mainLogo: 'yw_login_title',
        logoPosition: 'top',
        extandLogo: [],
        mainColor: '#009b83',
        pattern: 'junk',
        loginBtnSize: '80%',
        rember: true,
        forget: false,
	    loginIcon: false

    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            // { text: '海通固废垃圾清运系统', type: 'h1' },
            // { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            // { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            // { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
}

/**
 *
 * 模板运用 ———————————————————————————————— PONY [/dwr]
 *
 */

const fuseCustomData = (model, data) => {
    let keys = Object.keys(data)
    keys.forEach(item => {
        data[item]?Object.assign(model[item], data[item]):model[item] = null
    })
    return model
}


const dwr = fuseCustomData(JSON.parse(JSON.stringify(pony)), {
    net: {
        title: '道为尔北斗智联信息服务平台',
    },
    modal: {
        mainLogo: 'dwr_login_logo',
        mainColor: '#5298FD',
    },
    guide: {
        word: [
            { text: '道为尔北斗智联信息服务平台', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    language: null
})


const cgo8 = fuseCustomData(JSON.parse(JSON.stringify(pony)), {
    net: {
        title: 'Cgo8车辆主动安全智能防控系统',
    },
    modal: {
        mainLogo: 'cgo8_login_logo',
        mainColor: '#FF5C33',
    },
    guide: {
        logo: '',
        word: [
            { text: 'Cgo8车辆主动安全智能防控系统', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    language: null
})


const locoway = fuseCustomData(JSON.parse(JSON.stringify(pony)), {
    net: {
        title: '城市渣土运输智慧管理系统',
    },
    modal: {
        mainLogo: 'locoway_login_logo',
    },
    guide: {
        word: [
            { text: '道路运输安全智慧云平台', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    language: null
})


const ningd = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '宁德市渣土运输智慧管理系统',
        href: './static/imgNewVersion/userCustom/ningd_tab.png'
    },
    modal: {
        mainLogo: 'ningd_login_logo',
        extandLogo: ['sponsor_ycy'],
    },
    guide: {
        logo: '',
        word: [
            { text: '宁德市渣土运输智慧管理系统', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    language: null
})


const yx = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '宜兴市城市环卫信息管理系统',
    },
    layout: {
        personalview: 'yx_login_view',
    },
    modal: {
        mainLogo: 'yx_login_logo',
    },
    guide: {
        logo: '',
        view: 'yx-view',
        viewFilter: '',
        word: [
            { text: '宜兴市“城市环卫”监督管理系统', type: 'h1' },
            { text: '宜兴市“城市环卫”监督管理系统, 是宜兴市城市管理局针对城市建筑垃圾、工程渣土及散装混凝土运输管理平台。主要实现以下几项功能：', type: 'h3' },
            { type: 'h3', text: '1.实现源头管控，实施对建筑工地起始点和运输终点的目标信息管理，实现对工地甲方、施工单位及运输单位管理，对工地的土方类型以出土量；实现对渣土运输目的地的点位信息管理。' },
            { type: 'h3', text: '2.实现运输过程管控，对运输单位建档，运输车辆准运审核备案，驾驶员备案，实施运输起始点、运输线路及运输终点的定位管控，建立有效的可追溯数据统计，防止违规运输等违法行为。' }
        ]
    },
    language: null
})

const nanp = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '南平市智慧渣土平台',
    },
    modal: {
        mainLogo: 'nanp_login_logo',
    },
    guide: {
        logo: '',
        word: [
            { text: '南平市智慧渣土平台', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    language: null
})

// 淮安-泰晟，2022年4月18日13:44:25
const huaian = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '淮安智慧渣土管控系统',
        href: './static/imgNewVersion/userCustom/huaian_tab.png'
    },
    modal: {
        mainLogo: 'huaian_login_logo',
    },
    guide: {
        logo: '',
        word: [
            { text: '淮安智慧渣土管控系统', type: 'h1' },
            { type: 'h3', text: '本系统是面向淮安城市渣土车工程车辆、为用户提供使用简单、可用性高的一体化综合管控平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，统计汇总车辆运营数据，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、企业等多用户角色，企业端在线申请工地、消纳场和车辆运输通行许可电子证书，管理端对申请数据进行验证审核，打通数据壁垒，提升管理效率。' }
        ]
    },
    language: null
})


// 福田冷链
const coldchain = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
    net: {
        title: 'SuperFleet 冷链运输智慧管理系统',
    },
    layout: {
        background: 'futian_coldchain_login_bg',
    },
    modal: {
        mainLogo: 'futian_coldchain_login_logo',
    },
    guide: {
        logo: '',
        word: [
            { text: 'SuperFleet 冷链运输智慧管理系统', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据及车厢温度数据，全程监控冷链物流，及时推送温度异常提醒，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，事故溯源。' },
            { type: 'h3', text: '提升冷链运输管理过程的信息处理效率，结合多维度的业务报表，实现运输过程透明化管理，帮助企业有效找到问题，提出相关建议并有效解决问题，大大提高人工效率。' }
        ]
    }
})

// 福田主动安全
const safety = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
    net: {
        title: '北京智科车辆卫星定位及视频监控平台',
    },
    layout: {
        background: 'futian_safety_login_bg',
    },
    modal: {
        mainLogo: 'safety_login_logo',
    },
    guide: {
        logo: '',
        word: [
            { text: 'SuperFleet 主动安全车队管理系统', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})
const safetylz = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
    net: {
        title: '主动安全车队管理系统',
    },
    layout: {
        background: 'futian_safety_login_bg',
    },
    modal: {
        mainLogo: 'futian_safety_login_logo',
        style: "padding: 43px 60px 50px 60px; width: 520px; border-radius: 6px",
		mainLogoTop: "safetylz_logo",
		mainLogoTopStyle: "position: absolute;left: 25px;top: 0px;width:auto",
    },
    guide: {
        logo: '',
        word: [
            { text: 'SuperFleet 主动安全车队管理系统', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})
// 同治车联
const tzcl = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
    net: {
        title: '同治车联',
        href: './static/imgNewVersion/userCustom/tzcl_tab.png'

    },
    layout: {
        background: 'tzcl_safety_login_bg',
    },
    modal: {
        mainLogo: 'futian_safety_login_logo',
    },
    guide: {
        logo: '',
        word: [
            { text: '同治车联 主动安全车队管理系统', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})
const xyjc = {
    theme: "lightBlue",
    net: {
        title: "金华市血液中心智慧监控系统",
        href: "./static/imgNewVersion/userCustom/xyjc_tab.png"
    },
    layout: {
        name: "xyjc",
        pattern: "white",
        language: "zh-CN",
        background: "xyjc_login_bg",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "right",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        style: "padding: 43px 60px 40px 60px;",
        mainLogo: "xyjc_login_logo",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#db4242",
        pattern: "white",
        loginBtnSize: "60%",
        rember: true,
        forget: false,
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "金华市血液中心智慧监控系统",
                "type": "h1"
            },
            {
                "type": "h2",
                "text": "Dedicated to the field of car safety driving"
            },
            {
                "type": "h3",
                "text": "能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。"
            },
            {
                "type": "h3",
                "text": "系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。"
            }
        ]
    },
    menuHeader: {
        "show": false,
        "position": "",
        "url": "",
        "size": ""
    }
}
const ptzt = {
    theme: "lightBlue",
    net: {
        title: "北斗三号应用示范",
        href: "./static/imgNewVersion/userCustom/ptzt_tab.png"
    },
    layout: {
        name: "ptzt",
        pattern: "white",
        language: "zh-CN",
        background: "ptzt_login_bg",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "right",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        style: "padding: 20px 40px 40px 40px; width: 400px; border-radius: 6px",
        mainLogo: "ptzt_login_logo",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#52CBFF",
        pattern: "picture",
        loginBtnSize: "70%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "北斗三号应用示范",
                "type": "h1"
            },
            {
                "type": "h3",
                "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
            },
            {
                "type": "h3",
                "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
            },
            {
                "type": "h3",
                "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
            }
        ]
    },
    menuHeader: {
        "show": false,
        "position": "",
        "url": "",
        "size": ""
    }
}
const sgs_cspc = {
  net: {
      title: 'SIP CSPC',
      href: './static/imgNewVersion/userCustom/sgs_tab.png'
  },
  layout: {
      name: 'sgs',
      pattern: 'white',
      language: 'zh-CN',
      background: 'sgs_login_bg',
      personalview: 'sgs_login_view',
      viewHeight: '500px',
      defaultViews: [],
      position: 'center',
      viewPosition: 'viewoncenter',
      copyRight: false,
      shadow: false,
      viewPositionBgc: "nobgc",
      bottomImg: "./static/imgNewVersion/userCustom/sgs_bottomImg.png"

  },
  modal: {
      style: 'padding: 45px 30px; width: 320px',
      mainLogo: 'cspc_login_logo',
      mainLogoStyle:'width:290px;margin-left:-15px;margin-bottom:10px',
      logoPosition: 'top',
      extandLogo: [],
      mainColor: '#ff9217',
      pattern: 'sgs',
      loginBtnSize: '80%',
      rember: true,
      remberColor:"#064b82",
      forget: false,
    loginIcon: false,

  },
  guide: {
      type: 'mould',
      position: 'viewonright',
      logo: '',
      topRight: 'default-tr',
      bottomLeft: 'default-bl',
      view: 'default-view',
      viewFilter: '50',
      extandLogo: [],
      extandSize: '',
      word: [
          {
              "text": "中海壳牌石油化工有限公司",
              "type": "h1"
          },
      ]
  },
  menuHeader: {
      show: false,
      position: '',
      url: '',
      size: '',
  },
}
const sgs = {
  net: {
      title: 'SIP SECCO',
      href: './static/imgNewVersion/userCustom/sgs_tab.png'
  },
  layout: {
      name: 'sgs',
      pattern: 'white',
      language: 'zh-CN',
      background: 'sgs_login_bg',
      personalview: 'sgs_login_view',
      viewHeight: '500px',
      defaultViews: [],
      position: 'center',
      viewPosition: 'viewoncenter',
      copyRight: false,
  shadow: false,
  bottomImg: "./static/imgNewVersion/userCustom/sgs_bottomImg.png"


  },
  modal: {
      style: 'padding: 45px 30px; width: 320px',
      mainLogo: 'sgs_login_logo',
      mainLogoStyle:'width:270px;margin-left:-5px;margin-bottom:10px',
      logoPosition: 'top',
      extandLogo: [],
      mainColor: '#ff9217',
      pattern: 'sgs',
      loginBtnSize: '80%',
      rember: true,
      remberColor:"#064b82",
      forget: false,
    loginIcon: false

  },
  guide: {
      type: 'mould',
      position: 'viewonright',
      logo: 'default-logo',
      topRight: 'default-tr',
      bottomLeft: 'default-bl',
      view: 'default-view',
      viewFilter: '50',
      extandLogo: [],
      extandSize: '',
      word: [
          {
              "text": "上海赛科石化工有限责任公司",
              "type": "h1"
          },
          {
              "type": "h3",
              "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
          },
          {
              "type": "h3",
              "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
          },
          {
              "type": "h3",
              "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
          }
      ]
  },
  menuHeader: {
      show: false,
      position: '',
      url: '',
      size: '',
  },
}
const jnfj = {
    theme: "lightBlue",
    net: {
        title: "江南分局",
        href: "./static/imgNewVersion/userCustom/jnfj_tab.png"
    },
    layout: {
        name: "jnfj",
        pattern: "white",
        language: "zh-CN",
        background: "jnfj_login_bg",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "center",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        style: "padding: 20px 40px 40px 40px; width: 495px; border-radius: 10px",
        mainLogo: "jnfj_login_logo",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#233f7e",
        pattern: "jnfj",
        loginBtnSize: "60%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "江南分局",
                "type": "h1"
            },
            {
                "type": "h3",
                "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
            },
            {
                "type": "h3",
                "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
            },
            {
                "type": "h3",
                "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
            }
        ]
    },
    menuHeader: {
        "show": false,
        "position": "",
        "url": "",
        "size": ""
    }
}

const jhztn = {
    theme: "lightBlue",
    net: {
        title: "金东区工程渣土处置监督管理中心",
        href: "./static/imgNewVersion/userCustom/jhztc_tab.png"
    },
    layout: {
        name: "jhztn",
        pattern: "white",
        language: "zh-CN",
        background: "jhztn_login_bg",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "center",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        style: "padding: 20px 40px 40px 40px; width: 495px; border-radius: 10px",
        mainLogo: "jhztn_login_logo",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#104c7f",
        pattern: "jnfj",
        loginBtnSize: "60%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "金东区工程渣土处置监督管理中心",
                "type": "h1"
            },
            {
                "type": "h3",
                "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
            },
            {
                "type": "h3",
                "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
            },
            {
                "type": "h3",
                "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
            }
        ]
    },
    menuHeader: {
        "show": false,
        "position": "",
        "url": "",
        "size": ""
    }
}
const chache = {
  theme: "lightBlue",
  net: {
      title: "叉车智慧管理平台",
      href: "./static/imgNewVersion/userCustom/pony_tab.png"
  },
  layout: {
      name: "chache",
      pattern: "white",
      language: "zh-CN",
      background: "chache_login_bg",
      personalview: "",
      viewHeight: "500px",
      defaultViews: [],
      position: "center",
      viewPosition: "viewoncenter",
      copyRight: false
  },
  modal: {
      style: "padding: 20px 80px; width: 495px;height:410px; border-radius: 10px;background-color: rgba(234, 235, 237);position: absolute;right: 12%;top:50%;margin-top:-205px",
      mainLogo: "chache_login_logo",
      logoPosition: "top",
      extandLogo: [],
      mainColor: "#e38136",
      pattern: "jnfj",
      loginBtnSize: "75%",
      loginBtnStyle:"box-shadow: 4px 3px 9px 0px rgba(56,31,4,0.3);height:50px;",
      rember: true,
      forget: false,
      loginIcon:false
  },
  guide: {
      type: "mould",
      position: "viewonright",
      logo: "",
      topRight: "default-tr",
      bottomLeft: "default-bl",
      view: "default-view",
      viewFilter: "50",
      extandLogo: [],
      extandSize: "",
      word: [
          {
              "text": "叉车智慧管理平台",
              "type": "h1"
          },
          {
              "type": "h3",
              "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
          },
          {
              "type": "h3",
              "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
          },
          {
              "type": "h3",
              "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
          }
      ]
  },
  menuHeader: {
      "show": false,
      "position": "",
      "url": "",
      "size": ""
  }
}
const shyh = {
    theme: "lightBlue",
    net: {
        title: "行车驾驶员智慧监控系统",
        href: "./static/imgNewVersion/userCustom/pony_tab.png"
    },
    layout: {
        name: "shyh",
        pattern: "white",
        language: "zh-CN",
        background: "shyh_login_bg",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "center",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        style: "padding: 20px 40px 40px 40px; width: 495px; border-radius: 10px;background-color: rgba(255, 255, 255, 0.9)",
        mainLogo: "shyh_login_logo",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#234380",
        pattern: "jnfj",
        loginBtnSize: "60%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        logo: "",
        topRight: "default-tr",
        bottomLeft: "default-bl",
        view: "default-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        word: [
            {
                "text": "行车驾驶员智慧监控系统",
                "type": "h1"
            },
            {
                "type": "h3",
                "text": "本系统属于北斗应急体系支撑示范应用中的业务子系统，基于北斗时空数据底座面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理业务平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。"
            },
            {
                "type": "h3",
                "text": "基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。"
            },
            {
                "type": "h3",
                "text": "本系统提供城管、交警、住建、市政、协会、企业等多用户角色，位置数据统一来源于北斗时空数据底座，为各级部门提供业务服务，打通数据壁垒。"
            }
        ]
    },
    menuHeader: {
        "show": false,
        "position": "",
        "url": "",
        "size": ""
    }
}
// 福田主动安全
const ftcar = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
  net: {
      title: '车辆智慧管理平台',
  },
  layout: {
      background: 'ftcar_login_bg',
  },
  modal: {
      style:'padding: 20px 40px 50px 60px; width: 390px; height:480px;border-radius: 10px;position: absolute;right: 15%;top: 50%;margin-top: -240px;',
      mainLogo: 'ftcar_login_logo',
      pattern: 'ftcar',
      mainColor: '#0f4c81',
      loginBtnSize:'70%',
      rember: true,
			forget: false,
      loginIcon: false
  },
  guide: {
      logo: '',
      word: [
          { text: 'SuperFleet 主动安全车队管理系统', type: 'h1' },
          { type: 'h2', text: 'Dedicated to the field of car safety driving'},
          { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
          { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
      ]
  }
})
const jhjy = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
  net: {
      title: '金华监狱安全管理服务系统',
      href: "./static/imgNewVersion/userCustom/jhjy_tab.png"
  },
  layout: {
      background: 'jhjy_login_bg',
      personalview: "jhjy_login_view",
			viewHeight: "auto",
      pattern: "jhjy",
  },
  modal: {
      style:'padding: 20px 40px 50px 60px; width: 390px; height:480px;border-radius: 10px;position: absolute;right: 12%;top: 50%;margin-top: -240px;',
      mainLogo: 'jhjy_login_logo',
      pattern: 'ftcar',
      mainColor: '#0f4c81',
      loginBtnSize:'70%',
      rember: true,
			forget: false,
      loginIcon: false
  },
  guide: {
      logo: '',
      view: "jhjy-view",
      topRight: "",
      view: "jhjy-view",
			viewFilter: "100",
      pattern:"jhjy",
      word: [
          { text: '金华监狱安全管理服务系统', type: 'h1' },
          { type: 'h2', text: 'Dedicated to the field of car safety driving'},
          { type: 'h3', text: '金华监狱安全管理服务平台，能够通过大规模地理数据、驾驶行为数据的采集、融合和统计分析，为客户提供更加精准的安全管理服务' },
      ]
  }
})
// 福田主动安全
const yw = fuseCustomData(JSON.parse(JSON.stringify(foton)), {
    net: {
        title: '义乌市重点车辆数字监管项目',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    layout: {
        background: 'yw_login_bg',
    },
    modal: {
        mainLogo: 'yw_login_logo',
        pattern: 'white',
        mainColor: '#0F4C81'
    },
    guide: {
        logo: '',
        word: [
            { text: '义乌市重点车辆数字监管项目', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})

const sws = fuseCustomData(JSON.parse(JSON.stringify(jhztc)), {
    net: {
        title: '邵武市渣土车智慧监管平台',
        href: './static/imgNewVersion/userCustom/wys_tab.png'
    },
    layout: {
        background: 'yw_login_bg',
    },
    modal: {
        mainLogo: 'sws_login_logo',
        pattern: 'white',
        mainColor: '#006ab7'
    },
    guide: {
        logo: '',
        word: [
            { text: '邵武市渣土车智慧监管平台', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})


const wys = fuseCustomData(JSON.parse(JSON.stringify(jhztc)), {
    net: {
        title: '武夷山市渣土车智慧监管平台',
        href: './static/imgNewVersion/userCustom/wys_tab.png'
    },
    layout: {
        background: 'yw_login_bg',
    },
    modal: {
        mainLogo: 'wys_login_logo',
        pattern: 'white',
        mainColor: '#006ab7'
    },
    guide: {
        logo: '',
        word: [
            { text: '武夷山市渣土车智慧监管平台', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})


// 宁德霞浦县，2022年7月1日14:16:22
const xiapu = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '霞浦县渣土运输智慧管理系统',
        href: './static/imgNewVersion/userCustom/pony_tab.png'
    },
    modal: {
        mainLogo: 'xiapu_login_logo',
        extandLogo: ['developer_xiaojviot'],
    },
    guide: {
        logo: '',
        word: [
            { text: '霞浦县渣土运输智慧管理系统', type: 'h1' },
            { text: 'Dedicated to the field of car safety driving', type: 'h2' },
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    language: null
})

const jinyu = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '锦宇汽车',
        href: './static/imgNewVersion/userCustom/jinyu_tab.png'
    },
    layout: {
        background: 'jinyu_login_bg',
        language: "zh-CN",
        personalview: "",
        viewHeight: "500px",
        defaultViews: [],
        position: "right",
        viewPosition: "viewoncenter",
        copyRight: false
    },
    modal: {
        mainLogo: 'jinyu_login_logo',
        style: "padding: 20px 40px 40px 40px; width: 400px; border-radius: 6px",
        logoPosition: "top",
        extandLogo: [],
        mainColor: "#52CBFF",
        pattern: "jinyu",
        loginBtnSize: "70%",
        rember: true,
        forget: false,
        loginIcon:false
    },
    guide: {
        type: "mould",
        position: "viewonright",
        topRight: '',
        logo: "jinyu-logo",
        view: "jinyu-view",
        viewFilter: "50",
        extandLogo: [],
        extandSize: "",
        pattern: "jinyu",
        word: [
            { text: '道路运输安全智慧云平台', type: 'h1' },
            { type: 'h2', text: 'Dedicated to the field of car safety driving'},
            { type: 'h3', text: '能够通过实时上传的多维度车辆数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
            { type: 'h3', text: '系统能够提高车队管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    }
})

// 周宁县
const zhouning = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '周宁县渣土车运输智慧管理系统',
    },
    modal: {
        mainLogo: 'zhouningztc',
    },
    guide: {
        logo: '',
        word: [
            { text: '周宁县渣土车运输智慧管理系统', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    language: null
})

// 苍南执法局
const cangnanzfj = fuseCustomData(JSON.parse(JSON.stringify(truck)), {
    net: {
        title: '苍南执法局智慧车辆管理系统',
    },
    modal: {
        mainLogo: 'cangnanzfj_mainlogo',
    },
    guide: {
        logo: '',
        word: [
            { text: '苍南执法局智慧车辆管理系统', type: 'h1' },
            { type: 'h3', text: '本系统是面向渣土车、商砼车等城市工程车辆、为用户提供使用简单、可用性高的一体化综合管理平台。既面向政府部门提供监管分析服务，又面向企业提供日常监控服务。' },
            { type: 'h3', text: '基于源头管控、过程监管和大数据分析的闭环业务逻辑，秉承“一平台操作、一张图展示、一张表统计，一标准覆盖”的设计理念。利用高效的多维度数据处理引擎MATISIS，科学分析驾驶行为，寻找事故隐患点，提高系统运行效率。' },
            { type: 'h3', text: '本系统提供城管、交警、城建、市政、协会、企业等多用户角色，方便多部门统一管理，打通数据壁垒。' }
        ]
    },
    language: null
})
const qwts =  {
    net: {
        title: '智能执勤肩灯管理系统',
        href: './static/imgNewVersion/userCustom/qwt_title.png'
    },
    layout: {
        name: 'qwts',
        pattern: 'white',
        language: 'zh-CN',
        background: 'qwts_bg',
        personalview: '',
        viewHeight: '500px',
        defaultViews: [],
        position: 'left',
        viewPosition: 'viewoncenter',
        copyRight: true,
    },
    modal: {
        mainLogo: '',
        logoPosition: 'top',
        extandLogo: [],
        style: "padding: 40px 40px 40px 40px; width: 480px;",
        mainColor: '#006ab7',
        loginIcon:false,
        pattern: 'qwts',
        loginBtnSize: '60%',
        rember: true,
        forget: false,
    },
    guide: {
        type: 'mould',
        position: 'viewonright',
        logo: 'default-logo',
        topRight: 'default-tr',
        bottomLeft: 'default-bl',
        view: 'default-view',
        viewFilter: '50',
        extandLogo: [],
        extandSize: '',
        word: [
            { text: '4G智能执勤肩灯管理系统', type: 'h1' },
                    { text: 'Intelligent shoulder lamp management system', type: 'h2' },
                    // { type: 'h3', text: '能够通过实时上传的多维度数据，结合大规模的地理数据分析，利用科学的驾驶分析模型，找到司机驾车的优缺点，找到日常驾驶路段的事故黑点，找到真正需要人工介入干预的危险驾驶状态，更好的在事故发生后进行科学准确的分析，找到原因。' },
                    { type: 'h3', text: '系统能够提高管理系统的信息处理效率，多维度分析的业务报表，帮助企业迅速找到问题，并提出有效的建议措施，大大提高人工效率。' }
        ]
    },
    menuHeader: {
        show: false,
        position: '',
        url: '',
        size: '',
    },
   
}

module.exports = {
    sws,
    pony,
    dwr,
    wdczn,
    clzcl,
    zglt,
    jhztc,
    hfjt,
    cgo8,
    jiande,
    zljt,
    hipony,
    jhpolice,
    macao,
    truck,
    locoway,
    ningd,
    yx,
    nanp,
    foton,
    transport,
    ptzt,
    safety,
    coldchain,
    yw,
    wys,
    tzcl,
    xiapu,
    xiapu,
    nfhb,
    jinyu,
    zhouning,
    cangnanzfj,
    qwts,
    sgs,
    safetylz,
    jnfj,
    jhztn,
    loop,
    junk,
    htgf,
    shyh,
    xyjc,
    sgs_cspc,
    ywhj,
    ftcar,
    chache,
    jhjy

}
