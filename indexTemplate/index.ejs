<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="renderer" content="webkit"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <title id="currentSystemTitle"></title>
    <link rel="shortcut icon" id="currentSystemLinkUrl" href="" type="image/x-icon"/>
    <!-- <link id="iconfont" href="../ srcRenderer/assets/styles/font/iconfont/iconfont.css" rel="stylesheet" /> -->

    <link id="iconfont" href="https://at.alicdn.com/t/c/font_2178396_vitncft9fma.css" rel="stylesheet"/>
    <!--    <link href="https://cdn.jsdelivr.net/npm/animate.css@3.5.2/animate.min.css" rel="stylesheet" />-->
    <!--    <link href="https://cdn.jsdelivr.net/npm/video.js@6.6.0/dist/video-js.min.css" rel="stylesheet" />-->
    <!-- <link href="https://cdn.staticfile.org/animate.css/3.5.2/animate.min.css" rel="stylesheet" /> -->
    <!-- <link href="https://cdn.staticfile.org/video.js/6.6.0/video-js.min.css" rel="stylesheet" /> -->
    <!-- <link rel="stylesheet" href="https://cdn.staticfile.org/leaflet/1.7.1/leaflet.min.css" /> -->

    <% if (htmlWebpackPlugin.options.nodeModules) { %>
        <!-- Add `node_modules/` to global paths so `require` works properly in development -->
        <script>
          require('module').globalPaths.push('<%= htmlWebpackPlugin.options.nodeModules.replace(/\\/g, '\\\\') %>')
        </script>
    <% } %>

</head>
<body>
<div id="app"></div>
<!-- Set `__static` path to static files in production -->
<% if (!process.browser) { %>
    <script>
      if (process.env.NODE_ENV !== 'development') window.__static = require('path').join(__dirname, '/static').replace(/\\/g, '\\\\')

    </script>
<% } %>
<!-- 开发环境写死一个线上地址(本地地址会报错) -->
<% if (process.env.NODE_ENV == 'development') { %>
    <script>
      const hrefD = 'https://coldchain.superfleet.com.cn'
      window._AMapSecurityConfig = {
        serviceHost: hrefD + '/_AMapService'
      }
    </script>
    <script src="static/jessibuca/jessibuca.js" ></script>
<% } %>
<!-- 生产环境动态生成生产环境地址 -->
<% if (process.env.NODE_ENV !== 'development') { %>
    <script>
      // const href = window.location.origin
      const href = 'https://coldchain.superfleet.com.cn'
      window._AMapSecurityConfig = {
        serviceHost: href + '/_AMapService'
      }
    </script>
    <script src="./jessibuca.js" ></script>
<% } %>
<!--    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/moment@2.22.2/moment.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/@reactivex/rxjs@5.5.2/dist/global/Rx.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.12.10/dist/xlsx.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/axios@0.18.0/dist/axios.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/video.js@6.6.0/dist/video.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/jspdf@1.4.0/dist/jspdf.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.5/dist/html2canvas.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/echarts@5.2.1/dist/echarts.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/@turf/turf@5.1.6/turf.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/jquery@3.4.1/dist/jquery.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/ztree@3.5.24/js/jquery.ztree.all.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/ztree@3.5.24/js/jquery.ztree.exhide.min.js"></script>-->
<!--    <script src="https://static.runoob.com/assets/qrcode/qrcode.min.js"></script>-->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/lodash.js/4.17.10/lodash.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/moment.js/2.22.2/moment.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/rxjs/5.5.2/Rx.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/xlsx/0.12.0/xlsx.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.18.0/axios.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/video.js/6.6.0/video.min.js"></script> -->
<!-- <script src="https://cdn.staticfile.org/jspdf/1.4.0/jspdf.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> -->

<!-- <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script> -->
<!-- <script src="https://cdn.staticfile.org/Turf.js/5.1.6/turf.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/Turf.js/6.5.0/turf.min.js"></script> -->

<!-- <script src="https://cdn.staticfile.org/jquery/3.7.1/jquery.min.js"></script> -->
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script> -->

<!-- <script src="https://cdn.staticfile.org/zTree.v3/3.5.24/js/jquery.ztree.all.js"></script> -->
<!-- <script src="static/ckplayer/jquery.ztree.all.js"></script> -->
<!-- <script src="/srcRenderer/components/ztree/util/jquery.ztree.exhide.min.js"></script> -->
<!-- <script type="text/javascript" src="static/ckplayer/ckplayer.js"></script> -->
<script type="text/javascript" src="static/ckplayer/bmapKey.js" async="async"></script>
</body>
</html>
